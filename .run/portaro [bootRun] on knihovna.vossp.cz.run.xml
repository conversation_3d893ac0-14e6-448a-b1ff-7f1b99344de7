<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="portaro [bootRun] on knihovna.vossp.cz" type="GradleRunConfiguration" factoryName="Gradle">
    <ExternalSystemSettings>
      <option name="env">
        <map>
          <entry key="APPSERVER_URL" value="http://*************:8209" />
          <entry key="DATABASE_HOST" value="*************" />
          <entry key="DATABASE_PORT" value="3094" />
          <entry key="PORTARO_INI__OPAC__ForceHttps" value="NE" />
          <entry key="PORTARO_INI__OPAC__URL" value="http://localhost" />
        </map>
      </option>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$/portaro-runner" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="-Penv=develop --warning-mode=all --stacktrace -Ptested" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value="bootRun" />
        </list>
      </option>
      <option name="vmOptions" value="" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <RunAsTest>false</RunAsTest>
    <method v="2" />
  </configuration>
</component>