package cz.kpsys.portaro.appserver;

import cz.kpsys.portaro.commons.io.ToByteArraySavingFileStreamConsumer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.client.match.MockRestRequestMatchers;
import org.springframework.test.web.client.response.DefaultResponseCreator;
import org.springframework.web.client.RestTemplate;

import static cz.kpsys.portaro.commons.web.HttpHeaderConstants.ContentDisposition;
import static cz.kpsys.portaro.commons.web.HttpHeaderConstants.ContentLength;
import static org.springframework.http.HttpMethod.GET;
import static org.springframework.http.HttpStatus.OK;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;

@Tag("ci")
@Tag("unit")
public class AppserverFileDataStreamerTest {

    public static final Long FILE_ID = 42704L;
    public static final String FILE_NAME = "cover_96771.jpg";
    private MockRestServiceServer mockServer;
    private RestTemplate rest;

    @BeforeEach
    public void init() {
        rest = new RestTemplate();
        mockServer = MockRestServiceServer.createServer(rest);
    }

    @Test
    public void testStreamData() {
        AppserverFileDataStreamer loader = AppserverFileDataStreamer.ofOriginalData("http://appserver-mock.cz", rest);

        mockServer.expect(
                request -> {
                    Assertions.assertEquals("appserver-mock.cz", request.getURI().getHost());
                    Assertions.assertEquals("/nacti_blob", request.getURI().getPath());
                })
                .andExpect(MockRestRequestMatchers.queryParam("ID_FULLTEXT", FILE_ID.toString()))
                .andExpect(MockRestRequestMatchers.queryParam("ZDROJ", "ORIGINAL"))
                .andExpect(method(GET))
                .andRespond(standardResponse());

        ToByteArraySavingFileStreamConsumer reader = new ToByteArraySavingFileStreamConsumer();

        loader.streamData(FILE_ID, null, reader);

        Assertions.assertNotNull(reader.getFilename());
        Assertions.assertEquals(FILE_NAME, reader.getFilename());
        Assertions.assertTrue(reader.getSize() > 0);
        Assertions.assertNotNull(reader.getData());
        Assertions.assertTrue(reader.getData().length > 0);
        Assertions.assertEquals(reader.getSize(), reader.getData().length);
    }

    @Test
    public void testStreamImageThumbnail() {
        AppserverFileDataStreamer loader = AppserverFileDataStreamer.ofThumbnailData("http://appserver-mock.cz", rest);

        mockServer.expect(
                        request -> {
                            Assertions.assertEquals("appserver-mock.cz", request.getURI().getHost());
                            Assertions.assertEquals("/nacti_blob", request.getURI().getPath());
                        })
                .andExpect(MockRestRequestMatchers.queryParam("ID_FULLTEXT", FILE_ID.toString()))
                .andExpect(MockRestRequestMatchers.queryParam("ZDROJ", "NAHLED"))
                .andExpect(method(GET))
                .andRespond(standardResponse());

        ToByteArraySavingFileStreamConsumer reader = new ToByteArraySavingFileStreamConsumer();

        loader.streamData(FILE_ID, null, reader);

        Assertions.assertNotNull(reader.getFilename());
        Assertions.assertEquals(FILE_NAME, reader.getFilename());
        Assertions.assertTrue(reader.getSize() > 0);
        Assertions.assertNotNull(reader.getData());
        Assertions.assertTrue(reader.getData().length > 0);
        Assertions.assertEquals(reader.getSize(), reader.getData().length);
    }

    private DefaultResponseCreator standardResponse() {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(ContentDisposition.NAME, ContentDisposition.Generator.ATTACHMENT_AND_FILENAME.apply(FILE_NAME));
        httpHeaders.add(ContentLength.NAME, "12");

        String fileContent = "file-content";

        return withStatus(OK)
                .contentType(APPLICATION_JSON)
                .headers(httpHeaders)
                .body(fileContent);
    }

}