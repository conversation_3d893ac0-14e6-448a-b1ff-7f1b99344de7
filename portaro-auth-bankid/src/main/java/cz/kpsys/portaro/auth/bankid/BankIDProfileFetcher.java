package cz.kpsys.portaro.auth.bankid;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.bankid.reponse.BankIDProfileResponse;
import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.token.Scope;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestOperations;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BankIDProfileFetcher {

    @NonNull RestOperations rest;
    @NonNull ContextualProvider<Department, @NonNull String> profileUrlProvider;
    @NonNull Eventer eventer;
    @NonNull Provider<@NonNull User> bankIDUserProvider;
    @NonNull BankIDLoginEventNameResolver bankIDLoginEventNameResolver;

    public BankIDProfileResult fetchProfileInfo(Department ctx, String accessToken, String refreshToken, Scope scope) {
        HttpEntity<?> request = createRequest(accessToken);
        String profileUrl = profileUrlProvider.getOn(ctx);
        ResponseEntity<BankIDProfileResponse> response = rest.exchange(profileUrl, HttpMethod.GET, request, BankIDProfileResponse.class);
        BankIDProfileResponse body = response.getBody();
        Assert.state(body != null, "BankID profile response body must be not null.");
        UserAuthentication bankIDCurrentAuth = CurrentAuth.createWithAbsoluteAuthenticity(bankIDUserProvider.get());

        eventer.save(bankIDLoginEventNameResolver.resolve(body, scope), bankIDCurrentAuth, ctx, new BankIDEventData(scope), UUID.fromString(body.sub()));
        return new BankIDProfileResult(body, refreshToken);
    }

    private static HttpEntity<?> createRequest(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        return new HttpEntity<>(headers);
    }
}