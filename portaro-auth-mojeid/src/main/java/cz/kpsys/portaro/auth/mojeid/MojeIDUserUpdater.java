package cz.kpsys.portaro.auth.mojeid;

import cz.kpsys.portaro.auth.AuthPairingProvider;
import cz.kpsys.portaro.auth.SideThreadAuthenticationIsolator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.edit.command.PersonEditationCommand;
import cz.kpsys.portaro.user.merge.UserMerger;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MojeIDUserUpdater {

    @NonNull MojeIDPopulator mojeIDPopulator;
    @NonNull Map<AuthPairingProvider, UserMerger<Department>> userMergerByAuthPairingProviderMap;
    @NonNull SideThreadAuthenticationIsolator authIsolator;

    public User update(Department ctx, User user, MojeIDUserInfoResponse userInfoResponse) {
        PersonEditationCommand populate = mojeIDPopulator.populate(ctx, userInfoResponse);

        return authIsolator.getAuthenticated(ctx, currentAuth -> userMergerByAuthPairingProviderMap.get(AuthPairingProvider.MOJEID).merge(ctx, currentAuth, populate, user));
    }
}
