package org.springframework.security.saml2.provider.service.authentication;

import cz.kpsys.portaro.auth.Authenticator;
import cz.kpsys.portaro.auth.saml2.sp.Saml2AuthenticationRequest;
import cz.kpsys.portaro.auth.saml2.sp.Saml2SuccessAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import net.shibboleth.utilities.java.support.xml.ParserPool;
import org.opensaml.core.config.ConfigurationService;
import org.opensaml.core.xml.config.XMLObjectProviderRegistry;
import org.opensaml.saml.common.assertion.AssertionValidationException;
import org.opensaml.saml.common.assertion.ValidationContext;
import org.opensaml.saml.common.assertion.ValidationResult;
import org.opensaml.saml.saml2.assertion.*;
import org.opensaml.saml.saml2.assertion.impl.AudienceRestrictionConditionValidator;
import org.opensaml.saml.saml2.assertion.impl.BearerSubjectConfirmationValidator;
import org.opensaml.saml.saml2.assertion.impl.DelegationRestrictionConditionValidator;
import org.opensaml.saml.saml2.core.*;
import org.opensaml.saml.saml2.core.impl.ResponseUnmarshaller;
import org.opensaml.saml.security.impl.SAMLSignatureProfileValidator;
import org.opensaml.xmlsec.signature.support.SignaturePrevalidator;
import org.opensaml.xmlsec.signature.support.SignatureTrustEngine;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.saml2.Saml2Exception;
import org.springframework.security.saml2.core.OpenSamlInitializationService;
import org.springframework.security.saml2.core.Saml2Error;
import org.springframework.security.saml2.core.Saml2ErrorCodes;
import org.springframework.security.saml2.core.Saml2ResponseValidatorResult;
import org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistration;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.namespace.QName;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public final class Saml2Authenticator implements Authenticator<Saml2AuthenticationRequest, Saml2SuccessAuthentication> {

    static {
        OpenSamlInitializationService.initialize();
    }

    @NonNull final ResponseUnmarshaller responseUnmarshaller;
    @NonNull final ParserPool parserPool;
    @NonNull final Duration responseTimeValidationSkew = Duration.ofMinutes(5);
    @NonNull final ContextualFunction<OpenSaml4AuthenticationProvider.ResponseToken, Department, Saml2SuccessAuthentication> responseAuthenticationConverter;
    @NonNull final Converter<OpenSaml4AuthenticationProvider.ResponseToken, Saml2ResponseValidatorResult> responseSignatureValidator = createDefaultResponseSignatureValidator();
    @NonNull final Consumer<OpenSaml4AuthenticationProvider.ResponseToken> responseElementsDecrypter = createDefaultResponseElementsDecrypter();
    @NonNull final Converter<OpenSaml4AuthenticationProvider.ResponseToken, Saml2ResponseValidatorResult> responseValidator = createDefaultResponseValidator();
    @NonNull final Converter<OpenSaml4AuthenticationProvider.AssertionToken, Saml2ResponseValidatorResult> assertionSignatureValidator = createDefaultAssertionSignatureValidator();
    @NonNull final Consumer<OpenSaml4AuthenticationProvider.AssertionToken> assertionElementsDecrypter = createDefaultAssertionElementsDecrypter();
    @NonNull final Converter<OpenSaml4AuthenticationProvider.AssertionToken, Saml2ResponseValidatorResult> assertionValidator = createCompatibleAssertionValidator();

    public Saml2Authenticator(@NonNull ContextualFunction<OpenSaml4AuthenticationProvider.ResponseToken, Department, Saml2SuccessAuthentication> responseAuthenticationConverter) {
        XMLObjectProviderRegistry registry = ConfigurationService.get(XMLObjectProviderRegistry.class);
        responseUnmarshaller = (ResponseUnmarshaller) registry.getUnmarshallerFactory().getUnmarshaller(Response.DEFAULT_ELEMENT_NAME);
        parserPool = registry.getParserPool();
        this.responseAuthenticationConverter = responseAuthenticationConverter;
    }

    @Override
    public Saml2SuccessAuthentication authenticate(Saml2AuthenticationRequest request) {
        try {
            Saml2AuthenticationToken token = new Saml2AuthenticationToken(request.getRelyingPartyRegistration(), request.getSaml2Response());
            String serializedResponse = request.getSaml2Response();
            Response response = parse(serializedResponse);
            process(token, response);
            return responseAuthenticationConverter.getOn(new OpenSaml4AuthenticationProvider.ResponseToken(response, token), request.getDepartment());

        } catch (Saml2AuthenticationException ex) {
            throw ex;
        } catch (Exception ex) {
            throw createAuthenticationException(Saml2ErrorCodes.INTERNAL_VALIDATION_ERROR, ex.getMessage(), ex);
        }
    }

    private Response parse(String response) throws Saml2Exception, Saml2AuthenticationException {
        try {
            Document document = parserPool.parse(new ByteArrayInputStream(response.getBytes(StandardCharsets.UTF_8)));
            Element element = document.getDocumentElement();
            return (Response) responseUnmarshaller.unmarshall(element);
        } catch (Exception ex) {
            throw createAuthenticationException(Saml2ErrorCodes.MALFORMED_RESPONSE_DATA, ex.getMessage(), ex);
        }
    }

    private void process(Saml2AuthenticationToken token, Response response) {
        String issuer = response.getIssuer().getValue();
        log.debug("Processing SAML response from {}", issuer);
        boolean responseSigned = response.isSigned();

        OpenSaml4AuthenticationProvider.ResponseToken responseToken = new OpenSaml4AuthenticationProvider.ResponseToken(response, token);
        Saml2ResponseValidatorResult result = responseSignatureValidator.convert(responseToken);
        if (responseSigned) {
            responseElementsDecrypter.accept(responseToken);
        }
        result = result.concat(responseValidator.convert(responseToken));
        boolean allAssertionsSigned = true;
        for (Assertion assertion : response.getAssertions()) {
            OpenSaml4AuthenticationProvider.AssertionToken assertionToken = new OpenSaml4AuthenticationProvider.AssertionToken(assertion, token);
            result = result.concat(assertionSignatureValidator.convert(assertionToken));
            allAssertionsSigned = allAssertionsSigned && assertion.isSigned();
            if (responseSigned || assertion.isSigned()) {
                assertionElementsDecrypter.accept(new OpenSaml4AuthenticationProvider.AssertionToken(assertion, token));
            }
            result = result.concat(assertionValidator.convert(assertionToken));
        }
        if (!responseSigned && !allAssertionsSigned) {
            String description = "Either the response or one of the assertions is unsigned. Please either sign the response or all of the assertions.";
            throw createAuthenticationException(Saml2ErrorCodes.INVALID_SIGNATURE, description, null);
        }
        Assertion firstAssertion = CollectionUtils.firstElement(response.getAssertions());
        if (!hasName(firstAssertion)) {
            Saml2Error error = new Saml2Error(Saml2ErrorCodes.SUBJECT_NOT_FOUND, "Assertion [%s] is missing a subject".formatted(firstAssertion.getID()));
            result = result.concat(error);
        }

        if (result.hasErrors()) {
            Collection<Saml2Error> errors = result.getErrors();
            log.debug("Found {} validation errors in SAML response [{}]: {}", errors.size(), response.getID(), errors);
            Saml2Error first = errors.iterator().next();
            throw createAuthenticationException(first.getErrorCode(), first.getDescription(), null);
        }
    }

    private Converter<OpenSaml4AuthenticationProvider.ResponseToken, Saml2ResponseValidatorResult> createDefaultResponseSignatureValidator() {
        return responseToken -> {
            Response response = responseToken.getResponse();
            RelyingPartyRegistration registration = responseToken.getToken().getRelyingPartyRegistration();
            if (response.isSigned()) {
                return OpenSamlVerificationUtils.verifySignature(response, registration).post(response.getSignature());
            }
            return Saml2ResponseValidatorResult.success();
        };
    }

    private Consumer<OpenSaml4AuthenticationProvider.ResponseToken> createDefaultResponseElementsDecrypter() {
        return responseToken -> {
            Response response = responseToken.getResponse();
            RelyingPartyRegistration registration = responseToken.getToken().getRelyingPartyRegistration();
            try {
                OpenSamlDecryptionUtils.decryptResponseElements(response, registration);
            } catch (Saml2Exception ex) {
                throw createAuthenticationException(Saml2ErrorCodes.DECRYPTION_ERROR, ex.getMessage(), ex);
            }
        };
    }

    private Converter<OpenSaml4AuthenticationProvider.ResponseToken, Saml2ResponseValidatorResult> createDefaultResponseValidator() {
        return responseToken -> {
            Response response = responseToken.getResponse();
            Saml2AuthenticationToken token = responseToken.getToken();
            Saml2ResponseValidatorResult result = Saml2ResponseValidatorResult.success();
            String statusCode = getStatusCode(response);
            if (!StatusCode.SUCCESS.equals(statusCode)) {
                String message = String.format("Invalid status [%s] for SAML response [%s]", statusCode, response.getID());
                result = result.concat(new Saml2Error(Saml2ErrorCodes.INVALID_RESPONSE, message));
            }
            String issuer = response.getIssuer().getValue();
            String destination = response.getDestination();
            String location = token.getRelyingPartyRegistration().getAssertionConsumerServiceLocation();
            if (StringUtils.hasText(destination) && !destination.equals(location)) {
                String message = "Invalid destination [" + destination + "] for SAML response [" + response.getID() + "]";
                result = result.concat(new Saml2Error(Saml2ErrorCodes.INVALID_DESTINATION, message));
            }
            String assertingPartyEntityId = token.getRelyingPartyRegistration().getAssertingPartyMetadata().getEntityId();
            if (!StringUtils.hasText(issuer) || !issuer.equals(assertingPartyEntityId)) {
                String message = String.format("Invalid issuer [%s] for SAML response [%s]", issuer, response.getID());
                result = result.concat(new Saml2Error(Saml2ErrorCodes.INVALID_ISSUER, message));
            }
            if (response.getAssertions().isEmpty()) {
                throw createAuthenticationException(Saml2ErrorCodes.MALFORMED_RESPONSE_DATA, "No assertions found in response.", null);
            }
            return result;
        };
    }

    private String getStatusCode(Response response) {
        if (response.getStatus() == null) {
            return StatusCode.SUCCESS;
        }
        if (response.getStatus().getStatusCode() == null) {
            return StatusCode.SUCCESS;
        }
        return response.getStatus().getStatusCode().getValue();
    }

    private Converter<OpenSaml4AuthenticationProvider.AssertionToken, Saml2ResponseValidatorResult> createDefaultAssertionSignatureValidator() {
        return createAssertionValidator(
                Saml2ErrorCodes.INVALID_SIGNATURE,
                assertionToken -> {
                    RelyingPartyRegistration registration = assertionToken.getToken().getRelyingPartyRegistration();
                    SignatureTrustEngine engine = OpenSamlVerificationUtils.trustEngine(registration);
                    return SAML20AssertionValidators.createSignatureValidator(engine);
                },
                assertionToken -> new ValidationContext(Map.of(SAML2AssertionValidationParameters.SIGNATURE_REQUIRED, false)));
    }

    private Consumer<OpenSaml4AuthenticationProvider.AssertionToken> createDefaultAssertionElementsDecrypter() {
        return assertionToken -> {
            Assertion assertion = assertionToken.getAssertion();
            RelyingPartyRegistration registration = assertionToken.getToken().getRelyingPartyRegistration();
            try {
                OpenSamlDecryptionUtils.decryptAssertionElements(assertion, registration);
            } catch (Saml2Exception ex) {
                throw createAuthenticationException(Saml2ErrorCodes.DECRYPTION_ERROR, ex.getMessage(), ex);
            }
        };
    }

    private Converter<OpenSaml4AuthenticationProvider.AssertionToken, Saml2ResponseValidatorResult> createCompatibleAssertionValidator() {
        return createAssertionValidator(Saml2ErrorCodes.INVALID_ASSERTION,
                assertionToken -> SAML20AssertionValidators.attributeValidator,
                this::createValidationContext);
    }

    private boolean hasName(Assertion assertion) {
        if (assertion == null) {
            return false;
        }
        if (assertion.getSubject() == null) {
            return false;
        }
        if (assertion.getSubject().getNameID() == null) {
            return false;
        }
        return assertion.getSubject().getNameID().getValue() != null;
    }

    private static Saml2AuthenticationException createAuthenticationException(String code, String message, Exception cause) {
        return new Saml2AuthenticationException(new Saml2Error(code, message), cause);
    }

    private static Converter<OpenSaml4AuthenticationProvider.AssertionToken, Saml2ResponseValidatorResult> createAssertionValidator(String errorCode,
                                                                                                                                    Converter<OpenSaml4AuthenticationProvider.AssertionToken, SAML20AssertionValidator> validatorConverter,
                                                                                                                                    Converter<OpenSaml4AuthenticationProvider.AssertionToken, ValidationContext> contextConverter) {
        return assertionToken -> {
            Assertion assertion = assertionToken.getAssertion();
            SAML20AssertionValidator validator = validatorConverter.convert(assertionToken);
            ValidationContext context = contextConverter.convert(assertionToken);
            try {
                ValidationResult result = validator.validate(assertion, context);
                if (result == ValidationResult.VALID) {
                    return Saml2ResponseValidatorResult.success();
                }
            } catch (Exception ex) {
                String message = String.format("Invalid assertion [%s] for SAML response [%s]: %s", assertion.getID(), ((Response) assertion.getParent()).getID(), ex.getMessage());
                return Saml2ResponseValidatorResult.failure(new Saml2Error(errorCode, message));
            }
            String message = String.format("Invalid assertion [%s] for SAML response [%s]: %s", assertion.getID(), ((Response) assertion.getParent()).getID(), context.getValidationFailureMessage());
            return Saml2ResponseValidatorResult.failure(new Saml2Error(errorCode, message));
        };
    }

    private ValidationContext createValidationContext(OpenSaml4AuthenticationProvider.AssertionToken assertionToken) {
        RelyingPartyRegistration relyingPartyRegistration = assertionToken.getToken().getRelyingPartyRegistration();

        Map<String, Object> params = new HashMap<>();
        params.put(SAML2AssertionValidationParameters.COND_VALID_AUDIENCES, Set.of(relyingPartyRegistration.getEntityId()));
        params.put(SAML2AssertionValidationParameters.VALID_ISSUERS, Set.of(relyingPartyRegistration.getAssertingPartyDetails().getEntityId()));
        params.put(SAML2AssertionValidationParameters.CLOCK_SKEW, responseTimeValidationSkew);
        params.put(SAML2AssertionValidationParameters.SC_VALID_RECIPIENTS, Set.of(relyingPartyRegistration.getAssertionConsumerServiceLocation()));
        params.put(SAML2AssertionValidationParameters.SC_CHECK_ADDRESS, false);
        return new ValidationContext(params);
    }

    private static class SAML20AssertionValidators {

        private static final Collection<ConditionValidator> conditionValidators = List.of(
                new AudienceRestrictionConditionValidator(),
                new DelegationRestrictionConditionValidator(),
                new ConditionValidator() {
                    @NonNull
                    @Override
                    public QName getServicedCondition() {
                        return OneTimeUse.DEFAULT_ELEMENT_NAME;
                    }

                    @NonNull
                    @Override
                    public ValidationResult validate(@NonNull Condition condition, @NonNull Assertion assertion, @NonNull ValidationContext context) {
                        // applications should validate their own OneTimeUse conditions
                        return ValidationResult.VALID;
                    }
                }
        );

        private static final Collection<SubjectConfirmationValidator> subjectValidators = List.of(
                new BearerSubjectConfirmationValidator() {

                    @Override
                    protected ValidationResult validateInResponseTo(@NonNull SubjectConfirmation confirmation, @NonNull Assertion assertion, @NonNull ValidationContext context, boolean required) throws AssertionValidationException {
                        return ValidationResult.VALID;
                    }

                    @NonNull
                    @Override
                    protected ValidationResult doValidate(@NonNull SubjectConfirmation confirmation, @NonNull Assertion assertion, @NonNull ValidationContext context) {
                        // applications should validate their own addresses - gh-7514
                        return ValidationResult.VALID;
                    }
                }
        );

        private static final Collection<StatementValidator> statements = new ArrayList<>();

        private static final SignaturePrevalidator validator = new SAMLSignatureProfileValidator();

        private static final SAML20AssertionValidator attributeValidator = new SAML20AssertionValidator(conditionValidators, subjectValidators, statements, null, null) {
            @NonNull
            @Override
            protected ValidationResult validateSignature(@NonNull Assertion token, @NonNull ValidationContext context) {
                return ValidationResult.VALID;
            }

            @NonNull
            @Override
            protected ValidationResult validateSubjectConfirmation(@NonNull Assertion assertion, @NonNull ValidationContext context) throws AssertionValidationException {
                return super.validateSubjectConfirmation(assertion, context);
            }
        };

        static SAML20AssertionValidator createSignatureValidator(SignatureTrustEngine engine) {
            return new SAML20AssertionValidator(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), engine, validator) {
                @NonNull
                @Override
                protected ValidationResult validateConditions(@NonNull Assertion assertion, @NonNull ValidationContext context) {
                    return ValidationResult.VALID;
                }

                @NonNull
                @Override
                protected ValidationResult validateSubjectConfirmation(@NonNull Assertion assertion, @NonNull ValidationContext context) {
                    return ValidationResult.VALID;
                }

                @NonNull
                @Override
                protected ValidationResult validateStatements(@NonNull Assertion assertion, @NonNull ValidationContext context) {
                    return ValidationResult.VALID;
                }
            };

        }

    }

}
