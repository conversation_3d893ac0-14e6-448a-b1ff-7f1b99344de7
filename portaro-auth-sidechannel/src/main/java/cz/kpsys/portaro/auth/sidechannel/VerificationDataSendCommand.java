package cz.kpsys.portaro.auth.sidechannel;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.web.EnablableUrl;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;

public record VerificationDataSendCommand(

        @NonNull
        BasicUser user,

        @NonNull
        String sideChannelCode,

        @NonNull
        EnablableUrl sideChannelLink,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth

) {}
