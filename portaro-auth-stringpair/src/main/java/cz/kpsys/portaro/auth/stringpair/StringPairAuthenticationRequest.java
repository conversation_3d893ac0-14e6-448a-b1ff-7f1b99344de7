package cz.kpsys.portaro.auth.stringpair;

import cz.kpsys.portaro.auth.process.AbstractAuthenticationRequest;
import cz.kpsys.portaro.auth.process.AuthenticationRequest;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class StringPairAuthenticationRequest extends AbstractAuthenticationRequest<String[]> implements AuthenticationRequest<String[]> {

    @Getter
    @NonNull
    String string1;

    @Getter
    @NonNull
    String string2;

    public StringPairAuthenticationRequest(Department department, @NonNull String string1, @NonNull String string2) {
        super(department);
        this.string1 = string1;
        this.string2 = string2;
    }

    @Override
    public String[] getCredentials() {
        return new String[] {string1, string2};
    }

}
