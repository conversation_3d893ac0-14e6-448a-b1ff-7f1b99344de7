package cz.kpsys.portaro.auth.useragent;

import cz.kpsys.portaro.auth.process.GenericAuthoritiedSuccessAuthentication;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.security.core.Transient;

@Transient
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserAgentSuccessAuthentication extends GenericAuthoritiedSuccessAuthentication {

    @Getter
    @NonNull
    String userAgent;

    public UserAgentSuccessAuthentication(User user, UserAgentAuthenticationRequest request) {
        super(user, request.getDepartment());
        this.userAgent = request.getUserAgent();
    }

    @Override
    public Object getCredentials() {
        return userAgent;
    }
}
