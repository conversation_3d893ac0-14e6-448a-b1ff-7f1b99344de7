dependencies {
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))

    implementation("org.springframework:spring-context:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework.security:spring-security-web:6.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
}
