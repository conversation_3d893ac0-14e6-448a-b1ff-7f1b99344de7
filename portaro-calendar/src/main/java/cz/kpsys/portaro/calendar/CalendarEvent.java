package cz.kpsys.portaro.calendar;

import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.department.Department;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.Set;
import java.util.UUID;

public record CalendarEvent(

        @NonNull
        UUID id,

        @Nullable
        CalendarEventGroup group,

        @NonNull
        Department department,

        @NonNull
        Set<CalendarEventLabel> labels,

        @NonNull
        @NotBlank
        String code,

        @NonNull DatetimeRange timespan

) implements IdentifiedRecord<UUID> {

    public static CalendarEvent testingWithoutPeriodicity(
            @NonNull UUID id,
            @NonNull Department department,
            @NonNull Set<CalendarEventLabel> labels,
            @NonNull @NotBlank String code,
            @NonNull DatetimeRange datetimeRange) {
        return new CalendarEvent(id, null, department, labels, code, datetimeRange);
    }

    @Override
    public boolean equals(Object o) {
        return this == o || o instanceof CalendarEvent that && id().equals(that.id());
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }
}
