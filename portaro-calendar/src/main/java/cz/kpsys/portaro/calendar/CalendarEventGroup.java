package cz.kpsys.portaro.calendar;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import lombok.NonNull;

import java.util.UUID;

public record CalendarEventGroup(

        @NonNull
        UUID id,

        @NonNull
        CalendarEventPeriodicity periodicity

) implements IdentifiedRecord<UUID> {

    @Override
    public boolean equals(Object o) {
        return this == o || o instanceof CalendarEventGroup that && id().equals(that.id());
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }
}
