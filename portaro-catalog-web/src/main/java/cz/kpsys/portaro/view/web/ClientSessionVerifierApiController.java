package cz.kpsys.portaro.view.web;

import cz.kpsys.portaro.commons.web.WebResolver;
import cz.kpsys.portaro.util.logging.ClientSessionByClientRequestUpdater;
import cz.kpsys.portaro.web.GenericApiController;
import cz.kpsys.portaro.web.client.WebClientRequest;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/api/session/verify")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ClientSessionVerifierApiController extends GenericApiController {


    @NonNull WebResolver<WebClientRequest> webClientRequestWebResolver;
    @NonNull ClientSessionByClientRequestUpdater clientSessionByClientRequestUpdater;

    @PostMapping
    public void verify(@RequestParam(name = "bot") boolean isBot,
                       HttpServletRequest httpRequest) {

        if (httpRequest.getSession(false) == null) {
            throw new MissingClientSessionException("Can not verify session - request does not contain session cookie");
        }

        WebClientRequest clientRequest = webClientRequestWebResolver.resolve(httpRequest);
        clientSessionByClientRequestUpdater.verifySession(clientRequest, isBot);
    }
}
