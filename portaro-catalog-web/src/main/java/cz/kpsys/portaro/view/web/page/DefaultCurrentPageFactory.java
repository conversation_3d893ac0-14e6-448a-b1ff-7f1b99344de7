package cz.kpsys.portaro.view.web.page;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.alert.Alert;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.auth.current.CurrentAuthResponse;
import cz.kpsys.portaro.auth.current.CurrentAuthWebResolver;
import cz.kpsys.portaro.commons.contextual.ContextualBiFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.WebResolver;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.data.dataview.ErpCustomDataViewPagesHolder;
import cz.kpsys.portaro.licence.portaro.PortaroVersion;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.template.StaticTemplateRenderer;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import cz.kpsys.portaro.user.relation.RepresentableUserLoader;
import cz.kpsys.portaro.view.domain.CurrentPage;
import cz.kpsys.portaro.view.domain.LoginView;
import cz.kpsys.portaro.view.domain.menu.MenuItem;
import cz.kpsys.portaro.view.domain.menu.response.MenuItemResponse;
import cz.kpsys.portaro.view.domain.menu.response.MenuResponseGenerator;
import cz.kpsys.portaro.web.page.CurrentPageDialogsResolver;
import cz.kpsys.portaro.web.page.CurrentPageFactory;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Locale;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DefaultCurrentPageFactory implements CurrentPageFactory<CurrentPage> {

    @NonNull PortaroVersion portaroVersion;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull SecurityManager securityManager;
    @NonNull Converter<UserAuthentication, CurrentAuthResponse> currentAuthToResponseConverter;
    @NonNull ContextualProvider<Department, LoginView> loginViewDepartmentedProvider;
    @NonNull AuthenticatedContextualProvider<Department, List<MenuItem>> menuItemsAuthenticatedContextualProvider;
    @NonNull Translator<Department> translator;
    @NonNull ContextualProvider<Department, ? extends List<? extends LabeledIdentified<String>>> enabledLanguagesProvider;
    @NonNull WebResolver<List<Alert>> alertWebResolver;
    @NonNull RepresentableUserLoader representableUserLoader;
    @NonNull ContextualProvider<Department, @NonNull Boolean> cameraScannerEnabledProvider;
    @NonNull CurrentAuthWebResolver currentAuthWebResolver;
    @NonNull ContextualProvider<Department, List<String>> forbiddenMenuItems;
    @NonNull StaticTemplateRenderer customFileTemplateRenderer;
    @NonNull CurrentPageDialogsResolver currentPageDialogsResolver;
    @NonNull ContextualProvider<Department, @NonNull Boolean> globalSearchInputEnabledProvider;
    @NonNull ContextualProvider<Department, @NonNull String> headerBackgroundColorProvider;
    @NonNull ContextualProvider<Department, @NonNull String> headerTextColorProvider;
    @NonNull ContextualProvider<Department, @NonNull String> headerLinkColorProvider;
    @NonNull ContextualProvider<Department, @NonNull String> mainMenuBackgroundColorProvider;
    @NonNull ContextualProvider<Department, @NonNull String> mainMenuTextColorProvider;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> mainMenuHighlightBackgroundColorProvider;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> mainMenuHighlightTextColorProvider;
    @NonNull ContextualProvider<Department, @NonNull String> globalSearchButtonColorProvider;
    @NonNull ContextualProvider<Department, @NonNull String> tableHeaderAccentColorProvider;
    @NonNull ContextualProvider<Department, @NonNull String> selectedTabHighlightColor;
    @NonNull WebResolver<String> themeResolver;
    @NonNull ContextualProvider<Department, @NonNull Boolean> sutorSutinEnabledProvider;
    @NonNull ContextualBiFunction<User, UserAuthentication, Department, Integer> numberOfUserAttentionRequiringActionsProvider;
    @NonNull UserByBasicUserLoader userLoader;
    @NonNull ContextualProvider<Department, @NonNull ErpCustomDataViewPagesHolder> erpCustomDataViewPagesHolderContextualProvider;
    @NonNull ContextualProvider<Department, @NonNull Boolean> homeDepartmentRedirectAfterLoginContextualProvider;

    @Override
    public CurrentPage create(Department ctx, Locale locale, HttpServletRequest request) {
        UserAuthentication currentAuth = currentAuthWebResolver.resolve(request);
        List<ActionResponse> dialogs = currentPageDialogsResolver.resolve(request, currentAuth, ctx);
        CurrentAuthResponse currentAuthResponse = Objects.requireNonNull(currentAuthToResponseConverter.convert(currentAuth));
        List<Alert> alerts = alertWebResolver.resolve(request);
        @NullableNotBlank String titlePrefix = alerts.stream().anyMatch(alert -> alert.severity().equals(Alert.SEVERRENITY_DANGER)) ? "! " : null;
        String themeName = themeResolver.tryResolve(request).orElse(CoreConstants.Web.Defaults.DEFAULT_THEME);
        List<MenuItem> menuItems = menuItemsAuthenticatedContextualProvider.getOn(currentAuth, ctx);
        LoginView loginView = loginViewDepartmentedProvider.getOn(ctx);
        List<MenuItemResponse> mainMenu = new MenuResponseGenerator(
                StringUtil.notNullString(publicContextPath.get()),
                () -> !loginView.isForAdministrationOnly(),
                representableUserLoader,
                forbiddenMenuItems.getOn(ctx),
                numberOfUserAttentionRequiringActionsProvider,
                userLoader)
                .generateMenu(menuItems, currentAuth, ctx);

        return CurrentPage.byRequest(
                request,
                serverUrlProvider.getOn(ctx),
                publicContextPath.get(),
                authenticationHolder,
                currentAuthResponse,
                enabledLanguagesProvider.getOn(ctx),
                menuItems,
                loginView,
                portaroVersion,
                locale,
                translator,
                securityManager,
                ctx,
                alerts,
                titlePrefix,
                cameraScannerEnabledProvider.getOn(ctx),
                dialogs,
                customFileTemplateRenderer,
                globalSearchInputEnabledProvider.getOn(ctx),
                headerBackgroundColorProvider.getOn(ctx),
                headerTextColorProvider.getOn(ctx),
                headerLinkColorProvider.getOn(ctx),
                mainMenuBackgroundColorProvider.getOn(ctx),
                mainMenuTextColorProvider.getOn(ctx),
                mainMenuHighlightBackgroundColorProvider.getOn(ctx),
                mainMenuHighlightTextColorProvider.getOn(ctx),
                globalSearchButtonColorProvider.getOn(ctx),
                tableHeaderAccentColorProvider.getOn(ctx),
                themeName,
                mainMenu,
                sutorSutinEnabledProvider.getOn(ctx),
                selectedTabHighlightColor.getOn(ctx),
                erpCustomDataViewPagesHolderContextualProvider.getOn(ctx),
                homeDepartmentRedirectAfterLoginContextualProvider.getOn(ctx)
        );
    }

    @Override
    public CurrentPage createFallback() {
        return CurrentPage.fallback(portaroVersion, Department.testingBuilding(0), translator, customFileTemplateRenderer);
    }

}
