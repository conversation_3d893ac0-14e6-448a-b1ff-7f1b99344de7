package cz.kpsys.portaro.view.web.page;

import cz.kpsys.portaro.app.PortaroUrls;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.web.DownloadFileStreamConsumer;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.file.cover.CoverService;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.factory.SearchFactory;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.view.web.ratelimit.RateLimited;
import cz.kpsys.portaro.web.GenericPageController;
import cz.kpsys.portaro.web.page.ModelAndPageViewFactory;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import java.util.UUID;

import static cz.kpsys.portaro.app.CatalogWebConstants.RECORDS_URL_PART;

@RequestMapping(RECORDS_URL_PART)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordPageController extends GenericPageController {

    @NonNull SearchFactoryResolver searchFactoryResolver;
    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull ByIdLoadable<Record, UUID> nonDetailedRichRecordLoader;
    @NonNull CoverService coverService;

    @GetMapping("/{record:" + UuidGenerator.UUID_PATTERN_WHOLE + "}/cover")
    @RateLimited("getRecordCover")
    public void cover(@PathVariable("record") UUID id,
                      @RequestParam(value = "withoutFallback", defaultValue = "false") boolean withoutFallback,
                      @CurrentDepartment Department currentDepartment,
                      HttpServletResponse response) {

        val recordHeader = nonDetailedRichRecordLoader.getById(id);

        coverService.loadCover(recordHeader, currentDepartment, new DownloadFileStreamConsumer(response), withoutFallback);
    }


    @GetMapping("/single")
    public ModelAndView single(MapBackedParams p,
                               @CurrentDepartment Department ctx,
                               UserAuthentication currentAuth) {
        SearchFactory searchFactory = searchFactoryResolver.resolve(p);
        AbstractStandardSearch<MapBackedParams, ?, RangePaging> s = searchFactory.createSearch(p, currentAuth, ctx);
        s.fetch(p, RangePaging.forFirstTwo(), currentAuth, ctx, CacheMode.NONE);
        PagedSearchResult<?, ?> result = s.getLastResult();
        Object record = DataUtils.requireSingle(result.content(), "Record", p);

        String url = publicContextPath.get() + PortaroUrls.getRecordPath(record);
        return ModelAndPageViewFactory.redirect(url);
    }
}
