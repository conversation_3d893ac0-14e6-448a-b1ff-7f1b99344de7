package cz.kpsys.portaro.view.web.ratelimit;

import cz.kpsys.portaro.commons.web.HttpHeaderConstants.RateLimitRemaining;
import cz.kpsys.portaro.commons.web.HttpHeaderConstants.RetryAfter;
import cz.kpsys.portaro.commons.web.WebResolver;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.Software;
import cz.kpsys.portaro.web.client.WebClientRequest;
import io.github.bucket4j.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.time.Duration;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RateLimitInterceptor implements HandlerInterceptor {

    @NonNull Bucket botBucket = Bucket4j.builder()
            .addLimit(Bandwidth.simple(20, Duration.ofMinutes(1)))
            .addLimit(Bandwidth.classic(10, Refill.intervally(4, Duration.ofSeconds(10))))
            .build();
    @NonNull Bucket noBotBucket = Bucket4j.builder()
            .addLimit(Bandwidth.simple(100, Duration.ofSeconds(1)))
            .build();

    @NonNull WebResolver<WebClientRequest> webClientRequestWebResolver;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (!HandlerMethod.class.isAssignableFrom(handler.getClass())) {
            return true;
        }
        RateLimited annotation = ((HandlerMethod) handler).getMethodAnnotation(RateLimited.class);
        if (annotation == null) {
            return true;
        }

        WebClientRequest webClientRequest = webClientRequestWebResolver.resolve(request);

        Bucket requestBucket = getBucket(webClientRequest);

        ConsumptionProbe probe = requestBucket.tryConsumeAndReturnRemaining(1);
        if (probe.isConsumed()) {
            response.addHeader(RateLimitRemaining.RATE_LIMIT_REMAINING, String.valueOf(probe.getRemainingTokens()));
            return true;
        }

        setTooManyRequestErrorResponse(webClientRequest, response, Duration.ofNanos(probe.getNanosToWaitForRefill()));

        return false;
    }

    private Bucket getBucket(WebClientRequest webClientRequest) {
        BasicUser user = webClientRequest.getInitiatorUser();
        if (user instanceof Software software && software.getWebCrawler()) {
            return botBucket;
        }

        return noBotBucket;
    }

    private void setTooManyRequestErrorResponse(WebClientRequest webClientRequest, HttpServletResponse response, Duration waitForRetryDuration) {
        if (log.isDebugEnabled()) {
            log.debug("Rate limit exceeded for {} (user agent {}, user {})", webClientRequest.getUrl(), webClientRequest.getUserAgent(), webClientRequest.getInitiatorUser());
        }
        response.addHeader(RetryAfter.NAME, String.valueOf(waitForRetryDuration.getSeconds()));
        throw new RateLimitExceededException(waitForRetryDuration);
    }
}
