package cz.kpsys.portaro.view.web.rest;

import cz.kpsys.portaro.autocomplete.AutocompleteByGroupLoader;
import cz.kpsys.portaro.commons.object.Valuable;
import cz.kpsys.portaro.search.SimpleListResult;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Optional;

import static cz.kpsys.portaro.app.CatalogWebConstants.API_URL_PREFIX;
import static cz.kpsys.portaro.app.CatalogWebConstants.AUTOCOMPLETE_URL_PART;

@RequestMapping(API_URL_PREFIX + AUTOCOMPLETE_URL_PART)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AutocompleteApiController extends GenericApiController {

    @NonNull AutocompleteByGroupLoader autocompleteByGroupLoader;

    @RequestMapping("{loaderId:.*}")
    public SimpleListResult<? extends Valuable<String>> search(@PathVariable("loaderId") String loaderId,
                                                               @RequestParam("infix") Optional<String> infix) {
        List<Valuable<String>> list = getList(loaderId, infix);
        return new SimpleListResult<>(list.size(), list);
    }

    private List<Valuable<String>> getList(String loaderId, Optional<String> infix) {
        if (infix.isPresent()) {
            return (List<Valuable<String>>) autocompleteByGroupLoader.getAllByInfix(loaderId, infix.get());
        }
        return (List<Valuable<String>>) autocompleteByGroupLoader.getAll(loaderId);
    }
    
}
