package cz.kpsys.portaro.view.web.rest;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.localization.LocalizationCodes;
import cz.kpsys.portaro.search.facet.*;
import cz.kpsys.portaro.user.sec.SecurityActions;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping(CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.FACET_TYPES_URL_PART)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FacetTypeApiController extends GenericApiController {

    @NonNull Codebook<FacetType, Integer> facetTypeLoader;
    @NonNull Converter<FacetTypeEditationRequest, FacetType> facetTypeEditationRequestToFacetTypeConverter;
    @NonNull FacetTypeCreator facetTypeCreator;
    @NonNull FacetTypeUpdater facetTypeUpdater;

    @GetMapping
    public List<FacetType> getAll() {
        return facetTypeLoader.getAll();
    }


    @GetMapping("{id}")
    public FacetType getById(@PathVariable("id") int id) {
        return facetTypeLoader.getById(id);
    }


    @PostMapping("/create")
    public ActionResponse save(@RequestBody @ValidFormObject FacetTypeCreationRequest request,
                               @CurrentDepartment Department currentDepartment,
                               UserAuthentication currentAuth) {

        securityManager.throwIfCannot(SecurityActions.FACET_TYPE_CREATE, currentAuth, currentDepartment);

        facetTypeCreator.create(new FacetTypeCreationCommand(
                request.name(),
                request.order(),
                request.definition(),
                request.definitionType(),
                request.exemplarType(),
                request.enabled(),
                request.sorting(),
                request.scope(),
                Datatype.scalar(request.datatype())
        ));

        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Saved));
    }

    @PostMapping("/edit")
    public ActionResponse edit(@RequestBody @ValidFormObject FacetTypeEditationRequest request,
                               @CurrentDepartment Department currentDepartment,
                               UserAuthentication currentAuth) {
        FacetType facetType = facetTypeEditationRequestToFacetTypeConverter.convert(request);
        securityManager.throwIfCannot(SecurityActions.FACET_TYPE_EDIT, currentAuth, currentDepartment, facetType);
        facetTypeUpdater.update(
                new FacetTypeEditationCommand(
                        request.id(),
                        request.name(),
                        request.order(),
                        request.definition(),
                        request.definitionType(),
                        request.exemplarType(),
                        request.enabled(),
                        request.sorting(),
                        request.scope(),
                        Datatype.scalar(request.datatype())
                )
        );
        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Saved));
    }


    @DeleteMapping("{id}")
    public ActionResponse delete(@PathVariable int id) {
        throw new UnsupportedOperationException();
    }

}
