package cz.kpsys.portaro.view.web.rest;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.commons.object.Ordered;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.AfterIntegrityValidationViolation;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.bool.BooleanEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.number.NumberEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.singleacceptable.SingleAcceptableEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextEditor;
import cz.kpsys.portaro.search.facet.FacetDefinitionType;
import cz.kpsys.portaro.search.facet.FacetScope;
import cz.kpsys.portaro.search.facet.FacetType;
import cz.kpsys.portaro.search.facet.FacetTypeSorting;
import cz.kpsys.portaro.sorting.SortingItem;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;

@Form(id = "facetTypeCreationRequest", title = "{commons.Uprava}")
@FormSubmit(path = CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.FACET_TYPES_URL_PART + "/create")
@AfterIntegrityValidationViolation(bean = "facetTypeRequestDefaulter")
@With
public record FacetTypeCreationRequest(

        @FormPropertyLabel("{commons.nazev}")
        @TextEditor
        @Size(min = 1, max = 80)
        @NotBlank
        String name,

        @FormPropertyLabel("{commons.Poradi}")
        @NumberEditor
        @NotNull
        @Min(0)
        Integer order,

        @FormPropertyLabel("Množina")
        @SingleAcceptableEditor(valuesSourceBean = "facetScopeLoader")
        @NotNull
        FacetScope scope,

        @FormPropertyLabel("{util.TypDefinice}")
        @SingleAcceptableEditor(valuesSourceBean = "facetDefinitionTypeLoader")
        @NotNull
        FacetDefinitionType definitionType,

        @FormPropertyLabel("{util.Definice}")
        @TextEditor
        @Size(min = 1, max = 2000)
        @NullableNotBlank
        String definition,

        @FormPropertyLabel("{util.ExemplarovyTyp}")
        @BooleanEditor
        Boolean exemplarType,

        @FormPropertyLabel("{commons.Zapnuto}")
        @BooleanEditor
        Boolean enabled,

        @FormPropertyLabel("Datový typ")
        @TextEditor
        @Size(min = 1, max = Datatype.NAME_MAX_LENGTH)
        @NullableNotBlank
        String datatype,

        @FormPropertyLabel("Řazení")
        @SingleAcceptableEditor(valuesSourceBean = "facetTypeSortingLoader")
        @NotNull
        SortingItem sorting
) {
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class FacetTypeRequestDefaulter implements TypedAuthenticatedContextualObjectModifier<FacetTypeCreationRequest> {

        @NonNull Codebook<FacetType, Integer> facetTypeLoader;

        @Override
        public FacetTypeCreationRequest modify(FacetTypeCreationRequest request, Department ctx, UserAuthentication currentAuth) {
            if (request.definitionType() == null) {
                request = request.withDefinitionType(FacetDefinitionType.LIST);
            }
            if (request.sorting() == null) {
                request = request.withSorting(FacetTypeSorting.SORTING_FREQ_DESC);
            }
            if (request.exemplarType() == null) {
                request = request.withExemplarType(false);
            }
            if (request.enabled() == null) {
                request = request.withEnabled(false);
            }
            if (request.order() == null) {
                Integer newOrder = getNewOrder();
                request = request.withOrder(newOrder);
            }
            return request;
        }


        private Integer getNewOrder() {
            return Ordered.getNewOrder(facetTypeLoader.getAll());
        }
    }
}
