package cz.kpsys.portaro.view.web.rest;

import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.search.facet.FacetType;
import org.springframework.core.convert.converter.Converter;

public class FacetTypeEditationRequestToFacetTypeConverter implements Converter<FacetTypeEditationRequest, FacetType> {

    @Override
    public FacetType convert(FacetTypeEditationRequest source) {
        return new FacetType(
                source.id(),
                source.name(),
                source.order(),
                source.definition(),
                source.definitionType(),
                source.exemplarType(),
                source.enabled(),
                source.sorting(),
                source.scope(),
                Datatype.scalar(source.datatype())
        );
    }
}
