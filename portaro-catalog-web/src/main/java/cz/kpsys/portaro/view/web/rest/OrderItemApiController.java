package cz.kpsys.portaro.view.web.rest;

import cz.kpsys.portaro.acquisition.OrderItem;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/api/order-items")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class OrderItemApiController extends GenericApiController {

    @NonNull IdAndIdsLoadable<OrderItem, Integer> orderItemLoader;

    @GetMapping("{id}")
    public OrderItem getById(@PathVariable("id") Integer id) {
        return orderItemLoader.getById(id);
    }
}
