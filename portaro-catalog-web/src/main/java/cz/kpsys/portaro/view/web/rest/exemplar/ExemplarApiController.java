package cz.kpsys.portaro.view.web.rest.exemplar;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.ActiveUser;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.object.Valuable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.Validity;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.ExceptionedFinishedResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.*;
import cz.kpsys.portaro.exemplar.accessnumber.AccessNumberSequenceItem;
import cz.kpsys.portaro.exemplar.accessnumber.AccessNumberSequenceLoader;
import cz.kpsys.portaro.exemplar.delete.ExemplarDeleter;
import cz.kpsys.portaro.exemplar.delete.ExemplarsDeletionCommand;
import cz.kpsys.portaro.exemplar.delete.ExemplarsDeletionRequest;
import cz.kpsys.portaro.exemplar.discard.ExemplarDiscarder;
import cz.kpsys.portaro.exemplar.discard.ExemplarDiscardionCommand;
import cz.kpsys.portaro.exemplar.discard.ExemplarDiscardionRequest;
import cz.kpsys.portaro.exemplar.discard.ExemplarRestorationRequest;
import cz.kpsys.portaro.exemplar.edit.EditedExemplar;
import cz.kpsys.portaro.exemplar.edit.ExemplarValidator;
import cz.kpsys.portaro.exemplar.restore.ExemplarDiscardionRestorer;
import cz.kpsys.portaro.exemplar.restore.ExemplarRestorationCommand;
import cz.kpsys.portaro.exemplar.signature.SignatureSequenceItem;
import cz.kpsys.portaro.exemplar.signature.SignatureSequenceLoader;
import cz.kpsys.portaro.exemplar.volume.Volume;
import cz.kpsys.portaro.form.remotevalidation.DefaultModelRemoteValidationRequest;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanConstants;
import cz.kpsys.portaro.loan.LoanService;
import cz.kpsys.portaro.loan.LoanState;
import cz.kpsys.portaro.loan.availability.AppserverDataBackedExemplarAvailability;
import cz.kpsys.portaro.loan.availability.ExemplarAvailabilityRequest;
import cz.kpsys.portaro.loan.availability.ExemplarAvailabilityService;
import cz.kpsys.portaro.loan.availability.ExemplarHolderService;
import cz.kpsys.portaro.localization.LocalizationCodes;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.NoConcreteUser;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.view.web.ratelimit.RateLimited;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.val;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.*;
import java.util.stream.Stream;

import static cz.kpsys.portaro.app.CatalogWebConstants.EXEMPLARS_URL_PATH;

@ResponseBody
@RequestMapping(EXEMPLARS_URL_PATH)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ExemplarApiController extends GenericApiController {

    public static final String VALIDATION_URL_PART = "/validation";
    public static final String VALIDITY_PATH = EXEMPLARS_URL_PATH + VALIDATION_URL_PART;

    @NonNull ExemplarLoader exemplarLoader;
    @NonNull PageSearchLoader<MapBackedParams, Exemplar, RangePaging> exemplarSearchLoader;
    @NonNull ExemplarDiscarder exemplarDiscarder;
    @NonNull ExemplarDeleter exemplarDeleter;
    @NonNull ExemplarValidator exemplarValidator;
    @NonNull ExemplarSequenceItemLoader<AccessNumberSequenceItem> accessNumberSequenceItemLoader;
    @NonNull ExemplarSequenceItemLoader<SignatureSequenceItem> signatureSequenceItemLoader;
    @NonNull AccessNumberSequenceLoader accessNumberSequenceLoader;
    @NonNull SignatureSequenceLoader signatureSequenceLoader;
    @NonNull ExemplarAvailabilityService exemplarAvailabilityService;
    @NonNull SecurityManager securityManager;
    @NonNull Saver<EditedExemplar, Exemplar> exemplarSaver;
    @NonNull ExemplarListSorter exemplarListSorter;
    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull ExemplarDiscardionRestorer exemplarDiscardionRestorer;
    @NonNull Converter<ExemplarRequest, List<Exemplar>> exemplarRequestToExemplarsConverter;
    @NonNull ContextualProvider<Department, Boolean> titleSignaturesEnabledDepartmentedProvider;
    @NonNull ExemplarRequestUtils exemplarRequestUtils;
    @NonNull PageSearchLoader<MapBackedParams, Loan, RangePaging> loanSearchLoader;
    @NonNull LoanService loanService;
    @NonNull ExemplarHolderService exemplarHolderService;


    @GetMapping("{id}")
    public Exemplar get(@PathVariable("id") Exemplar exemplar) {
        return exemplar;
    }


    @GetMapping(params = "volume")
    public List<? extends Exemplar> queryByVolume(@RequestParam("volume") Volume v,
                                                  @RequestParam(value = "viewable", defaultValue = "false") boolean viewable,
                                                  @RequestParam("user") Optional<User> requester,
                                                  UserAuthentication currentAuth,
                                                  @CurrentDepartment Department ctx) {
        List<Department> departments = departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.SUBTREE);
        List<Exemplar> exemplars = exemplarLoader.getAllByVolume(v, departments);

        return returnExemplars(exemplars, viewable, requester.orElseGet(NoConcreteUser::anonymousUser), ctx, currentAuth);
    }


    @GetMapping(params = {"!volume", "!id"})
    @RateLimited("getExemplars")
    public List<? extends Exemplar> query(@RequestParam("q") Optional<String> q,
                                          @RequestParam("document") Optional<Record> document,
                                          @RequestParam("allowedExemplarTypes") Optional<List<ExemplarType>> allowedExemplarTypes,
                                          @RequestParam(value = "viewable", defaultValue = "false") boolean viewable,
                                          @RequestParam("user") Optional<User> requester,
                                          @RequestParam(value = SearchViewConstants.PAGE_NUMBER_PARAM, defaultValue = "1") int pageNumber,
                                          @RequestParam(value = SearchViewConstants.PAGE_SIZE_PARAM, defaultValue = "0") int pageSize,
                                          @RequestParam(value = "includeDeleted", defaultValue = "false") boolean includeDeleted,
                                          UserAuthentication currentAuth,
                                          @CurrentDepartment Department ctx) {

        MapBackedParams params = MapBackedParams.build(p -> {
            document.ifPresent(d -> p.set(RecordConstants.SearchParams.RECORD, List.of(d.getId())));
            allowedExemplarTypes.ifPresent(exemplarTypes -> p.set(ExemplarConstants.SearchParams.EXEMPLAR_TYPE, exemplarTypes));
            q.ifPresent(query -> p.set(CoreSearchParams.Q, query));
            p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.SUBTREE));
            p.set(CoreSearchParams.INCLUDE_ACTIVE, true);
            if (includeDeleted) {
                p.set(CoreSearchParams.INCLUDE_DELETED, true);
            }
        });

        List<Exemplar> exemplars = exemplarSearchLoader.getPage(pageSize == 0 ? RangePaging.forAll() : RangePaging.of(pageNumber, pageSize), params).getItems();

        // if we are searching not by document, check for too broad exemplars size
        Assert.state(document.isPresent() || exemplars.size() < 500, "Cannot fetch more than 500 exemplars in request, filter parameters are probably too weak");

        return returnExemplars(exemplars, viewable, requester.orElseGet(NoConcreteUser::anonymousUser), ctx, currentAuth);
    }


    @GetMapping(params = {"id", "viewable=true"})
    @RateLimited("getViewableExemplars")
    public List<ViewableExemplar> getViewableExemplars(@RequestParam("id") List<Exemplar> exemplars,
                                                       UserAuthentication currentAuth,
                                                       @RequestParam("user") Optional<User> requester,
                                                       @CurrentDepartment Department ctx) {

        List<ViewableExemplar> viewableExemplars = extendExemplars(
                exemplars,
                requester.orElseGet(NoConcreteUser::anonymousUser),
                ctx,
                currentAuth
        );

        return exemplarListSorter.sort(viewableExemplars);
    }

    @PostMapping
    public ActionResponse save(@RequestBody @ValidFormObject ExemplarRequest exemplarRequest,
                               @CurrentDepartment Department ctx,
                               UserAuthentication currentAuth) {

        List<Exemplar> exemplars = Objects.requireNonNull(exemplarRequestToExemplarsConverter.convert(exemplarRequest));

        long countOfDistinctAccessNumbers = exemplars.stream()
                .map(Exemplar::getAccessNumber)
                .distinct()
                .count();

        if (countOfDistinctAccessNumbers != exemplars.size()) {
            return new ExceptionedFinishedResponse(Texts.ofMessageCoded("validation.AccessNumbersIsNotUnique"), new RuntimeException());
        }

        if (!titleSignaturesEnabledDepartmentedProvider.getOn(ctx)) {
            long countOfDistinctSignatures = exemplars.stream()
                    .map(Exemplar::getSignature)
                    .distinct()
                    .count();

            if (countOfDistinctSignatures != exemplars.size()) {
                return new ExceptionedFinishedResponse(Texts.ofMessageCoded("validation.SignaturesIsNotUnique"), new RuntimeException());
            }
        }

        long countOfNonNullExemplars = exemplars.stream()
                .map(Exemplar::getBarCode)
                .filter(Objects::nonNull)
                .count();

        long countOfNonNullDistinctBarCodes = exemplars.stream()
                .map(Exemplar::getBarCode)
                .filter(Objects::nonNull)
                .distinct()
                .count();

        if (countOfNonNullDistinctBarCodes != countOfNonNullExemplars) {
            return new ExceptionedFinishedResponse(Texts.ofMessageCoded("validation.BarCodesIsNotUnique"), new RuntimeException());
        }

        exemplars = exemplars.stream()
                .map(exemplar -> new EditedExemplar(exemplar, ctx, currentAuth))
                .map(editedExemplar -> {
                    editedExemplar.setLastModificationDate(Instant.now());
                    editedExemplar.setFond(exemplarRequest.document().getFond());
                    return exemplarSaver.save(editedExemplar);
                })
                .toList();

        return new FinishedSaveResponse<>(Texts.ofMessageCoded(LocalizationCodes.Saved), ListUtil.convertStrict(exemplars, source -> new BasicIdentified<>(source.getId())));
    }

    @PostMapping("/delete")
    public ActionResponse delete(@RequestBody @ValidFormObject ExemplarsDeletionRequest request,
                                 @CurrentDepartment Department ctx,
                                 UserAuthentication currentAuth) {
        return exemplarDeleter.deleteExemplars(new ExemplarsDeletionCommand(
                request.getExemplars(),
                ctx,
                currentAuth,
                ObjectUtil.firstNotNull(request.getIgnoreNotDeletableExemplarStatus(), false),
                ObjectUtil.isNullOrTrue(request.getIgnoreExistingLoansHistory()),
                ObjectUtil.isNullOrTrue(request.getIgnoreExistingExchangeSetsHistory()),
                ObjectUtil.isNullOrTrue(request.getIgnoreExistingSuppliedIssuesHistory())
        ));
    }

    @GetMapping("{exemplar}/loans")
    public List<Loan> getExemplarLoans(@PathVariable("exemplar") Exemplar exemplar,
                                       @RequestParam(value = SearchViewConstants.PAGE_NUMBER_PARAM, defaultValue = "1") int pageNumber,
                                       @RequestParam(value = SearchViewConstants.PAGE_SIZE_PARAM, defaultValue = "0") int pageSize,
                                       @CurrentDepartment Department ctx,
                                       UserAuthentication currentAuth) {
        MapBackedParams loanParams = MapBackedParams.build(StaticParamsModifier.of(
                LoanConstants.SearchParams.EXEMPLAR, List.of(exemplar.getId()),
                LoanConstants.SearchParams.LOAN_STATE, LoanState.allExemplarAssignedActive() //we want only active loans with assigned exemplar
        ));

        List<Loan> content = loanSearchLoader.getPage(pageSize == 0 ? RangePaging.forAll() : RangePaging.of(pageNumber, pageSize), SortingItem.ofSimpleDesc(Loan.LEND_DATE), loanParams).getItems();
        return loanService.decorateWithUserSpecificData(content, ctx, currentAuth);
    }


    @PostMapping("discard")
    public ActionResponse discard(@RequestBody @ValidFormObject ExemplarDiscardionRequest request,
                                  @CurrentDepartment Department ctx,
                                  @ActiveUser User activeUser,
                                  UserAuthentication currentAuth) {
        securityManager.throwIfCannot(ExemplarSecurityActions.EXEMPLAR_DISCARD, currentAuth, ctx, request.exemplar());

        ExemplarDiscardionCommand command = new ExemplarDiscardionCommand(
                request.exemplar(),
                request.discardNumber(),
                request.replacementWay(),
                request.cost(),
                request.reason(),
                request.consultationNumber(),
                ctx,
                activeUser
        );
        exemplarDiscarder.discard(command);

        return new FinishedActionResponse(Texts.ofMessageCoded("exemplar.ExeplarVyrazen"));
    }

    @PostMapping("restore")
    public ActionResponse restore(@RequestBody @ValidFormObject ExemplarRestorationRequest request,
                                  @CurrentDepartment Department ctx,
                                  @ActiveUser User activeUser,
                                  UserAuthentication currentAuth) {
        securityManager.throwIfCannot(ExemplarSecurityActions.EXEMPLAR_RESTORE, currentAuth, ctx, request.exemplar());

        ExemplarRestorationCommand command = new ExemplarRestorationCommand(
                request.exemplar(),
                ctx,
                activeUser
        );
        exemplarDiscardionRestorer.restore(command);

        return new FinishedActionResponse(Texts.ofMessageCoded("exemplar.ExemplarRestored"));
    }

    @PostMapping(VALIDATION_URL_PART + "/{property}")
    public Validity validate(@PathVariable("property") String property,
                             @RequestBody @ValidFormObject DefaultModelRemoteValidationRequest<Object, ExemplarRequest> validationDto,
                             @CurrentDepartment Department ctx) {

        List<Exemplar> exemplars = Objects.requireNonNull(exemplarRequestToExemplarsConverter.convert(validationDto.formModel()));
        Assert.notEmpty(exemplars, "Exemplar cannot be empty");

        // TODO: Pridat validace na unikatnost v ramci tohohle seznamu exemplaru. Tzn., aby to rvalo, kdyz uzivatel vyplni stejne polozky v requestu

        return exemplarValidator.validate(exemplars.getFirst(), property, validationDto.value(), ctx);
    }

    @PostMapping("sequences/{property}")
    public List<String> generate(@PathVariable("property") String property,
                                 @RequestBody @ValidFormObject ExemplarSequenceRequest exemplarSequenceRequest) {

        if (property.equals(BasicExemplar.Fields.accessNumber)) {
            return exemplarRequestUtils.getAccessNumberSequenceItems(exemplarSequenceRequest);
        } else {
            return exemplarRequestUtils.getSignatureSequenceItems(exemplarSequenceRequest);
        }
    }

    @PostMapping("identifiers")
    public ExemplarIdentifierRequest getNewExemplarIdentifiers(@RequestBody ExemplarRequest exemplarRequest) {

        if (exemplarRequest.identifiers() == null || exemplarRequest.identifiers().isEmpty()) {
            exemplarRequest = exemplarRequest.withIdentifiers(List.of(new ExemplarIdentifierRequest(exemplarRequest, null, null, null)));
        }

        Optional<Exemplar> exemplar = Optional.ofNullable(exemplarRequestToExemplarsConverter.convert(exemplarRequest))
                .map(Collection::stream)
                .flatMap(Stream::findFirst);

        ExemplarSequenceQueryDto exemplarSequenceQueryDto = new ExemplarSequenceQueryDto(exemplarRequest.document(), exemplarRequest.department(), exemplarRequest.location());



        val pseudoLastSignature = Optional.ofNullable(exemplarRequest.identifiers())
                .orElseGet(ArrayList::new)
                .stream()
                .map(ExemplarIdentifierRequest::getSignature)
                .filter(Objects::nonNull)
                .reduce((prev, last) -> last);

        Optional<String> signature = exemplar.stream()
                .flatMap(exemp -> signatureSequenceLoader.getByFond(exemp.getFond()).stream())
                .filter(sequence -> pseudoLastSignature.isPresent() && sequence.matches(pseudoLastSignature.get()))
                .map(sequence -> sequence.createKnown(pseudoLastSignature.get()).next())
                .findFirst()
                .or(() -> signatureSequenceItemLoader.get(exemplarSequenceQueryDto).stream().findFirst())
                .map(Valuable::getValue);



        val pseudoLastAccessNumber = Optional.ofNullable(exemplarRequest.identifiers())
                .orElseGet(ArrayList::new)
                .stream()
                .map(ExemplarIdentifierRequest::getAccessNumber)
                .filter(Objects::nonNull)
                .reduce((prev, last) -> last);

        Optional<String> accessNumber = exemplar.stream()
                .flatMap(exemp -> accessNumberSequenceLoader.getByFondAndLocation(exemp.getFond(), exemp.getLocation()).stream())
                .filter(sequence -> pseudoLastAccessNumber.isPresent() && sequence.matches(pseudoLastAccessNumber.get()))
                .map(sequence -> sequence.createKnown(pseudoLastAccessNumber.get()).next())
                .findFirst()
                .or(() -> accessNumberSequenceItemLoader.get(exemplarSequenceQueryDto).stream().findFirst())
                .map(Valuable::getValue);

        return new ExemplarIdentifierRequest(null, accessNumber.orElse(null), signature.orElse(null), null);
    }


    private List<? extends Exemplar> returnExemplars(@NonNull List<? extends Exemplar> exemplars,
                                                     boolean forView,
                                                     @NonNull User requester,
                                                     @NonNull Department ctx,
                                                     @NonNull UserAuthentication currentAuth) {
        if (forView) {
            exemplars = extendExemplars(exemplars, requester, ctx, currentAuth);
        }
        return exemplarListSorter.sort(exemplars);
    }


    private List<ViewableExemplar> extendExemplars(@NonNull List<? extends Exemplar> exemplars,
                                                   @NonNull User requester,
                                                   @NonNull Department ctx,
                                                   @NonNull UserAuthentication currentAuth) {
        var exemplarIdToHolderMap = exemplarHolderService.batchFindHoldingLoans(ListUtil.getListOfIds(exemplars));
        return exemplars.parallelStream()
                .map(ex -> {
                    boolean holderShowable = securityManager.can(ExemplarSecurityActions.EXEMPLAR_HOLDER_NAME_SHOW, currentAuth, ctx, ex);
                    BasicUser holder = (! holderShowable)
                            ? null
                            : ObjectUtil.elvis(exemplarIdToHolderMap.get(ex.getId()), Loan::getUser);
                    boolean holderDetailShowable = securityManager.can(ExemplarSecurityActions.EXEMPLAR_HOLDER_NAME_SHOW, currentAuth, ctx, ex);
                    boolean editable = !ex.isDiscarded() && securityManager.can(ExemplarSecurityActions.EXEMPLAR_EDIT, currentAuth, ctx, ex);
                    boolean discardable = !ex.isDiscarded() && securityManager.can(ExemplarSecurityActions.EXEMPLAR_DISCARD, currentAuth, ctx, ex);
                    boolean deletable = securityManager.can(ExemplarSecurityActions.EXEMPLAR_DELETE, currentAuth, ctx, ex);
                    boolean discardionRestorable = ex.isDiscarded() && securityManager.can(ExemplarSecurityActions.EXEMPLAR_RESTORE, currentAuth, ctx, ex);
                    AppserverDataBackedExemplarAvailability availability = exemplarAvailabilityService.getExemplarAvailability(new ExemplarAvailabilityRequest(ex, requester, ctx, currentAuth));
                    return new ViewableExemplar(ex, availability, holder, holderShowable, holderDetailShowable, editable, discardable, deletable, discardionRestorable);
                })
                .toList();
    }

}
