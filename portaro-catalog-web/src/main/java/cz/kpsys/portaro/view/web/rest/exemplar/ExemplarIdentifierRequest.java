package cz.kpsys.portaro.view.web.rest.exemplar;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.exemplar.BasicExemplar;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.form.valueeditor.object.ObjectEditorSubForm;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyHint;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formfield.EnabledFormFields;
import cz.kpsys.portaro.formannotation.annotations.remotevalidation.RemoteValidator;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorModifier;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.accessnumber.AccessNumberEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.scannabletext.ScannableTextEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.signature.SignatureEditor;
import jakarta.validation.constraints.Size;
import lombok.Value;
import lombok.With;

@Form(id = "exemplarIdentifierRequest")
@EnabledFormFields(bean = "editableExemplarRequestIdentifiersPropsProvider", resolveOnlyExplicitlyInvolved = false)
@With
@Value
public class ExemplarIdentifierRequest implements ObjectEditorSubForm<ExemplarRequest, ExemplarIdentifierRequest> {

    ExemplarRequest parentForm;

    @FormPropertyLabel("{exemplar.accessNumber}")
    @FormPropertyHint("{exemplar.accessNumber.hint}")
    @AccessNumberEditor(canDoActionProviderBean = "exemplarEditCanGenerateField")
    @RemoteValidator(path = ExemplarApiController.VALIDITY_PATH + "/" + BasicExemplar.Fields.accessNumber, sendWholeForm = true)
    @Size(min = 1, max = Exemplar.ACCESS_NUMBER_MAX_LENGTH)
    @NullableNotBlank
    String accessNumber;

    @FormPropertyLabel("{exemplar.signature}")
    @FormPropertyHint("{exemplar.signature.hint}")
    @SignatureEditor(canDoActionProviderBean = "exemplarEditCanGenerateField")
    @RemoteValidator(path = ExemplarApiController.VALIDITY_PATH + "/" + BasicExemplar.Fields.signature, sendWholeForm = true)
    @Size(min = 1, max = Exemplar.SIGNATURE_MAX_LENGTH)
    @NullableNotBlank
    String signature;

    @FormPropertyLabel("{exemplar.barCode}")
    @FormPropertyHint("{exemplar.barCode.hint}")
    @ScannableTextEditor(autosubmit = false)
    @ValueEditorModifier("exemplarEditationBarCodeScannableTextEditorAnnotationModifier")
    @RemoteValidator(path = ExemplarApiController.VALIDITY_PATH + "/" + BasicExemplar.Fields.barCode, sendWholeForm = true)
    @Size(min = 1, max = Exemplar.BAR_CODE_MAX_LENGTH)
    @NullableNotBlank
    String barCode;
}
