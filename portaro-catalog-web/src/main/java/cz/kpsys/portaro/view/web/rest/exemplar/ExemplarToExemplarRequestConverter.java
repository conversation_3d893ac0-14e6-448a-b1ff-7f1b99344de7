package cz.kpsys.portaro.view.web.rest.exemplar;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.exemplar.volume.Volume;
import cz.kpsys.portaro.exemplar.volume.VolumeLoader;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.ArrayList;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ExemplarToExemplarRequestConverter implements Converter<Exemplar, ExemplarRequest> {

    @NonNull ByIdLoadable<Record, UUID> nonDetailedDocumentLoader;
    @NonNull VolumeLoader volumeLoader;

    @Override
    @NonNull
    public ExemplarRequest convert(Exemplar exemplar) {
        Record document = nonDetailedDocumentLoader.getById(exemplar.getRecordId());

        Volume volume = null;
        if (exemplar.isIssue()) {
            volume = volumeLoader.getById(exemplar.getVolumeId());
        }

        ExemplarRequest exemplarRequest = new ExemplarRequest(
                exemplar.getId(),
                document,
                volume,
                true,
                exemplar.getInvoiceItem(),
                exemplar.getDepartment(),
                exemplar.getLocation(),
                exemplar.getLoanCategory(),
                exemplar.getThematicGroup(),
                exemplar.getAcquisitionWay(),
                exemplar.getCreationDate(),
                exemplar.getCustomValue(),
                exemplar.getStatus(),
                exemplar.getPrice(),
                exemplar.getOwner(),
                exemplar.getAcquisitionYear(),
                exemplar.getNote(),
                exemplar.getInternalNote(),
                exemplar.getAccount(),
                exemplar.getInvoiceNumber(),
                exemplar.getAttachments(),
                exemplar.getOrder(),
                exemplar.getSupplier(),
                exemplar.getType(),
                exemplar.getQuantity(),
                exemplar.getBundledVolumeNumber(),
                exemplar.getBundledVolumeYear(),
                exemplar.getBundledVolumeIssueRange(),
                exemplar.getIssueName(),
                exemplar.getIssueEvidenceNumber(),
                new ArrayList<>()
        );

        exemplarRequest.identifiers().add(new ExemplarIdentifierRequest(
                exemplarRequest,
                exemplar.getAccessNumber(),
                exemplar.getSignature(),
                exemplar.getBarCode()
        ));

        return exemplarRequest;
    }
}
