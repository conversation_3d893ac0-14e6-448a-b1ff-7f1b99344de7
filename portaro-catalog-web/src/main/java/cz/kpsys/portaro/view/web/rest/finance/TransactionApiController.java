package cz.kpsys.portaro.view.web.rest.finance;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.payment.*;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@ResponseBody
@RequestMapping("/api/transactions")
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TransactionApiController extends GenericApiController {

    @NonNull TransactionLoader transactionLoader;
    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull TransactionDeleter transactionDeleter;

    @GetMapping
    public List<Transaction> getAll(@RequestParam(Transaction.OWNER) BasicUser owner,
                                    UserAuthentication currentAuth,
                                    @CurrentDepartment Department ctx) {
        securityManager.throwIfCannot(PaymentSecurityActions.PAYMENTS_SHOW_OF_USER, currentAuth, ctx, owner);
        List<Department> depts = departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.SUBTREE);
        return transactionLoader.getAllOn(owner, depts);
    }


    @DeleteMapping("{id}")
    public void delete(@PathVariable("id") Transaction tx,
                       UserAuthentication currentAuth,
                       @CurrentDepartment Department ctx) {
        Assert.state(tx.isNotZero(), "Cannot delete zero value tx");
        if (tx.isPayment()) {
            securityManager.throwIfCannot(PaymentSecurityActions.PAYMENT_DELETE, currentAuth, ctx, tx.getOwner());
        }

        if (tx.isDebt()) {
            securityManager.throwIfCannot(PaymentSecurityActions.DEBT_DELETE, currentAuth, ctx, tx.getOwner());
        }
        transactionDeleter.processTransactionReset(tx);
    }

}
