package cz.kpsys.portaro.view.web.rest.finance;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.auth.BasicUserAuthentication;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.HierarchicalDepartment;
import cz.kpsys.portaro.department.HierarchicalDepartmentLoaderByDepartmentLoader;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.AfterIntegrityValidationViolation;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.acceptableroot.AcceptableRootEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.date.DateEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.date.DateValueEditorGranularity;
import cz.kpsys.portaro.formconfig.valueeditor.AcceptableValueResolver;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Past;
import jakarta.validation.constraints.PastOrPresent;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.util.List;

import static java.time.temporal.ChronoUnit.DAYS;

@Form(id = "transactionsPrint", title = "{print.PrintSetting}")
@FormSubmit(path = CatalogWebConstants.API_URL_PREFIX + "/transaction-prints")
@AfterIntegrityValidationViolation(bean = "transactionsPrintRequestDefaulter")
@With
public record TransactionsPrintRequest(

        @FormPropertyLabel("{commons.DateFrom}")
        @DateEditor(granularity = DateValueEditorGranularity.DATE_ONLY)
        @Past
        @NotNull
        Instant fromDate,

        @FormPropertyLabel("{commons.DateTo}")
        @DateEditor(granularity = DateValueEditorGranularity.DATE_ONLY)
        @PastOrPresent
        @NotNull
        Instant toDate,

        // TODO: přejmenovat lokalizaci?
        @FormPropertyLabel("{commons.Departments}")
        @AcceptableRootEditor(valuesSourceBean = "transactionsPrintAllowedParentDepartmentResolver")
        @NotEmpty
        List<Department> cashierDepartments

) {

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class TransactionsPrintRequestDefaulter implements TypedAuthenticatedContextualObjectModifier<TransactionsPrintRequest> {

        @NonNull HierarchyLoader<Department> departmentAccessor;

        @Override
        public TransactionsPrintRequest modify(TransactionsPrintRequest request, Department ctx, UserAuthentication currentAuth) {
            if (request.fromDate() == null) {
                request = request.withFromDate(Instant.now().minus(90, DAYS));
            }
            if (request.toDate() == null) {
                request = request.withToDate(Instant.now());
            }
            if (request.cashierDepartments() == null) {
                request = request.withCashierDepartments(departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.SUBTREE));
            }
            return request;
        }
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class TransactionsPrintAllowedParentDepartmentResolver implements AcceptableValueResolver<TransactionsPrintRequest, HierarchicalDepartment> {

        @NonNull HierarchicalDepartmentLoaderByDepartmentLoader hierarchicalDepartmentLoaderByDepartmentLoader;

        @Override
        public HierarchicalDepartment resolveAcceptableValue(@NonNull TransactionsPrintRequest request, @NonNull Department ctx, @NonNull BasicUserAuthentication auth) {
            return hierarchicalDepartmentLoaderByDepartmentLoader.toHierarchical(ctx);
        }
    }

}
