package cz.kpsys.portaro.view.web.rest.mail;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.localization.LocalizationCodes;
import cz.kpsys.portaro.mail.*;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Locale;

@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MailApiController extends GenericApiController {

    public static final String MAIL_PATH = "/api/mail";
    public static final String EXACT_USER_MAIL_PATH = "/api/mail/exact-user";

    @NonNull MailService mailService;

    @PostMapping(MailApiController.MAIL_PATH)
    public ActionResponse sendMail(@RequestBody @ValidFormObject RawBodyMailSendRequest request,
                                   @CurrentDepartment Department ctx,
                                   UserAuthentication currentAuth,
                                   Locale locale) {
        NonspecificMailSendCommand command = mapRequestRawBodyMailSendCommand(request, ctx, currentAuth, locale);
        mailService.sendRawBodyMail(command);
        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.MailWasSuccessfullySent));
    }

    @PostMapping(MailApiController.EXACT_USER_MAIL_PATH)
    public ActionResponse sendExactUserMail(@RequestBody @ValidFormObject ExactUserRawBodyMailSendRequest request,
                                            @CurrentDepartment Department ctx,
                                            UserAuthentication currentAuth,
                                            Locale locale) {
        NonspecificMailSendCommand command = mapRequestRawBodyMailSendCommand(request, ctx, currentAuth, locale);
        mailService.sendRawBodyMail(command);
        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.MailWasSuccessfullySent));
    }

    private NonspecificMailSendCommand mapRequestRawBodyMailSendCommand(RawBodyMailSendRequest request, Department ctx, UserAuthentication currentAuth, Locale locale) {
        return new NonspecificMailSendCommand(
                RawBodyMailTopics.COMMON,
                ctx,
                From.system(),
                To.emailsWithForcedLocale(request.recipients(), locale),
                Texts.ofNative(request.subject()),
                request.body(),
                request.attachments(),
                currentAuth
        );
    }

    private NonspecificMailSendCommand mapRequestRawBodyMailSendCommand(ExactUserRawBodyMailSendRequest request, Department ctx, UserAuthentication currentAuth, Locale locale) {
        return new NonspecificMailSendCommand(
                RawBodyMailTopics.COMMON,
                ctx,
                From.internal(currentAuth.getActiveUser()),
                To.users(request.recipients()),
                Texts.ofNative(request.subject()),
                request.body(),
                request.attachments(),
                currentAuth
        );
    }

}




