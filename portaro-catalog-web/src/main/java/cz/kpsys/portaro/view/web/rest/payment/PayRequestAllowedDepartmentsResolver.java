package cz.kpsys.portaro.view.web.rest.payment;

import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formconfig.valueeditor.AuthenticatedAcceptableValuesResolver;
import cz.kpsys.portaro.payment.PayCommand;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PayRequestAllowedDepartmentsResolver implements AuthenticatedAcceptableValuesResolver<PayRequest, Department> {

    @NonNull AuthenticatedContextualProvider<Department, List<Department>> editableDepartmentsAuthenticatedContextualProvider;

    @Override
    public List<Department> resolveAcceptableValues(PayRequest request, Department ctx, UserAuthentication currentAuth) {
        if (request.cashierDepartment() != null) {
            return List.of(request.cashierDepartment());
        }
        if (PayCommand.isOfType(request.provider(), PayCommand.PROVIDER_TYPE_CASH) || PayCommand.isOfType(request.provider(), PayCommand.PROVIDER_TYPE_TERMINAL)) { //cashier is logged librarian in cash/terminal payment
            return editableDepartmentsAuthenticatedContextualProvider.getOn(currentAuth, ctx);
        }
        if (PayCommand.isOfType(request.provider(), PayCommand.PROVIDER_TYPE_GATEWAY)) { //cashier is portaro in gateway payment
            return List.of(ctx);
        }
        throw new UnsupportedOperationException("Unknown payment provider %s".formatted(request.provider()));
    }

}
