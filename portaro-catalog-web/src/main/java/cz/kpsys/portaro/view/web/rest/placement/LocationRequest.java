package cz.kpsys.portaro.view.web.rest.placement;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.ConfirmableRequest;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.multipleacceptable.MultipleAcceptableEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.number.NumberEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextEditor;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.With;

import java.util.List;

import static cz.kpsys.portaro.location.LocationConstants.CODE_MAX_LENGTH;
import static cz.kpsys.portaro.location.LocationConstants.NAME_MAX_LENGTH;

@Form(id = "locationRequest", title = "{location.EditOrCreateLocation}")
@FormSubmit(path = CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.LOCATION_URL_PART)
@With
public record LocationRequest(

        Boolean confirmed,

        @Nullable
        Integer id,

        @FormPropertyLabel("{commons.Oznaceni}")
        @TextEditor
        @Size(min = 1, max = CODE_MAX_LENGTH)
        @NotBlank
        String code,

        @FormPropertyLabel("{commons.nazev}")
        @TextEditor
        @Size(min = 1, max = NAME_MAX_LENGTH)
        @NotBlank
        String name,

        @FormPropertyLabel("{commons.Poradi}")
        @NumberEditor
        @NotNull
        @Min(0)
        Integer order,

        @FormPropertyLabel("{commons.Departments}")
        @MultipleAcceptableEditor(valuesSourceBean = "editableDepartmentsAuthenticatedContextualProvider", visibleIfSingleValue = true)
        List<Department> departments

) implements ConfirmableRequest<LocationRequest> {}
