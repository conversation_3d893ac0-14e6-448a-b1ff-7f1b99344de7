package cz.kpsys.portaro.view.web.rest.record.requests;

import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.record.Record;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.util.UUID;

public record RecordSetPrimaryCoverFromExistingFileRequest(

        @Schema(implementation = UUID.class, description = "Target record UUID", example = Record.SCHEMA_EXAMPLE_DOCUMENT_ID)
        @NotNull Record record,

        @Schema(implementation = Long.class, description = "Cover file ID")
        @NotNull IdentifiedFile file

) {}
