package cz.kpsys.portaro.view.web.rest.user;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.penalty.DelayedReturnPenalty;
import cz.kpsys.portaro.loan.penalty.DelayedReturnPenaltyService;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.sec.SecurityActions;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Hidden
@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DelayedReturnPenaltyApiController extends GenericApiController {
    
    @NonNull DelayedReturnPenaltyService delayedReturnPenaltyService;
    @NonNull SecurityManager securityManager;


    @RequestMapping("/api/users/{user}/delayed-return-penalty")
    public DelayedReturnPenalty getDelayedReturnPenalty(@PathVariable("user") User user,
                                                        @CurrentDepartment Department currentDepartment,
                                                        UserAuthentication currentAuth) {
        securityManager.throwIfCannot(SecurityActions.USER_SHOW, currentAuth, currentDepartment, user);
        return delayedReturnPenaltyService.getDelayedReturnPenalty(user);
    }
    
}