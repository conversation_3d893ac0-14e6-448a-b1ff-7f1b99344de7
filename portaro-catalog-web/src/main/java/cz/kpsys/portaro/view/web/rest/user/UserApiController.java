package cz.kpsys.portaro.view.web.rest.user;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.ActiveUser;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.Validity;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.remotevalidation.DefaultModelRemoteValidationRequest;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.record.binding.department.LibraryDepartmentLinker;
import cz.kpsys.portaro.record.binding.user.UserFilesSaver;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.deletion.UserDeletionCommand;
import cz.kpsys.portaro.user.deletion.UserDeletionRequest;
import cz.kpsys.portaro.user.edit.*;
import cz.kpsys.portaro.user.edit.command.UserEditationCommand;
import cz.kpsys.portaro.user.edit.request.LibraryLinkToDepartmentRequest;
import cz.kpsys.portaro.user.edit.request.RegistrationExtensionRequest;
import cz.kpsys.portaro.user.edit.request.UserEditationRequest;
import cz.kpsys.portaro.user.edit.request.UserRelationsRequest;
import cz.kpsys.portaro.user.edit.request.single.IsicSingleUserFieldEditation;
import cz.kpsys.portaro.user.edit.single.IsicSingleUserFieldEditor;
import cz.kpsys.portaro.user.registration.*;
import cz.kpsys.portaro.user.relation.UserRelation;
import cz.kpsys.portaro.user.relation.UserRelationsSaveCommand;
import cz.kpsys.portaro.user.relation.UserRelationsValidator;
import cz.kpsys.portaro.user.role.reader.CardNumberSequenceItem;
import cz.kpsys.portaro.user.role.reader.CardNumberSequenceItemLoader;
import cz.kpsys.portaro.user.role.reader.ReaderBarCodeSequenceItem;
import cz.kpsys.portaro.user.role.reader.ReaderBarCodeSequenceItemLoader;
import cz.kpsys.portaro.user.sec.SecurityActions;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpSession;
import jakarta.validation.ConstraintViolationException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

import static cz.kpsys.portaro.app.CatalogWebConstants.REGISTRATION_EXTEND_URL_PART;
import static cz.kpsys.portaro.user.UserApiConstants.USERS_URL_PATH;

@Hidden
@RequestMapping(USERS_URL_PATH)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class UserApiController extends GenericApiController {

    @NonNull Deleter<UserDeletionCommand> userDeleter;
    @NonNull SecurityManager securityManager;
    @NonNull CardNumberSequenceItemLoader cardNumberSequenceItemLoader;
    @NonNull ReaderBarCodeSequenceItemLoader readerBarCodeSequenceItemLoader;
    @NonNull UserEditationFactory userEditationFactory;
    @NonNull AuthenticationAwareRegistrationPeriodExtender registrationPeriodExtender;
    @NonNull UserActivationLinkService userActivationLinkService;
    @NonNull Saver<UserRelationsSaveCommand, ?> userRelationsSaver;
    @NonNull UserRelationsValidator userRelationsValidator;
    @NonNull ContextualFunction<UserRelationsRequest, Department, List<UserRelation>> userRelationsRequestToFamilyRelationsConverter;
    @NonNull UserEditationRequestToUserEditation userEditationRequestToUserEditation;
    @NonNull UserEditationPermissionsChecker userEditationPermissionsChecker;
    @NonNull UserSaveResponseResolver userSaveResponseResolver;
    @NonNull UserEditationFormCreator userEditationFormCreator;
    @NonNull UserFilesSaver userFilesSaver;
    @NonNull IsicSingleUserFieldEditor isicSingleUserFieldEditor;
    @NonNull LibraryDepartmentLinker libraryDepartmentLinker;

    @GetMapping("{id}")
    public User get(@PathVariable("id") User user,
                    @CurrentDepartment Department currentDepartment,
                    UserAuthentication currentAuth) {
        securityManager.throwIfCannot(SecurityActions.USER_SHOW, currentAuth, currentDepartment, user);
        return user;
    }

    @PostMapping
    public ActionResponse save(@RequestBody @ValidFormObject UserEditationRequest userEditationRequest,
                               @CurrentDepartment Department currentDepartment,
                               UserAuthentication currentAuth,
                               HttpSession session) {
        UserEditation<? extends UserEditationCommand, ? extends User> userEditation = userEditationRequestToUserEditation.toEditation(userEditationRequest, currentAuth, currentDepartment);
        userEditationPermissionsChecker.check(userEditation, currentDepartment, currentAuth);

        try {
            UserEditationSaveResult<?> saveResult = userEditation.saveIfModified();
            return userSaveResponseResolver.successful(saveResult, session.getId());
        } catch (ConstraintViolationException e) {
            return userSaveResponseResolver.exception(e, currentDepartment, currentAuth, userEditation, userEditationRequest);
        }
    }

    @PostMapping("/edit/single/isic")
    public ActionResponse singleEditIsic(@RequestBody @ValidFormObject IsicSingleUserFieldEditation isicSingleUserFieldEditation,
                               @CurrentDepartment Department ctx,
                               UserAuthentication currentAuth) {
        securityManager.throwIfCannot(SecurityActions.USER_EDIT, currentAuth, ctx, isicSingleUserFieldEditation.user());
        User user = isicSingleUserFieldEditor.editIsic(isicSingleUserFieldEditation.user(), isicSingleUserFieldEditation.value(), ctx, currentAuth);
        return FinishedSaveResponse.saved(user);
    }

    @GetMapping("{id}/form")
    public ActionResponse form(@PathVariable("id") User user,
                               @CurrentDepartment Department currentDepartment,
                               UserAuthentication currentAuth) {
        securityManager.throwIfCannot(SecurityActions.USER_SHOW, currentAuth, currentDepartment, user);
        UserEditation<UserEditationCommand, User> userEditation = userEditationFactory.ofExistingUser(user, currentDepartment, currentAuth);
        return userEditationFormCreator.create(currentDepartment, currentAuth, userEditation);
    }

    @DeleteMapping("{id}")
    public ActionResponse delete(@PathVariable("id") User user,
                                 @RequestBody @ValidFormObject UserDeletionRequest userDeletionRequest,
                                 @CurrentDepartment Department currentDepartment,
                                 UserAuthentication currentAuth) {
        securityManager.throwIfCannot(SecurityActions.USER_DELETE, currentAuth, currentDepartment, user);
        var userDeletionCommand = new UserDeletionCommand(
                user,
                currentDepartment,
                currentAuth,
                ObjectUtil.isTrue(userDeletionRequest.ignoreUserRegistrationDebt()),
                ObjectUtil.isTrue(userDeletionRequest.ignoreActiveUserLoans()),
                ObjectUtil.isTrue(userDeletionRequest.ignoreUserIsRepresentativeOfGroup()),
                ObjectUtil.isTrue(userDeletionRequest.ignoreUserIsMemberOfGroup()),
                ObjectUtil.isTrue(userDeletionRequest.ignoreGroupHasMembers()));
        userDeleter.delete(userDeletionCommand);
        return new FinishedActionResponse(Texts.ofMessageCoded("editace.ctenarSmazan"));
    }

    @GetMapping("sequences/card-number")
    public String generateCardNumber(@RequestParam(value = "departments", defaultValue = "") List<Department> departments) {
        return cardNumberSequenceItemLoader.getNext(departments)
                .map(CardNumberSequenceItem::getValue)
                .orElse(null);
    }

    @GetMapping("sequences/bar-code")
    public String generateBarCode(@RequestParam(value = "department", defaultValue = "") Department department) {
        return readerBarCodeSequenceItemLoader.get(department)
                .map(ReaderBarCodeSequenceItem::getValue)
                .orElse(null);
    }

    @PostMapping(REGISTRATION_EXTEND_URL_PART)
    public ActionResponse extendRegistration(@RequestBody @ValidFormObject RegistrationExtensionRequest registrationExtensionRequest,
                                             UserAuthentication currentAuth,
                                             @ActiveUser User activeUser,
                                             @CurrentDepartment Department currentDepartment) {

        registrationPeriodExtender.extendRegistrationPeriod(new RegistrationPeriodExtensionCommand(registrationExtensionRequest.extendingUser(), activeUser, currentAuth, currentDepartment, registrationExtensionRequest.ignoreDiscountRequest()));

        return FinishedActionResponse.ok();
    }

    @PostMapping("{id}/activation")
    public ActionResponse activation(@PathVariable("id") User user,
                                     @RequestParam(TokenUserActivationLinkService.TOKEN_PARAMETER_NAME) String token,
                                     UserAuthentication currentAuth,
                                     @CurrentDepartment Department currentDepartment) {
        userActivationLinkService.acceptToken(new UserActivationCommand(user, currentDepartment, currentAuth, token));

        userEditationFactory.ofExistingUser(user, currentDepartment, currentAuth)
                .apply(editationRequest -> editationRequest.setActive(Optional.of(true)))
                .saveIfModified();

        return new FinishedActionResponse(MultiText.ofTexts("Uživatel {} byl úspěšně aktivován. Nyní můžete využívat všechny služby katalogu", user.getText()));
    }

    @Operation(summary = "Upload user files")
    @PostMapping(path = "/upload-user-files")
    public FinishedSaveResponse<List<String>> createFromFiles(
            @NonNull @ValidFormObject UploadedUserFilesRequest request,
            UserAuthentication currentAuth,
            @CurrentDepartment Department ctx) {
        List<String> savedFiles = userFilesSaver.save(request.toCommand(ctx, currentAuth));

        return new FinishedSaveResponse<>(getFileUploadResponseText(savedFiles), savedFiles);
    }

    private static Text getFileUploadResponseText(List<String> savedFiles) {
        if (savedFiles.size() == 1) {
            return Texts.ofNative("Soubor byl úspěšně nahrán");
        }
        return Texts.ofNative("Soubory byly úspěšně nahrány");
    }

    @PostMapping("relations")
    public ActionResponse setRelations(@RequestBody @ValidFormObject UserRelationsRequest userRelationsRequest,
                                       @CurrentDepartment Department currentDepartment) {

        val relationsToCreate = userRelationsRequestToFamilyRelationsConverter.getOn(userRelationsRequest, currentDepartment);

        userRelationsSaver.save(new UserRelationsSaveCommand(List.of(), relationsToCreate, currentDepartment));

        return FinishedActionResponse.ok();
    }

    @PostMapping("link-department")
    public ActionResponse linkDepartment(@RequestBody @ValidFormObject LibraryLinkToDepartmentRequest request,
                                         UserAuthentication currentAuth,
                                         @CurrentDepartment Department currentDepartment) {

        libraryDepartmentLinker.link(request.toCommand(currentDepartment, currentAuth));

        return FinishedActionResponse.ok();
    }

    @PostMapping("relations-validator")
    public Validity validate(@RequestBody @ValidFormObject DefaultModelRemoteValidationRequest<User, UserRelationsRequest> validationRequest) {
        String fieldName = validationRequest.fieldName();

        if (fieldName.equals(UserRelationsRequest.Fields.representative)) {
            if (validationRequest.value() == null) {
                return Validity.ofEmptyAsValid(fieldName);
            }

            return userRelationsValidator.canBeRepresentative(validationRequest.value()).toValidity(validationRequest.fieldName());
        }

        if (fieldName.startsWith(UserRelationsRequest.Fields.sources)) {
            if (validationRequest.value() == null) {
                return new Validity(fieldName, Validity.TYPE_EMPTY, false, Texts.ofNative("User cannot be empty"));
            }

            return userRelationsValidator.canBeFamilyMember(validationRequest.value(), validationRequest.formModel().representative(), validationRequest.formModel().target()).toValidity(validationRequest.fieldName());
        }

        if (fieldName.equals(UserRelationsRequest.Fields.target)) {
            if (validationRequest.value() == null) {
                return Validity.ofEmptyAsValid(fieldName);
            }

            return userRelationsValidator.canBeFamily(validationRequest.value()).toValidity(validationRequest.fieldName());
        }

        return Validity.ofEmptyAsValid(fieldName);
    }

}
