package cz.kpsys.portaro.view.web.rest.user;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualBiFunction;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.intent.LabeledMappedAction;
import cz.kpsys.portaro.intent.MappedAction;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import cz.kpsys.portaro.user.sec.SecurityActions;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserAttentionRequiringActionsProvider implements ContextualBiFunction<User, UserAuthentication, Department, List<LabeledMappedAction<?>>> {

    public static final String EXTEND_USER_REGISTRATION_ACTION = "extend-user-registration";

    @NonNull SecurityManager securityManager;

    @Override
    public List<LabeledMappedAction<?>> getOn(@NonNull User user, UserAuthentication currentAuth, Department ctx) throws ItemNotFoundException {
        ArrayList<LabeledMappedAction<?>> actions = new ArrayList<>();

        if (ReaderRole.isRegistrationNonExisting(user) && securityManager.can(SecurityActions.USER_REGISTRATION_PERIOD_EXTEND, currentAuth, ctx, user)) {
            actions.add(new LabeledMappedAction<>(new MappedAction<>(EXTEND_USER_REGISTRATION_ACTION, new BasicIdentified<>(user.getId())), Texts.ofMessageCoded("ctenar.CompleteRegistrationDescription"), Texts.ofMessageCoded("ctenar.CompleteRegistration")));
        } else if (securityManager.can(SecurityActions.USER_REGISTRATION_PERIOD_EXTEND, currentAuth, ctx, user)) {
            actions.add(new LabeledMappedAction<>(new MappedAction<>(EXTEND_USER_REGISTRATION_ACTION, new BasicIdentified<>(user.getId())), Texts.ofMessageCoded("ctenar.ProdlouzitRegistraciDescription"), Texts.ofMessageCoded("ctenar.ProdlouzitRegistraci")));
        }

        return actions;
    }
}
