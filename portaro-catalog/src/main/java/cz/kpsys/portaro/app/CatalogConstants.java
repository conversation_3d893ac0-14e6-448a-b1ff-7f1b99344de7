package cz.kpsys.portaro.app;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import cz.kpsys.portaro.file.AccessTypeAware;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.Resource;
import cz.kpsys.portaro.matcher.MatcherType;
import cz.kpsys.portaro.search.restriction.serialize.lucene.LuceneSortItem;
import cz.kpsys.portaro.user.matcher.Access;
import cz.kpsys.portaro.user.matcher.WebAccess;

import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.Set;

public class CatalogConstants {

    public static final PlaceholderTemplate PORTARO_USER_AGENT_TEMPLATE = new PlaceholderTemplate("Portaro/{version}");
    public static final String PORTARO_USER_AGENT_TEMPLATE_VERSION_PLACEHOLDER = "version";

    public static final String SERIAL_CODE_TESTING_DB = "100007000999";
    public static final String SERIAL_CODE_KINSTELLAR = "100007000029";

    public static final String CUSTOM_FOLDER_HTML = "html";
    public static final String CUSTOM_FOLDER_PAGES = "pages";
    public static final String CUSTOM_FOLDER_DESIGN = "design";

    //velocity
    public static final String VELOCITY_TEMPLATES_CLASSPATH_DIRECTORY_NAME = "velocity";
    public static final String VELOCITY_TEMPLATES_FILE_EXTENSION_WITH_DOT = ".vtl";
    public static final String VELOCITY_MACROS_FILENAME = "macros.vtl";

    //freemarker
    public static final String FREEMARKER_TEMPLATES_CLASSPATH_DIRECTORY_NAME = "freemarker";

    //ini in properties
    public static final String SETTING_SYSTEM_PROPERTY_PREFIX = "PORTARO_INI__";

    public static final Locale DEFAULT_LOCALE = CoreConstants.Locales.CS;
    public static final Locale CURRENCY_CZK_LOCALE = new Locale("cs", "CZ");
    public static final String DATE_FORMAT_PATTERN = "d.M.yyyy";
    public static final String DATETIME_FORMAT_PATTERN = "d.M.yyyy HH:mm:ss";

    public static final DateTimeFormatter FIELD_DATA_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");


    /**
     * Deprecated - use SettingKeys.ACME_CERTIFICATE_LOCATION and SettingKeys.ACME_PRIVATE_KEY_LOCATION
     */
    @Deprecated
    public static final String HTTPS_ACME_SSL_CUSTOM_DIRECTORY = "cert";
    /**
     * Deprecated - use SettingKeys.ACME_CERTIFICATE_LOCATION
     */
    @Deprecated
    public static final String HTTPS_ACME_SSL_CERTIFICATE_FILENAME = "acme-fullchain.pem";
    /**
     * Deprecated - use SettingKeys.ACME_PRIVATE_KEY_LOCATION
     */
    @Deprecated
    public static final String HTTPS_ACME_SSL_PRIVATE_KEY_FILENAME = "acme-privkey.pem";

    public static final String VERSION_FILE_VERSION_PROPERTY = "git.build.version";
    public static final String VERSION_FILE_VERSION_DATE_PROPERTY = "git.commit.time";
    public static final String VERSION_FILE_COMMIT_ABBREV_PROPERTY = "git.commit.id.abbrev";
    public static final String VERSION_FILE_VERSION_BRANCH_PROPERTY = "git.branch";
    public static final String VERSION_FILE_LAST_COMMIT_MESSAGE_PROPERTY = "git.commit.message.short";


    public static final Set<Integer> FONDS_FOR_CONVERT_TO_DEFAULT_AFTER_FIRST_EXEMPLAR_CREATION = Set.of(21, 23, 24, 25, 26, 27, 28, 29);

    public static class Search {

        public static final int AUTHORITY_SEARCH_DEFAULT_PAGE_SIZE = 30;

        public static final String SEARCH_CACHE_NAME = "search";

        public static class Lucene {
            public static final String FIELD_WHATEVER = "PALL";
            public static final String FIELD_NAME = "PNAZEV";
            public static final String FIELD_FOND = "REZS_FOND";
            public static final String FIELD_DEP = "REZS_PUBLIC_DEP";
            public static final String FIELD_UNRESTRICTED_DEP = "REZS_PUJC";
            public static final String FIELD_DRAFT_DEP = "REZS_DRAFT_DEP";
            public static final String FIELD_RECORD_STATUS = "STATUS";
            public static final String FIELD_COMMON_AUTHOR = "REZS_AUTOR";
            public static final String FIELD_LOCATION = "REZS_LOKACE";
            public static final String FIELD_ISBN = "P20";
            public static final String FIELD_ISSN = "P22";
            public static final String FIELD_DOCUMENT_NAME = "P245";
            public static final String FIELD_DOCUMENT_AUTHOR = "P100";
            public static final String FIELD_DOCUMENT_AUTHOR_CORPORATION = "P110";
            public static final String FIELD_DOCUMENT_AUTHOR_ACTION = "P111";
            public static final String FIELD_DOCUMENT_YEAR = "REZS_ROK";
            public static final String FIELD_DOCUMENT_PUBLISHER_OLD_FORM = "P260";
            public static final String FIELD_DOCUMENT_PUBLISHER = "P264";
            public static final String FIELD_DOCUMENT_NOTE = "P500";
            public static final String FIELD_RECORD_RELATED_RECORD = "REL_RECORD";
            public static final String FIELD_RECORD_CREATION_DATE = "REZS_DATVYT";
            public static final String FIELD_AUTHORITY_PERSON_NAME = "P100";
            public static final String FIELD_AUTHORITY_CORPORATION_NAME = "P110";
            public static final String FIELD_AUTHORITY_ACTION_NAME = "P111";
            public static final String FIELD_AUTHORITY_KEYWORD_NAME = "P150";
            public static final String FIELD_AUTHORITY_CNA = "P1";
            public static final String FIELD_AUTHORITY_CHRONOLOGIC_KEYWORD_NAME = "P148";
            public static final String FIELD_AUTHORITY_GEOGRAPHICS_NAME = "P151";
            public static final String FIELD_AUTHORITY_UNIFIED_NAME = "P240";
            public static final String FIELD_AUTHORITY_CONSPECT = "P190";
            public static final String FIELD_RECORD_SOURCE_DOCUMENT_NAME = "P773";
            public static final String FIELD_RECORD_ID = "RECORD_ID";
            public static final String FIELD_RECORD_FILE_CATEGORY = "REZS_TYP_FULLTEXT";
            public static final String FIELD_LOAN_SOURCE = "REZS_EXTERNI";
            public static final String FIELD_DOCUMENT_SIGNATURE = "PSIGNATURA";
            public static final String FIELD_DOCUMENT_ACCESS_NUMBER = "PPRIRCISLO";
            public static final String FIELD_TABLE_OF_CONTENT = "PTOC";

            public static final LuceneSortItem SORT_BY_NAME = LuceneSortItem.ofAsc(FIELD_NAME);
            public static final LuceneSortItem SORT_BY_AUTHOR = LuceneSortItem.ofAsc(FIELD_COMMON_AUTHOR);
            public static final LuceneSortItem SORT_BY_DOCUMENT_YEAR = LuceneSortItem.ofAsc(FIELD_DOCUMENT_YEAR);
            public static final LuceneSortItem SORT_BY_RECORD_CREATION_DATE = LuceneSortItem.ofAsc(FIELD_RECORD_CREATION_DATE);
        }

    }

    public static class Ini {
        public static final String SECTION_SEARCH_FACET = "search.facet";
        public static final String SECTION_SEARCH = "search";
        public static final String SECTION_KNIHOVNA = "KNIHOVNA";
        public static final String SECTION_EXEMP = "EXEMP";
        public static final String SECTION_CENTRAL_INDEX = "CENTRAL_INDEX";
        public static final String SECTION_EMAIL = "EMAIL";
        public static final String SECTION_SMS = "sms";
        public static final String SECTION_KPWIN = "KPWIN";
        public static final String SECTION_EXT_ZDROJE = "EXT_ZDROJE";
        public static final String SECTION_GDPR = "GDPR";
        public static final String SECTION_POPS = "POPS";
        public static final String SECTION_OPAC_DEPARTMENT = "OPAC_DEPARTMENT";
        public static final String SECTION_DEPARTMENT_DOMAINED = "department.domained";
        public static final String SECTION_SUTOR_SUTIN = "integ.sutin";
    }

    public static class Users {
        public static final String SBA_USER_USERNAME = "system-sba-server";
        public static final String SBA_USER_PASSWORD = "52d74776-54bf-43a5-aa63-e39550e46fe1";
        public static final String SBA_USER_PASSWORD_HASH = "$2a$10$HSKTCgaNsrt56JRdXGuhvuIDmyznHL2qw859ilyP7UOROMRB0OAkG";

        public static final String OPAC_USERNAME = "OPAC";
        public static final String SU_USERNAME = "SU";
        public static final String ANONYMOUS_USERNAME = "verbisanonymous";
        public static final String ADMIN_USERNAME = "su";
        public static final String UNIVERSAL_PASS_HASH = "$2a$10$KDqqelxJlFCHU8UVOIeYGOo4yM2hYx/9NOHC7986DmLfZHBWFtY/a";
        public static final String VERBIS_USERNAME = "verbisapplication";
        public static final String APPSERVER_USERNAME = "verbisappserver";

        public static final String CPK_USERNAME = "cpk";
        public static final String MOJEID_USERNAME = "mojeid";
        public static final String BANKID_USERNAME = "bankid";

        public static final String UPTIMEROBOT_USERNAME = "uptimerobot";
        public static final String AHREFSBOT_USERNAME = "ahrefsbot";
        public static final String APPLEBOT_USERNAME = "applelebot";
        public static final String BINGBOT_USERNAME = "bingbot";
        public static final String GOOGLEBOT_USERNAME = "googlebot";
        public static final String MJ12BOT_USERNAME = "mj12bot";
        public static final String PETALBOT_USERNAME = "petalbot";
        public static final String SEMRUSHBOT_USERNAME = "semrushbot";
        public static final String SEZNAMBOT_USERNAME = "seznambot";
        public static final String YANDEXBOT_USERNAME = "yandexbot";
    }

    public static class MatcherTypes {
        public static final MatcherType<Access<?>> MATCHER_LOGGED_USER = new MatcherType<>("LoggedUser");
        public static final MatcherType<Access<?>> MATCHER_LOGGED_USER_ROLE = new MatcherType<>("LoggedUserRole");
        public static final MatcherType<Access<?>> MATCHER_BLOCKED_USER = new MatcherType<>("BlockedUser");
        public static final MatcherType<Access<?>> MATCHER_ADMINISTRATOR_LOGGED_USER = new MatcherType<>("LoggedAdministratorUser");
        public static final MatcherType<Access<?>> MATCHER_CONCRETE_USER = new MatcherType<>("ConcreteUser");
        public static final MatcherType<Access<?>> MATCHER_READER_CATEGORY = new MatcherType<>("UserReaderCategory");
        public static final MatcherType<Access<?>> MATCHER_VALID_REGISTRATION = new MatcherType<>("ValidRegistration");
        public static final MatcherType<Access<AccessTypeAware>> MATCHER_FILE_ACCESS_TYPE = new MatcherType<>("FileAccessType");
        public static final MatcherType<Access<Resource>> MATCHER_FILE_CATEGORY = new MatcherType<>("FileCategory");
        public static final MatcherType<Access<Resource>> MATCHER_FILE_DIRECTORY_INHERITING_ACCESS_TYPE = new MatcherType<>("FileDirectoryInheritableAccessType");
        public static final MatcherType<Access<IdentifiedFile>> MATCHER_FILE_DOCUMENT = new MatcherType<>("FileDocument");
        public static final MatcherType<Access<IdentifiedFile>> MATCHER_FILE_DOCUMENT_SUBFIELD_VALUE = new MatcherType<>("FileDocumentSubfieldValue");
        public static final MatcherType<Access<IdentifiedFile>> MATCHER_FILE_DOCUMENT_COLLECTION = new MatcherType<>("FileDocumentCollection");
        public static final MatcherType<WebAccess<?>> MATCHER_IP_ADDRESS = new MatcherType<>("IpAddress");
        public static final MatcherType<WebAccess<?>> MATCHER_IP_ADDRESS_RANGE = new MatcherType<>("IpAddressRange");
        public static final MatcherType<WebAccess<?>> MATCHER_INTERNAL_IP = new MatcherType<>("InternaIp");
        public static final MatcherType<?> MATCHER_ALWAYS_MATCH = new MatcherType<>("Always");
    }

    public static class Export {

        public static class Tag {
            public static final String RECORD = "record";
            public static final String SEARCHED_RECORDS = "searched-records";
            public static final String SEARCHED_EXEMPLARS = "searched-exemplars";
            public static final String FILES = "files";
            public static final String USER = "user";
            public static final String LOANS = "loans";
            public static final String SEARCHED_LOANS = "searched-loans";

        }

        public static class Exporters {
            public static final String RECORD_CSV = "record-csv:";
            public static final String RECORD_XLS = "record-xls:";
            public static final String RECORD_DOC = "record-doc:";
            public static final String RECORD_RIS = "record-ris:";
            public static final String RECORDS_CSV = "records-csv:";
            public static final String RECORDS_XLS = "records-xls:";
            public static final String RECORDS_DOC = "records-doc:";
            public static final String RECORDS_RIS = "records-ris:";
            public static final String SEARCHED_RECORDS_CSV = "searched-records-csv:";
            public static final String SEARCHED_RECORDS_XLS = "searched-records-xls:";
            public static final String SEARCHED_RECORDS_DOC = "searched-records-doc:";
            public static final String SEARCHED_RECORDS_RIS = "searched-records-ris:";
            public static final String EXEMPLARS_CSV = "exemplars-csv:";
            public static final String LOANS_CSV = "loans-csv:";
            public static final String LOANS_XLS = "loans-xls:";
            public static final String LOANS_COVERS_ZIP = "loans-covers-zip:";
            public static final String SEARCHED_LOANS_CSV = "searched-loans-csv:";
            public static final String SEARCHED_LOANS_XLS = "searched-loans-xls:";
            public static final String USER_ACTIVE_LOANS_CSV = "user-active-loans-csv:";
            public static final String USER_ACTIVE_LOANS_XLS = "user-active-loans-xls:";
            public static final String USER_ACTIVE_LOANS_COVERS_ZIP = "user-active-loans-covers-zip:";
            public static final String SEARCHED_EXEMPLARS_CSV = "searched-exemplars-csv:";
            public static final String SEARCHED_EXEMPLARS_XLS = "searched-exemplars-xls:";
            public static final String PRINT_FILES = "files-print:";
            public static final String PERSONAL_DATA_EVIDENCE_JSON = "personal-data-evidence-json:";
        }

    }
}
