package cz.kpsys.portaro.search.impl;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.AbstractSingleColumnSpringDbSearchLoader;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.commons.util.ListUtil.getListOfIds;
import static cz.kpsys.portaro.databasestructure.RecordDb.*;
import static cz.kpsys.portaro.search.CoreSearchParams.DEPARTMENT;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbMostWatchedDocumentsLoader extends AbstractSingleColumnSpringDbSearchLoader<MapBackedParams, UUID, RangePaging> {

    public SpringDbMostWatchedDocumentsLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                              @NonNull QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory, RECORD.TABLE, RECORD.ID, new SelectedColumnRowMapper<>(UUID.class, RECORD.ID));
        setDistinct(true);
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.from(RECORD.TABLE);
        sq.joins().add(RECORD_HOLDING.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.RECORD_ID)));
        sq.joins().add(OPAC_RATING.OPAC_RATING, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(OPAC_RATING.OPAC_RATING, OPAC_RATING.RECORD_ID)));


        sq.where().and().gt(TC(OPAC_RATING.OPAC_RATING, OPAC_RATING.COUNTER), 0);

        if (p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD)) {
            sq.where().and().notIn(TC(RECORD.TABLE, RECORD.ID), p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD));
        }

        if (p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS)) {
            sq.where().and().notIn(TC(RECORD.TABLE, RECORD.RECORD_STATUS_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.FOND)) {
            if (!p.hasLength(RecordConstants.SearchParams.FOND)) {
                return false;
            }
            sq.where().and().in(TC(RECORD.TABLE, RECORD.FOND_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.FOND)));
        }

        if (p.hasNotNull(DEPARTMENT)) {
            if (!p.hasLength(DEPARTMENT)) {
                return false;
            }
            sq.where().and().in(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.DEPARTMENT_ID), getListOfIds(p.get(DEPARTMENT)));
        }

        if (p.isEmptyOrFalse(CoreSearchParams.INCLUDE_DRAFT)) {
            sq.where().and().isNotNull(withoutIndexString(TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID)));
        }

        if (p.isEmptyOrFalse(RecordConstants.SearchParams.INCLUDE_EXCLUDED)) {
            sq.where().and().isNull(withoutIndexString(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.DISCARDION_EVENT_ID)));
        }

        if (p.isEmptyOrFalse(CoreSearchParams.INCLUDE_DELETED)) {
            sq.where().and().isNull(withoutIndexString(TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID)));
            sq.where().and().isNull(withoutIndexString(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.DELETION_EVENT_ID)));
        }

        return true;
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        return Sorting.of(SortingItem.ofSimpleDesc(TC(OPAC_RATING.OPAC_RATING, OPAC_RATING.COUNTER)), SortingItem.ofSimpleAsc(TC(RECORD.TABLE, RECORD.SORTING_KEY)));
    }
}
