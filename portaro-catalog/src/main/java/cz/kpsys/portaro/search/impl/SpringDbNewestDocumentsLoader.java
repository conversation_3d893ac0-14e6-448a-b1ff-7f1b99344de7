package cz.kpsys.portaro.search.impl;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.RangePagingResultSetExtractor;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.databasestructure.RecordDb.RECORD;
import cz.kpsys.portaro.exemplar.ExemplarConstants;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.VIEW_NEWS;
import static cz.kpsys.portaro.search.CoreSearchParams.DEPARTMENT;

/**
 * Novinky view (VIEW_OPAC_NEWS) maji upravene:
 * Slany - puvodne meli upravene, ale asi jsme jim to prepsali,
 * kfbz,
 * npmk
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbNewestDocumentsLoader extends AbstractSpringDbSearchLoader<MapBackedParams, UUID, RangePaging> {

    private static final String VIEW_ALIAS = "news";
    private static final String MAX_PUBLISH_DATE = "max_publish_date";

    @NonNull Provider<@NonNull String> viewNameProvider;

    public SpringDbNewestDocumentsLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                         @NonNull QueryFactory queryFactory,
                                         @NonNull Provider<@NonNull String> viewNameProvider) {
        super(jdbcTemplate, queryFactory);
        this.viewNameProvider = viewNameProvider;
    }


    @Override
    protected void select(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.select(
                TC(RECORD.TABLE, RECORD.ID),
                AS(MAX(TC(VIEW_ALIAS, VIEW_NEWS.PUBLISH_DATE)), MAX_PUBLISH_DATE));
        sq.joins().add(RECORD.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(VIEW_ALIAS, VIEW_NEWS.RECORD_ID)));
        sq.groupBy(TC(RECORD.TABLE, RECORD.ID), TC(RECORD.TABLE, RECORD.SORTING_KEY));
    }


    @Override
    protected void selectCount(@NonNull SelectQuery sq, @NonNull MapBackedParams p) {
        sq.selectCountDistinct(TC(VIEW_ALIAS, VIEW_NEWS.RECORD_ID));
        sq.joins().add(RECORD.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(VIEW_ALIAS, VIEW_NEWS.RECORD_ID)));
    }


    @Override
    protected ResultSetExtractor<Chunk<UUID, RangePaging>> createResultSetExtractor(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @NonNull RangePaging paging, @NonNull Sorting sorting) {
        return new RangePagingResultSetExtractor<>(new SelectedColumnRowMapper<>(UUID.class, RECORD.ID), paging);
    }


    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.from(AS(viewNameProvider.get(), VIEW_ALIAS));


        if (p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD)) {
            sq.where().and().notIn(TC(RECORD.TABLE, RECORD.ID), p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD));
        }

        if (p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS)) {
            sq.where().and().notIn(TC(RECORD.TABLE, RECORD.RECORD_STATUS_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.FOND)) {
            if (!p.hasLength(RecordConstants.SearchParams.FOND)) {
                return false;
            }
            sq.where().and().in(TC(RECORD.TABLE, RECORD.FOND_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.FOND)));
        }

        if (p.hasNotNull(CoreSearchParams.FROM_DATE)) {
            sq.where().and().gtEq(TC(VIEW_ALIAS, VIEW_NEWS.PUBLISH_DATE), p.get(CoreSearchParams.FROM_DATE));
        }

        if (p.hasNotNull(CoreSearchParams.TO_DATE)) {
            sq.where().and().ltEq(TC(VIEW_ALIAS, VIEW_NEWS.PUBLISH_DATE), p.get(CoreSearchParams.TO_DATE));
        }

        if (p.hasNotNull(ExemplarConstants.SearchParams.EXEMPLAR_STATUS)) {
            if (!p.hasLength(ExemplarConstants.SearchParams.EXEMPLAR_STATUS)) {
                return false;
            }
            sq.where().and().in(TC(VIEW_ALIAS, VIEW_NEWS.EXEMPLAR_STATUS_ID), ListUtil.getListOfIds(p.get(ExemplarConstants.SearchParams.EXEMPLAR_STATUS)));
        }

        if (p.hasNotNull(DEPARTMENT)) {
            if (!p.hasLength(DEPARTMENT)) {
                return false;
            }
            sq.where().and().in(TC(VIEW_ALIAS, VIEW_NEWS.DEPARTMENT_ID), ListUtil.getListOfIds(p.get(DEPARTMENT)));
        }

        if (p.isEmptyOrFalse(CoreSearchParams.INCLUDE_DRAFT)) {
            sq.where().and().isNotNull(withoutIndexString(TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID)));
        }

        if (p.isEmptyOrFalse(CoreSearchParams.INCLUDE_DELETED)) {
            sq.where().and().isNull(withoutIndexString(TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID)));
        }

        return true;
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        return Sorting.of(SortingItem.ofSimpleDesc(MAX_PUBLISH_DATE), SortingItem.ofSimpleAsc(TC(RECORD.TABLE, RECORD.SORTING_KEY)));
    }
}
