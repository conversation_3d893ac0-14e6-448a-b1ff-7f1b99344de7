package cz.kpsys.portaro.setting.view;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.setting.CustomSetting;
import cz.kpsys.portaro.setting.CustomSettingId;
import cz.kpsys.portaro.setting.SettingTypeDto;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Value;
import lombok.experimental.FieldNameConstants;
import org.jspecify.annotations.Nullable;

@Value
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@FieldNameConstants
public class ViewableSettingValue {

    @EqualsAndHashCode.Include
    String id;
    Object value;
    LabeledIdentified<Integer> department;
    LabeledIdentified<Integer> fond;
    String note;
    boolean editable;
    boolean overridable;
    boolean isDefault;
    boolean effective;
    @Nullable
    Text errorText;

    public static ViewableSettingValue createByCustom(CustomSetting<Object> dto, LabeledIdentified<Integer> department, LabeledIdentified<Integer> fond, boolean overridable, boolean effective, Text errorText) {
        return new ViewableSettingValue(dto.getId().getValue(), dto.getValue(), department, fond, dto.getNote(), true, overridable, false, effective, errorText);
    }

    public static ViewableSettingValue createByDefault(SettingTypeDto<Object> dto, boolean effective) {
        return new ViewableSettingValue(CustomSettingId.createForDefault(dto.getId()).getValue(), dto.getValue(), null, null, null, false, true, true, effective, null);
    }

}
