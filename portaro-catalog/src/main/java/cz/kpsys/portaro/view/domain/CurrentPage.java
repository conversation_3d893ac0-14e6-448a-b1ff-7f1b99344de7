package cz.kpsys.portaro.view.domain;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.alert.Alert;
import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.AuthableUserResponse;
import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.auth.current.CurrentAuthResponse;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.LabeledId;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.util.UrlUtils;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.Link;
import cz.kpsys.portaro.commons.web.StaticLink;
import cz.kpsys.portaro.commons.web.UrlCreator;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.data.dataview.ErpCustomDataViewPagesHolder;
import cz.kpsys.portaro.licence.portaro.PortaroVersion;
import cz.kpsys.portaro.logging.Levelable;
import cz.kpsys.portaro.logging.SimpleLogger;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.security.StaticSecurityManager;
import cz.kpsys.portaro.template.BasicTemplateDescriptor;
import cz.kpsys.portaro.template.StaticTemplateRenderer;
import cz.kpsys.portaro.user.NoConcreteUser;
import cz.kpsys.portaro.user.RoleNamesResolver;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.sec.SecurityActions;
import cz.kpsys.portaro.view.domain.menu.MenuItem;
import cz.kpsys.portaro.view.domain.menu.response.MenuItemResponse;
import cz.kpsys.portaro.web.page.LocalizableView;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.*;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CurrentPage extends LocalizableView {

    @NonNull UserAuthentication currentAuth;
    @NonNull CurrentAuthResponse currentAuthResponse;
    @NonNull List<? extends LabeledIdentified<String>> enabledLanguages;
    @Getter @NonNull List<MenuItem> menuItems;
    @Getter @NonNull LoginView loginView;
    @Getter @NonNull PortaroVersion portaroVersion;
    @Getter @NonNull String serverUrl;
    @NonNull String publicContextPath;
    @NonNull SecurityManager securityManager;
    @Getter @NonNull List<Alert> alerts;
    @Getter @NonNull Map<String, List<String>> queryParams;
    @Getter @NullableNotBlank String titlePrefix;
    @Getter @NonNull List<Link> alternativePageLanguageLinks;
    @Getter @NonNull Levelable frontendLoggerLevel;
    @Getter @NonNull Boolean cameraScannerEnabled;
    @NonNull StaticTemplateRenderer customFileTemplateRenderer;
    @Getter @NonNull Boolean globalSearchInputEnabled;
    @Getter @NonNull String headerBackgroundColor;
    @Getter @NonNull String headerTextColor;
    @Getter @NonNull String headerLinkColor;
    @Getter @NonNull String mainMenuBackgroundColor;
    @Getter @NonNull String mainMenuTextColor;
    @Getter @NullableNotBlank String mainMenuHighlightBackgroundColor;
    @Getter @NullableNotBlank String mainMenuHighlightTextColor;
    @Getter @NonNull String globalSearchButtonColor;
    @Getter @NonNull String tableHeaderAccentColor;
    @Getter @NonNull String themeName;
    @Getter @NonNull List<MenuItemResponse> mainMenu;
    @Getter @NonNull Boolean isSutorSutinLayout;
    @Getter @NonNull String selectedTabHighlightColor;
    @Getter @NonNull ErpCustomDataViewPagesHolder customDataViewPages;
    @Getter @NonNull Boolean homeDepartmentRedirectAfterLogin;

    private CurrentPage(@NonNull Locale locale,
                        @NonNull Translator<Department> translator,
                        @NonNull UserAuthentication currentAuth,
                        @NonNull CurrentAuthResponse currentAuthResponse,
                        @NonNull List<? extends LabeledIdentified<String>> enabledLanguages,
                        @NonNull List<MenuItem> menuItems,
                        @NonNull LoginView loginView,
                        @NonNull PortaroVersion portaroVersion,
                        @NonNull String serverUrl,
                        @NonNull String publicContextPath,
                        @NonNull SecurityManager securityManager,
                        @NonNull Department currentDepartment,
                        @NonNull List<Alert> alerts,
                        @NonNull Map<String, List<String>> queryParams,
                        @NullableNotBlank String titlePrefix,
                        @NonNull List<Link> alternativePageLanguageLinks,
                        @NonNull Boolean cameraScannerEnabled,
                        @NonNull List<ActionResponse> dialogs,
                        @NonNull StaticTemplateRenderer customFileTemplateRenderer,
                        @NonNull Boolean globalSearchInputEnabled,
                        @NonNull String headerBackgroundColor,
                        @NonNull String headerTextColor,
                        @NonNull String headerLinkColor,
                        @NonNull String mainMenuBackgroundColor,
                        @NonNull String mainMenuTextColor,
                        @NullableNotBlank String mainMenuHighlightBackgroundColor,
                        @NullableNotBlank String mainMenuHighlightTextColor,
                        @NonNull String globalSearchButtonColor,
                        @NonNull String tableHeaderAccentColor,
                        @NonNull String themeName,
                        @NonNull List<MenuItemResponse> mainMenu,
                        @NonNull Boolean isSutorSutinLayout,
                        @NonNull String selectedTabHighlightColor,
                        @NonNull ErpCustomDataViewPagesHolder customDataViewPages,
                        @NonNull Boolean homeDepartmentRedirectAfterLogin) {
        super(locale, currentDepartment, translator);
        withDialogs(dialogs);
        this.currentAuth = currentAuth;
        this.currentAuthResponse = currentAuthResponse;
        this.enabledLanguages = enabledLanguages;
        this.menuItems = menuItems;
        this.loginView = loginView;
        this.portaroVersion = portaroVersion;
        this.serverUrl = serverUrl;
        this.publicContextPath = publicContextPath;
        this.securityManager = securityManager;
        this.alerts = alerts;
        this.queryParams = queryParams;
        this.titlePrefix = titlePrefix;
        this.alternativePageLanguageLinks = alternativePageLanguageLinks;
        this.cameraScannerEnabled = cameraScannerEnabled;
        this.frontendLoggerLevel = new SimpleLogger("cz.kpsys.portaro.frontend");
        this.customFileTemplateRenderer = customFileTemplateRenderer;
        this.globalSearchInputEnabled = globalSearchInputEnabled;
        this.headerBackgroundColor = headerBackgroundColor;
        this.headerTextColor = headerTextColor;
        this.headerLinkColor = headerLinkColor;
        this.mainMenuBackgroundColor = mainMenuBackgroundColor;
        this.mainMenuTextColor = mainMenuTextColor;
        this.mainMenuHighlightBackgroundColor = mainMenuHighlightBackgroundColor;
        this.mainMenuHighlightTextColor = mainMenuHighlightTextColor;
        this.globalSearchButtonColor = globalSearchButtonColor;
        this.tableHeaderAccentColor = tableHeaderAccentColor;
        this.themeName = themeName;
        this.mainMenu = mainMenu;
        this.isSutorSutinLayout = isSutorSutinLayout;
        this.selectedTabHighlightColor = selectedTabHighlightColor;
        this.customDataViewPages = customDataViewPages;
        this.homeDepartmentRedirectAfterLogin = homeDepartmentRedirectAfterLogin;
    }

    public static CurrentPage byRequest(HttpServletRequest request,
                                        String serverUrl,
                                        String publicContextPath,
                                        AuthenticationHolder authenticationHolder,
                                        @NonNull CurrentAuthResponse currentAuthResponse,
                                        List<? extends LabeledIdentified<String>> enabledLanguages,
                                        List<MenuItem> menuItems,
                                        LoginView loginView,
                                        PortaroVersion portaroVersion,
                                        Locale locale,
                                        Translator<Department> translator,
                                        SecurityManager securityManager,
                                        Department currentDepartment,
                                        @NonNull List<Alert> alerts,
                                        @NullableNotBlank String titlePrefix,
                                        Boolean cameraScannerEnabled,
                                        List<ActionResponse> dialogs,
                                        StaticTemplateRenderer customFileTemplateRenderer,
                                        Boolean globalSearchInputEnabled,
                                        String headerBackgroundColor,
                                        String headerTextColor,
                                        String headerLinkColor,
                                        String mainMenuBackgroundColor,
                                        String mainMenuTextColor,
                                        @NullableNotBlank String mainMenuHighlightBackgroundColor,
                                        @NullableNotBlank String mainMenuHighlightTextColor,
                                        String globalSearchButtonColor,
                                        String tableHeaderAccentColor,
                                        String themeName,
                                        @NonNull List<MenuItemResponse> mainMenu,
                                        @NonNull Boolean isSutorSutin,
                                        @NonNull String selectedTabHighlightColor,
                                        @NonNull ErpCustomDataViewPagesHolder customDataViewPages,
                                        @NonNull Boolean homeDepartmentRedirectAfterLogin) {
        List<Link> alternativePageLanguageLinks = enabledLanguages.stream()
                .map(enabledLanguage -> {
                    String url = UrlCreator.byRequest(request, serverUrl, "")
                            .setParameter(CatalogWebConstants.LOCALE_URL_PARAMETER_NAME, enabledLanguage.getId())
                            .build();
                    return new StaticLink(url, Texts.ofNative(enabledLanguage.getId()));
                })
                .collect(Collectors.toUnmodifiableList());
        UserAuthentication currentAuth = authenticationHolder.getCurrentAuth();

        return new CurrentPage(
                locale,
                translator,
                currentAuth,
                currentAuthResponse,
                enabledLanguages,
                menuItems,
                loginView,
                portaroVersion,
                serverUrl,
                publicContextPath,
                securityManager,
                currentDepartment,
                alerts,
                UrlUtils.getQueryParamsAsMap(request),
                titlePrefix,
                alternativePageLanguageLinks,
                cameraScannerEnabled,
                dialogs,
                customFileTemplateRenderer,
                globalSearchInputEnabled,
                headerBackgroundColor,
                headerTextColor,
                headerLinkColor,
                mainMenuBackgroundColor,
                mainMenuTextColor,
                mainMenuHighlightBackgroundColor,
                mainMenuHighlightTextColor,
                globalSearchButtonColor,
                tableHeaderAccentColor,
                themeName,
                mainMenu,
                isSutorSutin,
                selectedTabHighlightColor,
                customDataViewPages,
                homeDepartmentRedirectAfterLogin
        );
    }

    public static CurrentPage fallback(PortaroVersion portaroVersion, Department currentDepartment, Translator<Department> translator, StaticTemplateRenderer customFileTemplateRenderer) {
        SecurityManager securityManager = StaticSecurityManager.allDenying();
        User activeUser = NoConcreteUser.anonymousUser();
        CurrentAuth currentAuth = CurrentAuth.createWithAbsoluteAuthenticity(activeUser);
        AuthableUserResponse activeUserResponse = new AuthableUserResponse(
                currentAuth.getActiveUser().getId(),
                currentAuth.getActiveUser().getText(),
                currentAuth.getActiveUser().isEvided(),
                new RoleNamesResolver().resolveRoles(activeUser),
                currentAuth.getActiveUser().getRecordId()
        );
        CurrentAuthResponse currentAuthResponse = new CurrentAuthResponse(currentAuth.isEvided(), activeUserResponse, currentAuth.getRole(), List.of(), List.of(), List.of(), null);
        return new CurrentPage(
                CatalogConstants.DEFAULT_LOCALE,
                translator,
                currentAuth,
                currentAuthResponse,
                List.of(),
                List.of(),
                new LoginView(
                        false,
                        false,
                        false,
                        false,
                        false,
                        List.of()
                ),
                portaroVersion,
                "",
                "",
                securityManager,
                currentDepartment,
                List.of(),
                Collections.emptyMap(),
                null,
                List.of(),
                false,
                List.of(),
                customFileTemplateRenderer,
                false,
                CoreConstants.Web.Defaults.HEADER_BACKGROUND_COLOR,
                CoreConstants.Web.Defaults.HEADER_TEXT_COLOR,
                CoreConstants.Web.Defaults.HEADER_LINK_COLOR,
                CoreConstants.Web.Defaults.MAIN_MENU_BACKGROUND_COLOR,
                CoreConstants.Web.Defaults.MAIN_MENU_TEXT_COLOR,
                null,
                null,
                CoreConstants.Web.Defaults.GLOBAL_SEARCH_BUTTON,
                CoreConstants.Web.Defaults.TABLE_HEADER_ACCENT_COLOR,
                CoreConstants.Web.Defaults.DEFAULT_THEME,
                List.of(),
                false,
                CoreConstants.Web.Defaults.SELECTED_TAB_HIGHLIGHT_COLOR,
                new ErpCustomDataViewPagesHolder(List.of()),
                false
        );
    }


    @NonNull
    public CurrentAuthResponse getCurrentAuth() {
        return currentAuthResponse;
    }

    @NonNull
    public List<? extends LabeledIdentified<String>> getAvailableLanguages() {
        List<LabeledIdentified<String>> res = new ArrayList<>(enabledLanguages);
        if (securityManager.can(SecurityActions.LOCALIZATION_EDIT, currentAuth, getCurrentDepartment())) {
            res.add(new LabeledId<>("xx", Texts.ofStaticallyLocalized("[localization mode]").addCzech("[lokalizační režim]")));
        }
        return res;
    }

    @NonNull
    public List<? extends LabeledIdentified<String>> getSwitchableLanguages() {
        return getAvailableLanguages().stream()
                .filter(lang -> !lang.equals(getCurrentLanguage()))
                .toList();
    }

    @NonNull
    public LabeledIdentified<String> getCurrentLanguage() {
        return ListUtil.getById(getAvailableLanguages(), getLocale().getLanguage());
    }

    @NonNull
    public String getContextPath() {
        return StringUtil.notNullString(publicContextPath); //fallback for when OPAC.SERVER_URL is not set, publicContextPath will be null
    }

    @NonNull
    public String renderCustomFile(String directory, String filenameWithoutExtension, String extension) {
        return customFileTemplateRenderer.render(new BasicTemplateDescriptor(directory, filenameWithoutExtension, extension), currentAuth, getCurrentDepartment(), getLocale());
    }

    public boolean isForAdministrationOnly() {
        return loginView.isForAdministrationOnly();
    }

    public String escapeHtmlTags(String input) {
        return StringUtil.escapeHtmlTags(input);
    }
}
