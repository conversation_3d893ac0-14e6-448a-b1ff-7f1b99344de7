package cz.kpsys.portaro.commons.converter;

import cz.kpsys.portaro.commons.convert.StringToEnumConverter;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

@Tag("ci")
@Tag("unit")
public class StringToEnumConverterTest {
    
    private enum TestEnum { ENUM1, LONG_ENUM }
    
    @Test
    public void shouldConvertSimpleEnumType() {
        StringToEnumConverter<TestEnum> converter = new StringToEnumConverter<>(TestEnum.values());
        TestEnum result = converter.convert("enum1");
        Assertions.assertEquals(TestEnum.ENUM1, result);
    }
    
    @Test
    public void shouldConvertCamelCasedEnumType() {
        StringToEnumConverter<TestEnum> converter = new StringToEnumConverter<>(TestEnum.values());
        TestEnum result = converter.convert("longEnum");
        Assertions.assertEquals(TestEnum.LONG_ENUM, result);
    }
    
    @Test
    public void shouldConvertCamelCasedCapitalizedEnumType() {
        StringToEnumConverter<TestEnum> converter = new StringToEnumConverter<>(TestEnum.values());
        TestEnum result = converter.convert("LongEnum");
        Assertions.assertEquals(TestEnum.LONG_ENUM, result);
    }
    
}
