package cz.kpsys.portaro.loan.availability;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanId;
import cz.kpsys.portaro.loan.LoanImpl;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordFactory;
import cz.kpsys.portaro.search.AbstractInMemorySearchLoader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.ParameterizedSearchLoaderImpl;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

class ExemplarHolderServiceTest {

    Loan lentLoan = mkLoan(1, 0);
    Loan firstInLineLoan = mkLoan(2, 1);
    <PERSON>an secondInLineLoan = mkLoan(3, 2);
    <PERSON>an reservedUnsentLoan = mkLoan(4, 51);
    <PERSON>an reservedSentLoan = mkLoan(5, 52);
    Loan orderUnsentLoan = mkLoan(6, 53);
    Loan orderSentLoan = mkLoan(7, 54);
    Loan mvsPreparedLoan = mkLoan(8, 55);

    @Test
    void testLentPriority() {
        var loader = loaderOf(List.of(mvsPreparedLoan, orderSentLoan, orderUnsentLoan, reservedSentLoan, reservedUnsentLoan, secondInLineLoan, firstInLineLoan, lentLoan));
        ExemplarHolderService service = new ExemplarHolderService(loader);
        Optional<Loan> foundLoan = service.findHoldingLoan(0 /* cokoliv */);
        assertEquals(lentLoan, foundLoan.orElseThrow());
    }

    @Test
    void testMvsPriority() {
        var loader = loaderOf(List.of(firstInLineLoan, secondInLineLoan, reservedUnsentLoan, reservedSentLoan, orderUnsentLoan, orderSentLoan, mvsPreparedLoan));
        ExemplarHolderService service = new ExemplarHolderService(loader);
        Optional<Loan> foundLoan = service.findHoldingLoan(0 /* cokoliv */);
        assertEquals(mvsPreparedLoan, foundLoan.orElseThrow());
    }

    @Test
    void testOrderSentPriority() {
        var loader = loaderOf(List.of(firstInLineLoan, secondInLineLoan, reservedUnsentLoan, reservedSentLoan, orderUnsentLoan, orderSentLoan));
        ExemplarHolderService service = new ExemplarHolderService(loader);
        Optional<Loan> foundLoan = service.findHoldingLoan(0 /* cokoliv */);
        assertEquals(orderSentLoan, foundLoan.orElseThrow());
    }

    @Test
    void testReservationSentPriority() {
        var loader = loaderOf(List.of(firstInLineLoan, secondInLineLoan, reservedUnsentLoan, reservedSentLoan, orderUnsentLoan));
        ExemplarHolderService service = new ExemplarHolderService(loader);
        Optional<Loan> foundLoan = service.findHoldingLoan(0 /* cokoliv */);
        assertEquals(reservedSentLoan, foundLoan.orElseThrow());
    }

    @Test
    void testOrderUnsentPriority() {
        var loader = loaderOf(List.of(firstInLineLoan, secondInLineLoan, reservedUnsentLoan, orderUnsentLoan));
        ExemplarHolderService service = new ExemplarHolderService(loader);
        Optional<Loan> foundLoan = service.findHoldingLoan(0 /* cokoliv */);
        assertEquals(orderUnsentLoan, foundLoan.orElseThrow());
    }

    @Test
    void testReservationUnsentPriority() {
        var loader = loaderOf(List.of(firstInLineLoan, secondInLineLoan, reservedUnsentLoan));
        ExemplarHolderService service = new ExemplarHolderService(loader);
        Optional<Loan> foundLoan = service.findHoldingLoan(0 /* cokoliv */);
        assertEquals(reservedUnsentLoan, foundLoan.orElseThrow());
    }

    @Test
    void testQueuePriority() {
        var loader = loaderOf(List.of(firstInLineLoan, secondInLineLoan));
        ExemplarHolderService service = new ExemplarHolderService(loader);
        Optional<Loan> foundLoan = service.findHoldingLoan(0 /* cokoliv */);
        assertEquals(firstInLineLoan, foundLoan.orElseThrow());
    }

    @Test
    void testQueuePriorityReversed() {
        var loader = loaderOf(List.of(secondInLineLoan, firstInLineLoan));
        ExemplarHolderService service = new ExemplarHolderService(loader);
        Optional<Loan> foundLoan = service.findHoldingLoan(0 /* cokoliv */);
        assertEquals(firstInLineLoan, foundLoan.orElseThrow());
    }

    private ParameterizedSearchLoader<MapBackedParams, Loan> loaderOf(List<Loan> list) {
        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                new AbstractInMemorySearchLoader<>(() -> list) {}
        );
    }

    private Loan mkLoan(int loanRealizationId, int queueNumber) {
        return new LoanImpl(
                LoanId.ofActive(loanRealizationId),
                null,
                mkRecord("b51522bb-37a6-42b0-8b6c-2464e2747f71"),
                Department.testingRoot(),
                null,
                null,
                queueNumber,
                null,
                null,
                null,
                null,
                null,
                Instant.now(),
                Instant.now(),
                null,
                List.of()
        );
    }

    private Record mkRecord(String uuid) {
        return RecordFactory.testingMonography(UUID.fromString(uuid), 5555);
    }

}