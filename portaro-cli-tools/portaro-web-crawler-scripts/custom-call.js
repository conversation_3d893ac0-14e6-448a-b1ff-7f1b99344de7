import {createConfiguredDatabaseRowsFetcher} from './fetcher/configured-rows-fetcher.js';
import {Fetcher} from './fetcher/fetcher.js';
import {PortaroApiFetcher} from './fetcher/portaro-api-fetcher.js';


const SEATABLE_PORTARO_URL_COLUMN = 'portaro_url';
const SEATABLE_SERIAL_CODE_COLUMN = 'serial_code';
const SEATABLE_PORTARO_OS_TYPE_COLUMN = 'portaro_os_type';
const VSB_SERIAL_CODE = '100007000053';
const SUTORTEST_SERIAL_CODE = '100007001019';
const OS_TYPE_WINDOWS = 'windows';
const OS_TYPE_LINUX = 'linux';

function all(row) {
    return true;
}

function notVsb(row) {
    return !serial(VSB_SERIAL_CODE)(row);
}

function serial(serial) {
    return (row) => {
        return row[SEATABLE_SERIAL_CODE_COLUMN] === serial;
    };
}

function linux(row) {
    return row[SEATABLE_PORTARO_OS_TYPE_COLUMN] === OS_TYPE_LINUX;
}

function windows(row) {
    return row[SEATABLE_PORTARO_OS_TYPE_COLUMN] === OS_TYPE_WINDOWS;
}

async function call(rowFilter, input) {
    try {
        const fetcher = createConfiguredDatabaseRowsFetcher();
        const portaroUrls = (await fetcher.getRowsWithValidPortaroUrls())
            .filter(rowFilter)
            .map(row => row[SEATABLE_PORTARO_URL_COLUMN]);

        const PORTARO_USERNAME = 'su';
        const PORTARO_PASSWORD = 'REPLACE WITH SU PASSWORD';

        async function doWithUrl(portaroUrl) {
            const portaroApiFetcher = new PortaroApiFetcher(new Fetcher(portaroUrl, () => `Basic ${btoa(`${PORTARO_USERNAME}:${PORTARO_PASSWORD}`)}`));
            try {
                await input.func(portaroApiFetcher, portaroUrl);
            } catch (error) {
                //console.log(`Problem while performing remote SQL at ${portaroUrl}: ${error.text || JSON.stringify(error)}`, error);
                //console.log(`Problem while performing remote SQL at ${portaroUrl}: ${error.text}`);
            }
        }

        console.log("======================================================");
        console.log(`==== STARTING WITH ${portaroUrls.length} items ====`);
        console.log("======================================================");

        const resultPromises = portaroUrls.map(portaro_url => doWithUrl(portaro_url));

        Promise.all(resultPromises)
            .then(() => {
                console.log("==================================================");
                console.log(`==== FINISHED ${portaroUrls.length} items ====`);
                console.log("==================================================");
            });

    } catch (error) {
        console.log(`Exception while performing get-custom-sql-query: ${error.text || JSON.stringify(error)}`, error);
    }
}

const sqlSelectExcludingZero = (sql) => sqlSelectExcluding([0], sql);

const sqlSelectExcludingNA = (sql) => sqlSelectExcluding(['n/a'], sql);

const sqlSelectExcluding = (valuesToExcludeFromLog, sql) => ({
    func: async (portaroApiFetcher, portaroUrl) => {
        const result = await portaroApiFetcher.getSqlQueryResult(sql);
        if (Array.isArray(result)) {
            if (result.length !== 0 && !valuesToExcludeFromLog.includes(result[0].val) && !valuesToExcludeFromLog.includes(result[0].VAL)) {
                console.log(`${portaroUrl} --> ${JSON.stringify(result[0])}`);
            }
        } else {
            console.log(`${portaroUrl} --> ${JSON.stringify(result)}`);
        }
    }
});

const getProperty = (propertyName) => ({
    func: async (portaroApiFetcher, portaroUrl) => {
        const result = await portaroApiFetcher.getProperty(propertyName);
        console.log(`${portaroUrl} --> ${JSON.stringify(result)}`);
    }
});

const noResultCall = (func) => ({
    func: async (portaroApiFetcher, portaroUrl) => {
        await func(portaroApiFetcher);
        console.log(`${portaroUrl} --> OK`);
    }
});

const http404WantedCall = (func) => ({
    func: async (portaroApiFetcher, portaroUrl) => {
        try {
            await func(portaroApiFetcher);
        } catch (e) {
            if (e.type === 'fetchError' && e.httpStatus === 404) {
                console.log(`${portaroUrl}`);
            }
        }
    }
});



const sqlUpdate = (sql) => noResultCall((portaroApiFetcher) => portaroApiFetcher.getSqlQueryResult(sql));

const getMissingProperty = (propertyName) => http404WantedCall((portaroApiFetcher) => portaroApiFetcher.getProperty(propertyName));

const selectV23ValFromDbStruct = (expectedVersion) => sqlSelectExcluding([expectedVersion], `select v2_3 val from db_struct`);

const selectCustomIniValue = (iniKeySection, iniKeyName, valueInfix = null) => {
    let suffix = valueInfix ? ` and hodnota like '%${valueInfix}%'` : '';
    return sqlSelectExcludingNA(`select coalesce(hodnota, 'n/a') val from ini_file where fk_sekce = '${iniKeySection}' and fk_klic = '${iniKeyName}'${suffix}`);
};

const selectCustomIniParagraphTemplateValueContaining = (containingValue) => sqlSelectExcludingZero(`select count(*) as val from ini_file where hodnota like '%${containingValue}%'`);

const insertCustomIniValueBySql = () => sqlUpdate(`insert into ini_file (fk_sekce, fk_klic, hodnota, fk_pujc, poznamka) values ('VYPUC', 'PRIMAREZERVACEPOZADAVKU', 'ANO', 0, 'temp-lkdfjgldf')`);

const iniTriggers = ['TRG_SEC_INI_FILE_BIUD0', 'TRG_INI_FILE_AI0_LOG', 'TRG_INI_FILE_AD0_LOG'];
const disableIniTrigger = () => sqlUpdate(`alter trigger TRG_INI_FILE_AD0_LOG inactive`);

const insertIniCustomValueByApi = noResultCall((portaroApiFetcher) => portaroApiFetcher.insertIni('BAKALARI', 'PREFIX', 'test', 'temp-lkdfjgldf'));

const deleteIniCustomValueByApi = noResultCall((portaroApiFetcher) => portaroApiFetcher.deleteIni('BAKALARI', 'PREFIX'));

const selectDependencyCount = () => sqlSelectExcludingZero(`select count(*) as val from rdb$dependencies where rdb$field_name='ID_CTEN' and RDB$DEPENDED_ON_NAME='CTENARI'`);

const selectColumnsCount = (tableName, columnName) => sqlSelectExcludingZero(`select count(*) as val from rdb$relation_fields where rdb$relation_name='${tableName.toUpperCase()}' and rdb$field_name='${columnName.toUpperCase()}'`);

const selectCustomVtlTemplateFilesCount = (infix) => sqlSelectExcludingZero(`select count(*) as val, list(fk_fulltext_skupiny) dirs, list(filename) filenames from fulltext_soubory where filename like '%.vtl' and text like '%${infix}%'`);

const selectCustomFilesCount = (...infixes) => {
    const infixesCondition = infixes.map(infix => `and original like '%${infix}%'`).join(' ');
    return sqlSelectExcludingZero(`select count(*) as val, list(fk_fulltext_skupiny) dirs, list(filename) filenames
                                   from fulltext_soubory
                                   where (filename like '%.vtl' or filename like '%.html' or filename like '%.css') ${infixesCondition}`);
};

const restart = noResultCall((portaroApiFetcher) => portaroApiFetcher.restart());

const update = noResultCall((portaroApiFetcher) => portaroApiFetcher.update());

call(row => all(row), selectCustomIniValue('gui.components', 'subSearchLinks', '&kind=document'));
