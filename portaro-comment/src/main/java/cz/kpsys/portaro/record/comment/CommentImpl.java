package cz.kpsys.portaro.record.comment;

import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class CommentImpl extends BasicIdentified<String> implements Comment {

    @NonNull UUID recordId;
    @Nullable BasicUser creator;
    @NonNull Instant date;
    @NonNull String content;
    @Nullable @NonFinal Instant deleteDate;

    public CommentImpl(String id, @NonNull UUID recordId, @Nullable BasicUser creator, @NonNull Instant date, @NonNull String content, @Nullable Instant deleteDate) {
        super(id);
        this.recordId = recordId;
        this.creator = creator;
        this.date = date;
        this.content = content;
        this.deleteDate = deleteDate;
    }

    public static CommentImpl createNew(UUID recordId, @Nullable BasicUser creator, String text) {
        return new CommentImpl(UuidGenerator.forIdentifierWithoutDashes(), recordId, creator, Instant.now(), text, null);
    }

    @Override
    public Optional<BasicUser> getCreator() {
        return Optional.ofNullable(creator);
    }

    @Override
    public Optional<Instant> getDeleteDate() {
        return Optional.ofNullable(deleteDate);
    }

    @Override
    public void setDeleted(Instant when) {
        this.deleteDate = when;
    }
}
