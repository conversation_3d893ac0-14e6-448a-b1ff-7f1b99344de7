package cz.kpsys.portaro.record.comment;

import cz.kpsys.portaro.commons.object.Identified;
import org.springframework.core.convert.converter.Converter;

public class CommentToEntityConverter implements Converter<Comment, CommentEntity> {

    @Override
    public CommentEntity convert(Comment comment) {
        return new CommentEntity(
                comment.getId(),
                comment.getRecordId(),
                comment.getCreator().map(Identified::getId).orElse(null),
                comment.getDate(),
                comment.getContent(), comment.getDeleteDate().orElse(null)
        );
    }
}
