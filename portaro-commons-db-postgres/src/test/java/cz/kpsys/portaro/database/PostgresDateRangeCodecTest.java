package cz.kpsys.portaro.database;

import cz.kpsys.portaro.commons.date.DateRange;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.postgresql.util.PGobject;

import java.sql.ResultSet;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@Tag("ci")
@Tag("unit")
public class PostgresDateRangeCodecTest {

    private final PostgresDateRangeCodec codec = PostgresDateRangeCodec.of();

    private static PGobject pg(String type, String value) throws Exception {
        PGobject o = new PGobject();
        o.setType(type);
        o.setValue(value);
        return o;
    }

    private static ResultSet rsWithObject(String column, Object value) throws Exception {
        ResultSet rs = Mockito.mock(ResultSet.class);
        when(rs.getObject(column)).thenReturn(value);
        return rs;
    }

    @Test
    void read_empty_returnsEmptyRecord() throws Exception {
        ResultSet rs = rsWithObject("validity", pg("daterange", "empty"));

        DateRange r = codec.read(rs, "validity");

        assertNotNull(r);
        assertTrue(r.empty());
        assertNull(r.lower());
        assertNull(r.upper());
        assertFalse(r.lowerInclusive());
        assertFalse(r.upperInclusive());
    }

    @Test
    void read_closedOpen_normalDates() throws Exception {
        // [2025-01-01,2025-02-01)
        ResultSet rs = rsWithObject("validity", pg("daterange", "[2025-01-01,2025-02-01)"));

        DateRange r = codec.read(rs, "validity");

        assertNotNull(r);
        assertFalse(r.empty());
        assertEquals(LocalDate.of(2025, 1, 1), r.lower());
        assertEquals(LocalDate.of(2025, 2, 1), r.upper());
        assertTrue(r.lowerInclusive());
        assertFalse(r.upperInclusive());
    }

    @Test
    void read_unboundedHandlesInfinity() throws Exception {
        // (-infinity,2025-01-01]
        ResultSet rs = rsWithObject("validity", pg("daterange", "[-infinity,2025-01-01]"));

        DateRange r = codec.read(rs, "validity");

        assertNotNull(r);
        assertFalse(r.empty());
        assertNull(r.lower()); // -infinity -> null
        assertEquals(LocalDate.of(2025, 1, 1), r.upper());
        assertFalse(r.lowerInclusive());
        assertTrue(r.upperInclusive());
    }

    @Test
    void write_roundTrip_closedOpen() throws Exception {
        DateRange src = DateRange.of(
                LocalDate.of(2025, 5, 10),
                LocalDate.of(2025, 6, 01),
                true,   // [
                false   // )
        );

        PGobject pg = codec.toDatabaseObject(src);
        assertEquals("daterange", pg.getType());
        assertEquals("[2025-05-10,2025-06-01)", pg.getValue());

        // simulate reading back
        ResultSet rs = rsWithObject("validity", pg);
        DateRange back = codec.read(rs, "validity");

        assertEquals(src.lower(), back.lower());
        assertEquals(src.upper(), back.upper());
        assertEquals(src.lowerInclusive(), back.lowerInclusive());
        assertEquals(src.upperInclusive(), back.upperInclusive());
        assertEquals(src.empty(), back.empty());
    }

    @Test
    void write_roundTrip_unbounded_with_lower_infinity_inclusive_should_canonize_to_open_lower_infinity() throws Exception {
        DateRange src = DateRange.of(null, LocalDate.of(2030, 12, 31), true, true);

        PGobject pg = codec.toDatabaseObject(src);
        assertEquals("(-infinity,2030-12-31]", pg.getValue());

        ResultSet rs = rsWithObject("validity", pg);
        DateRange back = codec.read(rs, "validity");

        assertNull(back.lower());
        assertEquals(LocalDate.of(2030, 12, 31), back.upper());
        assertFalse(back.lowerInclusive());
        assertTrue(back.upperInclusive());
        assertFalse(back.empty());
    }

    @Test
    void write_emptyProducesEmptyLiteral() throws Exception {
        DateRange empty = DateRange.ofEmpty();

        PGobject pg = codec.toDatabaseObject(empty);
        assertEquals("empty", pg.getValue());

        ResultSet rs = rsWithObject("validity", pg);
        DateRange back = codec.read(rs, "validity");

        assertTrue(back.empty());
        assertNull(back.lower());
        assertNull(back.upper());
    }
}