dependencies {
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-commons"))

    api("org.apache.pdfbox:pdfbox:3.+")
    implementation("org.slf4j:slf4j-api:+")
    implementation("org.springframework:spring-core:6.+")
    implementation("com.itextpdf:itext-core:9.+")
}
