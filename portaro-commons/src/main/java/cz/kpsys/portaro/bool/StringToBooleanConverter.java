package cz.kpsys.portaro.bool;

import cz.kpsys.portaro.commons.convert.EmptySourceForConversionException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.io.Serializable;
import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class StringToBooleanConverter implements Converter<String, Boolean>, Serializable {

    private static final Set<String> TRUE_VALUES = Set.of("ANO", "TRUE", "ON", "YES", "1");

    boolean throwWhenNullSource;
    boolean returnNullWhenNullSource;

    public static StringToBooleanConverter notAllowingNullSource() {
        return new StringToBooleanConverter(true, false);
    }

    public static StringToBooleanConverter returningNullWhenNullSource() {
        return new StringToBooleanConverter(false, true);
    }

    @Override
    public Boolean convert(String source) {
        if (source == null) {
            if (throwWhenNullSource) {
                throw new EmptySourceForConversionException(getClass());
            }
            if (returnNullWhenNullSource) {
                return null;
            }
            return false;
        }
        source = source.toUpperCase();

        return TRUE_VALUES.stream().anyMatch(source::equals);
    }

    @Override
    public String toString() {
        return "String to Boolean converter";
    }
    
}
