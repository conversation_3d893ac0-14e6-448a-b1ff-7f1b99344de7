package cz.kpsys.portaro.commons.async;

import cz.kpsys.portaro.commons.object.repo.ByIdOptLoadableRepository;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AsyncProcessesManager<ID> {
    @NonNull ByIdOptLoadableRepository<AsyncProcessStatus<ID>, ID> asyncProcessStatusRepository;

    public boolean isTaskRunning(ID id) {
        return asyncProcessStatusRepository.findById(id)
                .map(AsyncProcessStatus::asyncTaskFuture)
                .map(future -> !future.isDone())
                .orElse(false);
    }

    public AsyncProcessStatus<ID> getTaskStatus(ID id) {
        return asyncProcessStatusRepository.findById(id).orElse(null);
    }

    public void cancelRunningTask(ID id) {
        var future = asyncProcessStatusRepository.getById(id).asyncTaskFuture();
        Assert.state(Objects.nonNull(future), "Can not cancel, async process must be running");
        future.cancel(true); // true to force interrupt running thread
    }

    public void deleteTaskIfPresent(ID id) {
        asyncProcessStatusRepository.findById(id).ifPresent(asyncProcessStatusRepository::delete);
    }

}
