package cz.kpsys.portaro.commons.cache;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.util.*;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CacheService {
    
    @NonNull Map<String, List<CacheCleaner>> cacheCleanersMap = new HashMap<>();
    @NonNull CacheManager cacheManager;
    
    
    public void invalidateAllCaches() {
        streamAllCacheCleaners().forEach(this::clean);
    }

    private Stream<CacheCleaner> streamAllCacheCleaners() {
        return cacheCleanersMap.values().stream()
                .flatMap(Collection::stream);
    }
    
    public void deleteCache(@NonNull String cacheName) {
        List<CacheCleaner> cacheCleaners = cacheCleanersMap.getOrDefault(cacheName, List.of());
        log.info("Cleaning {} cache", cacheName);
        if (cacheCleaners.isEmpty()) {
            log.info("There is not any {} cache", cacheName);
        }
        cacheCleaners.forEach(this::clean);
    }

    private void clean(CacheCleaner cacheCleaner) {
        log.info("Cleaning {} cache", cacheCleaner);
        cacheCleaner.clearCache();
    }

    public void registerCleaner(@NonNull String cacheName, @NonNull CacheCleaner bean) {
        cacheCleanersMap.computeIfAbsent(cacheName, k -> new ArrayList<>()).add(bean);
    }

    public void registerSpringCacheCleaner(@NonNull String cacheName, @NonNull String springCacheName) {
        registerCleaner(cacheName, new SpringCacheCleaner(springCacheName));
    }

    public void registerSpringCacheCleaner(@NonNull String springCacheName) {
        registerSpringCacheCleaner(Object.class.getSimpleName(), springCacheName);
    }
    
    public void registerCleaner(@NonNull CacheCleaner bean) {
        registerCleaner(Object.class.getSimpleName(), bean);
    }

    public CacheCleaner createCleanerFor(String cacheName) {
        return new CacheServiceCacheDeletable(cacheName);
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private class SpringCacheCleaner implements CacheCleaner {

        @NonNull String springCacheName;

        @Override
        public void clearCache() {
            Cache cache = Objects.requireNonNull(cacheManager.getCache(springCacheName), () -> "Spring cache with name '%s' does not exist".formatted(springCacheName));
            cache.clear();
        }

        @Override
        public String toString() {
            return "SpringCacheCleaner of '%s'".formatted(springCacheName);
        }
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private class CacheServiceCacheDeletable implements CacheCleaner {

        @NonNull String cacheName;

        @Override
        public void clearCache() {
            deleteCache(cacheName);
        }

    }

}
