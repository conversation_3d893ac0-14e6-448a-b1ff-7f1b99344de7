package cz.kpsys.portaro.commons.contextual;

import cz.kpsys.portaro.commons.object.MissingValueException;
import lombok.NonNull;

import java.util.Objects;
import java.util.function.Function;

@FunctionalInterface
public interface ContextualBiFunction<IN1, IN2, CTX, OUT> {

    OUT getOn(IN1 input1, IN2 input2, CTX ctx);

    default ContextualBiFunction<IN1, IN2, CTX, @NonNull OUT> throwingWhenNull() {
        return throwingWhenNull(ctx -> new MissingValueException("Value by context %s is null".formatted(ctx)));
    }

    default ContextualBiFunction<IN1, IN2, CTX, @NonNull OUT> throwingWhenNull(ContextualProvider<CTX, ? extends RuntimeException> exceptionProvider) {
        return (input1, input2, ctx) -> {
            OUT value = getOn(input1, input2, ctx);
            if (Objects.isNull(value)) {
                throw exceptionProvider.getOn(ctx);
            }
            return value;
        };
    }

    default <NEXT> ContextualBiFunction<IN1, IN2, CTX, NEXT> andThen(@NonNull Function<@NonNull ? super OUT, ? extends NEXT> after) {
        return (input1, input2, ctx) -> {
            @NonNull OUT intermediate = throwingWhenNull().getOn(input1, input2, ctx);
            return after.apply(intermediate);
        };
    }

}
