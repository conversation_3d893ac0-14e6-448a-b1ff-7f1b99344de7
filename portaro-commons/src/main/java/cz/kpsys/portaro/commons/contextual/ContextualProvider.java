package cz.kpsys.portaro.commons.contextual;

import cz.kpsys.portaro.commons.licence.FeatureNotEnabledException;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.MissingValueException;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import jakarta.annotation.Nullable;
import lombok.NonNull;

import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

@FunctionalInterface
public interface ContextualProvider<CTX, V> {

    V getOn(CTX ctx) throws ItemNotFoundException;

    default ContextualProvider<CTX, @NonNull Optional<V>> optionally() {
        return ctx -> Optional.ofNullable(getOn(ctx));
    }

    default ContextualProvider<CTX, @NonNull V> throwingWhenNull() {
        return throwingWhenNull(ctx -> new MissingValueException("Value on context %s is null".formatted(ctx)));
    }

    default ContextualProvider<CTX, @NonNull V> throwingWhenNull(ContextualProvider<CTX, ? extends RuntimeException> exceptionProvider) {
        return ValidatingContextualProvider.notNull(this, exceptionProvider);
    }

    default ContextualProvider<CTX, @NonNull V> invalidWhen(Predicate<@NonNull V> isInvalidPredicate) {
        return ValidatingContextualProvider.invalidWhen(this, isInvalidPredicate,
                (value, ctx) -> new IllegalStateException("Invalid value %s (on context %s)".formatted(value, ctx)));
    }

    default Consumer<CTX> toEnabledAsserter(Predicate<@NonNull V> isEnabledPredicate, @NonNull String featureName, @Nullable Text text) {
        return ValidatingContextualProvider.validWhen(this, isEnabledPredicate, (value, ctx) ->
                new FeatureNotEnabledException(
                        featureName,
                        "Feature %s is not enabled (value %s on context %s)".formatted(featureName, value, ctx),
                        text
                )
        )::getOn;
    }

    default Predicate<CTX> valueNotNull() {
        return ContextualProviderToPredicate.valueNotNull(this);
    }

    default <NEXT> ContextualProvider<CTX, NEXT> andThenContextual(@NonNull BiFunction<@NonNull CTX, @NonNull V, ? extends NEXT> after) {
        return PostConvertingContextualProvider.throwingOnNull(this, after);
    }

    default <NEXT> ContextualProvider<CTX, NEXT> andThen(@NonNull Function<@NonNull V, NEXT> after) {
        return PostConvertingContextualProvider.throwingOnNull(this, after);
    }

    default <NEXT> ContextualProvider<CTX, NEXT> andThenFastReturningNull(@NonNull Function<@NonNull V, ? extends NEXT> after) {
        return PostConvertingContextualProvider.fastReturningNull(this, after);
    }

    default ContextualProvider<CTX, V> fallbacked(@NonNull Provider<@NonNull V> fallback) {
        return FallbackedContextualProvider.of(this, fallback);
    }

    default ContextualProvider<CTX, V> fallbacked(@NonNull ContextualProvider<CTX, @NonNull V> fallback) {
        return FallbackedContextualProvider.of(this, fallback);
    }

    default CachedContextualProvider<CTX, V> cached() {
        return new CachedContextualProvider<>(this);
    }

}
