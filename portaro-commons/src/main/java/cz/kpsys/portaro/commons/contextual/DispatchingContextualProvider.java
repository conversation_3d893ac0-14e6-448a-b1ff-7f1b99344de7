package cz.kpsys.portaro.commons.contextual;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DispatchingContextualProvider<CTX, V> implements ContextualProvider<@NonNull CTX, @NonNull V> {

    @NonNull List<EnablableContextualProvider<CTX, V>> providers = new ArrayList<>();
    @NonNull Class<V> desiredType;
    
    public DispatchingContextualProvider<CTX, V> add(@NonNull ContextualProvider<@NonNull CTX, @NonNull Boolean> enabledProvider,
                                                     @NonNull ContextualProvider<@NonNull CTX, @NonNull V> valueProvider) {
        this.providers.add(new EnablableContextualProvider<>(enabledProvider, valueProvider));
        return this;
    }

    @NonNull
    @Override
    public V getOn(@NonNull CTX ctx) throws ItemNotFoundException {
        for (EnablableContextualProvider<CTX, V> provider : providers) {
            if (provider.isEnabledOn(ctx)) {
                return provider.getValueOn(ctx);
            }
        }
        throw new ItemNotFoundException(desiredType.getSimpleName(), ctx, "No enabled %s provider on context %s".formatted(desiredType.getSimpleName(), ctx), Texts.ofNative("No enabled %s provider".formatted(desiredType.getSimpleName())));
    }

    public record EnablableContextualProvider<CTX, V>(
            @NonNull ContextualProvider<@NonNull CTX, @NonNull Boolean> enabledProvider,
            @NonNull ContextualProvider<@NonNull CTX, @NonNull V> valueProvider
    ) {

        boolean isEnabledOn(CTX ctx) {
            return enabledProvider.getOn(ctx);
        }

        V getValueOn(CTX ctx) {
            return valueProvider.getOn(ctx);
        }

    }
}