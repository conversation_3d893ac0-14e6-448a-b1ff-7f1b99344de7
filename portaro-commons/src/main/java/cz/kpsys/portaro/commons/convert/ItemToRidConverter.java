package cz.kpsys.portaro.commons.convert;

import cz.kpsys.portaro.commons.object.RIdentified;
import org.springframework.core.convert.converter.Converter;

/**
 *
 * <AUTHOR>
 * @param <E>
 * @param <ID>
 */
public class ItemToRidConverter<E extends RIdentified<ID>, ID> implements Converter<E, ID> {

    private boolean nullConvertToDefaultValue = false;
    private ID defaultValue = null;

    public ItemToRidConverter() {
    }

    public ItemToRidConverter<E, ID> nullConvertTo(ID defaultValue) {
        this.nullConvertToDefaultValue = true;
        this.defaultValue = defaultValue;
        return this;
    }

    @Override
    public ID convert(E source) {
        if (source == null) {
            if (nullConvertToDefaultValue) {
                return defaultValue;
            }
            throw new ConversionException(String.format("Cannot convert null value (in converter %s)", getClass().getSimpleName()));
        }
        return source.getRid();
    }
    
}
