package cz.kpsys.portaro.commons.convert;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ListToModifiedListExtendingConverter<S, T> implements Converter<List<S>, List<? extends T>> {

    @NonNull Converter<S, T> singleItemConverter;
    @NonFinal boolean allowNullTargetValues = false;
    @NonFinal boolean allowNullSourceValues = false;
    @NonFinal boolean preserveTargetNullValues = false;

    public ListToModifiedListExtendingConverter<S, T> allowNullSourceValues() {
        return this.allowNullSourceValues(true);
    }

    public ListToModifiedListExtendingConverter<S, T> allowNullSourceValues(boolean allowNullSourceValues) {
        this.allowNullSourceValues = allowNullSourceValues;
        return this;
    }

    public ListToModifiedListExtendingConverter<S, T> allowNullTargetValues() {
        return this.allowNullTargetValues(true);
    }

    public ListToModifiedListExtendingConverter<S, T> allowNullTargetValues(boolean allowNullTargetValues) {
        this.allowNullTargetValues = allowNullTargetValues;
        return this;
    }

    public ListToModifiedListExtendingConverter<S, T> preserveNullValues(boolean preserveNullValues) {
        this.preserveTargetNullValues = preserveNullValues;
        return this;
    }

    @Override
    public List<? extends T> convert(@NonNull List<S> source) {
        return ListUtil.convert(source, singleItemConverter, allowNullSourceValues, preserveTargetNullValues, allowNullTargetValues);
    }

}
