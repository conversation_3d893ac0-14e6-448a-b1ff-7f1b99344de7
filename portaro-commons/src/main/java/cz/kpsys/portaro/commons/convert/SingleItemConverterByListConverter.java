package cz.kpsys.portaro.commons.convert;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SingleItemConverterByListConverter<S, T> implements Converter<S, T> {

    @NonNull Converter<List<? extends S>, List<T>> listConverter;

    @Override
    public T convert(@NonNull S source) {
        return ListUtil.convertSingle(source, listConverter);
    }
}
