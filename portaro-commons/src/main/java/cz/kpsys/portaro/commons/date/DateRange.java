package cz.kpsys.portaro.commons.date;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;

public record DateRange(

        /// mů<PERSON>e být null = -infinity
        @Nullable
        LocalDate lower,

        /// může být null = +infinity
        @Nullable
        LocalDate upper,

        boolean lowerInclusive,
        boolean upperInclusive,
        boolean empty
) {

    public static DateRange of(LocalDate lo, LocalDate hi, boolean lowerInclusive, boolean upperInclusive) {
        if (lo == null && lowerInclusive) {
            lowerInclusive = false;
        }
        if (hi == null && upperInclusive) {
            upperInclusive = false;
        }
        return new DateRange(lo, hi, lowerInclusive, upperInclusive, false);
    }

    public static DateRange ofInclusive(LocalDate lo, LocalDate hi) {
        return of(lo, hi, true, true);
    }

    public static DateRange ofExclusive(LocalDate lo, LocalDate hi) {
        return of(lo, hi, false, false);
    }

    public static DateRange ofLeftExclusive(LocalDate lo, LocalDate hi) {
        return of(lo, hi, false, true);
    }

    public static DateRange ofRightExclusive(LocalDate lo, LocalDate hi) {
        return of(lo, hi, true, false);
    }

    public static DateRange ofEmpty() {
        return new DateRange(null, null, false, false, true);
    }

    public boolean isLeftInfinite() {
        return lower == null;
    }

    public boolean isRightInfinite() {
        return upper == null;
    }

    @Override
    public String toString() {
        if (empty) {
            return "empty";
        }
        return (lowerInclusive ? "[" : "(") +
               ObjectUtil.firstNotNull(lower, "-inf") +
               ", " +
               ObjectUtil.firstNotNull(upper, "inf") +
               (upperInclusive ? "]" : ")");
    }
}
