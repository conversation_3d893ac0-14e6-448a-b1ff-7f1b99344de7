package cz.kpsys.portaro.commons.date;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;

public interface DatetimeRange {

    UnboundedDatetimeRange ALL = new UnboundedDatetimeRange(null, null);
    EmptyDatetimeRange EMPTY = new EmptyDatetimeRange();

    @JsonCreator
    static DatetimeRange of(@JsonProperty("fromDate") Instant inclusiveFromDate, @JsonProperty("toDate") Instant exclusiveToDate) {
        if (inclusiveFromDate == null && exclusiveToDate == null) {
            return ofAll();
        }
        if (inclusiveFromDate == null || exclusiveToDate == null) {
            return new UnboundedDatetimeRange(inclusiveFromDate, exclusiveToDate);
        }
        if (!inclusiveFromDate.isBefore(exclusiveToDate)) {
            return ofEmpty();
        }
        return new NotEmptyBoundedDatetimeRange(inclusiveFromDate, exclusiveToDate);
    }

    static DatetimeRange of(@NonNull LocalDateTime inclusiveFromDate, @NonNull LocalDateTime exclusiveToDate, @NonNull ZoneId zoneId) {
        return of(inclusiveFromDate.atZone(zoneId).toInstant(), exclusiveToDate.atZone(zoneId).toInstant());
    }

    static DatetimeRange of(@NonNull ZonedDateTime inclusiveFromDate, @NonNull ZonedDateTime exclusiveToDate) {
        return of(inclusiveFromDate.toInstant(), exclusiveToDate.toInstant());
    }

    static UnboundedDatetimeRange ofAll() {
        return DatetimeRange.ALL;
    }

    static EmptyDatetimeRange ofEmpty() {
        return EmptyDatetimeRange.EMPTY;
    }

    static NotEmptyBoundedDatetimeRange ofDay(@NonNull LocalDate date, @NonNull ZoneId timeZoneId) {
        return ofStartOfDays(date, date.plusDays(1), timeZoneId).toNotEmptyBounded();
    }

    static BoundedDatetimeRange ofStartOfDays(LocalDate inclusiveFromDate, LocalDate exclusiveToDate, ZoneId zoneId) {
        if (inclusiveFromDate.isEqual(exclusiveToDate)) {
            return ofEmpty();
        }
        Instant inclusiveFromDateAtStartOfDay = inclusiveFromDate.atStartOfDay(zoneId).toInstant();
        Instant exclusiveToDateAtStartOfDay = exclusiveToDate.atStartOfDay(zoneId).toInstant();
        return new NotEmptyBoundedDatetimeRange(inclusiveFromDateAtStartOfDay, exclusiveToDateAtStartOfDay);
    }

    static BoundedDatetimeRange ofDaysInclusive(LocalDate inclusiveFromDate, LocalDate inclusiveToDate, ZoneId zoneId) {
        return ofStartOfDays(inclusiveFromDate, inclusiveToDate.plusDays(1), zoneId);
    }

    static NotEmptyBoundedDatetimeRange ofMonth(YearMonth yearMonth, ZoneId timeZone) {
        ZonedDateTime zonedFromDate = yearMonth.atDay(1).atStartOfDay(timeZone);
        ZonedDateTime zonedToDate = zonedFromDate.plusMonths(1);
        return of(zonedFromDate, zonedToDate).toNotEmptyBounded();
    }

    static NotEmptyBoundedDatetimeRange ofMonth(Instant fromDate, boolean truncateDate, ZoneId timeZone) {
        ZonedDateTime zonedFromDate = fromDate.atZone(timeZone);
        if (truncateDate) {
            zonedFromDate = zonedFromDate.with(TemporalAdjusters.firstDayOfMonth()).truncatedTo(ChronoUnit.DAYS);
        }
        ZonedDateTime zonedToDate = zonedFromDate.plusMonths(1);
        return of(zonedFromDate, zonedToDate).toNotEmptyBounded();
    }

    static NotEmptyBoundedDatetimeRange ofYear(Instant fromDate, boolean truncateDate, ZoneId timeZone) {
        ZonedDateTime zonedFromDate = fromDate.atZone(timeZone);
        if (truncateDate) {
            zonedFromDate = zonedFromDate.with(TemporalAdjusters.firstDayOfYear()).truncatedTo(ChronoUnit.DAYS);
        }
        ZonedDateTime zonedToDate = zonedFromDate.plusYears(1);
        return of(zonedFromDate, zonedToDate).toNotEmptyBounded();
    }

    @JsonProperty("fromDate")
    @Nullable
    Instant inclusiveFromDate(); // null = -infinity

    @JsonProperty("toDate")
    @Nullable
    Instant exclusiveToDate(); // null = infinity

    @JsonIgnore
    boolean empty();

    default BoundedDatetimeRange toBounded() {
        if (this instanceof BoundedDatetimeRange boundedThis) {
            return boundedThis;
        }
        if (empty()) {
            return DatetimeRange.ofEmpty();
        }
        return new NotEmptyBoundedDatetimeRange(
                Objects.requireNonNull(inclusiveFromDate(), "Cannot get bounded inclusive from date, because is not bounded"),
                Objects.requireNonNull(exclusiveToDate(), "Cannot get bounded exclusive to date, because is not bounded")
        );
    }

    default NotEmptyBoundedDatetimeRange toNotEmptyBounded() {
        return toBounded().toNotEmpty();
    }

    default boolean touches(DatetimeRange other) {
        if (empty() || other.empty()) {
            return false;
        }
        return (inclusiveFromDate() == null || other.exclusiveToDate() == null || inclusiveFromDate().isBefore(other.exclusiveToDate())) &&
               (exclusiveToDate() == null || other.inclusiveFromDate() == null || exclusiveToDate().isAfter(other.inclusiveFromDate()));
    }

}
