package cz.kpsys.portaro.commons.date;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import static cz.kpsys.portaro.commons.date.PostgresRangeFormatConstants.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DatetimeRangeToNoTimezonedStringConverter implements Converter<@NonNull DatetimeRange, @NonNull String> {

    @NonNull Provider<ZoneId> timeZoneProvider;
    @NonNull DateTimeFormatter instantFormatter;

    public static DatetimeRangeToNoTimezonedStringConverter ofInternalFormat(@NonNull Provider<ZoneId> timeZoneProvider) {
        return ofPostgresFormat(timeZoneProvider);
    }

    public static DatetimeRangeToNoTimezonedStringConverter ofPostgresFormat(@NonNull Provider<ZoneId> timeZoneProvider) {
        return new DatetimeRangeToNoTimezonedStringConverter(timeZoneProvider, DATETIME_NOTZ_FORMAT);
    }

    @Override
    public @NonNull String convert(@NonNull DatetimeRange range) {
        if (range instanceof EmptyDatetimeRange) {
            return EMPTY_RANGE;
        }

        String lower = ObjectUtil.firstNotNull(ObjectUtil.elvis(range.inclusiveFromDate(), instant -> quote(formatInstant(instant))), NEGATIVE_INFINITY);
        String upper = ObjectUtil.firstNotNull(ObjectUtil.elvis(range.exclusiveToDate(), instant -> quote(formatInstant(instant))), POSITIVE_INFINITY);

        // vždy zapisujeme jako [lower,upper), jen -infinity ma otevrenou hranici
        return (range.inclusiveFromDate() == null ? "(" : "[") + lower + "," + upper + ")";
    }

    private String formatInstant(Instant instant) {
        ZonedDateTime zdt = instant.atZone(timeZoneProvider.get());
        // oříznout na mikrosekundy (PG defaultně drží do 6 desetinných míst)
        int nanos = zdt.getNano();
        int micros = (nanos / 1_000);
        zdt = zdt.withNano(micros * 1_000);
        return instantFormatter.format(zdt);
    }

    private static String quote(String s) {
        // timestamp string obsahuje mezeru -> bezpečně vždy citujeme
        return "\"" + s + "\"";
    }

}