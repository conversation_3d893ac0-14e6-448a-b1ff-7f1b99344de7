package cz.kpsys.portaro.commons.date;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.time.Instant;
import java.util.Date;
import java.util.Objects;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class IsoStringToDateConverter implements Converter<String, Date> {

    @NonNull IsoStringToInstantConverter isoStringToInstantConverter = new IsoStringToInstantConverter();

    @Override
    public Date convert(@NonNull String source) {
        Instant instant = Objects.requireNonNull(isoStringToInstantConverter.convert(source));
        return Date.from(instant);
    }

}