package cz.kpsys.portaro.commons.date;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.util.DateUtils;
import lombok.NonNull;
import lombok.With;

import java.time.*;
import java.util.stream.Stream;

public record NotEmptyBoundedDatetimeRange(

        @JsonProperty("fromDate")
        @With
        @NonNull
        Instant inclusiveFromDate,

        @JsonProperty("toDate")
        @With
        @NonNull
        Instant exclusiveToDate

) implements BoundedDatetimeRange {

    public NotEmptyBoundedDatetimeRange {
        if (!inclusiveFromDate.isBefore(exclusiveToDate)) {
            throw new IllegalStateException("From date must be before to date");
        }
    }

    @Override
    public boolean empty() {
        return false;
    }

    @Override
    public Duration duration() {
        return Duration.between(inclusiveFromDate, exclusiveToDate);
    }

    @Override
    public Stream<LocalDate> streamLocalDates(@NonNull ZoneId zoneId) {
        return DateUtils.streamLocalDates(inclusiveFromDate, exclusiveToDate, zoneId);
    }

    public LocalTimeRange toLocalTimeRange(ZoneId timeZone) {
        LocalTime fromLocalTime = inclusiveFromDate.atZone(timeZone).toLocalTime();
        LocalTime toLocalTime = exclusiveToDate.atZone(timeZone).toLocalTime();
        return new LocalTimeRange(fromLocalTime, toLocalTime);
    }

    @Override
    public boolean equals(Object o) {
        return o instanceof NotEmptyBoundedDatetimeRange that && exclusiveToDate.equals(that.exclusiveToDate) && inclusiveFromDate.equals(that.inclusiveFromDate);
    }

    @Override
    public int hashCode() {
        int result = inclusiveFromDate.hashCode();
        result = 31 * result + exclusiveToDate.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "[" + inclusiveFromDate + ", " + exclusiveToDate + ")";
    }
}
