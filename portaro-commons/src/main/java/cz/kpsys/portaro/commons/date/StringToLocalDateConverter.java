package cz.kpsys.portaro.commons.date;

import cz.kpsys.portaro.commons.convert.ConversionException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.stream.Collectors;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class StringToLocalDateConverter implements Converter<String, LocalDate> {

    @NonNull List<DateTimeFormatter> dateFormatsToTry;

    public static StringToLocalDateConverter withoutIsoFallback(DateTimeFormatter... dateFormatsToTry) {
        return new StringToLocalDateConverter(List.of(dateFormatsToTry));
    }

    @Override
    public @NonNull LocalDate convert(@NonNull String source) {
        Exception e = null;

        for (DateTimeFormatter dateFormat : dateFormatsToTry) {
            try {
                return LocalDate.parse(source, dateFormat);
            } catch (DateTimeParseException ex) {
                if (e == null) {
                    e = ex;
                }
            }
        }

        String allowedFormats = dateFormatsToTry.stream()
                .map(Object::toString)
                .collect(Collectors.joining(","));
        throw new ConversionException(String.format("Cannot convert \"%s\" to instant, probably wrong or unsupported format (allowed formats: %s)", source, allowedFormats), e);
    }

}