package cz.kpsys.portaro.commons.date;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.core.convert.converter.Converter;

import java.util.Optional;

import static cz.kpsys.portaro.commons.date.PostgresRangeFormatConstants.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StringToPostgresFormatRangeConverter implements Converter<@NonNull String, StringToPostgresFormatRangeConverter.@NonNull Range> {

    public static final StringToPostgresFormatRangeConverter INSTANCE = new StringToPostgresFormatRangeConverter();

    @Override
    public @NonNull Range convert(@NonNull String input) {
        if (EMPTY_RANGE.equals(input)) {
            return new Range(Optional.empty(), false, Optional.empty(), false, true);
        }

        if (input.length() < 2) {
            throw new IllegalArgumentException("Invalid datetime range literal (too short): " + input);
        }

        boolean leftInclusive = parseLowerBracketIsInclusive(input);
        boolean rightInclusive = parseUpperBracketIsInclusive(input);

        String inside = input.substring(1, input.length() - 1);
        int comma = inside.indexOf(',');
        if (comma < 0) {
            throw new IllegalArgumentException("Invalid datetime range literal (missing comma): " + input);
        }

        String left = inside.substring(0, comma).trim();
        String right = inside.substring(comma + 1).trim();

        left = unquote(left);
        right = unquote(right);

        String from = parseBound(left);
        String to = parseBound(right);

        // prosazujeme výhradně [ ... ), krome "(-infinity, ..."
        if (!leftInclusive && from != null) {
            throw new IllegalArgumentException("Expected '[lower,...' or '(-infinity,...' datetime range, got: " + input);
        }
        if (rightInclusive) {
            throw new IllegalArgumentException("Expected '...,upper)' datetime range, got: " + input);
        }

        return new Range(Optional.ofNullable(from), leftInclusive, Optional.ofNullable(to), rightInclusive, false);
    }

    private static boolean parseLowerBracketIsInclusive(@NonNull String input) {
        char l = input.charAt(0);
        if (l != LOWER_INCLUSIVE_SYMBOL && l != LOWER_EXCLUSIVE_SYMBOL) {
            throw new IllegalArgumentException("Expected '[lower,...' or '(lower,...' datetime range, got: " + input);
        }
        return l == LOWER_INCLUSIVE_SYMBOL;
    }

    private static boolean parseUpperBracketIsInclusive(@NonNull String input) {
        char r = input.charAt(input.length() - 1);
        if (r != UPPER_INCLUSIVE_SYMBOL && r != UPPER_EXCLUSIVE_SYMBOL) {
            throw new IllegalArgumentException("Expected '...,upper]' or '...,upper)' datetime range, got: " + input);
        }
        return r == UPPER_INCLUSIVE_SYMBOL;
    }

    private @Nullable String parseBound(String s) {
        if (NEGATIVE_INFINITY.equals(s)) {
            return null;
        }
        if (POSITIVE_INFINITY.equals(s)) {
            return null;
        }
        return s;
    }

    private static String unquote(String s) {
        if (s.length() >= 2 && s.charAt(0) == '"' && s.charAt(s.length() - 1) == '"') {
            return s.substring(1, s.length() - 1);
        }
        return s;
    }

    public record Range(
            Optional<String> left,
            boolean leftInclusive,
            Optional<String> right,
            boolean rightInclusive,
            boolean empty
    ) {}

}
