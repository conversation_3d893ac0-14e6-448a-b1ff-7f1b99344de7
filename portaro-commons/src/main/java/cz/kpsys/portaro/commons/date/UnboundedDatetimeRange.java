package cz.kpsys.portaro.commons.date;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.With;
import org.jspecify.annotations.Nullable;

import java.time.Instant;
import java.util.Objects;

@JsonIgnoreProperties
public record UnboundedDatetimeRange(

        @JsonProperty("fromDate")
        @With
        @Nullable
        Instant inclusiveFromDate,

        @JsonProperty("toDate")
        @With
        @Nullable
        Instant exclusiveToDate

) implements DatetimeRange {

    public UnboundedDatetimeRange {
        if (inclusiveFromDate != null && exclusiveToDate != null && !inclusiveFromDate.isBefore(exclusiveToDate)) {
            throw new IllegalArgumentException("From date must be before to date - empty range is not allowed here");
        }
    }

    @Override
    public boolean empty() {
        return false;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        return o instanceof UnboundedDatetimeRange datetimeRange && Objects.equals(inclusiveFromDate, datetimeRange.inclusiveFromDate) && Objects.equals(exclusiveToDate, datetimeRange.exclusiveToDate);
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(inclusiveFromDate);
        result = 31 * result + Objects.hashCode(exclusiveToDate);
        return result;
    }

    @Override
    public String toString() {
        return (inclusiveFromDate == null ? "(" : "[") +
               ObjectUtil.firstNotNull(inclusiveFromDate, "-inf") +
               ", " +
               ObjectUtil.firstNotNull(exclusiveToDate, "inf") +
               ")";
    }

}
