package cz.kpsys.portaro.commons.file;

import cz.kpsys.portaro.commons.io.StreamUtils;
import cz.kpsys.portaro.commons.util.RegExpUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.InputStreamSource;
import org.springframework.core.io.Resource;
import org.springframework.lang.Nullable;
import org.springframework.util.FileCopyUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class FileUtils {
    
    public static final String[] TEXT_FILE_EXTENSIONS = {"css", "html", "htm", "txt", "vm", "vtl", "xml", "js"};
    public static final String[] IMAGE_FILE_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "tiff", "bmp", "webp"};
    public static final String[] VIDEO_FILE_EXTENSIONS = {"mp4"};
    public static final String[] AUDIO_FILE_EXTENSIONS = {"mp3"};

    public static boolean isTextual(String filename) {
        return hasOneOfExtensions(filename, TEXT_FILE_EXTENSIONS);
    }

    public static boolean isTextFile(File f) {
        return isTextual(f.getName());
    }

    public static boolean hasOneOfExtensions(String filename, String[] availableLowercasedExtensions) {
        String name = filename.toLowerCase();
        return Arrays.stream(availableLowercasedExtensions)
                .map(extension -> "." + extension)
                .anyMatch(name::endsWith);
    }
    
    /**
     * Upravi nazev souboru tak, aby byla koncovka v lowercase
     */
    public static String extensionToLowerCase(String filename) {
        int dotPosition = filename.lastIndexOf('.');
        if (dotPosition < 0) {
            return filename;
        }
        int firstLetterOfExtIdx = dotPosition + 1;
        String ext = filename.substring(firstLetterOfExtIdx);
        String lowerCasedExt = ext.toLowerCase();
        return filename.substring(0, firstLetterOfExtIdx).concat(lowerCasedExt);
    }

    public static byte[] getBytesOfResource(InputStreamSource resource) {
        try {
            return FileCopyUtils.copyToByteArray(resource.getInputStream());
        } catch (IOException ex) {
            throw new RuntimeException("Error while loading bytes from resource %s".formatted(resource), ex);
        }
    }
    
    public static byte[] getBytesOfFile(File file) {
        try {
            return FileCopyUtils.copyToByteArray(file);
        } catch (Exception ex) {
            throw new RuntimeException("Error while loading bytes from file %s".formatted(file), ex);
        }
    }

    public static byte[] getBytesOfInputStream(InputStream inputStream) {
        try {
            return FileCopyUtils.copyToByteArray(inputStream);
        } catch (IOException ex) {
            throw new RuntimeException("Error while loading bytes from input stream", ex);
        }
    }


    public static String readTextFile(File f) throws IOException {
        if (!f.exists()) {
            throw new FileNotFoundException(String.format("Soubor (%s) neexistuje.", f.getAbsolutePath()));
        }
        String content = org.apache.commons.io.FileUtils.readFileToString(f, "UTF-8");
        return content.trim();
    }

    @SneakyThrows
    public static String readTextResource(Resource resource) {
        if (!resource.exists()) {
            throw new FileNotFoundException(String.format("Resource %s does not exist.", resource));
        }
        try (InputStream is = resource.getInputStream()) {
            String content = IOUtils.toString(is, StandardCharsets.UTF_8);
            return content.trim();
        }
    }
    
    /**
     * Zapise string do souboru. Pokud soubor neexistuje, vytvori ho.
     */
    public static void writeTextFile(File f, String content) throws IOException {
        org.apache.commons.io.FileUtils.writeStringToFile(f, content, "UTF-8");
    }
    
    public static void writeByteArrayToFile(File file, byte[] data) throws IOException {
        org.apache.commons.io.FileUtils.writeByteArrayToFile(file, data);
    }

    public static void writeStreamToFile(File file, InputStream readStream, boolean finallyCloseReadStream) throws IOException {
        FileOutputStream writeStream = null;
        try {
            writeStream = new FileOutputStream(file);
            IOUtils.copy(readStream, writeStream);
        } finally {
            StreamUtils.quietCloseAll(writeStream);
            if (finallyCloseReadStream) {
                StreamUtils.quietCloseAll(readStream);
            }
        }
    }

    /**
     * Returns file extension in lowercase or empty {@link Optional} if cannot be resolved.
     */
    public static Optional<String> tryParseExtension(@Nullable String name) {
        if (name == null) {
            return Optional.empty();
        }
        return ParsedFilename.parse(name).extension().map(String::toLowerCase);
    }

    public static String path(String...parts) {
        String separator = File.separator;
        StringBuilder sb = new StringBuilder();
        for (String part : parts) {
            if (!sb.isEmpty()) {
                sb.append(separator);
            }
            sb.append(part);
        }
        return sb.toString();
    }


    public static int toMB(long sizeInBytes) {
        if (sizeInBytes < 0) {
            return -1;
        }
        return Math.round(sizeInBytes / 1024 / 1024);
    }


    public static int toKB(long sizeInBytes) {
        if (sizeInBytes < 0) {
            return -1;
        }
        return Math.round(sizeInBytes / 1024);
    }


    public static String bytesToString(@Nullable Long sizeInBytes) {
        if (sizeInBytes == null || sizeInBytes < 0) {
            return "N/A";
        }
        int mb = toMB(sizeInBytes);
        if (mb < 2) {
            return toKB(sizeInBytes) + "kB";
        }
        return mb + "MB";
    }


    public static long kBToB(long sizeInKB) {
        return sizeInKB * 1024;
    }


    public static long mBToB(long sizeInMb) {
        return sizeInMb * 1024 * 1024;
    }


    @SneakyThrows
    public static File createTempFile(String prefix, String suffix) {
        File file = File.createTempFile(prefix, suffix);
        file.deleteOnExit();
        return file;
    }


    @SneakyThrows
    public static File createTestTempFile(Class<?> testClass, String filename) {
        ParsedFilename parsedFilename = ParsedFilename.parse(filename);
        File file = File.createTempFile("test-" + testClass.getSimpleName() + "-" + parsedFilename.mainName(), "." + parsedFilename.extension().orElse(null));
        file.deleteOnExit();
        return file;
    }


    @SneakyThrows
    public static File createTestTempDir(Class<?> testClass, String dirname) {
        Path dirPath = Files.createTempDirectory("test-" + testClass.getSimpleName() + "-" + dirname);
        File dir = dirPath.toFile();
        dir.deleteOnExit();
        return dir;
    }


    public static void quietlyTryDeleteFile(@Nullable File zipFile) {
        try {
            if (zipFile != null) {
                zipFile.delete();
            }
        } catch (Exception e) {
            log.warn("Cannot delete file {}", zipFile, e);
        }
    }

    /**
     *
     * @param allowedExtensions
     * @return {@code "(.txt|.jpg|.pdf)$"}
     */
    public static String extensionCheckingRegex(String[] allowedExtensions) {
        return "(" +
                Arrays.stream(allowedExtensions)
                        .<String>map(ext -> RegExpUtils.escape(ensureExtensionStartsWithDot(ext)))
                        .collect(Collectors.joining("|")) +
                ")$";
    }

    private static String ensureExtensionStartsWithDot(String extension) {
        return (extension.charAt(0) == '.') ? extension : '.' + extension;
    }

}
