package cz.kpsys.portaro.commons.health;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.Objects;

public record Liveness(

        boolean up,

        @Nullable
        Exception downException

) {

    public static Liveness ofUp() {
        return new Liveness(true, null);
    }

    public static Liveness ofDown(Exception ex) {
        return new Liveness(false, ex);
    }

    public Liveness {
        if (up && downException != null) {
            throw new IllegalArgumentException("State is up, cannot have downException");
        }
    }

    @Override
    public @NonNull Exception downException() {
        if (up) {
            throw new IllegalStateException("State is up, cannot get downException");
        }
        return Objects.requireNonNull(downException);
    }
}
