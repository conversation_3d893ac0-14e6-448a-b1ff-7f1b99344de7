package cz.kpsys.portaro.commons.io;

import cz.kpsys.portaro.commons.object.Provider;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FallbackingFileDataStreamer implements FileDataStreamer {

    @NonNull FileDataStreamer filesystemFileDataStreamer;
    @NonNull FileDataStreamer fallbackFileDataStreamer;
    @NonNull Provider<@NonNull Boolean> primaryStreamerEnabled;

    @Override
    public void streamData(@NonNull Long id, @Nullable BytesRange range, @NonNull FileStreamConsumer streamConsumer) {
        if (primaryStreamerEnabled.get()) {
            filesystemFileDataStreamer.streamData(id, range, streamConsumer);
        } else {
            fallbackFileDataStreamer.streamData(id, range, streamConsumer);
        }
    }
}
