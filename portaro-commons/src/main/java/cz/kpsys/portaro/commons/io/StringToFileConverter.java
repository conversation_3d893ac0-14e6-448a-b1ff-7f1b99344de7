package cz.kpsys.portaro.commons.io;

import cz.kpsys.portaro.commons.convert.ConversionException;
import org.springframework.core.convert.converter.Converter;

import java.io.File;

/**
 * Created by Jan on 11. 5. 2015.
 */
public class StringToFileConverter implements Converter<String, File> {

    @Override
    public File convert(String filepath) {
        File f = new File(filepath);
        if (!f.exists()) {
            throw new ConversionException(String.format("Cannot convert, file %s does not exist", filepath));
        }
        return f;
    }

}
