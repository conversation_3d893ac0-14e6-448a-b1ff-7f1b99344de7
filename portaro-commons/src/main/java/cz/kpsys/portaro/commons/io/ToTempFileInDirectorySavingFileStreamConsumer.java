package cz.kpsys.portaro.commons.io;

import cz.kpsys.portaro.commons.file.ParsedFilename;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.io.File;
import java.io.IOException;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ToTempFileInDirectorySavingFileStreamConsumer extends ToAbstractFileSavingFileStreamConsumer {

    @NonFinal File tempFile;

    public File getFile() {
        return tempFile;
    }

    @Override
    protected File getFile(String filename) {
        ParsedFilename parsedFilename = ParsedFilename.parse(filename);
        try {
            tempFile = File.createTempFile(parsedFilename.mainName(), "." + parsedFilename.extension().orElse("dat"));
            tempFile.deleteOnExit();
            return tempFile;

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
