package cz.kpsys.portaro.commons.localization;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.Locale;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ContextualLocaleLocalizer<CTX> {

    @NonNull Translator<CTX> translator;
    @NonNull ContextualProvider<CTX, @NonNull Locale> localeProvider;

    public String localize(Text text, CTX ctx) {
        Locale locale = localeProvider.getOn(ctx);
        return text.localize(translator, ctx, locale);
    }

}
