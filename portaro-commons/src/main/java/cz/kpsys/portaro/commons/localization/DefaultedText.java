package cz.kpsys.portaro.commons.localization;

import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Locale;
import java.util.Optional;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode
public class DefaultedText implements Text {

    @NonNull Text primary;
    @NonNull Text dflt;

    public static DefaultedText create(Text primary, Text dflt) {
        return new DefaultedText(primary, dflt);
    }

    @Override
    public String toString() {
        return "DefaultedText (primary " + primary + ", default " + dflt + ")";
    }

    @Override
    public boolean isEmpty() {
        return primary.isEmpty() && dflt.isEmpty();
    }

    @Override
    public <CTX> Optional<String> tryLocalize(Translator<CTX> translator, CTX ctx, Locale locale) {
        return Stream.of(primary, dflt)
                .map(text -> text.tryLocalize(translator, ctx, locale))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst();
    }

}
