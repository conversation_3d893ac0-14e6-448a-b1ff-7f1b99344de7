package cz.kpsys.portaro.commons.localization;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.text.MessageFormat;
import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TestingTranslator<CTX> implements Translator<CTX> {

    public static final Locale USED_LOCALE = Locale.ENGLISH;

    @NonNull Map<String, Map<Locale, String>> messageMap = new HashMap<>();

    public TestingTranslator<CTX> with(String codeSameAsTranslation) {
        return with(codeSameAsTranslation, codeSameAsTranslation);
    }

    public TestingTranslator<CTX> with(String code, String translation) {
        messageMap.computeIfAbsent(code, key -> new HashMap<>(4)).put(USED_LOCALE, translation);
        return this;
    }

    @Override
    public Optional<String> getMessage(@NonNull String code, @NonNull List<Object> args, @NonNull CTX ctx, @NonNull Locale locale) {
        Map<Locale, String> localeToMessageMap = messageMap.get(code);
        if (localeToMessageMap == null) {
            return Optional.empty();
        }
        String message = localeToMessageMap.get(USED_LOCALE);
        if (message == null) {
            return Optional.empty();
        }
        return Optional.of(new MessageFormat(message, USED_LOCALE).format(args.toArray()));
    }
}
