package cz.kpsys.portaro.commons.mail;

import lombok.*;
import org.springframework.lang.Nullable;

@Value
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class MailSending {

    @Getter
    @NonNull
    Mail<?> mail;

    @Getter
    @Nullable
    Throwable exception;

    public static MailSending success(Mail<?> mail) {
        return new MailSending(mail, null);
    }

    public static MailSending failed(Mail<?> mail, Throwable exception) {
        return new MailSending(mail, exception);
    }

}
