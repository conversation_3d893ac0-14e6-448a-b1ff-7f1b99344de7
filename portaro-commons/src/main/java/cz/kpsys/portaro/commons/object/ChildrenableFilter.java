package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ChildrenableFilter<E extends Childrenable<E>> {

    @NonNull BiFunction<E, List<E>, E> newItemFactory;

    public E filterChildren(E root, Predicate<E> itemFilter) {
        Assert.state(itemFilter.test(root), "Cannot filter children, because root itself is filtered out (root %s)".formatted(root));
        Node<E> rootNode = itemToNode(root);
        filterChildren(rootNode, node -> itemFilter.test(node.item()));
        return nodeToItem(rootNode, newItemFactory);
    }

    private void filterChildren(Node<E> root, Predicate<Node<E>> nodeFilter) {
        root.children().removeIf(e -> !satisfiesInSubtree(e, nodeFilter));
        root.children().forEach(e -> filterChildren(e, nodeFilter));
    }

    /**
     * Creates tree with removed not-satisfying subtrees
     */
    public E filterBranches(E root, Predicate<E> itemFilter) {
        Node<E> rootNode = itemToNode(root);
        filterRoots(rootNode, node -> itemFilter.test(node.item()));
        return nodeToItem(rootNode, newItemFactory);
    }

    private void filterRoots(Node<E> root, Predicate<Node<E>> nodeFilter) {
        if (nodeFilter.test(root)) {
            return;
        }
        root.children().removeIf(e -> !satisfiesInSubtree(e, nodeFilter));
        root.children().forEach(e -> filterRoots(e, nodeFilter));
    }

    private boolean satisfiesInSubtree(Node<E> root, Predicate<Node<E>> nodeFilter) {
        if (nodeFilter.test(root)) {
            return true;
        }
        return root.children().stream().anyMatch(e -> satisfiesInSubtree(e, nodeFilter));
    }

    private Node<E> itemToNode(E root) {
        List<Node<E>> childrenNodes = ListUtil.convert(root.children(), this::itemToNode);
        return new Node<E>(root, childrenNodes);
    }

    private E nodeToItem(Node<E> node, BiFunction<E, List<E>, E> newItemFactory) {
        E item = node.item();
        List<E> children = ListUtil.convert(node.children(), source -> nodeToItem(source, newItemFactory));
        return newItemFactory.apply(item, children);
    }

    private record Node<E>(@NonNull E item, @NonNull List<Node<E>> children) implements Childrenable<Node<E>> {

        @Override
        public boolean equals(Object o) {
            return ObjectUtil.equals(this, o, Node.class, Node::item);
        }

        @Override
        public int hashCode() {
            return item.hashCode();
        }
    }
}
