package cz.kpsys.portaro.commons.object;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FallbackingSettableProvider<E> implements SameTypeSettableProvider<E> {

    @NonNull Provider<E> fallbackValueProvider;
    @NonFinal E value = null;

    @Override
    public E get() {
        if (value != null) {
            return value;
        }
        return fallbackValueProvider.get();
    }

    @Override
    public void set(E value) {
        this.value = value;
    }
}
