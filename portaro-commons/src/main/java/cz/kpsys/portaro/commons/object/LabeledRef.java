package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.NonNull;

public record LabeledRef<ID>(

        @NonNull
        ID id,

        @NonNull
        String kind,

        @NonNull
        Text text

) implements LabeledReference<ID> {

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, LabeledRef.class, IdentifiedRecord::id, LabeledRef::kind);
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }
}
