package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.licence.FeatureNotEnabledException;
import cz.kpsys.portaro.commons.localization.Text;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Duration;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;

public interface Provider<V> extends Supplier<V> {

    static <V> @NonNull Provider<V> of(@NonNull Supplier<V> provider) {
        return provider::get;
    }

    V get();

    default Provider<Optional<V>> optionally() {
        return new OptionalProvider<>(this);
    }

    default Provider<@NonNull V> throwingWhenNull() {
        return new NotNullProvider<>(this, () -> new MissingValueException("Value returned by provider is null"));
    }

    default Provider<@NonNull V> invalidWhen(Predicate<@NonNull V> isInvalidPredicate) {
        return ValidatingProvider.invalidWhen(this, isInvalidPredicate,
                (value) -> new IllegalStateException("Invalid value %s".formatted(value)));
    }

    default Asserter<? extends FeatureNotEnabledException> toEnabledAsserter(Predicate<@NonNull V> isEnabledPredicate,
                                                                             @NonNull String featureName,
                                                                             @Nullable Text text) {
        return ValidatingProvider.validWhen(this, isEnabledPredicate, (value) ->
                new FeatureNotEnabledException(
                        featureName,
                        "Feature %s is not enabled (value %s)".formatted(featureName, value),
                        text
                )
        )::get;
    }

    default <NEXT> Provider<NEXT> andThen(@NonNull Function<@NonNull V, ? extends NEXT> after) {
        Provider<@NonNull V> nonNullThis = throwingWhenNull();
        return () -> {
            @NonNull V intermediate = nonNullThis.get();
            return after.apply(intermediate);
        };
    }

    default <NEXT> Provider<NEXT> andThenFastReturningNull(@NonNull Function<@NonNull V, ? extends NEXT> after) {
        return () -> {
            V intermediate = get();
            if (intermediate == null) {
                return null;
            }
            return after.apply(intermediate);
        };
    }

    default @NonNull CachedProvider<V> cached() {
        return new CachedProvider<>(this);
    }

    default @NonNull TtlCachedProvider<V> cached(Duration ttl) {
        return TtlCachedProvider.of(this, ttl);
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    class OptionalProvider<V> implements Provider<Optional<V>> {

        @NonNull Provider<V> delegate;

        @Override
        public Optional<V> get() {
            V nullableValue = delegate.get();
            return Optional.ofNullable(nullableValue);
        }

        @Override
        public String toString() {
            return "OptionalProvider{->" + delegate + "}";
        }
    }
}