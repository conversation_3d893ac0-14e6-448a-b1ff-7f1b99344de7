package cz.kpsys.portaro.commons.object;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringCachedAllValuesProvider<E> implements AllValuesProvider<E> {

    @NonNull Callable<List<E>> cacheLoader;
    @NonNull CacheManager cacheManager;
    @NonNull String springCacheName;

    public static <E> SpringCachedAllValuesProvider<E> create(@NonNull AllValuesProvider<E> delegate, @NonNull CacheManager cacheManager, @NonNull String springCacheName) {
        return new SpringCachedAllValuesProvider<E>(new CacheLoaderAdapter<>(delegate), cacheManager, springCacheName);
    }

    public static <E> SpringCachedAllValuesProvider<E> createLazy(@NonNull AllValuesProvider<E> delegate, @NonNull CacheManager cacheManager, @NonNull String springCacheName) {
        return new SpringCachedAllValuesProvider<E>(new CacheLoaderAdapter<>(delegate), cacheManager, springCacheName);
    }

    private Cache getCache() {
        return Objects.requireNonNull(cacheManager.getCache(springCacheName), () -> "Spring cache with name '%s' does not exist".formatted(springCacheName));
    }

    @Override
    public List<E> getAll() {
        return getCache().get("all", cacheLoader);
    }

    @Override
    public String toString() {
        return "SpringCachedAllValuesProvider{-> %s, cacheName=%s}".formatted(cacheLoader, springCacheName);
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class CacheLoaderAdapter<E> implements Callable<List<E>> {

        @NonNull AllValuesProvider<E> allValuesProvider;

        @Override
        public List<E> call() {
            return allValuesProvider.getAll();
        }

        @Override
        public String toString() {
            return "CacheLoader{-> %s}".formatted(allValuesProvider);
        }
    }
}
