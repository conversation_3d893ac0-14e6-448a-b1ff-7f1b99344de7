package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.io.Serializable;
import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StaticCodebook<E extends Identified<ID>, ID extends Serializable> implements Codebook<E, ID>, Converter<ID, E> {

    @NonNull Map<ID, E> map;

    public static <E extends Identified<ID>, ID extends Serializable> StaticCodebook<E, ID> ofDynamicSize() {
        return new StaticCodebook<>(new HashMap<>());
    }

    @SafeVarargs
    public StaticCodebook(E... values) {
        this(new HashMap<>(values.length));
        for (E value : values) {
            map.put(value.getId(), value);
        }
    }

    public StaticCodebook(Collection<E> values) {
        this(new HashMap<>(values.size()));
        for (E value : values) {
            map.put(value.getId(), value);
        }
    }

    public StaticCodebook<E, ID> add(E value) {
        map.put(value.getId(), value);
        return this;
    }

    @Override
    public E getById(@NonNull ID id) {
        return findById(id)
                .orElseThrow(() -> ItemNotFoundException.ofResolvedDesiredItemType(getAll(), id));
    }

    @Override
    public Optional<E> findById(@NonNull ID id) {
        return Optional.ofNullable(map.get(id));
    }

    @Override
    public List<E> getAll() {
        return new ArrayList<>(map.values());
    }

    @Override
    public E convert(@NonNull ID source) {
        return getById(source);
    }

    @Override
    public String toString() {
        var it = map.values().iterator();
        if (it.hasNext()) {
            return "StaticCodebook[" + it.next().getClass().getSimpleName() + "]";
        } else {
            return Objects.toIdentityString(this);
        }
    }

}
