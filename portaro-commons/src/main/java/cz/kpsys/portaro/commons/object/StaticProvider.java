package cz.kpsys.portaro.commons.object;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;


/**
 *
 * <AUTHOR>
 * @param <E> Typ ulozene hodnoty.
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class StaticProvider<E> implements Provider<E> {

    E value;

    public static <E> StaticProvider<E> ofNull() {
        return new StaticProvider<>(null);
    }

    public static <E> StaticProvider<@NonNull E> of(@NonNull E value) {
        return new StaticProvider<>(value);
    }


    @JsonProperty("value")
    @Override
    public E get() {
        return value;
    }


    @Override
    public String toString() {
        return String.valueOf(value);
    }
    
}
