package cz.kpsys.portaro.commons.object.repo;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ActionDecoratingSaver<E, RET> implements Saver<E, RET> {

    @NonNull Saver<E, RET> target;
    @NonFinal Consumer<E> beforeSaveAction = e -> {};
    @NonFinal Consumer<RET> afterSaveAction = e -> {};

    public ActionDecoratingSaver<E, RET> doBefore(Consumer<E> callback) {
        this.beforeSaveAction = callback;
        return this;
    }

    public ActionDecoratingSaver<E, RET> doAfter(Consumer<RET> callback) {
        this.afterSaveAction = callback;
        return this;
    }

    @Override
    public @NonNull RET save(@NonNull E object) {
        beforeSaveAction.accept(object);
        RET saved = target.save(object);
        afterSaveAction.accept(saved);
        return saved;
    }
}
