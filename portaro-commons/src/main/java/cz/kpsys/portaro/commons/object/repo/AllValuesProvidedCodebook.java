package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.RIdentified;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class AllValuesProvidedCodebook<E, ID> implements Codebook<E, ID> {

    @NonNull AllValuesProvider<E> allValuesProvider;
    @NonNull ByIdOptLoadable<E, ID> byIdLoader;

    public static <E extends Identified<ID>, ID> AllValuesProvidedCodebook<E, ID> ofIdentified(@NonNull AllValuesProvider<E> allValuesProvider) {
        return ofCustomId(allValuesProvider, Identified::getId);
    }

    public static <R extends RIdentified<RID>, RID> AllValuesProvidedCodebook<R, RID> ofRidentified(@NonNull AllValuesProvider<R> allValuesProvider) {
        return ofCustomId(allValuesProvider, RIdentified::getRid);
    }

    public static <E extends Identified<ID>, ID> AllValuesProvidedCodebook<E, ID> ofEmptyIdentified() {
        return ofIdentified(List::of);
    }

    public static <E, ID> AllValuesProvidedCodebook<E, ID> ofCustomId(@NonNull AllValuesProvider<E> allValuesProvider, @NonNull Function<E, ID> itemIdGetter) {
        ByIdOptLoadable<E, ID> byIdLoader = allValuesProvider instanceof ByIdOptLoadable<?, ?>
                ? (ByIdOptLoadable<E, ID>) allValuesProvider
                : new AllValuesIteratingByIdOptLoadable<>(allValuesProvider, itemIdGetter);
        return of(allValuesProvider, byIdLoader);
    }

    public static <E, ID> AllValuesProvidedCodebook<E, ID> of(@NonNull AllValuesProvider<E> allValuesProvider, @NonNull ByIdOptLoadable<E, ID> byIdLoader) {
        return new AllValuesProvidedCodebook<>(allValuesProvider, byIdLoader);
    }

    @Override
    public List<E> getAll() {
        return allValuesProvider.getAll();
    }

    @Override
    public E getById(@NonNull ID id) {
        return findById(id)
                .orElseThrow(() -> ItemNotFoundException.ofResolvedDesiredItemType(getAll(), id));
    }

    @Override
    public Optional<E> findById(@NonNull ID id) {
        return byIdLoader.findById(id);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + "[" + allValuesProvider + "]";
    }
}
