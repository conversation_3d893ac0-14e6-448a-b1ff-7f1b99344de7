package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.object.Identified;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PACKAGE)
public class BatchFiller<VALUE extends Identified<VALUE_ID>, VALUE_ID> {

    @NonNull AllByIdsLoadable<VALUE, VALUE_ID> valuesByIdsLoader;

    public static <VALUE extends Identified<VALUE_ID>, VALUE_ID> BatchFiller<VALUE, VALUE_ID> of(@NonNull AllByIdsLoadable<VALUE, VALUE_ID> valuesByIdsLoader) {
        return new BatchFiller<VALUE, VALUE_ID>(valuesByIdsLoader);
    }

    public <DTO> Map<DTO, @NonNull VALUE> load(Collection<DTO> dtos, Function<DTO, @NonNull VALUE_ID> dtoToValueId) {
        return DataUtils.batchLoadDtoValue(dtos, dtoToValueId, Identified::getId, valuesByIdsLoader, false, false);
    }

    public <DTO> Map<DTO, VALUE> loadNullable(Collection<DTO> dtos, Function<DTO, VALUE_ID> dtoToNullableValueId) {
        return DataUtils.batchLoadDtoValue(dtos, dtoToNullableValueId, Identified::getId, valuesByIdsLoader, true, false);
    }
}
