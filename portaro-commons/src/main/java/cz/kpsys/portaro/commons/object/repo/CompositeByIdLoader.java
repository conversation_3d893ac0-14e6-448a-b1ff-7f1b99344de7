package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.jspecify.annotations.Nullable;
import org.springframework.dao.EmptyResultDataAccessException;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class CompositeByIdLoader<E, ID> implements IdAndOptIdAndIdsLoadable<E, ID> {

    @NonNull List<IdAndIdsLoadable<? extends E, ID>> sources = new CopyOnWriteArrayList<>();
    @NonNull List<ByIdOptLoadable<? extends E, ID>> byIdOptLoadables = new CopyOnWriteArrayList<>();
    @Nullable @NonFinal IdAndIdsLoadable<? extends E, ID> fallbackSource = null;
    @Nullable @NonFinal ByIdOptLoadable<E, ID> fallbackOptLoadable = null;
    @NonFinal boolean lastIsFallback = false;

    @SafeVarargs
    public static <E, ID> CompositeByIdLoader<E, ID> of(IdAndIdsLoadable<? extends E, ID>... sources) {
        CompositeByIdLoader<E, ID> loader = new CompositeByIdLoader<>();
        for (IdAndIdsLoadable<? extends E, ID> source : sources) {
            loader.add(source);
        }
        return loader;
    }

    private static <E, ID> @NonNull ByIdOptLoadable<E, ID> getNativeOrWrappedByIdOptLoader(IdAndIdsLoadable<E, ID> source) {
        return source instanceof ByIdOptLoadable<?, ?>
                ? (ByIdOptLoadable<E, ID>) source
                : ByIdAdaptingByIdOptLoadable.of(source);
    }

    public CompositeByIdLoader<E, ID> add(@NonNull IdAndIdsLoadable<? extends E, ID> loader) {
        int position = lastIsFallback ? sources.size() - 1 : sources.size();
        sources.add(position, loader);
        byIdOptLoadables.add(position, getNativeOrWrappedByIdOptLoader(loader));
        return this;
    }

    public CompositeByIdLoader<E, ID> addFallback(@NonNull IdAndIdsLoadable<E, ID> loader) {
        add(loader);
        lastIsFallback = true;
        return this;
    }

    @Override
    public Optional<E> findById(@NonNull ID id) {
        for (ByIdOptLoadable<? extends E, ID> source : byIdOptLoadables) {
            Optional<? extends E> byId = source.findById(id);
            if (byId.isPresent()) {
                return (Optional<E>) byId;
            }
        }
        return Optional.empty();
    }

    @Override
    public E getById(@NonNull ID id) {
        return findById(id)
                .orElseThrow(() -> new ItemNotFoundException("unknown type", id, "No object by id " + id + " found in any sources: " + sources, Texts.ofNative("No object by id " + id + " found in any sources")));
    }

    /// Zatim prilis striktni implementace, ktera z danych loaderu nacte vzdy vsechno nebo nic.
    /// V budoucnu by to asi chtelo vylepsit tak, aby nejak prubezne nacitala postupne chybejici
    @Override
    public List<E> getAllByIds(@NonNull List<ID> ids) {
        if (ids.isEmpty()) {
            return List.of();
        }

        List<Exception> exceptions = List.of();
        for (AllByIdsLoadable<? extends E, ID> source : sources) {
            try {
                List<? extends E> byIds = source.getAllByIds(ids);
                if (!byIds.isEmpty()) {
                    return (List<E>) byIds;
                }
            } catch (EmptyResultDataAccessException | ItemNotFoundException e) {
                exceptions = ListUtil.createNewListAppending(exceptions, e);
            }
        }
        throw new ItemNotFoundException("unknown type", ids, "No objects by ids " + ids + " found in any sources: " + exceptions.stream().map(Throwable::getMessage).collect(Collectors.joining(", ", "[", "]")), Texts.ofNative("No objects by ids " + ids + " found in any sources"));
    }

    @Override
    public String toString() {
        return "CompositeByIdLoader{" + sources + "}";
    }
}
