package cz.kpsys.portaro.commons.object.repo;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.NestedRuntimeException;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class DataAccessException extends NestedRuntimeException {

    @NonNull String dataDescription;

    public DataAccessException(String msg, @NonNull String dataDescription) {
        super(msg);
        this.dataDescription = dataDescription;
    }

    public DataAccessException(String msg, @NonNull String dataDescription, Throwable cause) {
        super(msg, cause);
        this.dataDescription = dataDescription;
    }

}
