package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;

/**
 * Created by janpa on 26.05.2017.
 */
public class MoreThanOneItemFoundException extends DataAccessException implements UserFriendlyException {

    private final Text text;

    private Object desiredItemType;
    private Object desiredItemIdentifier;

    public MoreThanOneItemFoundException(Object desiredItemType, Object desiredItemIdentifier) {
        super(createDefaultMessage(desiredItemType, desiredItemIdentifier), String.valueOf(desiredItemIdentifier));
        ofItem(desiredItemType, desiredItemIdentifier);
        this.text = Texts.ofNative(getMessage());
    }

    public MoreThanOneItemFoundException(Object desiredItemType, Object desiredItemIdentifier, Text text) {
        super(createDefaultMessage(desiredItemType, desiredItemIdentifier), String.valueOf(desiredItemIdentifier));
        ofItem(desiredItemType, desiredItemIdentifier);
        this.text = text;
    }

    public MoreThanOneItemFoundException(Object desiredItemType, Object desiredItemIdentifier, String message, Text text) {
        super(message != null ? message : createDefaultMessage(desiredItemType, desiredItemIdentifier), String.valueOf(desiredItemIdentifier));
        ofItem(desiredItemType, desiredItemIdentifier);
        this.text = text;
    }

    public MoreThanOneItemFoundException(Object desiredItemType, Object desiredItemIdentifier, String message, Text text, Throwable cause) {
        super(message != null ? message : createDefaultMessage(desiredItemType, desiredItemIdentifier), String.valueOf(desiredItemIdentifier), cause);
        ofItem(desiredItemType, desiredItemIdentifier);
        this.text = text;
    }


    public static void check(int count, Object desiredItemType, Object desiredItemIdentifier) {
        if (count > 1) {
            throw new MoreThanOneItemFoundException(desiredItemType, desiredItemIdentifier);
        }
    }

    public static void check(int count, Object desiredItemType, Object desiredItemIdentifier, String message) {
        check(count, desiredItemType, desiredItemIdentifier, message, Texts.ofNative(message));
    }

    public static void check(int count, Object desiredItemType, Object desiredItemIdentifier, String message, Text text) {
        if (count > 1) {
            throw new MoreThanOneItemFoundException(desiredItemType, desiredItemIdentifier, message, text);
        }
    }


    public Object getDesiredItemType() {
        return desiredItemType;
    }


    public Object getDesiredItemIdentifier() {
        return desiredItemIdentifier;
    }


    public static String createDefaultMessage(Object desiredItemType, Object desiredItemIdentifier) {
        return "Required one item of " + desiredItemType + " matching " + desiredItemIdentifier + ", but more found";
    }


    public MoreThanOneItemFoundException ofItem(Object desiredItemType, Object desiredItemIdentifier) {
        this.desiredItemType = desiredItemType;
        this.desiredItemIdentifier = desiredItemIdentifier;
        return this;
    }


    @Override
    public Text getText() {
        return text;
    }

}
