package cz.kpsys.portaro.commons.object.repo;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class OverridableSaver<E, RET> implements Saver<E, RET> {

    @NonNull Saver<E, RET> fallback;
    @NonNull List<ConditionalSaver<E, RET>> overrides = new ArrayList<>();

    public void overrideWhen(@NonNull Function<E, @NonNull Boolean> enabledResolver,
                             @NonNull ConditionalSaver<E, RET> function,
                             @NonNull String featureName) {
        overrides.add(new ConditionalSaver<>(enabledResolver, function, featureName));
    }

    @Override
    public @NonNull RET save(@NonNull E input) {
        for (ConditionalSaver<E, RET> override : overrides) {
            if (override.isEnabled(input)) {
                return override.save(input);
            }
        }

        return fallback.save(input);
    }

}
