package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TransactionalAllValuesProvider<ITEM> implements AllValuesProvider<ITEM> {

    @NonNull AllValuesProvider<ITEM> delegate;
    @NonNull TransactionTemplate transactionTemplate;

    @Override
    public List<ITEM> getAll() {
        return transactionTemplate.execute(_ -> delegate.getAll());
    }

}
