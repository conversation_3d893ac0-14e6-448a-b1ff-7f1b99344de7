package cz.kpsys.portaro.commons.util;

import cz.kpsys.portaro.commons.convert.ItemToIdConverter;
import cz.kpsys.portaro.commons.convert.ItemToRidConverter;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.RIdentified;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.Comparator;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ComparatorForExplicitIdSorting<E, ID> implements Comparator<E> {

    @NonNull List<ID> sortRule;
    @NonNull Converter<E, ID> objectToIdConverter;

    public static <E extends Identified<ID>, ID> ComparatorForExplicitIdSorting<E, ID> forIdentified(List<ID> sortRule) {
        return new ComparatorForExplicitIdSorting<>(sortRule, new ItemToIdConverter<E, ID>());
    }

    public static <E extends RIdentified<ID>, ID> ComparatorForExplicitIdSorting<E, ID> forRIdentified(List<ID> sortRule) {
        return new ComparatorForExplicitIdSorting<>(sortRule, new ItemToRidConverter<>());
    }

    public static <E> ComparatorForExplicitIdSorting<E, E> forIdentity(List<E> sortRule) {
        return new ComparatorForExplicitIdSorting<>(sortRule, e -> e);
    }

    @Override
    public int compare(E o1, E o2) {
        Integer i1 = getPriority(o1);
        Integer i2 = getPriority(o2);
        return i1.compareTo(i2);
    }
    
    protected int getPriority(E object) {
        int index = sortRule.indexOf(itemToId(object));
        return index >= 0 ? index : Integer.MAX_VALUE;
    }

    protected ID itemToId(E object) {
        return objectToIdConverter.convert(object);
    }
    
}
