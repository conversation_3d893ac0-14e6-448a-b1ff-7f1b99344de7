package cz.kpsys.portaro.commons.util;

import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

public class DateUtils {

    public static boolean isSameDay(Date date1, Date date2) {
        return org.apache.commons.lang3.time.DateUtils.isSameDay(date1, date2);
    }

    public static Date truncate(final Date date, final int field) {
        return org.apache.commons.lang3.time.DateUtils.truncate(date, field);
    }

    public static Date addMonths(final Date date, final int amount) {
        return org.apache.commons.lang3.time.DateUtils.addMonths(date, amount);
    }

    public static Date addWeeks(final Date date, final int amount) {
        return org.apache.commons.lang3.time.DateUtils.addWeeks(date, amount);
    }

    public static Date addDays(final Date date, final int amount) {
        return org.apache.commons.lang3.time.DateUtils.addDays(date, amount);
    }

    public static Instant addDays(final Instant date, final int amount) {
        return date.plus(amount, ChronoUnit.DAYS);
    }

    public static Duration sum(Duration... durations) {
        Duration acc = Duration.ZERO;
        for (Duration duration : durations) {
            acc = acc.plus(duration);
        }
        return acc;
    }

    public static String toHumanReadableDuration(Duration duration) {
        return toHumanReadableDuration(duration, TimeUnit.NANOSECONDS);
    }

    public static String toHumanReadableDuration(Duration duration, TimeUnit maxGranularity) {
        if (duration.isZero()) {
            return "0ms";
        }

        double omitCoefficient = 0.04;
        List<String> parts = new ArrayList<>();

        extracted(duration.toDaysPart(), TimeUnit.DAYS, null, duration, maxGranularity, omitCoefficient, parts, "d");
        extracted(duration.toHoursPart(), TimeUnit.HOURS, ChronoUnit.DAYS, duration, maxGranularity, omitCoefficient, parts, "h");
        extracted(duration.toMinutesPart(), TimeUnit.MINUTES, ChronoUnit.HOURS, duration, maxGranularity, omitCoefficient, parts, "m");
        extracted(duration.toSecondsPart(), TimeUnit.SECONDS, ChronoUnit.MINUTES, duration, maxGranularity, omitCoefficient, parts, "s");

        // The length of the duration is stored using two fields - seconds and nanoseconds -> we have to take nanos part and distribute to parts
        long residualNanos = duration.toNanosPart();

        long millisWritten = residualNanos / TimeUnit.MILLISECONDS.toNanos(1);
        residualNanos = residualNanos - (millisWritten * TimeUnit.MILLISECONDS.toNanos(1));
        extracted(millisWritten, TimeUnit.MILLISECONDS, ChronoUnit.SECONDS, duration, maxGranularity, omitCoefficient, parts, "ms");

        long microsWritten = residualNanos / TimeUnit.MICROSECONDS.toNanos(1);
        residualNanos = residualNanos - (microsWritten * TimeUnit.MICROSECONDS.toNanos(1));
        extracted(microsWritten, TimeUnit.MICROSECONDS, ChronoUnit.MILLIS, duration, maxGranularity, omitCoefficient, parts, "us");

        extracted(residualNanos, TimeUnit.NANOSECONDS, ChronoUnit.MICROS, duration, maxGranularity, omitCoefficient, parts, "ns");

        return String.join(" ", parts);
    }

    private static void extracted(long valuePart, TimeUnit timeUnit, @Nullable TemporalUnit upLevelTimeUnit, Duration duration, TimeUnit maxGranularity, double omitCoefficient, List<String> parts, String suffix) {
        if (maxGranularity.toNanos(1) <= timeUnit.toNanos(1)) {
            Duration upLevelDuration = upLevelTimeUnit == null ? Duration.ZERO : duration.truncatedTo(upLevelTimeUnit);
            if (valuePart != 0 && (upLevelDuration.isZero() || timeUnit.toNanos(valuePart) / (double) upLevelDuration.toNanos() > omitCoefficient)) {
                parts.add(valuePart + suffix);
            }
        }
    }

    public static boolean isAnyOfWeekdays(@NonNull Instant date, @NonNull ZoneId timeZone, @NonNull Collection<DayOfWeek> weekdays) {
        DayOfWeek dayOfWeekForDate = getDayOfWeek(date, timeZone);
        return weekdays.contains(dayOfWeekForDate);
    }

    public static DayOfWeek getDayOfWeek(@NonNull Instant date, @NonNull ZoneId timeZone) {
        ZonedDateTime zonedDate = date.atZone(timeZone);
        return zonedDate.getDayOfWeek();
    }

    public static int getYear(@NonNull ZoneId timeZone) {
        return Year.now(timeZone).getValue();
    }

    public static boolean isToday(@NonNull Instant date, @NonNull ZoneId zoneId) {
        return isSameDay(date, Instant.now(), zoneId);
    }

    public static boolean isSameDay(@NonNull Instant date1, @NonNull Instant date2, @NonNull ZoneId zoneId) {
        LocalDate localDate1 = LocalDate.ofInstant(date1, zoneId);
        LocalDate localDate2 = LocalDate.ofInstant(date2, zoneId);
        return localDate1.equals(localDate2);
    }

    public static LocalDate instantToLocalDate(@NonNull Instant instant, @NonNull ZoneId zoneId) {
        return instant.atZone(zoneId).toLocalDate();
    }

    public static Instant localDateToStartOfDayInstant(@NonNull LocalDate localDate, @NonNull ZoneId zoneId) {
        return localDate.atStartOfDay(zoneId).toInstant();
    }

    public static boolean isSameDay(@NonNull Instant instant1, @NonNull Instant instant2) {
        return instant1.truncatedTo(ChronoUnit.DAYS).equals(instant2.truncatedTo(ChronoUnit.DAYS));
    }

    public static Instant atEndOfDay(@NonNull ZonedDateTime date) {
        return date.truncatedTo(ChronoUnit.DAYS).plusDays(1).minusSeconds(1).toInstant();
    }

    public static boolean isWeekend(LocalDate dt) {
        return switch (dt.getDayOfWeek()) {
            case SATURDAY, SUNDAY -> true;
            default -> false;
        };
    }

    public static Optional<YearMonth> getIfExactlyStartOfMonth(@NonNull Instant instant, @NonNull ZoneId zoneId) {
        ZonedDateTime localDate = instant.atZone(zoneId);
        if (localDate.getDayOfMonth() != 1) {
            return Optional.empty();
        }
        // Získání YearMonth z původního Instant
        YearMonth yearMonth = YearMonth.from(localDate);
        Instant startOfMonth = yearMonth.atDay(1).atStartOfDay(zoneId).toInstant();
        if (startOfMonth.equals(instant)) {
            return Optional.of(yearMonth);
        }
        return Optional.empty();
    }

    public static @Nullable Integer ageFromOrNull(@Nullable LocalDate date) {
        if (date == null) {
            return null;
        }
        return ageFrom(date);
    }

    public static @NonNull Integer ageFrom(@NonNull LocalDate date) {
        return Period.between(date, LocalDate.now()).getYears();
    }

    public static boolean isFirstDayOfMonth() {
        return LocalDate.now().getDayOfMonth() == 1;
    }


    public static @NonNull Stream<LocalDate> streamLocalDates(@NonNull Instant from, @NonNull Instant exclusiveTo, @NonNull ZoneId zoneId) {
        LocalDate fromDate = DateUtils.instantToLocalDate(from, zoneId);
        LocalDate exclusiveToDate = DateUtils.instantToLocalDate(exclusiveTo, zoneId);
        return fromDate.datesUntil(exclusiveToDate);
    }

}
