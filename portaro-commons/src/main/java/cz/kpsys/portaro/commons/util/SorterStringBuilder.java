package cz.kpsys.portaro.commons.util;

public class SorterStringBuilder {

    private int numOfPrefixChars = 10;
    private char prefixAppender = ' ';

    private int numOfBodyChars = 10;
    private char bodyPrepender = ' ';

    private int numOfSuffixChars = 10;
    private char suffixAppender = ' ';


    public static String parseAndBuild(String input) {
        return new SorterStringBuilder().parseAndBuildInternal(input);
    }


    /**
     * Doplni mezery do stringu. <br/>
     * Pokud je nektera z casti delsi nez maximalni pocet znaku, tak ji na konci ustrihne.
     */
    public static String fillToSize(String prefix, String body, String suffix) {
        return new SorterStringBuilder().fillToSizeInternal(prefix, body, suffix);
    }



    private String parseAndBuildInternal(String input) {
        if (input == null) {
            return null;
        }

        int i = 0;
        int bodyStartIndex = 0;
        int suffixStartIndex = input.length();
        boolean bodyStartFound = false;
        boolean suffixStartFound = false;
        while (i < input.length()) {
            char actualChar = input.charAt(i);
            if (!bodyStartFound) { //dokud jsme jeste nenalezli cely prefix
                if (Character.isDigit(actualChar)) { //jedna se o cislo -> nalezli jsme zacatek tela
                    bodyStartFound = true;
                    bodyStartIndex = i;
                }
            } else if (!suffixStartFound) { //prefix je nalezen, hledame suffix
                if (!Character.isDigit(actualChar)) { //nejedna se o cislo -> nalezli jsme zacatek suffixu
                    suffixStartFound = true;
                    suffixStartIndex = i;
                }
            }
            i++;
        }

        String prefix = input.substring(0, bodyStartIndex);
        String body = input.substring(bodyStartIndex, suffixStartIndex);
        String suffix = input.substring(suffixStartIndex);

        return fillToSizeInternal(prefix, body, suffix);
    }


    private String fillToSizeInternal(String prefix, String body, String suffix) {
        if (StringUtil.isNullOrEmpty(prefix) && StringUtil.isNullOrEmpty(body) && StringUtil.isNullOrEmpty(suffix)) {
            return null;
        }

        prefix = StringUtil.appendToSize(prefix, prefixAppender, numOfPrefixChars);
        body = StringUtil.prependToSize(body, bodyPrepender, numOfBodyChars);
        suffix = StringUtil.appendToSize(suffix, suffixAppender, numOfSuffixChars);

        prefix = prefix.length() > numOfPrefixChars ? prefix.substring(0, numOfPrefixChars) : prefix;
        body = body.length() > numOfBodyChars ? body.substring(0, numOfBodyChars) : body;
        suffix = suffix.length() > numOfSuffixChars ? suffix.substring(0, numOfSuffixChars) : suffix;

        return (prefix + body + suffix).toUpperCase();
    }
}
