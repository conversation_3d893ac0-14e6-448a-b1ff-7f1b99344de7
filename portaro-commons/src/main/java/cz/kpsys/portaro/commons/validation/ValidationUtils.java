package cz.kpsys.portaro.commons.validation;

import cz.kpsys.portaro.commons.util.ListUtil;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ElementKind;

import java.util.Collection;
import java.util.Optional;

public class ValidationUtils {

    public static final String REQUIRED_INVALID_MESSAGE = "This field is required";

    public static final SimpleFieldValidator IS_TRUE_VALIDATOR = SimpleFieldValidator.of(ValidationUtils::isTrue, "This field must be true");
    public static final SimpleFieldValidator HAS_VALUE_VALIDATOR = SimpleFieldValidator.of(ValidationUtils::hasValue, REQUIRED_INVALID_MESSAGE);
    public static final SimpleFieldValidator HAS_CONTENT_VALIDATOR = SimpleFieldValidator.of(ValidationUtils::hasContent, REQUIRED_INVALID_MESSAGE);

    public static void setFieldFailedValidationToContextWithMessage(String failedField, ConstraintValidatorContext context, String message) {
        context.buildConstraintViolationWithTemplate(message)
                .addPropertyNode(failedField)
                .addConstraintViolation();
    }

    private static boolean isTrue(Object fieldValue) {
        return fieldValue instanceof Boolean && (Boolean) fieldValue;
    }

    public static boolean hasValue(Object fieldValue) {
        if (fieldValue == null) {
            return false;
        }

        if (fieldValue instanceof Optional) {
            if (((Optional<?>) fieldValue).isPresent()) {
                return hasValue(((Optional<?>) fieldValue).get());
            }
            return false;
        }

        return true;
    }

    public static boolean hasContent(Object fieldValue) {
        if (!hasValue(fieldValue)) {
            return false;
        }

        if (fieldValue instanceof Optional) {
            if (((Optional<?>) fieldValue).isPresent()) {
                return hasContent(((Optional<?>) fieldValue).get());
            }
            return false;
        }

        if (fieldValue instanceof Collection && ((Collection<?>) fieldValue).isEmpty()) {
            return false;
        }

        if (fieldValue instanceof String && ((String) fieldValue).isEmpty()) {
            return false;
        }

        return true;
    }

    public static boolean isClassLevelConstraintViolation(ConstraintViolation<?> constraintViolation) {
        return ListUtil.iteratorToStream(constraintViolation.getPropertyPath().iterator()).allMatch(node -> node.getKind() == ElementKind.BEAN);
    }

}
