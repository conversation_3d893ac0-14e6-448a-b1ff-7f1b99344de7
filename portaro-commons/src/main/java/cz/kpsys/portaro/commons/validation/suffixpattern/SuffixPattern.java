package cz.kpsys.portaro.commons.validation.suffixpattern;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({FIELD, ANNOTATION_TYPE})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = SuffixPatternValidator.class)
public @interface SuffixPattern {

    String message() default "{jakarta.validation.constraints.SuffixPattern.message}";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };

    String[] suffixes();

}
