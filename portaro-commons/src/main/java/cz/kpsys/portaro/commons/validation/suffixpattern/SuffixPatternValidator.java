package cz.kpsys.portaro.commons.validation.suffixpattern;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import jakarta.annotation.Nullable;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE)
public class SuffixPatternValidator implements ConstraintValidator<SuffixPattern, String> {

    @Nullable SuffixPattern constraintAnnotation;

    @Override
    public void initialize(SuffixPattern constraintAnnotation) {
        this.constraintAnnotation = constraintAnnotation;
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        Assert.notNull(constraintAnnotation, "Annotation must not be null!");
        Assert.notNull(constraintAnnotation.suffixes(), "Suffixes must not be null!");
        Assert.notNull(constraintAnnotation.message(), "Message must not be null!");

        List<String> suffixes = List.of(constraintAnnotation.suffixes());

        if (value != null) {
            for (String suffix : suffixes) {
                if (value.endsWith(suffix)) {
                    return true;
                }
            }
        }

        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(
                "Suffix is not from allowed list! " + formatSuffixes(constraintAnnotation.suffixes()))
                .addConstraintViolation();
        return false;
    }

    public static Text formatErrorMessage(SuffixPattern annotation) {
        return MultiText.ofTexts(
                Texts.ofCurlyBracesFormat(annotation.message()),
                Texts.ofNative("! " + formatSuffixes(annotation.suffixes()))
        );
    }

    private static String formatSuffixes(String[] suffixes) {
        return '[' + String.join(",", suffixes) + ']';
    }

}
