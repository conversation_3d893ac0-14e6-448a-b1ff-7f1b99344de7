package cz.kpsys.portaro.commons.web;

import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ProvidedWebResolver<E> implements WebResolver<E> {

    @NonNull Provider<@NonNull E> provider;

    @Override
    public Optional<E> tryResolve(@NonNull HttpServletRequest request) {
        return Optional.of(provider.get());
    }
}
