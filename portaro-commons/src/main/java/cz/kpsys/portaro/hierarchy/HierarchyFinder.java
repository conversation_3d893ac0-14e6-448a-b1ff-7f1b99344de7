package cz.kpsys.portaro.hierarchy;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class HierarchyFinder<CTX> {

    @NonNull Function<CTX, Optional<CTX>> getParenCtxFunction;

    public <SEARCHED_ITEM> Optional<SEARCHED_ITEM> findOn(@NonNull CTX ctx, @NonNull Map<CTX, SEARCHED_ITEM> valuesByDeps) {
        return findOn(ctx, valuesByDeps::containsKey, valuesByDeps::get);
    }

    public <SEARCHED_ITEM> Optional<SEARCHED_ITEM> findOn(@NonNull CTX ctx, @NonNull Predicate<@NonNull CTX> itemExistsOnCtxPredicate, @NonNull Function<CTX, @NonNull SEARCHED_ITEM> itemOfCtxGetter) {
        return findOn(ctx, currentLevelDepId -> {
            if (!itemExistsOnCtxPredicate.test(currentLevelDepId)) {
                return Optional.empty();
            }
            return Optional.of(itemOfCtxGetter.apply(currentLevelDepId));
        });
    }

    public <SEARCHED_ITEM> Optional<SEARCHED_ITEM> findOn(@NonNull CTX ctx, @NonNull Function<CTX, Optional<SEARCHED_ITEM>> findItemFunction) {
        Optional<SEARCHED_ITEM> found = findItemFunction.apply(ctx);
        if (found.isPresent()) {
            return found;
        }
        Optional<CTX> parentCtx = getParenCtxFunction.apply(ctx);
        if (parentCtx.isEmpty()) {
            return Optional.empty();
        }
        return findOn(parentCtx.get(), findItemFunction);
    }
}
