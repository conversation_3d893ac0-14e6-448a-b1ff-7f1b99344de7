package cz.kpsys.portaro.logging;

import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.id.UuidGenerator;
import lombok.NonNull;
import org.slf4j.MDC;

import java.util.UUID;

public class UuidMdcTraceIdRepository implements TraceIdRepository {

    private static final String TRACE_ID_KEY_NAME = "traceId";

    @NonNull StringToUuidConverter stringToUuidConverter = StringToUuidConverter.INSTANCE;

    @Override
    public @NonNull UUID get() {
        String fromMdc = MDC.get(TRACE_ID_KEY_NAME);
        if (fromMdc != null) {
            return stringToUuidConverter.convert(fromMdc);
        }
        UUID newTraceId = generateTraceId();
        set(newTraceId);
        return newTraceId;
    }

    @Override
    public void set(UUID traceId) {
        MDC.put(TRACE_ID_KEY_NAME, traceId.toString());
    }

    @Override
    public void create() {
        set(generateTraceId());
    }

    @Override
    public void clear() {
        MDC.remove(TRACE_ID_KEY_NAME);
    }

    private @NonNull UUID generateTraceId() {
        return UuidGenerator.forIdentifier();
    }
}
