package cz.kpsys.portaro.matcher;

import cz.kpsys.portaro.commons.object.CachedProvider;
import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LazyMatcher<E> implements DescriptedMatcher<E> {

    @NonNull CachedProvider<DescriptedMatcher<E>> target;

    public static <E> LazyMatcher<E> create(@NonNull Provider<DescriptedMatcher<E>> target) {
        CachedProvider<DescriptedMatcher<E>> cachedProvider = target.cached();
        return new LazyMatcher<E>(cachedProvider);
    }

    @Override
    public Optional<String> description() {
        if (target.isCacheValid()) {
            return target.get().description();
        }
        return Optional.of("LazyMatcher[-> not-loaded]");
    }

    @Override
    public boolean matches(E item) {
        return target.get().matches(item);
    }
}
