package cz.kpsys.portaro.matcher.string;

import cz.kpsys.portaro.matcher.Matcher;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PrefixStringMatcher implements Matcher<String> {

    @NonNull Set<String> prefixes;

    public static PrefixStringMatcher ofSingle(String prefix) {
        return new PrefixStringMatcher(Set.of(prefix));
    }

    public static PrefixStringMatcher ofAnyOf(String...s) {
        return new PrefixStringMatcher(Set.of(s));
    }

    @Override
    public boolean matches(@NonNull String s) {
        return prefixes.stream()
                .anyMatch(s::startsWith);
    }

}
