package cz.kpsys.portaro.number;

import cz.kpsys.portaro.commons.convert.ConversionException;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.math.BigDecimal;

public class StringToBigDecimalConverter implements Converter<String, BigDecimal> {

    public static final @NonNull StringToBigDecimalConverter INSTANCE = new StringToBigDecimalConverter();

    @Override
    public @NonNull BigDecimal convert(@NonNull String source) {
        try {
            return new BigDecimal(source);
        } catch (Exception e) {
            throw new ConversionException("Cannot convert string '%s' to long: %s".formatted(source, e.getMessage()), e);
        }
    }

}
