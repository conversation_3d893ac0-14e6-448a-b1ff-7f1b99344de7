package cz.kpsys.portaro.resource;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ClasspathResourceContextualResolver<CTX> implements ContextualFunction<String, CTX, Resource> {

    /// Location can be "/index.html"
    public @NonNull Resource getOn(@NonNull String location, @NonNull CTX ctx) {
        return new ClassPathResource(location);
    }
}
