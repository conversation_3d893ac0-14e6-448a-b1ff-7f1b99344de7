package cz.kpsys.portaro.search;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.lang.Nullable;

import java.util.LinkedHashMap;

public record KeysetPaging(

        int pageSize,

        int pageNumber,

        /**
         * LinkedHashMap to emphasize that map must be ordered!
         */
        @Nullable
        @NotEmpty
        @JsonDeserialize(using = LastValuesDeserializer.class) // Needed to retain a correct type of objects.
        @JsonSerialize(using = LastValuesSerializer.class) // Needed to retain a correct type of objects.
        LinkedHashMap<String, Object> lastValues) implements Paging {

    public static KeysetPaging forFirstPage(int pageSize) {
        return new KeysetPaging(pageSize, FIRST_PAGE_NUMBER, null);
    }
    
    @JsonIgnore
    public boolean first() {
        return lastValues == null;
    }
}
