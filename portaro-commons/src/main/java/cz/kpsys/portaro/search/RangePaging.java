package cz.kpsys.portaro.search;

import cz.kpsys.portaro.commons.object.Range;
import jakarta.validation.constraints.Min;
import lombok.NonNull;

import static cz.kpsys.portaro.commons.object.Range.FIRST_PAGE_NUMBER;
import static cz.kpsys.portaro.commons.object.Range.MIN_PAGE_SIZE;

public record RangePaging(@Min(FIRST_PAGE_NUMBER) int pageNumber, @Min(MIN_PAGE_SIZE) int pageSize) implements Paging {

    public RangePaging(@NonNull Range range) {
        this(range.getPageNumber(), range.getPageSize());
    }

    public Range range() {
        return Range.of(pageNumber(), pageSize());
    }

    public RangePaging withPageSize(int pageSize) {
        return new RangePaging(range().withPageSize(pageSize));
    }

    public static RangePaging forAll() {
        return new RangePaging(Range.forAll());
    }

    public static RangePaging of(@Min(FIRST_PAGE_NUMBER) int pageNumber, @Min(MIN_PAGE_SIZE) int pageSize) {
        return new RangePaging(pageNumber, pageSize);
    }

    public static RangePaging forFirstPage(int pageSize) {
        return new RangePaging(Range.forFirstPage(pageSize));
    }

    public static RangePaging forSingle() {
        return forFirstPage(1);
    }

    public static RangePaging forFirstTwo() {
        return forFirstPage(2);
    }

    public RangePaging incrementPage() {
        return new RangePaging(range().incrementPage());
    }

}
