package cz.kpsys.portaro.sorting;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import lombok.NonNull;
import lombok.With;

import java.util.function.Function;

public record SimpleSortingItem(

        @JsonIgnore
        @With
        @NonNull
        String field,

        @JsonIgnore
        @With
        boolean asc,

        @NonNull
        Text text

) implements SortingItem {

    @Override
    public SimpleSortingItem reverse() {
        return withAsc(!asc);
    }

    @Override
    public SimpleSortingItem mapField(Function<String, String> fieldNameConverter) {
        return withField(fieldNameConverter.apply(field()));
    }

    @Override
    public boolean equals(Object o) {
        return o instanceof SortingItem that && asc == that.asc() && field.equals(that.field());
    }

    @Override
    public int hashCode() {
        int result = field.hashCode();
        result = 31 * result + Boolean.hashCode(asc);
        return result;
    }

    @Override
    public @NonNull String toString() {
        return "SortItem " + id();
    }

}
