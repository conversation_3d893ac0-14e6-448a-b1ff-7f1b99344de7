package cz.kpsys.portaro.sorting;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

public record Sorting(List<SortingItem> sortingItems) implements Iterable<SortingItem> {

    public static Sorting of(SortingItem... items) {
        return of(List.of(items));
    }

    public static Sorting of(Collection<SortingItem> items) {
        return new Sorting(items.stream().distinct().toList());
    }

    public static Sorting ofAsc(String... items) {
        return ofAsc(List.of(items));
    }

    public static Sorting ofAsc(Collection<String> items) {
        return of(ListUtil.convert(items, SortingItem::ofSimpleAsc));
    }

    public static Sorting ofDesc(String... items) {
        return of(ListUtil.convert(List.of(items), SortingItem::ofSimpleDesc));
    }

    public static Sorting ofNullable(SortingItem item) {
        return item == null ? empty()
                : of(item);
    }

    public static Sorting empty() {
        return of();
    }

    public Sorting append(Sorting sorting) {
        return of(ListUtil.concat(sortingItems(), sorting.sortingItems()));
    }

    public Sorting appendNotEmptyOptionals(List<Optional<SortingItem>> sorting) {
        return of(ListUtil.concat(sortingItems(), sorting.stream().filter(Optional::isPresent).map(Optional::get).toList()));
    }

    public Sorting modifyFieldNames(Function<String, String> fieldNameConverter) {
        return of(ListUtil.convert(sortingItems, sortingItem -> sortingItem.mapField(fieldNameConverter)));
    }

    public @Nullable SortingItem getByField(@NonNull String field) {
        return sortingItems.stream().filter(item -> item.field().equals(field)).findFirst().orElse(null);
    }

    @Override
    public Iterator<SortingItem> iterator() {
        return sortingItems().iterator();
    }
}
