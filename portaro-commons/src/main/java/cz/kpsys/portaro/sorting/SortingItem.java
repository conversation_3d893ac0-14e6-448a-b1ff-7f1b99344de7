package cz.kpsys.portaro.sorting;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentifiedRecord;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import lombok.NonNull;

import java.util.function.Function;

public interface SortingItem extends LabeledIdentifiedRecord<String> {

    PlaceholderTemplate SEARCH_SORTING_MESSAGE_CODE_TEMPLATE = new PlaceholderTemplate("hledani.razeni.podle.{id}");
    String ASC_PREFIX = "";
    String DESC_PREFIX = "-";
    String DEFAULT_SORTING_ID = "relevance";

    static SortingItem ofSimpleFieldAndOrder(String field, boolean asc) {
        Text text = createDefaultText(field, asc);
        return new SimpleSortingItem(field, asc, text);
    }

    static SortingItem ofSimpleId(String id) {
        String field = parseField(id);
        boolean asc = parseAsc(id);
        return ofSimpleFieldAndOrder(field, asc);
    }

    static SortingItem ofSimpleAsc(String field) {
        return ofSimpleFieldAndOrder(field, true);
    }

    static SortingItem ofSimpleDesc(String field) {
        return ofSimpleFieldAndOrder(field, false);
    }

    static SortingItem ofSimpleIdAsNativeTextAsc(String field) {
        return new SimpleSortingItem(field, true, Texts.ofNative(field));
    }

    static SortingItem ofSimpleIdAsNativeTextDesc(String field) {
        return new SimpleSortingItem(field, false, Texts.ofNative(field));
    }

    static @NonNull Text createDefaultText(String field, boolean asc) {
        String id = (asc ? ASC_PREFIX : DESC_PREFIX) + field;
        String messageCode = SortingItem.SEARCH_SORTING_MESSAGE_CODE_TEMPLATE
                .withParameter("id", id)
                .build();
        return Texts.ofMessageCodedOrNativeOrEmptyNative(messageCode, id);
    }

    private static String parseField(String id) {
        boolean asc = parseAsc(id);
        return StringUtil.removePrefix(id, asc ? ASC_PREFIX : DESC_PREFIX);
    }

    private static boolean parseAsc(String id) {
        return !id.startsWith(DESC_PREFIX);
    }

    @Override
    default String id() {
        return (asc() ? ASC_PREFIX : DESC_PREFIX) + field();
    }

    default boolean isDefault() {
        return id().equals(DEFAULT_SORTING_ID);
    }

    @JsonIgnore
    @NonNull
    String field();

    @JsonIgnore
    boolean asc();

    @JsonIgnore
    SortingItem reverse();

    SortingItem mapField(Function<String, String> fieldNameConverter);
}
