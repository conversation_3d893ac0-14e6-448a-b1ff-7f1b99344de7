package cz.kpsys.portaro.suitability;

import cz.kpsys.portaro.commons.object.ValuableRecord;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import lombok.NonNull;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface Suitability<SUBJ> extends ValuableRecord<@NonNull Double> {

    double ZERO_VALUE = 0.0;
    double NEARLY_ZERO_VALUE_THRESHOLD = 0.01;
    double MAX_VALUE = 1.0;
    Comparator<Suitability<?>> COMPARATOR = Comparator.comparing(Suitability::value);

    SUBJ subject();

    @DecimalMin("0.0")
    @DecimalMax("1.0")
    @Override
    @NonNull
    Double value();

    default boolean isNearlyZero() {
        return value() <= NEARLY_ZERO_VALUE_THRESHOLD;
    }

    default CompositeSuitability<SUBJ> createNewComposedWith(@NonNull Double thisItemWeight, @NonNull String thisItemDescription, WeightedValue... weightedValues) {
        Set<WeightedValue> allWeightedValues = Stream.concat(
                Stream.of(new StaticWeightedValue(value(), thisItemWeight, thisItemDescription)),
                Arrays.stream(weightedValues)
        ).collect(Collectors.toUnmodifiableSet());
        return new CompositeSuitability<>(subject(), allWeightedValues);
    }

}
