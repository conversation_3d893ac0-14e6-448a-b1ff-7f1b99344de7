package cz.kpsys.portaro.token;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.logging.LogOnlyAsDebug;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@LogOnlyAsDebug
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class InsufficientScopeException extends RuntimeException implements UserFriendlyException {

    @NonNull Text text;

    public InsufficientScopeException(@NonNull String message, @NonNull Text text) {
        super(message);
        this.text = text;
    }

}
