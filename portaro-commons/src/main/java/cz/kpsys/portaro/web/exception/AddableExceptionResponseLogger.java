package cz.kpsys.portaro.web.exception;

public interface AddableExceptionResponseLogger extends ExceptionResponseLogger {

    AddableExceptionResponseLogger withDebug(Class<?> clazz);

    AddableExceptionResponseLogger withError(Class<?> clazz);

    <EXC extends Throwable> AddableExceptionResponseLogger withUnwrapping(Class<EXC> clazz, ExceptionResponseLogger.LogLevel ifNullCause);

    <EXC extends Throwable> AddableExceptionResponseLogger withDoubleUnwrapping(Class<EXC> clazz, ExceptionResponseLogger.LogLevel ifNullCause);
}
