package cz.kpsys.portaro.commons.date;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
class DatetimeRangeToTimezonedStringConverterTest {

    DatetimeRangeToTimezonedStringConverter converter = DatetimeRangeToTimezonedStringConverter.ofInternalFormat();

    @ParameterizedTest
    @CsvSource(delimiter = ';', nullValues = "<null>", value = {
            "2025-05-10T14:30:00.987654Z;  2025-05-10T16:00:00Z; [\"2025-05-10T14:30:00.987654Z\",\"2025-05-10T16:00:00Z\")",
            "<null>;                       2030-01-01T00:00:00Z; (-infinity,\"2030-01-01T00:00:00Z\")",
            "2030-01-01T00:00:00Z;         <null>;               [\"2030-01-01T00:00:00Z\",infinity)",
            "<null>;                       <null>;               (-infinity,infinity)",
            "2025-05-10T16:00:00Z;         2025-05-10T00:00:00Z; empty",
            "2030-01-01T01:00:00+01:00;    <null>;               [\"2030-01-01T00:00:00Z\",infinity)",
    })
    void test(String from, String to, String expected) {
        Instant fromInstant = ObjectUtil.elvis(from, Instant::parse);
        Instant toInstant = ObjectUtil.elvis(to, Instant::parse);
        DatetimeRange range = DatetimeRange.of(fromInstant, toInstant);

        String serialized = converter.convert(range);
        assertEquals(expected, serialized);
    }
}