package cz.kpsys.portaro.commons.date;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
class TimezonedStringToDatetimeRangeConverterTest {


    @ParameterizedTest
    @CsvSource(delimiter = ';', nullValues = "<null>", value = {
            "[\"2025-05-10T14:30:00.987654Z\",\"2025-05-10T16:00:00Z\");  2025-05-10T14:30:00.987654Z;  2025-05-10T16:00:00Z",
            "[\"2025-01-01T12:00:00+01\",\"2027-01-02T00:00:00+01\");     2025-01-01T11:00:00Z;         2027-01-01T23:00:00Z",
            "[-infinity,\"2030-01-01T00:00:00Z\");                        <null>;                       2030-01-01T00:00:00Z",
            "[\"2030-01-01T00:00:00Z\",infinity);                         2030-01-01T00:00:00Z;         <null>              ",
            "[-infinity,infinity);                                        <null>;                       <null>              ",
            "(-infinity,infinity);                                        <null>;                       <null>              ",
            "empty;                                                       2025-05-10T16:00:00Z;         2025-05-10T00:00:00Z",
            "[\"2030-01-01T01:00:00+01:00\",infinity);                    2030-01-01T00:00:00Z;         <null>              ",
    })
    void testInternalFormat(String input, String expectedFrom, String expectedTo) {
        TimezonedStringToDatetimeRangeConverter converter = TimezonedStringToDatetimeRangeConverter.ofInternalFormat();
        DatetimeRange deserialized = converter.convert(input);

        Instant expectedFromInstant = ObjectUtil.elvis(expectedFrom, Instant::parse);
        Instant expectedTooInstant = ObjectUtil.elvis(expectedTo, Instant::parse);
        DatetimeRange expected = DatetimeRange.of(expectedFromInstant, expectedTooInstant);

        assertEquals(expected, deserialized);
    }

    @ParameterizedTest
    @CsvSource(delimiter = ';', nullValues = "<null>", value = {
            "[\"2025-05-10 14:30:00.987654Z\",\"2025-05-10 16:00:00Z\");  2025-05-10T14:30:00.987654Z;  2025-05-10T16:00:00Z",
            "[\"2025-01-01 12:00:00+01\",\"2027-01-02 00:00:00+01\");     2025-01-01T11:00:00Z;         2027-01-01T23:00:00Z",
            "[-infinity,\"2030-01-01 00:00:00Z\");                        <null>;                       2030-01-01T00:00:00Z",
            "[\"2030-01-01 00:00:00Z\",infinity);                         2030-01-01T00:00:00Z;         <null>              ",
            "[-infinity,infinity);                                        <null>;                       <null>              ",
            "(-infinity,infinity);                                        <null>;                       <null>              ",
            "empty;                                                       2025-05-10T16:00:00Z;         2025-05-10T00:00:00Z",
            "[\"2030-01-01 01:00:00+01:00\",infinity);                    2030-01-01T00:00:00Z;         <null>              ",
    })
    void testPostgresFormat(String input, String expectedFrom, String expectedTo) {
        TimezonedStringToDatetimeRangeConverter converter = TimezonedStringToDatetimeRangeConverter.ofPostgresFormat();
        DatetimeRange deserialized = converter.convert(input);

        Instant expectedFromInstant = ObjectUtil.elvis(expectedFrom, Instant::parse);
        Instant expectedTooInstant = ObjectUtil.elvis(expectedTo, Instant::parse);
        DatetimeRange expected = DatetimeRange.of(expectedFromInstant, expectedTooInstant);

        assertEquals(expected, deserialized);
    }

}