package cz.kpsys.portaro.token;

import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.object.InMemorySameTypeSettableProvider;
import cz.kpsys.portaro.commons.object.Provider;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.time.Instant;

@Tag("ci")
@Tag("unit")
class CachingAndRefreshingTokenProviderTest {

    public static final Instant TIME_TOKEN_1_EXPIRATION_IS_TOO_FAR = Instant.parse("2023-10-24T13:23:39Z");
    public static final Instant TIME_TOKEN_1_EXPIRATION_SOON = Instant.parse("2023-10-24T14:56:39Z");
    public static final Instant TOKEN_1_VALIDITY = Instant.parse("2023-10-24T14:00:00Z");
    public static final Instant TOKEN_2_VALIDITY = Instant.parse("2023-10-24T16:00:00Z");

    @Test
    void shouldRefreshExpiredToken() {
        AccessCountingStringProvider remoteTokenProvider = new AccessCountingStringProvider();
        TokenExpirationResolver tokenExpirationResolver = token -> {
            if (token.equals("token-1")) {
                return TOKEN_1_VALIDITY;
            }
            return TOKEN_2_VALIDITY;
        };
        InMemorySameTypeSettableProvider<Instant> nowSettableProvider = new InMemorySameTypeSettableProvider<>(TIME_TOKEN_1_EXPIRATION_IS_TOO_FAR);
        CachingAndRefreshingTokenProvider<Object> provider = new CachingAndRefreshingTokenProvider<>(
                Duration.ofMinutes(30),
                ContextIgnoringContextualProvider.of(remoteTokenProvider),
                tokenExpirationResolver
        ).withCustomNowProvider(nowSettableProvider);

        // first access should be ok and saved to cache
        String access1Token = provider.getOn(Void.TYPE);
        Assertions.assertEquals("token-1", access1Token);
        Assertions.assertEquals(1, remoteTokenProvider.accessesCount);

        // second access should get same token again (cached)
        String access2Token = provider.getOn(Void.TYPE);
        Assertions.assertEquals("token-1", access2Token);
        Assertions.assertEquals(1, remoteTokenProvider.accessesCount);

        nowSettableProvider.set(TIME_TOKEN_1_EXPIRATION_SOON);

        // third access should cause call for new token and saved to cache
        String access3Token = provider.getOn(Void.TYPE);
        Assertions.assertEquals("token-2", access3Token);
        Assertions.assertEquals(2, remoteTokenProvider.accessesCount);

        // fourth access should get same token again (cached)
        String access4Token = provider.getOn(Void.TYPE);
        Assertions.assertEquals("token-2", access4Token);
        Assertions.assertEquals(2, remoteTokenProvider.accessesCount);
    }

    private static class AccessCountingStringProvider implements Provider<String> {
        int accessesCount = 0;

        @Override
        public String get() {
            accessesCount++;
            if (accessesCount == 1) {
                return "token-1";
            }
            return "token-2";
        }
    }
}