package cz.kpsys.portaro.database;

import cz.kpsys.portaro.search.Chunk;
import cz.kpsys.portaro.search.KeysetPaging;
import cz.kpsys.portaro.search.Paging;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class KeysetPagingResultSetExtractor<T> implements ResultSetExtractor<Chunk<T, KeysetPaging>> {

    @NonNull RowMapper<T> rowMapper;
    @NonNull KeysetPaging paging;
    @NonNull Map<String, String> sortColumnsNameMapping;
    @NonNull Map<String, String> sortColumnsMapping;
    @NonNull Sorting sorting;

    @Override
    public Chunk<T, KeysetPaging> extractData(ResultSet rs) throws SQLException, DataAccessException {
        List<T> results = new ArrayList<>(paging.pageSize());
        int rowNum = 0;
        LinkedHashMap<String, Object> lastValues = null;
        int nextPageNumber = paging.pageNumber() + 1;

        while (rs.next()) {
            if (lastValues == null) {
                lastValues = new LinkedHashMap<>();
            } else {
                lastValues.clear();
            }
            for (SortingItem sortingItem : sorting) {
                lastValues.put(sortColumnsNameMapping.getOrDefault(sortingItem.field(), sortingItem.field()), DbUtils.getStandardObject(rs, sortColumnsMapping.getOrDefault(sortingItem.field(), sortingItem.field())));
            }

            results.add(rowMapper.mapRow(rs, rowNum++));
        }

        if (results.size() < paging.pageSize()) {
            return Chunk.ofDefinitelyFinal(results);
        }
        return Chunk.of(results, Paging.ofKeySet(paging.pageSize(), nextPageNumber, lastValues));
    }
}
