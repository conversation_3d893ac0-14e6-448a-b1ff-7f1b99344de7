package cz.kpsys.portaro.datatype;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public abstract sealed class Datatype permits NestedDatatype, ScalarDatatype {

    public static final String DATATYPE_LIST = "LIST";
    public static final int NAME_MAX_LENGTH = 30;

    @Getter
    @NotBlank
    @NonNull
    String name;

    public static Datatype of(String datatype, Structure structure) {
        return switch (structure) {
            case SCALAR -> scalar(datatype);
            case LIST -> listOf(datatype);
        };
    }

    /// Deprecated - use one with javaType parameter
    @Deprecated
    public static ScalarDatatype scalar(@NonNull @NotBlank String datatype) {
        Assert.hasText(datatype, "Datatype cannot be blank");
        return new ScalarDatatype(datatype, String.class, null);
    }

    public static ScalarDatatype scalar(@NonNull @NotBlank String datatype, Class<?> javaType) {
        Assert.hasText(datatype, "Datatype cannot be blank");
        return new ScalarDatatype(datatype, javaType, null);
    }

    public static <T extends Identified<ID>, ID> ScalarDatatype scalar(@NonNull @NotBlank String datatype, Class<T> javaType, Class<ID> idType) {
        Assert.hasText(datatype, "Datatype cannot be blank");
        return new ScalarDatatype(datatype, javaType, idType);
    }

    public static ScalarDatatype scalarSplitted(@NonNull @NotBlank String prefix, @NonNull @NotBlank String suffix, Class<?> javaType) {
        Assert.hasText(prefix, "Datatype prefix cannot be blank");
        Assert.hasText(suffix, "Datatype suffix cannot be blank");
        return new ScalarDatatype(prefix + suffix, javaType, null);
    }

    public static ListDatatype listOf(@NonNull ScalarDatatype itemDatatype) {
        return new ListDatatype(Datatype.DATATYPE_LIST, itemDatatype);
    }

    public static ListDatatype listOf(@NonNull @NotBlank String datatype) {
        return new ListDatatype(Datatype.DATATYPE_LIST, scalar(datatype));
    }

    @Override
    public abstract boolean equals(Object obj);
}
