package cz.kpsys.portaro.event;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.user.BasicUser;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;

import java.time.Instant;
import java.util.UUID;

@Value
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class Event implements Identified<UUID> {

    public static final class Codes {
        public static final String DOMAINED_DEPARTMENT_CREATION = "domained-department-creation";
        public static final String DEPARTMENT_CREATION = "department-creation";
        public static final String DEPARTMENT_ACTIVATION = "department-activation";
        public static final String DEPARTMENT_DELETION = "department-deletion";
        public static final String USER_CREATION = "user-creation";
        public static final String USER_ACTIVATION = "user-activation";
        public static final String USER_DELETION = "user-deletion";
        public static final String USER_LOGIN = "user-login";
        public static final String USER_PASSWORD_SETTING = "user-password-setting";
        public static final String RECORD_CREATION = "record-creation";
        public static final String RECORD_ACTIVATION = "record-activation";
        public static final String RECORD_DELETION = "record-deletion";
        public static final String MESSAGE_CREATION = "message-creation";
        public static final String MESSAGE_SENDING = "message-sending";
        public static final String OAI_SERVER_HANDLING_COMPLETION = "oai-server-handling-completion";
        public static final String OAI_SERVER_HANDLING_ERROR = "oai-server-handling-error";
        public static final String SIP2_HANDLING_COMPLETION = "sip2-handling-completion";
        public static final String SIP2_HANDLING_ERROR = "sip2-handling-error";
        public static final String UNIS_API_CALL_COMPLETION = "unis-api-call-completion";
        public static final String UNIS_SYNC_UPDATE = "unis-sync-update";
        public static final String UNIS_SYNC_ERROR = "unis-sync-error";
        public static final String LDAP_SYNC_UPDATE = "ldap-sync-update";
        public static final String LDAP_SYNC_USER_UPDATE = "ldap-sync-user-update";
        public static final String LDAP_SYNC_USER_ADD = "ldap-sync-user-add";
        public static final String LDAP_SYNC_USER_INACTIVATE = "ldap-sync-user-inactivate";
        public static final String UNOB_SYNC_UPDATE = "unob-sync-update";
        public static final String UNOB_SYNC_USER_UPDATE = "unob-sync-user-update";
        public static final String UNOB_SYNC_USER_ADD = "unob-sync-user-add";
        public static final String UNOB_SYNC_USER_INACTIVATE = "unob-sync-user-inactivate";
        public static final String SUTIN_SYNC_UPDATE = "sutin-sync-update";
        public static final String SUTIN_SYNC_USER_UPDATE = "sutin-sync-user-update";
        public static final String SUTIN_SYNC_USER_ADD = "sutin-sync-user-add";
        public static final String SUTIN_SYNC_USER_INACTIVATE = "sutin-sync-user-inactivate";
        public static final String RECORD_COLLECTION_CREATION = "record-collection-creation";
        public static final String RECORD_COLLECTION_UPDATE = "record-collection-update";
        public static final String RECORD_COLLECTION_ITEM_INSERTION = "record-collection-item-insertion";
        public static final String RECORD_COLLECTION_ITEM_REMOVAL = "record-collection-item-removal";
        public static final String IMPORT_EXTERNAL_RECORD = "import-external-record";
        public static final String IMPORT_EXTERNAL_RECORD_PROBLEM = "import-external-record-problem";
        public static final String LOAN_MIGRATION = "loan-migration";
    }

    public static final int DATA_MAX_LENGTH = 2000;

    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @NonNull
    String code;

    @NonNull
    Instant createDate;

    @Nullable
    String sessionId;

    @Nullable
    BasicUser initiatorUser;

    @Nullable
    Department department;

    @Size(min = 1, max = Event.DATA_MAX_LENGTH)
    @NullableNotBlank
    String data;

    @Nullable
    UUID subjectId;

    public static Event createNew(@NonNull String code,
                                  @Nullable String sessionId,
                                  @Nullable BasicUser initiatorUser,
                                  @Nullable Department department,
                                  @Nullable String data,
                                  @Nullable UUID subjectId) {
        return new Event(
                UuidGenerator.forIdentifier(),
                code,
                Instant.now(),
                sessionId,
                initiatorUser,
                department,
                data,
                subjectId
        );
    }

    public static Event createNew(@NonNull UUID id,
                                  @NonNull String code,
                                  @Nullable String sessionId,
                                  @Nullable BasicUser initiatorUser,
                                  @Nullable Department department,
                                  @Nullable String data,
                                  @Nullable UUID subjectId) {
        return new Event(
                id,
                code,
                Instant.now(),
                sessionId,
                initiatorUser,
                department,
                data,
                subjectId
        );
    }

    public static Object createErrorObject(Exception e, @NonNull Object additionalData) {
        return new ErrorData(
                e.getClass().getSimpleName(),
                e.getMessage(),
                additionalData
        );
    }

    record ErrorData(@NonNull String exceptionName, @Nullable String message, @NonNull Object additionalData) {
    }

}
