package cz.kpsys.portaro.event;

import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class EventToEntityConverter implements Converter<Event, EventEntity> {

    @Override
    public EventEntity convert(@NonNull Event event) {
        return new EventEntity(
                event.getId(),
                event.getCode(),
                event.getCreateDate(),
                event.getSessionId(),
                event.getInitiatorUser().isEvided() ? event.getInitiatorUser().getId() : null,
                event.getDepartment().getId(),
                event.getData(),
                event.getSubjectId()
        );
    }

}
