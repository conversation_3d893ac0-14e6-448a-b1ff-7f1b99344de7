package cz.kpsys.portaro.localization;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.databasestructure.LocalizationsDb;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.time.Instant;

@Entity
@Table(name = LocalizationsDb.LOKALIZACE.TABLE)
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
@Getter
@IdClass(LocalizationDbId.class)
public class LocalizationEntity implements Identified<String> {

    @Id
    @Column(name = LocalizationsDb.LOKALIZACE.ID_LOKALIZACE)
    @NonNull
    String id;

    @Id
    @NonNull
    @Column(name = LocalizationsDb.LOKALIZACE.FK_PUJC)
    Integer departmentId;

    @NonNull
    @Column(name = LocalizationsDb.LOKALIZACE.JE_ZMENA)
    Boolean changed;

    @Nullable
    @Column(name = LocalizationsDb.LOKALIZACE.TEXT_CZE)
    String textCze;

    @Nullable
    @Column(name = LocalizationsDb.LOKALIZACE.TEXT_ENG)
    String textEng;

    @Nullable
    @Column(name = LocalizationsDb.LOKALIZACE.TEXT_GER)
    String textGer;

    @Nullable
    @Column(name = LocalizationsDb.LOKALIZACE.TEXT_FRA)
    String textFra;

    @Nullable
    @Column(name = LocalizationsDb.LOKALIZACE.TEXT_SVK)
    String textSvk;

    @Nullable
    @Column(name = LocalizationsDb.LOKALIZACE.TEXT_POL)
    String textPol;

    @Nullable
    @Column(name = LocalizationsDb.LOKALIZACE.TEXT_BGR)
    String textBgr;

    @Nullable
    @Column(name = LocalizationsDb.LOKALIZACE.DATCAS)
    Instant timestamp;
}
