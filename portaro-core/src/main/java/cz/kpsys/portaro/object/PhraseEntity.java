package cz.kpsys.portaro.object;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Ordered;
import cz.kpsys.portaro.commons.object.Valuable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static cz.kpsys.portaro.databasestructure.AcceptableValuesDb.DEF_FRAZE.*;

@Entity
@Table(name = DEF_FRAZE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class PhraseEntity implements Identified<Integer>, Ordered, Valuable<String> {

    @Id
    @Column(name = ID_FRAZE)
    @EqualsAndHashCode.Include
    Integer id;

    @Column(name = FK_FRAZE)
    String phraseGroupId;

    @Column(name = PORADI)
    Integer order;

    @Column(name = SLOVO)
    String value;

}
