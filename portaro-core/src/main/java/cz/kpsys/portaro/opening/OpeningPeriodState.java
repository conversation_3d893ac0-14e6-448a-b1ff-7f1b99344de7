package cz.kpsys.portaro.opening;

import cz.kpsys.portaro.commons.date.NotEmptyBoundedDatetimeRange;
import lombok.NonNull;

import java.time.Instant;

public record OpeningPeriodState(
        boolean known,
        boolean opened,
        NotEmptyBoundedDatetimeRange period
) {

    public static OpeningPeriodState ofKnown(boolean opened, @NonNull NotEmptyBoundedDatetimeRange period) {
        return new OpeningPeriodState(true, opened, period);
    }

    public static OpeningPeriodState ofUnknown() {
        return new OpeningPeriodState(false, false, null);
    }

    @NonNull
    public Instant getExclusiveEndDate() {
        if (period == null) {
            throw new UnsupportedOperationException("Opening period state is unknown, cannot call getExclusiveEndDate()");
        }
        return period.exclusiveToDate();
    }

    @Override
    public String toString() {
        return String.format(opened ? "Opened (%s)" : "Closed (%s)", period);
    }
}
