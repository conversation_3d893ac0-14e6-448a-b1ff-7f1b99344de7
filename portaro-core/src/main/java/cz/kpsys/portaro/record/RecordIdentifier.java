package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.id.UuidGenerator;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import lombok.With;
import org.springframework.util.Assert;

import java.util.Comparator;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Stream;

import static cz.kpsys.portaro.commons.util.StringUtil.addPrefixIfNotBlankOrNull;
import static cz.kpsys.portaro.commons.util.StringUtil.notNullString;

@With
public record RecordIdentifier(
        @NonNull UUID id,
        @Nullable UUID masterId
) implements Comparable<RecordIdentifier> {

    public static final Comparator<RecordIdentifier> COMPARATOR = Comparator.comparing(RecordIdentifier::id);

    public static RecordIdentifier of(@NonNull UUID id) {
        return new RecordIdentifier(id, null);
    }

    public static RecordIdentifier ofMergedRecord(@NonNull UUID id, @NonNull UUID masterRecordId) {
        Assert.state(!id.equals(masterRecordId), "Merged record cannot have same id as master record");
        return new RecordIdentifier(id, masterRecordId);
    }

    public Stream<UUID> streamIds() {
        if (masterId() == null) {
            return Stream.of(id());
        }
        return Stream.of(id(), masterId());
    }

    @Override
    public int compareTo(@NonNull RecordIdentifier o) {
        return COMPARATOR.compare(this, o);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof RecordIdentifier other)) {
            return false;
        }
        if (id.equals(other.id)) {
            return true;
        }
        if (id.equals(other.masterId)) {
            return true;
        }
        if (other.id.equals(masterId)) {
            return true;
        }
        if (ObjectUtil.nullSafeEquals(masterId, other.masterId)) {
            return true;
        }
        return false;
    }

    @Override
    public int hashCode() {
        int result = id.hashCode();
        result = 31 * result + Objects.hashCode(masterId);
        return result;
    }

    @Override
    public String toString() {
        return "Rec[" + abbr() + notNullString(addPrefixIfNotBlankOrNull(masterId(), ", master=")) + "]";
    }

    public String abbr() {
        return UuidGenerator.abbr6(id);
    }

    public String abbrDelim() {
        return abbr() + ":";
    }

}
