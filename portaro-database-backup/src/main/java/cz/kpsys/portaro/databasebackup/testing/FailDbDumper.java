package cz.kpsys.portaro.databasebackup.testing;

import cz.kpsys.portaro.databasebackup.DbDumper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.io.input.NullInputStream;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.util.concurrent.CompletableFuture;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class FailDbDumper implements DbDumper {

    public static FailDbDumper start() {
        return new FailDbDumper(new FailBackupProcess());
    }


    @NonNull Process process;

    @Override
    public InputStream getBackupStream() {
        return new BufferedInputStream(process.getInputStream());
    }

    @Override
    public CompletableFuture<Process> onExit() {
        return process.onExit();
    }

    @Override
    public String getBackupFilename() {
        return "fail.dump";
    }

    @Override
    public void close() {
        // NOOP
    }


    private static class FailBackupProcess extends TestProcess {

        protected FailBackupProcess() {
            super(1);
        }

        @Override
        public InputStream getInputStream() {
            return new NullInputStream();
        }

    }

}
