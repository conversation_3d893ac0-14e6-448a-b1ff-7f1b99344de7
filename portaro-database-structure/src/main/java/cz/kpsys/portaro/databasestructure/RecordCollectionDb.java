package cz.kpsys.portaro.databasestructure;

@SuppressWarnings("TypeName")
public class RecordCollectionDb {
    public static class RECORD_COLLECTION {
        public static final String TABLE = "record_collection";
        public static final String ID = "id";
        public static final String PARENT_ID = "parent_id";
        public static final String DEPARTMENT_ID = "department_id";
        public static final String NAME = "name";
        public static final String ORDER_NUMBER = "order_number";
        public static final String CREATION_EVENT_ID = "creation_event_id";
        public static final String NOTE = "note";
        public static final String RECORD_COLLECTION_CATEGORY_ID = "record_collection_category_id";
        public static final String LAST_UPDATE_EVENT_ID = "last_update_event_id";
    }

    public static class RECORD_COLLECTION_ITEM {
        public static final String TABLE = "record_collection_item";
        public static final String ID = "id";
        public static final String RECORD_COLLECTION_ID = "record_collection_id";
        public static final String RECORD_ID = "record_id";
        public static final String ORDER_NUMBER = "order_number";
    }

    public static class USER_RECORD_COLLECTION {
        public static final String TABLE = "user_record_collection";
        public static final String ID = "id";
        public static final String USER_ID = "user_id";
        public static final String RECORD_COLLECTION_ID = "record_collection_id";
    }

    public static class RECORD_COLLECTION_CATEGORY {
        public static final String TABLE = "record_collection_category";
        public static final String ID = "id";
        public static final String TYPE = "type";
        public static final String DESCRIPTION = "description";

    }
}
