package cz.kpsys.portaro.databasestructure;

@SuppressWarnings("TypeName")
public class RecordOperationDb {

    public static class DEF_VYKONY {
        public static final String TABLE = "def_vykony";
        public static final String ID_VYKON = "id_vykon";
        public static final String POPIS = "popis";
    }

    public static class RECORD_OPERATION {
        public static final String TABLE = "record_operation";
        public static final String ID = "id";
        public static final String TYPE_ID = "type_id";
        public static final String RECORD_ID = "record_id";
        public static final String FOND_ID = "fond_id";
        public static final String FK_UZIV = "fk_uziv";
        public static final String DEPARTMENT_ID = "department_id";
        public static final String CREATION_DATE = "creation_date";
        public static final String EVENT_ID = "event_id";
    }

}
