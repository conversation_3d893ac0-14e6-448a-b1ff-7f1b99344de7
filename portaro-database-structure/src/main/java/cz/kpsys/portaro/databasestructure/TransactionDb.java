package cz.kpsys.portaro.databasestructure;

@SuppressWarnings("TypeName")
public class TransactionDb {

    public static class PLATBY {
        public static final String PLATBY = "platby";
        public static final String ID_PLATBY = "id_platby";
        public static final String FK_PLACENI = "fk_platby_placeni";
        public static final String FK_VRACENI = "fk_platby_vraceni";
        public static final String CASTKA = "castka";
        public static final String DATUM = "datum";
        public static final String FK_POPL = "fk_popl";
        public static final String FK_UZIV_OWNER = "fk_uziv_vlastnik";
        public static final String FK_PUJC = "fk_pujc";
        public static final String FK_UZIV_CASHIER = "fk_uziv";
        public static final String SERVIS = "servis";
        public static final String VRACENO = "vraceno";
        public static final String POZNAMKA = "poznamka";
        public static final String TARGET_DEPARTMENT_ID = "target_department_id";

        public static final String SEQ_ID_PLATBY_FB = "seq_id_platby";
        public static final String SEQ_ID_PLATBY_PG = "platby_id_platby_seq";
    }

    public static class DEF_POPL {
        public static final String TABLE = "def_popl";
        public static final String ID_POPL = "id_popl";
        public static final String NAZEV = "nazev";
        public static final String JE_KREDITNI = "je_kreditni";
        public static final String FK_TYPPOPL = "fk_typpopl";
        public static final String PORADI = "poradi";
        public static final String VAT_RATE = "vat_rate";
    }

    public static class DEF_MENY {
        public static final String DEF_MENY = "def_meny";
        public static final String ID_MENY = "id_meny";
        public static final String NAZEV = "nazev";
        public static final String KURZ = "kurz";
        public static final String PORADI = "poradi";
    }
}
