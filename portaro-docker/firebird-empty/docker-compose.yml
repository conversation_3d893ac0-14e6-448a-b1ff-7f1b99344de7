version: '3'

services:

  firebird:
    image: docker.kpsys.cz/kpsys/firebird-empty:latest
    ports:
      - 3050:3050
    volumes:
      - ./verbis_2_0_empty.fdb:/db.fdb

  appserver:
    image: docker.kpsys.cz/kpsys/appserver:latest
    ports:
      - 8182:8182
    environment:
      - DATABASE_URL=jdbc:firebirdsql:firebird:db_verbis?lc_ctype=UTF8
      - APPSERVER_INDEXING_ENABLED=false
      - APPSERVER_SCHEDULED_TASKS_ENABLED=false
      - INI_KPWIN__LicenceKey=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W3N5Dt3EMIig5kiN_sqDzh-0dj6ytN3rZ6Er2kmuIBwiBpGcXRRaTijvRstb-rN5oEd8_mcbiZNdQ2vnOcczLVS3aZawrrJ2KxdRHEDlaXbQJ8sSSYnAVxF3ZGtg-1z-f2sleZWo8nI6JvJ9OlobgM7C-noj7W-yUgy6qG-xnjbptKKTI2ZtOp9xdn5k0vAFPn6t3GOqp6FttjwCB0lrHuiCTEg56eG37jH-InXeHAYoZ8XyN6jSReOrfESKDZG2VlrtiC8u7DMO3GivCOXaFdETehAXGyIINO4FruQU4WkHGyIGzVwn8UV6_1JEW6lD4qdt9fGOpXO6w_6vidqxyw
    depends_on:
      - firebird

  portaro:
    image: docker.kpsys.cz/kpsys/portaro:develop
    ports:
      - 80:80
    environment:
      - APPSERVER_URL=http://appserver:8182
      - DATABASE_TYPE=firebird
      - DATABASE_HOST=firebird
      - DATABASE_PORT=3050
      - DATABASE_FILE=db_verbis
      - PORTARO_INI__KPWIN__LicenceKey=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W3N5Dt3EMIig5kiN_sqDzh-0dj6ytN3rZ6Er2kmuIBwiBpGcXRRaTijvRstb-rN5oEd8_mcbiZNdQ2vnOcczLVS3aZawrrJ2KxdRHEDlaXbQJ8sSSYnAVxF3ZGtg-1z-f2sleZWo8nI6JvJ9OlobgM7C-noj7W-yUgy6qG-xnjbptKKTI2ZtOp9xdn5k0vAFPn6t3GOqp6FttjwCB0lrHuiCTEg56eG37jH-InXeHAYoZ8XyN6jSReOrfESKDZG2VlrtiC8u7DMO3GivCOXaFdETehAXGyIINO4FruQU4WkHGyIGzVwn8UV6_1JEW6lD4qdt9fGOpXO6w_6vidqxyw
    depends_on:
      - firebird
