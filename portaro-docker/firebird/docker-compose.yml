version: '3'

services:

  firebird:
    image: docker.kpsys.cz/kpsys/firebird:latest
    ports: 
      - 3050:3050
    volumes: 
      - ./data/kpsys_develop_2_0.fdb:/db.fdb
      - /var/run/docker.sock:/var/run/docker.sock
#    environment:
#      - SQLKPWIN_PASSWORD # Set specific password for user S<PERSON><PERSON><PERSON>WI<PERSON>
#      - OPAC_PASSWORD # Set specific password for user OPAC
#      - KATALOG_PASSWORD # Set specific password for user K<PERSON><PERSON>OG
#      - DATABASE_NAME=kpsys_develop_2_0 # Set specific DB name
#      - EXTERNAL_USER=USER # Set new user name
#      - EXTERNAL_PASSWORD=Passw0rd # Set new user password
        
  appserver:
    image: docker.kpsys.cz/kpsys/appserver:develop
    ports: 
      - 8182:8182
    volumes:
      - ./workspace/:/workspace
      - /var/run/docker.sock:/var/run/docker.sock
    environment:                                                                                                       
      - DATABASE_URL=jdbc:firebirdsql:firebird:kpsys_develop_2_0?lc_ctype=UTF8                                    
    restart: always    
    depends_on:
      - firebird

  portaro:
    image: docker.kpsys.cz/kpsys/portaro
    ports:
      - 8080:80
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - APPSERVER_URL=http://appserver:8182
    depends_on:
      - appserver
      - proxy
    restart: always

  proxy:
    image: nginx
    ports:
      - 8888:80
      - 443:443
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /home/<USER>/nginx:/etc/nginx/conf.d:ro
    restart: always

  watchtower:
    image: docker.kpsys.cz/v2tec/watchtower
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command: --schedule "0 0 2 * * *" --cleanup
    restart: always
