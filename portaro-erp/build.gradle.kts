dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.junit.jupiter:junit-jupiter-params:+")
    testImplementation("org.mockito:mockito-core:+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-calendar"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-grid"))
    implementation(project(":portaro-record-grid"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-web"))
    implementation(project(":portaro-finance"))

    implementation("org.springframework:spring-context:6.+")
    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-tx:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework.boot:spring-boot:3.+")

    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("org.slf4j:slf4j-api:+")
    implementation("com.fasterxml.jackson.core:jackson-core:2.+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
    implementation("io.swagger.core.v3:swagger-annotations-jakarta:+")
}
