package cz.kpsys.portaro.erp;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheDeleter;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.ProviderByIdProvider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.data.assets.ErpAssetsController;
import cz.kpsys.portaro.erp.data.days.DayApiController;
import cz.kpsys.portaro.erp.data.homepage.ErpHomepageLinkCardsConfigurationResponseProvider;
import cz.kpsys.portaro.erp.data.homepage.SutorHomepagePageDataProvider;
import cz.kpsys.portaro.erp.data.homepage.web.ErpHomepageLinkCardsConfigurationResponse;
import cz.kpsys.portaro.erp.employee.detail.EmployeeDetailApiController;
import cz.kpsys.portaro.erp.employee.detail.EmployeeDetailLoader;
import cz.kpsys.portaro.erp.employee.monthattendance.MonthAttendanceStateSetter;
import cz.kpsys.portaro.erp.employee.monthattendance.MonthAttendanceStateSetterApiController;
import cz.kpsys.portaro.erp.employee.workattendance.*;
import cz.kpsys.portaro.erp.employee.workattendance.web.WorkAttendanceApiController;
import cz.kpsys.portaro.erp.employee.workattendance.web.WorkAttendanceToResponseMapper;
import cz.kpsys.portaro.erp.fondpermissions.FondPermissionsApiController;
import cz.kpsys.portaro.erp.reports.projectfinancialoverview.ProjectFinancialOverviewLoader;
import cz.kpsys.portaro.erp.reports.projectfinancialoverview.web.ProjectFinancialOverviewApiController;
import cz.kpsys.portaro.erp.reports.reportoptimisation.GroupedProjectItemsLoader;
import cz.kpsys.portaro.erp.reports.reportoptimisation.ReportOptimisationItemOverviewLoader;
import cz.kpsys.portaro.erp.reports.reportoptimisation.ReportOptimisationOverallOverviewLoader;
import cz.kpsys.portaro.erp.reports.reportoptimisation.ReportsOptimisationFinancialSumsLoader;
import cz.kpsys.portaro.erp.reports.reportoptimisation.web.ReportOptimisationApiController;
import cz.kpsys.portaro.erp.reports.reportreferences.ReportReferencesReportedLoader;
import cz.kpsys.portaro.erp.reports.reportreferences.web.ReportReferencesApiController;
import cz.kpsys.portaro.grid.RecordRow;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeLoader;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.grid.edit.RecordGridRowEditor;
import cz.kpsys.portaro.record.load.RecordDayIdLoader;
import cz.kpsys.portaro.record.search.restriction.FieldTypeSearchField;
import cz.kpsys.portaro.record.sec.CurrentAuthFondsLoader;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.io.Resource;

import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ErpConfig {

    @NonNull SettingLoader settingLoader;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> nonDetailedRecordSearchSqlLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchSqlLoader;
    @NonNull WorkAttendanceReportLoader workAttendanceReportLoader;
    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull Map<String, ContextualFunction<UserAuthentication, Department, ?>> pageDataProviders;
    @NonNull Provider<@NonNull ZoneId> httpRequestsTimeZoneProvider;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull Provider<@NonNull Fond> dayFondProvider;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull Converter<List<UUID>, List<? extends Record>> idsToRecordsConverter;
    @NonNull RecordDayIdLoader recordDayIdLoader;
    @NonNull RecordDayLoader recordDayLoader;
    @NonNull ViewableItemsTypedConverter<Record, RecordRow> recordsToRecordRowsConverter;
    @NonNull FieldTypeLoader fieldTypeLoader;
    @NonNull CurrentAuthFondsLoader currentAuthShowableFondsLoader;
    @NonNull CurrentAuthFondsLoader currentAuthEditableFondsLoader;
    @NonNull Function<List<String>, ? extends ContextualProvider<Department, @NonNull Resource>> firstMatchingResourceResolverFactory;
    @NonNull RecordGridRowEditor recordGridRowEditor;
    @NonNull SearchService<InternalSearchResult<String, MapBackedParams, RangePaging>, MapBackedParams> recordSearchService;
    @NonNull Converter<List<String>, List<Record>> detailedSearchResultConverter;
    @NonNull CacheDeleter<Record> recordCache;
    @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader;
    @NonNull IdAndIdsLoadable<User, UUID> userByUuidLoader;
    @NonNull Provider<@Nullable Fond> userPersonAuthorityFondProvider;

    @Bean
    public Provider<@NonNull ZoneId> erpRecordsTimeZoneProvider() {
        return StaticProvider.of(CoreConstants.CZECH_TIME_ZONE_ID);
    }

    @Bean
    public WorkAttendanceApiController workAttendanceApiController() {
        return new WorkAttendanceApiController(
                recordLoader,
                activitySearchLoader(),
                httpRequestsTimeZoneProvider,
                workAttendanceReportLoader,
                workAttendanceToResponseMapper(),
                recordDayLoader,
                rangedEmploymentStatusByUserRecordIdLoader(),
                rangedSalaryByEmploymentStatusRecordIdLoader(),
                rangedWorkCommitmentByUserRecordIdLoader(),
                userPersonAuthorityFondProvider.throwingWhenNull()
        );
    }

    @Bean
    public DayApiController dayApiController() {
        return new DayApiController(
                recordDayHelper()
        );
    }

    @Bean
    public RecordDayHelper recordDayHelper() {
        return new RecordDayHelper(
                recordEditationFactory,
                recordEditationHelper,
                recordDayIdLoader,
                idsToRecordsConverter,
                dayFondProvider,
                recordEntryFieldTypeIdResolver,
                httpRequestsTimeZoneProvider
        );
    }

    @Bean
    public ActivitySearchLoader activitySearchLoader() {
        return new ActivitySearchLoader(
                erpRecordsTimeZoneProvider(),
                activityBySalaryIdSearchLoader(),
                workReportFondProvider(),
                absenceReportFondProvider(),
                operatedMachineReportFondProvider(),
                overheadReportFondProvider(),
                machineReportFondProvider(),
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public RangedEmploymentStatusByUserRecordIdLoader rangedEmploymentStatusByUserRecordIdLoader() {
        return new RangedEmploymentStatusByUserRecordIdLoader(
                employmentStatusByUserIdSearchLoader(),
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public RangedWorkCommitmentByUserRecordIdLoader rangedWorkCommitmentByUserRecordIdLoader() {
        return new RangedWorkCommitmentByUserRecordIdLoader(rangedEmploymentStatusByUserRecordIdLoader());
    }

    @Bean
    public RangedSalaryByEmploymentStatusRecordIdLoader rangedSalaryByEmploymentStatusRecordIdLoader() {
        return new RangedSalaryByEmploymentStatusRecordIdLoader(
                salaryByEmploymentStatusIdSearchLoader(),
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public WorkAttendanceToResponseMapper workAttendanceToResponseMapper() {
        return new WorkAttendanceToResponseMapper();
    }

    // TODO: create named fond (like day fond) ?
    @Bean
    public Provider<@NonNull Fond> operatedMachineReportFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 20).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> workReportFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 21).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> machineReportFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 22).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> optimisationReportFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 24).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> absenceReportFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 26).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> costReportParentFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 27).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> overheadReportFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 37).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> rechargedReportFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 39).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> salaryRootFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 8).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> monthlySalaryFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 81).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> hourlySalaryFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 82).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> employmentStatusFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 88).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> jobFileFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 83).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> workPositionFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 70).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> professionWorkCatalogFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 16).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> propertyWorkCatalogLinkFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 86).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> personWorkCatalogLinkFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 87).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> divisionFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 60).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> projectBusinessItemFond() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 75).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> projectWorkBusinessItemFond() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 76).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> projectInstallationBusinessItemFond() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 78).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> monthAttendanceFondProvider() {
        return ProviderByIdProvider.ofStaticId(fondLoader, 89).throwingWhenNull().cached();
    }

    @Bean
    public LinkedRecordSearchLoader employmentStatusByUserIdSearchLoader() {
        return new LinkedRecordSearchLoader(
                RecordSutinSpecificFields.PersonLink.Main.TYPE_ID,
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                employmentStatusFondProvider(),
                fieldTypeLoader,
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public LinkedRecordSearchLoader salaryByEmploymentStatusIdSearchLoader() {
        return new LinkedRecordSearchLoader(
                RecordSutinSpecificFields.FondSalaryCost.EmploymentStatus.Main.TYPE_ID,
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                salaryRootFondProvider(),
                fieldTypeLoader,
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public LinkedRecordSearchLoader activityBySalaryIdSearchLoader() {
        return new LinkedRecordSearchLoader(
                RecordSutinSpecificFields.WorkerSalary.Main.TYPE_ID,
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                costReportParentFondProvider(),
                fieldTypeLoader,
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public LinkedRecordSearchLoader personWorkCatalogLinkByUserIdSearchLoader() {
        return new LinkedRecordSearchLoader(
                RecordSutinSpecificFields.FondWorkCatalog.Person.Main.TYPE_ID,
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                personWorkCatalogLinkFondProvider(),
                fieldTypeLoader,
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public LinkedRecordSearchLoader propertyWorkCatalogLinkByRecordIdSearchLoader() {
        return new LinkedRecordSearchLoader(
                RecordSutinSpecificFields.FondWorkCatalog.Property.Name.TYPE_ID,
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                propertyWorkCatalogLinkFondProvider(),
                fieldTypeLoader,
                fieldTypeSearchFieldLoader
        );
    }

    // Report optimisation
    @Bean
    public Provider<@NonNull List<Fond>> reportOptimisationFondsProvider() {
        return Provider.of(() -> List.of(
                operatedMachineReportFondProvider().get(),
                workReportFondProvider().get(),
                machineReportFondProvider().get(),
                operatedMachineReportFondProvider().get(),
                rechargedReportFondProvider().get(),
                optimisationReportFondProvider().get()
        )).cached();
    }

    @Bean
    public Provider<@NonNull List<Fond>> projectBusinessItemFondsProvider() {
        return () -> List.of(
                projectWorkBusinessItemFond().get(),
                projectInstallationBusinessItemFond().get()
        );
    }

    @Bean
    public ReportOptimisationItemOverviewLoader reportOptimisationDayOverviewLoader() {
        return new ReportOptimisationItemOverviewLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                reportOptimisationFondsProvider(),
                optimisationReportFondProvider(),
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public ReportOptimisationOverallOverviewLoader reportOptimisationOverallOverviewLoader() {
        return new ReportOptimisationOverallOverviewLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                reportOptimisationFondsProvider(),
                optimisationReportFondProvider(),
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public ReportsOptimisationFinancialSumsLoader reportsOptimisationFinancialSumsLoader() {
        return new ReportsOptimisationFinancialSumsLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                reportOptimisationFondsProvider(),
                optimisationReportFondProvider(),
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public GroupedProjectItemsLoader groupedProjectItemsLoader() {
        return new GroupedProjectItemsLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                recordsToRecordRowsConverter,
                projectBusinessItemFondsProvider(),
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public ReportOptimisationApiController reportOptimisationApiController() {
        return new ReportOptimisationApiController(
                reportOptimisationDayOverviewLoader(),
                reportOptimisationOverallOverviewLoader(),
                reportsOptimisationFinancialSumsLoader(),
                groupedProjectItemsLoader()
        );
    }

    // Report references
    @Bean
    public ReportReferencesReportedLoader reportReferencesReportedValuesLoader() {
        return new ReportReferencesReportedLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                reportOptimisationFondsProvider(),
                optimisationReportFondProvider(),
                fieldTypeSearchFieldLoader
        );
    }

    @Bean
    public ReportReferencesApiController reportReferencesApiController() {
        return new ReportReferencesApiController(
                reportReferencesReportedValuesLoader()
        );
    }

    // Project financial overview
    @Bean
    public ProjectFinancialOverviewLoader projectFinancialOverviewLoader() {
        return new ProjectFinancialOverviewLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                workReportFondProvider()
        );
    }

    @Bean
    public ProjectFinancialOverviewApiController projectFinancialOverviewApiController() {
        return new ProjectFinancialOverviewApiController(
                projectFinancialOverviewLoader()
        );
    }

    // Fond permissions
    @Bean
    public FondPermissionsApiController fondPermissionsApiController() {
        return new FondPermissionsApiController(
                currentAuthShowableFondsLoader,
                currentAuthEditableFondsLoader
        );
    }

    // ERP assets
    @Bean
    public ErpAssetsController erpAssetsController() {
        return new ErpAssetsController(
                firstMatchingResourceResolverFactory.apply(List.of("custom:/design/erp-logo-no-text.svg", "classpath:/erp/erp-logo-no-text.svg")),
                firstMatchingResourceResolverFactory.apply(List.of("custom:/design/erp-logo.svg", "classpath:/erp/erp-logo.svg")),
                firstMatchingResourceResolverFactory.apply(List.of("custom:/design/erp-text-logo.svg", "classpath:/erp/erp-text-logo.svg"))
        );
    }

    // Month attendance
    @Bean
    public MonthAttendanceStateSetter monthAttendanceStateSetter() {
        return new MonthAttendanceStateSetter(
                recordGridRowEditor,
                recordSearchService,
                detailedSearchResultConverter,
                recordCache
        );
    }

    @Bean
    public MonthAttendanceStateSetterApiController monthAttendanceApiController() {
        return new MonthAttendanceStateSetterApiController(
                monthAttendanceStateSetter()
        );
    }

    // Employee detail
    @Bean
    public EmployeeDetailLoader employeeDetailLoader() {
        return new EmployeeDetailLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                fieldTypeSearchFieldLoader,
                recordsToRecordRowsConverter,
                userByUuidLoader,
                employmentStatusFondProvider(),
                jobFileFondProvider()
        );
    }

    @Bean
    public EmployeeDetailApiController employeeDetailApiController() {
        return new EmployeeDetailApiController(
                employeeDetailLoader()
        );
    }

    // Register page data providers
    @Bean
    public ContextualProvider<Department, ErpHomepageLinkCardsConfigurationResponse> sutorHomepageLinkCardsConfigurationResponseContextualProvider() {
        return new ErpHomepageLinkCardsConfigurationResponseProvider(
                contextHierarchyLoader,
                nonDetailedRecordSearchSqlLoader,
                fondLoader,
                settingLoader.getDepartmentedProvider(ErpSettingsKeys.ERP_HOMEPAGE_LINK_CARDS)
        );
    }

    @Bean
    public ContextualFunction<UserAuthentication, Department, ?> sutorHomepagePageDataProvider() {
        return new SutorHomepagePageDataProvider(
                sutorHomepageLinkCardsConfigurationResponseContextualProvider()
        );
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        pageDataProviders.put("sutor-homepage", sutorHomepagePageDataProvider());
    }
}