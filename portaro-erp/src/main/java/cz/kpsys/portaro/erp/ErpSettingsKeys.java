package cz.kpsys.portaro.erp;

import cz.kpsys.portaro.erp.dataview.ErpCustomDataViewPagesHolder;
import cz.kpsys.portaro.erp.homepage.ErpHomepageLinkCardsHolder;
import cz.kpsys.portaro.setting.SettingKey;
import lombok.NonNull;

public class ErpSettingsKeys {

    public static final String SECTION_ERP = "erp";
    public static final String SECTION_ERP_WORK_REPORT = "erp.workReport";

    public static final SettingKey<@NonNull ErpHomepageLinkCardsHolder> ERP_HOMEPAGE_LINK_CARDS = new SettingKey<>(SECTION_ERP, "homepageLinkCards");
    public static final SettingKey<@NonNull ErpCustomDataViewPagesHolder> ERP_CUSTOM_DATA_VIEW_PAGES = new SettingKey<>(SECTION_ERP, "customDataViewPages");

    public static final SettingKey<@NonNull Boolean> WORK_REPORT_ENABLED = new SettingKey<>(SECTION_ERP_WORK_REPORT, "enabled");
}
