package cz.kpsys.portaro.erp;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.object.AcceptableValueGroupItem;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.value.*;

import java.math.BigDecimal;
import java.time.LocalDate;

public class RecordSutinSpecificFields {

    public static class ElementId {
        public static final String CODE = "d3";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);
    }

    public static class RowId {
        public static final String CODE = "d4";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);
        public static final RecordFieldFinder<StringFieldValue, String> FIELD_FINDER = RecordFieldFinder.byDeepTypeId(TYPE_ID, StringFieldValue::extract);
    }

    public static final class PersonLink {
        public static final String CODE = "d1810";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Main {
            public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PersonLink.TYPE_ID, CODE);
        }
    }

    public static class SalaryLink {
        public static final String CODE = "d8980";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Main {
            public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(SalaryLink.TYPE_ID, CODE);

            public static class Surname {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Main.TYPE_ID, CODE);
            }

            public static class Name {
                public static final String CODE = "b";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Main.TYPE_ID, CODE);
            }
        }
    }

    public static class FondPerson {

        public static class Person {
            public static final String CODE = "d1710";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class LastName {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Person.TYPE_ID, CODE);
                public static final RecordFieldFinder<StringFieldValue, String> FIELD_FINDER = RecordFieldFinder.byDeepTypeId(TYPE_ID, StringFieldValue::extract);
            }

            public static class FirstName {
                public static final String CODE = "b";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Person.TYPE_ID, CODE);
            }

            public static class Degree {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Person.TYPE_ID, CODE);
            }

            public static class Extension {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Person.TYPE_ID, CODE);
            }
        }

        public static class EmploymentData {
            public static final String CODE = "d1720";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class PohodaId {
                public static final String CODE = "b";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(EmploymentData.TYPE_ID, CODE);
            }
        }

    }


    // Fond 88
    public static final class EmploymentStatus {

        public static class EmploymentData {
            public static final String CODE = "d1815";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class PersonalNumber {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(EmploymentData.TYPE_ID, CODE);
                public static final RecordFieldFinder<StringFieldValue, String> FIELD_FINDER = RecordFieldFinder.byDeepTypeId(TYPE_ID, StringFieldValue::extract);
            }
        }

        public static class JobData {
            public static final String CODE = "d1820";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class JobBeginDate {
                public static final String CODE = "b";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobData.TYPE_ID, CODE);
            }

            public static class TestTimeEndDate {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobData.TYPE_ID, CODE);
            }

            public static class JobEndDate {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobData.TYPE_ID, CODE);
            }

            public static class ContractType {
                public static final String CODE = "e";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobData.TYPE_ID, CODE);
            }

            public static class ContractDate {
                public static final String CODE = "f";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobData.TYPE_ID, CODE);
            }

            public static class Category {
                public static final String CODE = "k";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobData.TYPE_ID, CODE);
            }
        }

        public static final class WorkPosition {
            public static final String CODE = "d1825";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Name {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(WorkPosition.TYPE_ID, CODE);
            }
        }

        public static final class WeeklyCommitment {
            public static final String CODE = "d1840";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Hours {
                public static final String CODE = "m";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(WeeklyCommitment.TYPE_ID, CODE);
                public static final RecordFieldFinder<NumberFieldValue, BigDecimal> FIELD_FINDER = RecordFieldFinder.byDeepTypeId(TYPE_ID, NumberFieldValue::extract);
            }
        }

        public static class Employer {
            public static final String CODE = "d1850";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Employer.TYPE_ID, CODE);
            }

            // to znamená IČO
            public static class Ico {
                public static final String CODE = "i";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Employer.TYPE_ID, CODE);
            }

            // to znamená DIČ
            public static class Dic {
                public static final String CODE = "j";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Employer.TYPE_ID, CODE);
            }
        }

        public static class Division {
            public static final String CODE = "d1851";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Division.TYPE_ID, CODE);
            }
        }

        public static class EmploymentTermination {
            public static final String CODE = "d1861";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class InitiatorSide {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(EmploymentTermination.TYPE_ID, CODE);
            }

            public static class Type {
                public static final String CODE = "b";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(EmploymentTermination.TYPE_ID, CODE);
            }

            public static class Reason {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(EmploymentTermination.TYPE_ID, CODE);
            }
        }

        public static final class Validity {
            public static final String CODE = "d1871";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Valid {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Validity.TYPE_ID, CODE);
            }

            public static class ValidFrom {
                public static final String CODE = "n";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Validity.TYPE_ID, CODE);
            }

            public static class ValidTo {
                public static final String CODE = "o";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Validity.TYPE_ID, CODE);
                public static final RecordFieldFinder<LocalDateFieldValue, LocalDate> FIELD_FINDER = RecordFieldFinder.byDeepTypeId(TYPE_ID, LocalDateFieldValue::extract);
            }
        }

    }


    /// Fond 83
    public static final class FondJobFile {

        public static class PersonalData {
            public static final String CODE = "d1845";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class BirthDate {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PersonalData.TYPE_ID, CODE);
            }

            public static class PersonalIdentificationNumber {
                public static final String CODE = "b";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PersonalData.TYPE_ID, CODE);
            }

            public static class BirthPlace {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PersonalData.TYPE_ID, CODE);
            }

            public static class Nationality {
                public static final String CODE = "f";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PersonalData.TYPE_ID, CODE);
            }

            // ADDITIONAL
            public static class EducationLevel {
                public static final String CODE = "j";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PersonalData.TYPE_ID, CODE);
            }

            public static class EducationInstitution {
                public static final String CODE = "k";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PersonalData.TYPE_ID, CODE);
            }

            public static class Gender {
                public static final String CODE = "l";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PersonalData.TYPE_ID, CODE);
            }
        }

        public static final class JobFile {
            public static final String CODE = "d1870";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class IdentityCardNumber {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobFile.TYPE_ID, CODE);
            }

            public static class IdentityCardValidity {
                public static final String CODE = "e";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobFile.TYPE_ID, CODE);
            }

            public static class HealthInsuranceCode {
                public static final String CODE = "g";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobFile.TYPE_ID, CODE);
            }

            public static class Handicap {
                public static final String CODE = "i";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobFile.TYPE_ID, CODE);
            }

            public static class IdentificationCardNumber {
                public static final String CODE = "m";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobFile.TYPE_ID, CODE);
            }

            public static class JobPlace {
                public static final String CODE = "p";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobFile.TYPE_ID, CODE);
            }

            public static class WorkPlacementValidity {
                public static final String CODE = "q";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobFile.TYPE_ID, CODE);
            }
        }

        public static final class JobFileValidity {
            public static final String CODE = "d1872";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Validity {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobFileValidity.TYPE_ID, CODE);
            }

            public static class JobFileValidFrom {
                public static final String CODE = "n";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobFileValidity.TYPE_ID, CODE);
            }

            public static class JobFileValidTo {
                public static final String CODE = "o";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(JobFileValidity.TYPE_ID, CODE);
                public static final RecordFieldFinder<LocalDateFieldValue, LocalDate> FIELD_FINDER = RecordFieldFinder.byDeepTypeId(TYPE_ID, LocalDateFieldValue::extract);
            }
        }

        public static class AdditionalData {
            public static final String CODE = "d1860";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class MaternityBeginDate {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AdditionalData.TYPE_ID, CODE);
            }

            public static class MaternityEndDate {
                public static final String CODE = "b";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AdditionalData.TYPE_ID, CODE);
            }

            public static class ParentalBeginDate {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AdditionalData.TYPE_ID, CODE);
            }

            public static class ParentalEndDate {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AdditionalData.TYPE_ID, CODE);
            }

            public static class ClaimedChildren {
                public static final String CODE = "e";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AdditionalData.TYPE_ID, CODE);
            }

            public static class UnclaimedChildren {
                public static final String CODE = "f";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AdditionalData.TYPE_ID, CODE);
            }

            public static class Foreclosure {
                public static final String CODE = "g";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AdditionalData.TYPE_ID, CODE);
            }
        }

    }


    // Fond 8
    public static final class FondSalaryCost {

        public static final class EmploymentStatus {
            public static final String CODE = "d1880";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Main {
                public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(EmploymentStatus.TYPE_ID, CODE);
            }
        }

        public static final class BasicSalary {
            public static final String CODE = "d1830";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Value {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(BasicSalary.TYPE_ID, CODE);
            }
        }

        public static final class FloatingSalary {
            public static final String CODE = "d1831";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Value {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(FloatingSalary.TYPE_ID, CODE);
            }
        }

        public static final class RewardMoney {
            public static final String CODE = "d1832";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Value {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(RewardMoney.TYPE_ID, CODE);
            }
        }

        public static final class HourlyRate {
            public static final String CODE = "d1833";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Value {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(HourlyRate.TYPE_ID, CODE);
            }
        }

        // TODO: není?
        public static final class FixedCost {
            public static final String CODE = "d1835";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Value {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(FixedCost.TYPE_ID, CODE);
            }
        }

        public static final class Validity {
            public static final String CODE = "d1838";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class ValidFrom {
                public static final String CODE = "n";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Validity.TYPE_ID, CODE);
            }

            public static final class ValidTo {
                public static final String CODE = "o";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Validity.TYPE_ID, CODE);
                public static final RecordFieldFinder<LocalDateFieldValue, LocalDate> FIELD_FINDER = RecordFieldFinder.byDeepTypeId(TYPE_ID, LocalDateFieldValue::extract);
            }
        }

        public static final class Note {
            public static final String CODE = "d1890";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Value {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Note.TYPE_ID, CODE);
            }
        }

    }


    public static class FondCompany {

        public static class ElementId {
            public static final String CODE = "d3";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);
        }

        public static class CompanyName {
            public static final String CODE = "d1200";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyName.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyName.TYPE_ID, CODE);
            }
        }

        public static class ParentCompany {
            public static final String CODE = "d1205";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final String QUERY_CODE = FieldTypeId.delim(ParentCompany.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ParentCompany.TYPE_ID, CODE);
            }
        }

        public static class CompanyData {
            public static final String CODE = "d1210";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class CompanyIdNumber {
                public static final String CODE = "v";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyData.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyData.TYPE_ID, CODE);
            }

            public static class TaxIdNumber {
                public static final String CODE = "w";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyData.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyData.TYPE_ID, CODE);
            }
        }

        public static class CompanyAddress {
            public static final String CODE = "d1060";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Street {
                public static final String CODE = "u";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyAddress.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyAddress.TYPE_ID, CODE);
            }

            public static class City {
                public static final String CODE = "v";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyAddress.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyAddress.TYPE_ID, CODE);
            }

            public static class ZipCode {
                public static final String CODE = "w";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyAddress.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyAddress.TYPE_ID, CODE);
            }

            public static class State {
                public static final String CODE = "x";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyAddress.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyAddress.TYPE_ID, CODE);
            }

            public static class Description {
                public static final String CODE = "y";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyAddress.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyAddress.TYPE_ID, CODE);
            }
        }

        public static class CompanyContact {
            public static final String CODE = "d1061";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class PhoneNumber {
                public static final String CODE = "u";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyContact.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyContact.TYPE_ID, CODE);
            }

            public static class Email {
                public static final String CODE = "v";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyContact.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyContact.TYPE_ID, CODE);
            }

            public static class BankAccount {
                public static final String CODE = "w";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyContact.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyContact.TYPE_ID, CODE);
            }

            public static class Website {
                public static final String CODE = "x";
                public static final String QUERY_CODE = FieldTypeId.delim(CompanyContact.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CompanyContact.TYPE_ID, CODE);
            }
        }
    }


    public static class FondProperty {

        public static class ElementId {
            public static final String CODE = "d3";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);
        }

        public static class PropertyName {
            public static final String CODE = "d1910";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final String QUERY_CODE = FieldTypeId.delim(PropertyName.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PropertyName.TYPE_ID, CODE);
            }
        }

        public static class PropertyData {
            public static final String CODE = "d1920";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class RegistrationNumber {
                public static final String CODE = "a";
                public static final String QUERY_CODE = FieldTypeId.delim(PropertyData.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PropertyData.TYPE_ID, CODE);
            }

            public static class Label {
                public static final String CODE = "b";
                public static final String QUERY_CODE = FieldTypeId.delim(PropertyData.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PropertyData.TYPE_ID, CODE);
            }

            public static class PurchaseDate {
                public static final String CODE = "d";
                public static final String QUERY_CODE = FieldTypeId.delim(PropertyData.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PropertyData.TYPE_ID, CODE);
            }

            public static class LiquidationDate {
                public static final String CODE = "x";
                public static final String QUERY_CODE = FieldTypeId.delim(PropertyData.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PropertyData.TYPE_ID, CODE);
            }
        }

        public static class DeviceGroup {
            public static final String CODE = "d1925";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Main {
                public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
                public static final String QUERY_CODE = FieldTypeId.delim(DeviceGroup.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DeviceGroup.TYPE_ID, CODE);

                public static class Name {
                    public static final String CODE = "a";
                    public static final String QUERY_CODE = FieldTypeId.delim(Main.CODE, CODE);
                    public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Main.TYPE_ID, CODE);
                }
            }
        }

        public static class Operator {
            public static final String CODE = "d1931";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final String QUERY_CODE = FieldTypeId.delim(Operator.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Operator.TYPE_ID, CODE);
            }
        }

        public static class ProductionRegistrationData {
            public static final String CODE = "d1937";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class SerialNumber {
                public static final String CODE = "b";
                public static final String QUERY_CODE = FieldTypeId.delim(ProductionRegistrationData.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ProductionRegistrationData.TYPE_ID, CODE);
            }
        }

        public static class Note {
            public static final String CODE = "d1950";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class NoteText {
                public static final String CODE = "a";
                public static final String QUERY_CODE = FieldTypeId.delim(Note.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Note.TYPE_ID, CODE);
            }
        }
    }

    public static class CostDate {
        public static final String CODE = "d2030";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "d";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CostDate.TYPE_ID, CODE);
            public static final FieldFinder<FieldContainer, Field<LocalDateFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
        }
    }

    public static class FondWorkReport {

        public static class Profession {
            public static final String CODE = "d2054";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Main {
                public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(FondWorkReport.Profession.TYPE_ID, CODE);

                public static class ProfessionName {
                    public static final String CODE = "a";
                    public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(FondWorkReport.Profession.Main.TYPE_ID, CODE);
                    public static final FieldFinder<FieldContainer, Field<StringFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
                }
            }
        }

        public static class Reported {
            public static final String CODE = "d2070";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class StandardHoursValue {
                public static final String CODE = "m";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Reported.TYPE_ID, CODE);
                public static final FieldFinder<FieldContainer, Field<NumberFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
            }

            public static class OvertimeHoursValue {
                public static final String CODE = "n";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Reported.TYPE_ID, CODE);
                public static final FieldFinder<FieldContainer, Field<NumberFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
            }
        }

        public static class Charged {
            public static final String CODE = "d2080";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class StandardHoursValue {
                public static final String CODE = "m";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Charged.TYPE_ID, CODE);
                public static final FieldFinder<FieldContainer, Field<NumberFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
            }

            public static class OvertimeHoursValue {
                public static final String CODE = "n";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Charged.TYPE_ID, CODE);
                public static final FieldFinder<FieldContainer, Field<NumberFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
            }
        }

        public static class ReportedPrice {
            public static final String CODE = "d2071";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Value {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ReportedPrice.TYPE_ID, CODE);
                public static final FieldFinder<FieldContainer, Field<NumberFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
            }
        }

        public static class ChargedPrice {
            public static final String CODE = "d2081";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Value {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ChargedPrice.TYPE_ID, CODE);
                public static final FieldFinder<FieldContainer, Field<NumberFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
            }
        }

        public static class ParentProject {
            public static final String CODE = "d1001";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Main {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ParentProject.TYPE_ID, CODE);
            }
        }

        public static class Day {
            public static final String CODE = "d2030";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Day.TYPE_ID, CODE);


            public static class Date {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Day.TYPE_ID, CODE);
                public static final FieldFinder<FieldContainer, Field<LocalDateFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
            }
        }

        public static class Reference {
            public static final String CODE = "d2012";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Main {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Reference.TYPE_ID, CODE);
                public static final FieldFinder<FieldContainer, Field<StringFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
            }
        }
    }

    public static class FondInstallationLogbook {

        public static final int FOND_ID = 78;

        public static class Date {
            public static final String CODE = "d2030";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Value {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Date.TYPE_ID, CODE);
                public static final FieldFinder<FieldContainer, Field<LocalDateFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
            }
        }
    }

    public static class WorkType {
        public static final String CODE = "d2035";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "t";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(WorkType.TYPE_ID, CODE);
            public static final FieldFinder<FieldContainer, Field<AcceptableValueFieldValue<AcceptableValueGroupItem>>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);

            public static final String VALUE_WORK_PRESENTIAL = "WORK";
            public static final String VALUE_WORK_ON_CALL_DUTY = "ONCALL";
        }
    }

    public static class AbsenceType {
        public static final String CODE = "d2036";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "t";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AbsenceType.TYPE_ID, CODE);
            public static final FieldFinder<FieldContainer, Field<AcceptableValueFieldValue<AcceptableValueGroupItem>>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);

            public static final String VALUE_ABS_DOCTOR_VISIT = "MED";
            public static final String VALUE_ABS_VACATION = "VACA";
            public static final String VALUE_ABS_ILLNESS = "ILL";
            public static final String VALUE_ABS_OCR = "OCR";
            public static final String VALUE_ABS_PARAGRAPH = "PARA";
            public static final String VALUE_ABS_COMPENSATION = "COMPENS";
            public static final String VALUE_ABS_UNPAID = "UNPAID";
            public static final String VALUE_ABS_UNEXCUSED = "UNEXCUSED";
            public static final String VALUE_ABS_MATERNITY = "MATERNITY";
            public static final String VALUE_ABS_PATERNITY = "PATERNITY";
            public static final String VALUE_ABS_EMPLOYER_OBSTACLES = "PROBLEM";
            public static final String VALUE_ABS_EVENTS = "EVENTS";
        }
    }

    public static class Worker {
        public static final String CODE = "d2050";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Main {
            public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Worker.TYPE_ID, CODE);
            public static final FieldFinder<FieldContainer, Field<?>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);

            public static class FirstName {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Main.TYPE_ID, CODE);
                public static final FieldFinder<FieldContainer, Field<?>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
            }
        }
    }

    public static class CostReportDuration {
        public static final String CODE = "d2070";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class RegularValue {
            public static final String CODE = "m";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CostReportDuration.TYPE_ID, CODE);
            public static final FieldFinder<FieldContainer, Field<NumberFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
        }

        public static class OvertimeValue {
            public static final String CODE = "n";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CostReportDuration.TYPE_ID, CODE);
            public static final FieldFinder<FieldContainer, Field<NumberFieldValue>> FIELD_FINDER = FieldFinders.byDeepTypeId(TYPE_ID);
        }
    }

    public static class Day {
        public static final String CODE = "d2100";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Date {
            public static final String CODE = "d";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Day.TYPE_ID, CODE);
            public static final RecordFieldFinder<LocalDateFieldValue, LocalDate> FIELD_FINDER = RecordFieldFinder.byDeepTypeId(TYPE_ID, LocalDateFieldValue::extract);
        }
    }

    public static class Weekend {
        public static final String CODE = "d2115";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "o";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Weekend.TYPE_ID, CODE);
            public static final RecordFieldFinder<BooleanFieldValue, Boolean> FIELD_FINDER = RecordFieldFinder.byDeepTypeId(TYPE_ID, BooleanFieldValue::extract);
        }
    }

    public static class Holiday {
        public static final String CODE = "d2116";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "o";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Holiday.TYPE_ID, CODE);
            public static final RecordFieldFinder<BooleanFieldValue, Boolean> FIELD_FINDER = RecordFieldFinder.byDeepTypeId(TYPE_ID, BooleanFieldValue::extract);
        }
    }

    public static final class FondWorkCatalog {

        public static final class WorkPersonCatalog {
            public static final String CODE = "d8710";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Main {
                public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(WorkPersonCatalog.TYPE_ID, CODE);

                public static class Name {
                    public static final String CODE = "a";
                    public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(WorkPersonCatalog.Main.TYPE_ID, CODE);
                }
            }
        }

        public static final class WorkPropertyCatalog {
            public static final String CODE = "d8711";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Main {
                public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(WorkPropertyCatalog.TYPE_ID, CODE);

                public static class Name {
                    public static final String CODE = "a";
                    public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(WorkPropertyCatalog.Main.TYPE_ID, CODE);
                }
            }
        }

        public static final class WorkType {
            public static final String CODE = "d8715";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Type {
                public static final String CODE = "t";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(WorkType.TYPE_ID, CODE);
            }
        }

        public static final class Person {
            public static final String CODE = "d8720";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Main {
                public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Person.TYPE_ID, CODE);

                public static class Surname {
                    public static final String CODE = "a";
                    public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Person.Main.TYPE_ID, CODE);
                }

                public static class FirstName {
                    public static final String CODE = "b";
                    public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Person.Main.TYPE_ID, CODE);
                }
            }
        }

        public static final class Property {
            public static final String CODE = "d8721";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Property.TYPE_ID, CODE);
            }
        }

        public static final class ValidFrom {
            public static final String CODE = "d8730";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Value {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ValidFrom.TYPE_ID, CODE);
            }
        }

        public static final class ValidTo {
            public static final String CODE = "d8731";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Value {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ValidTo.TYPE_ID, CODE);
            }
        }

    }

    // Fond 89
    public static final class MonthlyAttendanceFond {

        public static final class ApprovementState {
            public static final String CODE = "d8905";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class State {
                public static final String CODE = "s";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ApprovementState.TYPE_ID, CODE);
            }
        }

        public static final class Period {
            public static final String CODE = "d8907";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Id {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Period.TYPE_ID, CODE);
            }
        }

        public static final class ApprovedOvertime {
            public static final String CODE = "d8910";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Workday {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ApprovedOvertime.TYPE_ID, CODE);
            }

            public static final class Weekend {
                public static final String CODE = "b";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ApprovedOvertime.TYPE_ID, CODE);
            }

            public static final class Bank {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ApprovedOvertime.TYPE_ID, CODE);
            }
        }

        public static final class OnCall {
            public static final String CODE = "d8915";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Days {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(OnCall.TYPE_ID, CODE);
            }

            public static final class Weeks {
                public static final String CODE = "b";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(OnCall.TYPE_ID, CODE);
            }
        }

        public static final class Compensation {
            public static final String CODE = "d8920";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Overtime {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Compensation.TYPE_ID, CODE);
            }

            public static final class OnCall {
                public static final String CODE = "b";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Compensation.TYPE_ID, CODE);
            }

            public static final class Idp {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Compensation.TYPE_ID, CODE);
            }
        }

        public static final class Note {
            public static final String CODE = "d8990";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static final class Text {
                public static final String CODE = "t";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Note.TYPE_ID, CODE);
            }
        }
    }

    public static final class ContractualOrders {

        public static class Status {
            public static final String CODE = "d1105";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Value {
                public static final String CODE = "h";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Status.TYPE_ID, CODE);
                public static final RecordFieldFinder<StringFieldValue, String> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, StringFieldValue::extract);
            }
        }

        public static class OrderType {
            public static final String CODE = "d1106";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Type {
                public static final String CODE = "t";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(OrderType.TYPE_ID, CODE);
                public static final RecordFieldFinder<StringFieldValue, String> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, StringFieldValue::extract);
            }
        }

        public static class Prices {
            public static final String CODE = "d1150";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class OfferedPrice {
                public static final String CODE = "c";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Prices.TYPE_ID, CODE);
                public static final RecordFieldFinder<NumberFieldValue, BigDecimal> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, NumberFieldValue::extract);
            }

            public static class ExpectedCost {
                public static final String CODE = "e";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Prices.TYPE_ID, CODE);
                public static final RecordFieldFinder<NumberFieldValue, BigDecimal> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, NumberFieldValue::extract);
            }

            public static class ExpectedRevenue {
                public static final String CODE = "f";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Prices.TYPE_ID, CODE);
                public static final RecordFieldFinder<NumberFieldValue, BigDecimal> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, NumberFieldValue::extract);
            }

            public static class TotalCost {
                public static final String CODE = "g";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Prices.TYPE_ID, CODE);
                public static final RecordFieldFinder<NumberFieldValue, BigDecimal> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, NumberFieldValue::extract);
            }

            public static class TotalRevenue {
                public static final String CODE = "h";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Prices.TYPE_ID, CODE);
                public static final RecordFieldFinder<NumberFieldValue, BigDecimal> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, NumberFieldValue::extract);
            }

            public static class Currency {
                public static final String CODE = "j";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Prices.TYPE_ID, CODE);
                public static final RecordFieldFinder<AcceptableValueFieldValue<Identified<String>>, Identified<String>> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, AcceptableValueFieldValue::extract);
            }
        }

        public static class SubmittedAt {
            public static final String CODE = "d1047";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Date {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(SubmittedAt.TYPE_ID, CODE);
                public static final RecordFieldFinder<LocalDateFieldValue, LocalDate> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, LocalDateFieldValue::extract);
            }
        }

        public static class PlannedStart {
            public static final String CODE = "d1040";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Date {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PlannedStart.TYPE_ID, CODE);
                public static final RecordFieldFinder<LocalDateFieldValue, LocalDate> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, LocalDateFieldValue::extract);
            }
        }

        public static class ActualStart {
            public static final String CODE = "d1041";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Date {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ActualStart.TYPE_ID, CODE);
                public static final RecordFieldFinder<LocalDateFieldValue, LocalDate> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, LocalDateFieldValue::extract);
            }
        }

        public static class PlannedDelivery {
            public static final String CODE = "d1042";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Date {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PlannedDelivery.TYPE_ID, CODE);
                public static final RecordFieldFinder<LocalDateFieldValue, LocalDate> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, LocalDateFieldValue::extract);
            }
        }

        public static class ActualDelivery {
            public static final String CODE = "d1043";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Date {
                public static final String CODE = "d";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ActualDelivery.TYPE_ID, CODE);
                public static final RecordFieldFinder<LocalDateFieldValue, LocalDate> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, LocalDateFieldValue::extract);
            }
        }

        public static class ResponsiblePerson {
            public static final String CODE = "d1112";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Main {
                public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ResponsiblePerson.TYPE_ID, CODE);

                public static class Surname {
                    public static final String CODE = "a";
                    public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ResponsiblePerson.Main.TYPE_ID, CODE);
                    public static final RecordFieldFinder<StringFieldValue, String> FIELD_FINDER =
                            RecordFieldFinder.byDeepTypeId(TYPE_ID, StringFieldValue::extract);
                }

                public static class Name {
                    public static final String CODE = "b";
                    public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ResponsiblePerson.Main.TYPE_ID, CODE);
                    public static final RecordFieldFinder<StringFieldValue, String> FIELD_FINDER =
                            RecordFieldFinder.byDeepTypeId(TYPE_ID, StringFieldValue::extract);
                }
            }
        }

        public static class EndCustomer {
            public static final String CODE = "d1010";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(EndCustomer.TYPE_ID, CODE);
                public static final RecordFieldFinder<StringFieldValue, String> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, StringFieldValue::extract);
            }
        }

        public static class OrderingParty {
            public static final String CODE = "d1011";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(OrderingParty.TYPE_ID, CODE);
                public static final RecordFieldFinder<StringFieldValue, String> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, StringFieldValue::extract);
            }
        }

        public static class Supplier {
            public static final String CODE = "d1012";
            public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Supplier.TYPE_ID, CODE);
                public static final RecordFieldFinder<StringFieldValue, String> FIELD_FINDER =
                        RecordFieldFinder.byDeepTypeId(TYPE_ID, StringFieldValue::extract);
            }
        }

    }


}
