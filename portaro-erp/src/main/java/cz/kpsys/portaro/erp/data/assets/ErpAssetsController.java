package cz.kpsys.portaro.erp.data.assets;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.io.ResourceDataStreamer;
import cz.kpsys.portaro.commons.web.DownloadFileStreamConsumer;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.RequestMapping;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@RequestMapping(ErpAssetsController.ERP_ASSETS_PATH)
public class ErpAssetsController {

    public static final String ERP_ASSETS_PATH = "/erp/assets";

    @NonNull ContextualProvider<Department, @NonNull Resource> logoNoTextResourceResolver;
    @NonNull ContextualProvider<Department, @NonNull Resource> logoResourceResolver;
    @NonNull ContextualProvider<Department, @NonNull Resource> textLogoResourceResolver;
    @NonNull ResourceDataStreamer resourceDataStreamer = new ResourceDataStreamer();

    @RequestMapping("logo-no-text.svg")
    public void getLogoNoText(@CurrentDepartment Department ctx,
                              @NonNull HttpServletResponse response) {
        Resource resource = logoNoTextResourceResolver.getOn(ctx);
        download(response, resource);
    }

    @RequestMapping("logo.svg")
    public void getLogo(@CurrentDepartment Department ctx,
                        @NonNull HttpServletResponse response) {
        Resource resource = logoResourceResolver.getOn(ctx);
        download(response, resource);
    }

    @RequestMapping("text-logo.svg")
    public void getTextualLogo(@CurrentDepartment Department ctx,
                               @NonNull HttpServletResponse response) {
        Resource resource = textLogoResourceResolver.getOn(ctx);
        download(response, resource);
    }

    private void download(@NonNull HttpServletResponse response, Resource resource) {
        resourceDataStreamer.streamData(resource, null, new DownloadFileStreamConsumer(response));
    }

}