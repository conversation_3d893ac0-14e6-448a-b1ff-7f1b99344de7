package cz.kpsys.portaro.erp.data.dataview;

import com.fasterxml.jackson.core.type.TypeReference;
import cz.kpsys.portaro.commons.convert.JsonToObjectConverter;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

public record ErpCustomDataViewPagesHolder(
        @NonNull List<Page> definedDataViewPages
) {

    public static Converter<String, ErpCustomDataViewPagesHolder> createConverter() {
        return new JsonToObjectConverter<>(new TypeReference<>() {});
    }

    public record Page(
            @NonNull String id,
            @NonNull String route,
            @Nullable Heading heading,
            @NonNull @NotEmpty List<Tab> tabs
    ) {

        public record Heading(
                @NonNull String text,
                @Nullable String icon,
                @Nullable CreateButton createButton,
                @Nullable String createButtonCustomComponent
        ) {

            public record CreateButton(
                    @NonNull String label,
                    @NonNull Integer rootFondId
            ) {}
        }

        public record Tab(
                @NonNull String id,
                @NonNull String name,
                @Nullable String icon,
                @Nullable Integer fondId,
                @Nullable Integer showPermissionFond,
                @Nullable Integer editPermissionFond,
                @Nullable String referenceFieldTypeId,
                @Nullable String referenceRecordId,
                @Nullable String customSearchQt,
                @Nullable String recordLinkFieldTypeId,
                @Nullable CustomComponent customComponent
        ) {

            public record CustomComponent(
                    @NonNull String componentName,
                    @Nullable Boolean noTabPadding,
                    @Nullable Object props
            ) {}
        }
    }
}