package cz.kpsys.portaro.erp.data.days;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.employee.workattendance.RecordDayHelper;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Tag(name = "days", description = "Endpoints for managing day records")
@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DayApiController extends GenericApiController {

    @NonNull RecordDayHelper recordDayHelper;


    @Operation(summary = "Create days for specified year")
    @PostMapping("/api/days/create-for-year")
    public ActionResponse createDayRecordsForWholeYear(@RequestParam("year") int year,
                                                       @CurrentDepartment Department ctx,
                                                       UserAuthentication currentAuth) {
        var days = recordDayHelper.createDaysFor(year, ctx, currentAuth);
        return new FinishedSaveResponse<>(Texts.ofNative("Created days for year " + year), days);
    }

    @Operation(summary = "Mark weekends and holidays for existing days in specified year")
    @PostMapping("/api/days/enrich-for-year")
    public ActionResponse enrichExistingRecordDays(@RequestParam("year") int year,
                                                   @CurrentDepartment Department ctx,
                                                   UserAuthentication currentAuth) {
        var enrichedDays = recordDayHelper.enrichExistingDaysIn(year, ctx, currentAuth);
        return new FinishedSaveResponse<>(Texts.ofNative("Enriched days for year " + year), enrichedDays);
    }

}
