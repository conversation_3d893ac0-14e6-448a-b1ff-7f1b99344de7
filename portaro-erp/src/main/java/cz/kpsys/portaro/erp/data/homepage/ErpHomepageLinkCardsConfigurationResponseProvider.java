package cz.kpsys.portaro.erp.data.homepage;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.data.homepage.web.ErpHomepageLinkCardsConfigurationResponse;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ErpHomepageLinkCardsConfigurationResponseProvider implements ContextualProvider<Department, @NonNull ErpHomepageLinkCardsConfigurationResponse> {

    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> nonDetailedRecordSearchLoader;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull ContextualProvider<Department, @NonNull ErpHomepageLinkCardsHolder> homepageLinkCardsProvider;

    @Override
    public ErpHomepageLinkCardsConfigurationResponse getOn(Department ctx) {
        ErpHomepageLinkCardsHolder homepageLinkCards = homepageLinkCardsProvider.getOn(ctx);

        List<ErpHomepageLinkCardsConfigurationResponse.LinkCardsSection> cardSections = homepageLinkCards.linkCardsSections().stream()
                .map((cardSection) -> new ErpHomepageLinkCardsConfigurationResponse.LinkCardsSection(
                        cardSection.color(),
                        cardSection.cards().stream()
                                .map((card) -> new ErpHomepageLinkCardsConfigurationResponse.LinkCard(
                                        card.href(),
                                        card.label(),
                                        card.icon(),
                                        card.countFond() == null ? null : getFondsItemsCounts(ctx, card.countFond()),
                                        card.showPermissionFond(),
                                        card.editPermissionFond()
                                ))
                                .toList()
                ))
                .toList();

        return new ErpHomepageLinkCardsConfigurationResponse(cardSections);
    }

    private int getFondsItemsCounts(Department ctx, int fondId) {
        MapBackedParams params = MapBackedParams.build(p -> {
            p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
            p.set(CoreSearchParams.FACETS_ENABLED, false);
            p.set(CoreSearchParams.INCLUDE_DRAFT, false);
            p.set(CoreSearchParams.INCLUDE_DELETED, false);
            p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
            p.set(CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.FAMILY));
            p.set(RecordConstants.SearchParams.ROOT_FOND, List.of(fondLoader.getById(fondId)));
        });

        return nonDetailedRecordSearchLoader.getTotalElements(params);
    }
}
