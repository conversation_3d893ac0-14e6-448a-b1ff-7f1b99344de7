package cz.kpsys.portaro.erp.data.homepage;

import cz.kpsys.portaro.commons.convert.JsonToObjectConverter;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.core.convert.converter.Converter;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.List;

public record ErpHomepageLinkCardsHolder(
        @NonNull List<LinkCardsSection> linkCardsSections
) {

    public static Converter<String, ErpHomepageLinkCardsHolder> createConverter() {
        return new JsonToObjectConverter<>(new TypeReference<>() {});
    }

    public record LinkCardsSection(
            @NonNull String color,
            @NonNull List<LinkCard> cards
    ) {

        public record LinkCard(
                @NonNull String label,
                @NonNull String icon,
                @NonNull String href,
                @Nullable Integer countFond,
                @Nullable Integer showPermissionFond,
                @Nullable Integer editPermissionFond
        ) {}
    }
}
