package cz.kpsys.portaro.erp.data.homepage;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.data.homepage.web.ErpHomepageLinkCardsConfigurationResponse;
import cz.kpsys.portaro.erp.data.homepage.web.SutorHomePageDataResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SutorHomepagePageDataProvider implements ContextualFunction<UserAuthentication, Department, @NonNull SutorHomePageDataResponse> {

    @NonNull ContextualProvider<Department, @NonNull ErpHomepageLinkCardsConfigurationResponse> homepageLinkCardsConfigurationResponseContextualProvider;

    @Transactional(readOnly = true)
    @Override
    @NonNull
    public SutorHomePageDataResponse getOn(UserAuthentication currentAuth, Department ctx) throws ItemNotFoundException {
        return new SutorHomePageDataResponse(homepageLinkCardsConfigurationResponseContextualProvider.getOn(ctx));
    }
}
