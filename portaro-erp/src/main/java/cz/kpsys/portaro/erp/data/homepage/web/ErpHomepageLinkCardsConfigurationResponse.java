package cz.kpsys.portaro.erp.data.homepage.web;

import io.micrometer.common.lang.Nullable;
import lombok.NonNull;

import java.util.List;

public record ErpHomepageLinkCardsConfigurationResponse(
        @NonNull List<LinkCardsSection> cardSections
) {

    public record LinkCardsSection(
            @NonNull String color,
            @NonNull List<LinkCard> cards
    ) {}

    public record LinkCard(
            @NonNull String href,
            @NonNull String label,
            @NonNull String icon,
            @Nullable Integer value,
            @Nullable Integer showPermissionFond,
            @Nullable Integer editPermissionFond
    ) {}
}
