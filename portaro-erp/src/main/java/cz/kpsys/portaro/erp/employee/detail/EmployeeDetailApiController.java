package cz.kpsys.portaro.erp.employee.detail;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Locale;
import java.util.UUID;

@RequestMapping("/api/employee/detail")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EmployeeDetailApiController {

    @NonNull EmployeeDetailLoader employeeDetailLoader;

    @RequestMapping("{id}")
    public EmployeeDetailResponse get(@PathVariable("id") UUID id,
                                      @CurrentDepartment Department ctx,
                                      UserAuthentication currentAuth,
                                      Locale locale) {

        return employeeDetailLoader.load(id, ctx, currentAuth, locale);
    }
}