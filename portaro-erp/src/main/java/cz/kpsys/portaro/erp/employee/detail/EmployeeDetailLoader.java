package cz.kpsys.portaro.erp.employee.detail;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields;
import cz.kpsys.portaro.grid.RecordRow;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.detail.RecordFieldFinder;
import cz.kpsys.portaro.record.detail.value.DatetimeRangeFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.search.restriction.FieldTypeSearchField;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PROTECTED, makeFinal = true)
@RequiredArgsConstructor
public class EmployeeDetailLoader {

    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader;
    @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader;
    @NonNull ViewableItemsTypedConverter<Record, RecordRow> recordsToRecordRowsConverter;
    @NonNull IdAndIdsLoadable<User, UUID> userByUuidLoader;
    @NonNull Provider<@NonNull Fond> personEmploymentFondProvider; // Employment status
    @NonNull Provider<@NonNull Fond> personCardFondProvider; // Job file

    public EmployeeDetailResponse load(UUID userId, Department ctx, UserAuthentication currentAuth, Locale locale) {
        List<Record> employmentStatuses = queryRecords(ctx, personEmploymentFondProvider.get(), userId);
        List<Record> jobFiles = queryRecords(ctx, personCardFondProvider.get(), userId);

        ActiveOrLatestRecord activeOrLatestEmploymentStatus = getActiveOrLatestRecord(employmentStatuses, RecordSutinSpecificFields.EmploymentStatus.Validity.TimeValidity.FIELD_FINDER);
        ActiveOrLatestRecord activeOrLatestJobFile = getActiveOrLatestRecord(jobFiles, RecordSutinSpecificFields.FondJobFile.JobFileValidity.TimeValidity.FIELD_FINDER);

        User user = userByUuidLoader.getById(userId);
        ReaderRole readerRole = user.getReaderAccounts().stream()
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);

        Department homeDepartment = readerRole != null ? readerRole.getDepartment() : null;

        return new EmployeeDetailResponse(
                homeDepartment,
                activeOrLatestEmploymentStatus == null ? null : recordsToRecordRowsConverter.convertSingle(activeOrLatestEmploymentStatus.record(), currentAuth, ctx, locale),
                activeOrLatestEmploymentStatus == null ? null : activeOrLatestEmploymentStatus.order(),
                activeOrLatestEmploymentStatus != null && activeOrLatestEmploymentStatus.active(),
                activeOrLatestJobFile == null ? null : recordsToRecordRowsConverter.convertSingle(activeOrLatestJobFile.record(), currentAuth, ctx, locale),
                activeOrLatestJobFile == null ? null : activeOrLatestJobFile.order(),
                activeOrLatestJobFile != null && activeOrLatestJobFile.active()
        );
    }

    private List<Record> queryRecords(Department ctx, Fond fond, UUID userId) {
        return detailedRecordSearchLoader.getContent(RangePaging.forAll(), p -> {
            p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
            p.set(CoreSearchParams.FACETS_ENABLED, false);
            p.set(CoreSearchParams.INCLUDE_DRAFT, false);
            p.set(CoreSearchParams.INCLUDE_DELETED, false);
            p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
            p.set(RecordConstants.SearchParams.ROOT_FOND, List.of(fond));
            p.set(CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.SUBTREE));
            p.set(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION, new Conjunction<>(
                    new Term<>(FieldTypeSearchField.loadOfLink(RecordSutinSpecificFields.PersonLink.Main.TYPE_ID, fieldTypeSearchFieldLoader), new Eq(userId))
            ));
        });
    }

    @Nullable
    private ActiveOrLatestRecord getActiveOrLatestRecord(List<Record> records, RecordFieldFinder<DatetimeRangeFieldValue, DatetimeRange> fieldFinder) {
        Map<Record, DatetimeRange> recordValidityRanges = records.stream()
                .collect(Collectors.toMap(
                        record -> record,
                        record -> fieldFinder.findFirstIn(record.getDetail(), DatetimeRangeFieldValue::extract).orElse(DatetimeRange.EMPTY)
                ));

        if (recordValidityRanges.isEmpty()) {
            return null;
        }

        Instant now = Instant.now();

        // Sort records by start date (null as -infinity), then by end date (null as +infinity)
        List<Map.Entry<Record, DatetimeRange>> sortedRecords = recordValidityRanges.entrySet().stream()
                .sorted((entry1, entry2) -> {
                    DatetimeRange range1 = entry1.getValue();
                    DatetimeRange range2 = entry2.getValue();

                    // Compare start dates: null is -infinity (smaller)
                    Instant start1 = range1.inclusiveFromDate();
                    Instant start2 = range2.inclusiveFromDate();

                    if (start1 == null && start2 == null) {
                        // Both null, compare end dates
                    } else if (start1 == null) {
                        return -1; // start1 is -infinity, so it's smaller
                    } else if (start2 == null) {
                        return 1; // start2 is -infinity, so it's smaller
                    } else {
                        int startComparison = start1.compareTo(start2);
                        if (startComparison != 0) {
                            return startComparison;
                        }
                    }

                    // Compare end dates: null is +infinity (larger)
                    Instant end1 = range1.exclusiveToDate();
                    Instant end2 = range2.exclusiveToDate();

                    if (end1 == null && end2 == null) {
                        return 0; // Both are +infinity
                    } else if (end1 == null) {
                        return 1; // end1 is +infinity, so it's larger
                    } else if (end2 == null) {
                        return -1; // end2 is +infinity, so it's larger
                    } else {
                        return end1.compareTo(end2);
                    }
                })
                .toList();

        // Check for the currently active record
        for (int i = 0; i < sortedRecords.size(); i++) {
            Map.Entry<Record, DatetimeRange> entry = sortedRecords.get(i);
            DatetimeRange range = entry.getValue();

            // Check if the current time is within the range
            boolean isActive = isCurrentlyActive(range, now);
            if (isActive) {
                return new ActiveOrLatestRecord(entry.getKey(), i + 1, true);
            }
        }

        // No active record found, return the latest (last in sorted order)
        if (!sortedRecords.isEmpty()) {
            Map.Entry<Record, DatetimeRange> latestEntry = sortedRecords.getLast();
            return new ActiveOrLatestRecord(latestEntry.getKey(), sortedRecords.size(), false);
        }

        return null;
    }

    private boolean isCurrentlyActive(DatetimeRange range, Instant now) {
        if (range.empty()) {
            return false;
        }

        // Check if now is within the range [inclusiveFromDate, exclusiveToDate)
        Instant fromDate = range.inclusiveFromDate(); // null = -infinity
        Instant toDate = range.exclusiveToDate(); // null = +infinity

        boolean afterOrAtStart = fromDate == null || !now.isBefore(fromDate);
        boolean beforeEnd = toDate == null || now.isBefore(toDate);

        return afterOrAtStart && beforeEnd;
    }

    private record ActiveOrLatestRecord(
            Record record,
            int order,
            boolean active
    ) {}
}
