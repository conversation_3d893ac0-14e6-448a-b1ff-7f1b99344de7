package cz.kpsys.portaro.erp.employee.detail;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.grid.RecordRow;
import jakarta.annotation.Nullable;
import lombok.NonNull;

public record EmployeeDetailResponse(
        @Nullable Department homeDepartment,

        // Pracovni pomer
        @Nullable RecordRow employmentRecord,
        @Nullable Integer employmentOrder,
        @NonNull Boolean employmentActive,

        // Pracovni karta
        @Nullable RecordRow personCardRecord,
        @Nullable Integer personCardOrder,
        @NonNull Boolean personCardActive
) {}