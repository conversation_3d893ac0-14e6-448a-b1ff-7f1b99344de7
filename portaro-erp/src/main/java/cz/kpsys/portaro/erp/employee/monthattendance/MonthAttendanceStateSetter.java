package cz.kpsys.portaro.erp.employee.monthattendance;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheDeleter;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.edit.fieldshierarchy.ContentFieldValue;
import cz.kpsys.portaro.record.grid.edit.GridCellEditRequest;
import cz.kpsys.portaro.record.grid.edit.GridEditRequest;
import cz.kpsys.portaro.record.search.ConvertingStandardSearch;
import cz.kpsys.portaro.record.search.SearchServiceBackedSearch;
import cz.kpsys.portaro.search.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import cz.kpsys.portaro.record.grid.edit.RecordGridRowEditor;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@FieldDefaults(level = AccessLevel.PROTECTED, makeFinal = true)
@RequiredArgsConstructor
public class MonthAttendanceStateSetter {

    @NonNull RecordGridRowEditor recordGridRowEditor;
    @NonNull SearchService<InternalSearchResult<String, MapBackedParams, RangePaging>, MapBackedParams> recordSearchService;
    @NonNull Converter<List<String>, List<Record>> detailedSearchResultConverter;
    @NonNull CacheDeleter<Record> recordCache;

    public void setWorkAttendanceState(@NonNull MapBackedParams allParams, @NonNull String workAttendanceStateId, Department ctx, UserAuthentication currentAuth) {
        ConvertingStandardSearch<String, Record, RangePaging> search = ConvertingStandardSearch.of(new SearchServiceBackedSearch<>(recordSearchService), detailedSearchResultConverter);
        search.fetch(RangePaging.forAll(), null, allParams, currentAuth, ctx, CacheMode.NONE);
        List<Record> records = search.getLastResult().content();

        // SearchIterator<MapBackedParams, Record> iterator = new SearchIterator<>(search, allParams, ctx, currentAuth);
        // iterator.iterate(SearchIterator.LIMIT_ITEMS_NO_LIMIT, -1, recordCache, records -> setWorkAttendanceStateToRecords(records, workAttendanceStateId, ctx, currentAuth));

        setWorkAttendanceStateToRecords(records, workAttendanceStateId, ctx, currentAuth);
    }

    private void setWorkAttendanceStateToRecords(List<Record> records, @NonNull String workAttendanceStateId, Department ctx, UserAuthentication currentAuth) {
        FieldId fieldId = RecordSutinSpecificFields.MonthlyAttendanceFond.ApprovementState.State.TYPE_ID.toFieldIdWithAllFirstIndices();

        recordGridRowEditor.editCells(new GridEditRequest(records.stream().map(record -> new GridCellEditRequest(
                record,
                fieldId,
                new ContentFieldValue<>(workAttendanceStateId)
        )).toList()), ctx, currentAuth);
    }
}
