package cz.kpsys.portaro.erp.employee.monthattendance;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.localization.LocalizationCodes;
import cz.kpsys.portaro.search.MapBackedParams;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping(MonthAttendanceStateSetterApiController.MONTH_ATTENDANCE_CONTROLLER_PATH)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MonthAttendanceStateSetterApiController {

    public static final String MONTH_ATTENDANCE_CONTROLLER_PATH = "/api/month-attendance";
    private static final String SET_STATE_PATH = "/set-state";

    @NonNull MonthAttendanceStateSetter monthAttendanceStateSetter;

    @PostMapping(SET_STATE_PATH)
    public ActionResponse setState(MapBackedParams customParams,
                                   @RequestBody SetStateRequest request,
                                   @CurrentDepartment Department ctx,
                                   UserAuthentication currentAuth) {

        monthAttendanceStateSetter.setWorkAttendanceState(customParams, request.workAttendanceStateId, ctx, currentAuth);

        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Saved));
    }

    public record SetStateRequest(@NonNull String workAttendanceStateId) {}
}
