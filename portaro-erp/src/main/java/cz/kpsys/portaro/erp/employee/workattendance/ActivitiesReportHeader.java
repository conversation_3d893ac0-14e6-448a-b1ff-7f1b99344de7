package cz.kpsys.portaro.erp.employee.workattendance;

import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.erp.employee.workattendance.web.DayEnrichedActivities;
import cz.kpsys.portaro.erp.employee.workattendance.web.DayEnrichedActivity;
import lombok.NonNull;

import java.time.Duration;

public record ActivitiesReportHeader(

        @NonNull WorkCommitment workCommitment,

        @NonNull Duration fond,

        @NonNull Duration presence,

        @NonNull Duration holidays,

        @NonNull Duration plan

) {

    public static @NonNull ActivitiesReportHeader compute(@NonNull DayEnrichedActivities enrichedActivities, @NonNull EnrichedLocalDates days, @NonNull WorkCommitment workCommitment) {
        var fond = mandaysToHoursDuration(days.weekdaysCount(), workCommitment);
        var presence = DateUtils.sum(
                enrichedActivities.sumAll(DayEnrichedActivity::regularDuration),
                enrichedActivities.sumAll(DayEnrichedActivity::overtimeDuration),
                enrichedActivities.sumAll(DayEnrichedActivity::idpDuration));
        var holidays = mandaysToHoursDuration(days.holidaysCount(), workCommitment);
        var plan = (fond.isZero())
                ? presence // TODO: tímhle si nejsem jistý, ale pokud zaměstnanec nemá fond data, tak sutin ukazuje počet odpracovaných
                : mandaysToHoursDuration(days.workdaysCount(), workCommitment)
                    .minus(enrichedActivities.sumAll(DayEnrichedActivity::absenceDuration));

        return new ActivitiesReportHeader(
                workCommitment,
                fond,
                presence,
                holidays,
                plan
        );
    }

    private static Duration mandaysToHoursDuration(long dayCount, WorkCommitment workCommitment) {
        return Duration.ofHours((long) (dayCount * WorkCommitment.MANDAY_HOURS * workCommitment.ratio()));
    }

}
