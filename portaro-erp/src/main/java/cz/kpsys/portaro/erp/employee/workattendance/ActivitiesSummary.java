package cz.kpsys.portaro.erp.employee.workattendance;

import cz.kpsys.portaro.erp.employee.workattendance.web.DayEnrichedActivities;
import cz.kpsys.portaro.erp.employee.workattendance.web.DayEnrichedActivity;
import lombok.NonNull;

import java.time.Duration;
import java.util.function.Predicate;

public record ActivitiesSummary(

        @NonNull Duration overtimeWorkday,

        @NonNull Duration overtimeWeekendAndHoliday,

        @NonNull Duration totalOvertime,

        @NonNull Duration absenceDoctor,

        @NonNull Duration absenceVacation,

        @NonNull Duration absenceIllness,

        @NonNull Duration absenceOcr,

        @NonNull Duration absenceParagraph,

        @NonNull Duration absenceCompensatory,

        @NonNull Duration absenceUnpaid,

        @NonNull Duration absenceUnexcused,

        @NonNull Duration absenceMaternity,

        @NonNull Duration absencePaternity,

        @NonNull Duration absenceObstacles,

        @NonNull Duration absenceChildrenAndYouth,

        @NonNull Duration totalAbsence,

        @NonNull Duration onCallDuty,

        @NonNull Duration idp

) {

    public static @NonNull ActivitiesSummary summarize(@NonNull ActivitiesReportHeader header, @NonNull DayEnrichedActivities enrichedActivities) {
        var overtimeWorkday = enrichedActivities.sumOvertime(DayEnrichedActivity::isWorkday);
        var overtimeWeekendAndHoliday = enrichedActivities.sumOvertime(Predicate.not(DayEnrichedActivity::isWorkday));
        var absenceCompensatory = enrichedActivities.sumAbsence(AbsenceReason.COMPENSATION);
        // Celkový přesčas dokáže jít do mínusu = počítá se z očekávaných hodin
        var totalOvertime = header.presence()
                .minus(header.plan())
                .minus(absenceCompensatory);

        var absenceDoctor = enrichedActivities.sumAbsence(AbsenceReason.DOCTOR_VISIT);
        var absenceVacation = enrichedActivities.sumAbsence(AbsenceReason.VACATION);
        var absenceIllness = enrichedActivities.sumAbsence(AbsenceReason.ILLNESS);
        var absenceOcr = enrichedActivities.sumAbsence(AbsenceReason.OCR);
        var absenceParagraph = enrichedActivities.sumAbsence(AbsenceReason.PARAGRAPH);
        var absenceUnpaid = enrichedActivities.sumAbsence(AbsenceReason.UNPAID);
        var absenceUnexcused = enrichedActivities.sumAbsence(AbsenceReason.UNEXCUSED);
        var absenceMaternity = enrichedActivities.sumAbsence(AbsenceReason.MATERNITY);
        var absencePaternity = enrichedActivities.sumAbsence(AbsenceReason.PATERNITY);
        var absenceObstacles = enrichedActivities.sumAbsence(AbsenceReason.EMPLOYER_OBSTACLES);
        var absenceChildrenAndYouth = enrichedActivities.sumAbsence(AbsenceReason.EVENTS);
        var totalAbsence = enrichedActivities.sumAll(DayEnrichedActivity::absenceDuration);
        var onCallDuty = enrichedActivities.sumAll(DayEnrichedActivity::onCallDutyDuration);
        var idp = enrichedActivities.sumAll(DayEnrichedActivity::idpDuration);

        return new ActivitiesSummary(
                overtimeWorkday,
                overtimeWeekendAndHoliday,
                totalOvertime,
                absenceDoctor,
                absenceVacation,
                absenceIllness,
                absenceOcr,
                absenceParagraph,
                absenceCompensatory,
                absenceUnpaid,
                absenceUnexcused,
                absenceMaternity,
                absencePaternity,
                absenceObstacles,
                absenceChildrenAndYouth,
                totalAbsence,
                onCallDuty,
                idp
        );
    }

}
