package cz.kpsys.portaro.erp.employee.workattendance;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Optional;
import java.util.UUID;

public record Activity(

        @NonNull
        UUID id,

        @NonNull
        UUID userId,

        @NonNull
        LocalDate startDay,

        @NonNull
        Instant startDate,

        @NonNull
        Duration regularDuration,

        @NonNull
        Duration overtimeDuration,

        @NonNull
        Duration onCallDutyDuration,

        @NonNull
        Duration absenceDuration,

        @NonNull
        Optional<AbsenceReason> absenceReason,

        @NonNull
        Duration idpDuration

) implements IdentifiedRecord<UUID> {

    public static Activity newWork(@NonNull Record workReportRecord,
                                   @NonNull UUID userId,
                                   @NonNull LocalDate startDay,
                                   @NonNull Duration regularDuration,
                                   @NonNull Duration overtimeDuration,
                                   @NonNull Provider<ZoneId> timeZoneProvider) {
        return new Activity(
                workReportRecord.getId(),
                userId,
                startDay,
                startDay.atStartOfDay().atZone(timeZoneProvider.get()).toInstant(),
                regularDuration,
                overtimeDuration,
                Duration.ZERO,
                Duration.ZERO,
                Optional.empty(),
                Duration.ZERO
        );
    }

    public static Activity newOnCall(@NonNull Record workReportRecord,
                                     @NonNull UUID userId,
                                     @NonNull LocalDate startDay,
                                     @NonNull Duration onCallDutyDuration,
                                     @NonNull Duration overtimeDuration,
                                     @NonNull Provider<ZoneId> timeZoneProvider) {
        assertNoOvertime(overtimeDuration, workReportRecord, startDay); // No overtimes when on call duty
        return new Activity(
                workReportRecord.getId(),
                userId,
                startDay,
                startDay.atStartOfDay().atZone(timeZoneProvider.get()).toInstant(),
                Duration.ZERO,
                Duration.ZERO,
                onCallDutyDuration,
                Duration.ZERO,
                Optional.empty(),
                Duration.ZERO
        );
    }

    public static Activity newIdp(@NonNull Record workReportRecord,
                                  @NonNull UUID userId,
                                  @NonNull LocalDate startDay,
                                  @NonNull Duration idpDuration,
                                  @NonNull Duration overtimeDuration,
                                  @NonNull Provider<ZoneId> timeZoneProvider) {
        assertNoOvertime(overtimeDuration, workReportRecord, startDay); // No overtimes when idp
        return new Activity(
                workReportRecord.getId(),
                userId,
                startDay,
                startDay.atStartOfDay().atZone(timeZoneProvider.get()).toInstant(),
                Duration.ZERO,
                Duration.ZERO,
                Duration.ZERO,
                Duration.ZERO,
                Optional.empty(),
                idpDuration
        );
    }

    public static Activity newAbsence(@NonNull Record workReportRecord,
                                      @NonNull UUID userId,
                                      @NonNull LocalDate startDay,
                                      @NonNull Duration absenceDuration,
                                      @NonNull AbsenceReason absenceReason,
                                      @NonNull Provider<ZoneId> timeZoneProvider) {
        return new Activity(
                workReportRecord.getId(),
                userId,
                startDay,
                startDay.atStartOfDay().atZone(timeZoneProvider.get()).toInstant(),
                Duration.ZERO,
                Duration.ZERO,
                Duration.ZERO,
                absenceDuration,
                Optional.of(absenceReason),
                Duration.ZERO
        );
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, Activity.class);
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }

    private static void assertNoOvertime(Duration duration, Record workReportRecord, LocalDate date) {
        if (!duration.isZero()) {
            throw new IllegalArgumentException("Work report %s at %s has overtime, which is not allowed!".formatted(workReportRecord, date));
        }
    }

}
