package cz.kpsys.portaro.erp.employee.workattendance;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.LinkedRecordSearchLoader;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.*;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondWorkReport.WorkInIdp;
import cz.kpsys.portaro.object.AcceptableValueGroupItem;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.value.AcceptableValueFieldValue;
import cz.kpsys.portaro.record.detail.value.LocalDateFieldValue;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.search.restriction.FieldTypeSearchField;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.In;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ActivitySearchLoader {

    @NonNull Provider<ZoneId> erpRecordsTimeZoneProvider;
    @NonNull LinkedRecordSearchLoader activityBySalaryIdSearchLoader;
    @NonNull Provider<@NonNull Fond> workReportFondProvider;
    @NonNull Provider<@NonNull Fond> absenceReportFondProvider;
    @NonNull Provider<@NonNull Fond> operatedMachineReportFondProvider;
    @NonNull Provider<@NonNull Fond> overheadReportFondProvider;
    @NonNull Provider<@NonNull Fond> machineReportFondProvider;
    @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader;

    @Transactional(readOnly = true)
    public List<Activity> load(@NonNull UUID userRecordId, @NonNull List<Record> salaries, @NonNull List<? extends Record> daysInPeriod, @NonNull Department ctx) {
        var ignoredFond = machineReportFondProvider.get();
        var salaryIds = ListUtil.convertStrict(salaries, Record::getId);
        var dayIds = ListUtil.convertStrict(daysInPeriod, Record::getId);
        List<Record> workReports = activityBySalaryIdSearchLoader.loadByTargetRecordIds(
                salaryIds,
                List.of(new Term<>(FieldTypeSearchField.loadOfLink(CostDate.Value.TYPE_ID, fieldTypeSearchFieldLoader), new In(dayIds))),
                ctx);
        return workReports.stream()
                .filter(record -> !record.getFond().equals(ignoredFond))
                .map(report -> map(userRecordId, report))
                .toList();
    }

    private @NonNull Activity map(UUID workerId, Record workReportRecord) {
        if (isWorkCostReport(workReportRecord)) {
            return mapWork(workerId, workReportRecord);
        }

        if (workReportRecord.getFond().equals(absenceReportFondProvider.get())) {
            return mapAbsence(workerId, workReportRecord);
        }

        throw new UnsupportedOperationException("Unknown report type! Record: %s".formatted(workReportRecord));
    }

    private @NonNull Activity mapWork(UUID workerId, Record workReportRecord) {
        Duration workHours = CostReportDuration.RegularValue.FIELD_FINDER
                .findFirstIn(workReportRecord.getDetail(), NumberFieldValue::extract)
                .map(ActivitySearchLoader::hoursToDuration)
                .orElse(Duration.ZERO);
        Duration overtimeDurationHours = CostReportDuration.OvertimeValue.FIELD_FINDER
                .findFirstIn(workReportRecord.getDetail(), NumberFieldValue::extract)
                .map(ActivitySearchLoader::hoursToDuration)
                .orElse(Duration.ZERO);
        LocalDate startDay = CostDate.Value.FIELD_FINDER.getFirstIn(workReportRecord.getDetail(), LocalDateFieldValue::extract);

        var costType = WorkType.Value.FIELD_FINDER.findFirstIn(workReportRecord.getDetail(), AcceptableValueFieldValue::extract);
        boolean onCall = costType
                .map(ActivitySearchLoader::isOnCallDuty)
                .orElse(false); // Pokud chybí typ činnosti, bude to nejspíš práce a ne on call
        if (onCall) {
            return Activity.newOnCall(
                    workReportRecord,
                    workerId,
                    startDay,
                    workHours,
                    overtimeDurationHours,
                    erpRecordsTimeZoneProvider
            );
        }

        Optional<Identified<String>> isIdp = workReportRecord.findFirst(WorkInIdp.YesNo.FIELD_FINDER);
        if (isIdp.isPresent() && isIdp.get().getId().equalsIgnoreCase("yes")) {
            return Activity.newIdp(
                    workReportRecord,
                    workerId,
                    startDay,
                    workHours,
                    overtimeDurationHours,
                    erpRecordsTimeZoneProvider
            );
        }

        return Activity.newWork(
                workReportRecord,
                workerId,
                startDay,
                workHours,
                overtimeDurationHours,
                erpRecordsTimeZoneProvider
        );
    }

    private @NonNull Activity mapAbsence(UUID workerId, Record workReportRecord) {
        BigDecimal overtimeDurationHours = CostReportDuration.OvertimeValue.FIELD_FINDER.findFirstIn(workReportRecord.getDetail(), NumberFieldValue::extract).orElse(BigDecimal.ZERO);
        NumberUtil.requireZero(overtimeDurationHours, "overtimeDurationHours"); // No overtimes for absence!
        AcceptableValueGroupItem costType = WorkType.Value.FIELD_FINDER.getFirstIn(workReportRecord.getDetail(), AcceptableValueFieldValue::extract);

        BigDecimal absenceHours = CostReportDuration.RegularValue.FIELD_FINDER.getFirstIn(workReportRecord.getDetail(), NumberFieldValue::extract);
        LocalDate startDay = CostDate.Value.FIELD_FINDER.getFirstIn(workReportRecord.getDetail(), LocalDateFieldValue::extract);

        return Activity.newAbsence(
                workReportRecord,
                workerId,
                startDay,
                hoursToDuration(absenceHours),
                getAbsenceReason(costType),
                erpRecordsTimeZoneProvider
        );
    }

    private static AbsenceReason getAbsenceReason(AcceptableValueGroupItem costType) {
        return switch (costType.getId()) {
            case AbsenceType.Value.VALUE_ABS_DOCTOR_VISIT -> AbsenceReason.DOCTOR_VISIT;
            case AbsenceType.Value.VALUE_ABS_VACATION -> AbsenceReason.VACATION;
            case AbsenceType.Value.VALUE_ABS_ILLNESS -> AbsenceReason.ILLNESS;
            case AbsenceType.Value.VALUE_ABS_OCR -> AbsenceReason.OCR;
            case AbsenceType.Value.VALUE_ABS_PARAGRAPH -> AbsenceReason.PARAGRAPH;
            case AbsenceType.Value.VALUE_ABS_COMPENSATION -> AbsenceReason.COMPENSATION;
            case AbsenceType.Value.VALUE_ABS_UNPAID -> AbsenceReason.UNPAID;
            case AbsenceType.Value.VALUE_ABS_UNEXCUSED -> AbsenceReason.UNEXCUSED;
            case AbsenceType.Value.VALUE_ABS_MATERNITY -> AbsenceReason.MATERNITY;
            case AbsenceType.Value.VALUE_ABS_PATERNITY -> AbsenceReason.PATERNITY;
            case AbsenceType.Value.VALUE_ABS_EMPLOYER_OBSTACLES -> AbsenceReason.EMPLOYER_OBSTACLES;
            case AbsenceType.Value.VALUE_ABS_EVENTS -> AbsenceReason.EVENTS;
            default ->
                    throw new IllegalStateException("Unexpected cost type id " + costType.getId() + " (in acceptable values group " + costType.getGroupId() + ")");
        };
    }

    private static Duration hoursToDuration(BigDecimal regularDurationHours) {
        return Duration.ofMinutes(Math.round(regularDurationHours.doubleValue() * 60));
    }

    private boolean isWorkCostReport(Record record) {
        var fond = record.getFond();
        return fond.equals(workReportFondProvider.get())
                || fond.equals(operatedMachineReportFondProvider.get())
                || fond.equals(overheadReportFondProvider.get());
    }

    private static boolean isOnCallDuty(AcceptableValueGroupItem costType) {
        return switch (costType.getId()) {
            case WorkType.Value.VALUE_WORK_PRESENTIAL -> false;
            case WorkType.Value.VALUE_WORK_ON_CALL_DUTY -> true;
            default -> throw new IllegalStateException("Unexpected cost type id " + costType.getId()
                    + " (in acceptable values group " + costType.getGroupId() + ")");
        };
    }

}
