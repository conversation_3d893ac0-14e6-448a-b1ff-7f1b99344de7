package cz.kpsys.portaro.erp.employee.workattendance;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.NonNull;

import java.time.LocalDate;
import java.util.Set;

public record EnrichedLocalDate(

        @NonNull
        LocalDate value,

        @NonNull
        Set<DateLabel> labels

) {

    public boolean isWorkday() {
        return labels.stream().allMatch(DateLabel::isWorkday);
    }

    public boolean isWeekday() {
        return !isWeekend();
    }

    public boolean isWeekend() {
        return isSaturday() || isSunday();
    }

    public boolean isSaturday() {
        return is(DateLabel.SATURDAY);
    }

    public boolean isSunday() {
        return is(DateLabel.SUNDAY);
    }

    public boolean isWorkdayHoliday() {
        return isWeekday() && is(DateLabel.HOLIDAY);
    }

    public boolean is(DateLabel label) {
        return labels.contains(label);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, EnrichedLocalDate.class, EnrichedLocalDate::value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }
}
