package cz.kpsys.portaro.erp.employee.workattendance;

import lombok.NonNull;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;

public record EnrichedLocalDates(

        @NonNull
        List<EnrichedLocalDate> days

) {

    public Stream<EnrichedLocalDate> stream() {
        return days.stream();
    }

    public Stream<LocalDate> streamDates() {
        return stream().map(EnrichedLocalDate::value);
    }

    public EnrichedLocalDate get(@NonNull LocalDate localDate) {
        return stream()
                .filter(day -> day.value().equals(localDate))
                .findFirst()
                .orElseThrow(() -> {
                    if (days.isEmpty()) {
                        return new IllegalArgumentException("This EnrichedLocalDates is empty so does not contain given day " + localDate);
                    }
                    LocalDate min = stream().min(Comparator.comparing(EnrichedLocalDate::value)).map(EnrichedLocalDate::value).orElseThrow();
                    LocalDate max = stream().max(Comparator.comparing(EnrichedLocalDate::value)).map(EnrichedLocalDate::value).orElseThrow();
                    return new IllegalArgumentException("This EnrichedLocalDates does not contain given day %s. Contains only %s values from %s to %s".formatted(localDate, days.size(), min, max));
                });
    }

    /// Days except weekends and holidays
    public long workdaysCount() {
        return stream().filter(EnrichedLocalDate::isWorkday).count();
    }

    /// Days except weekends
    public long weekdaysCount() {
        return stream().filter(EnrichedLocalDate::isWeekday).count();
    }

    public long holidaysCount() {
        return stream().filter(EnrichedLocalDate::isWorkdayHoliday).count();
    }

    public LocalDate min() {
        return streamDates().min(Comparator.naturalOrder()).orElseThrow();
    }

    public LocalDate max() {
        return streamDates().max(Comparator.naturalOrder()).orElseThrow();
    }
}
