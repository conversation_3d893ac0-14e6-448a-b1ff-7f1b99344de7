package cz.kpsys.portaro.erp.employee.workattendance;

import cz.kpsys.portaro.commons.date.NotEmptyBoundedDatetimeRange;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.EmploymentStatus.Validity.TimeValidity;
import cz.kpsys.portaro.erp.LinkedRecordSearchLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.search.restriction.FieldTypeSearchField;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.IsSuperset;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RangedEmploymentStatusByUserRecordIdLoader {

    @NonNull LinkedRecordSearchLoader employmentStatusByUserIdSearchLoader;
    @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader;

    public List<Record> load(@NonNull UUID userRecordId, @NonNull NotEmptyBoundedDatetimeRange period, @NonNull Department ctx) {
        return employmentStatusByUserIdSearchLoader.loadByTargetRecordId(
                userRecordId,
                List.of(new Term<>(FieldTypeSearchField.loadOfValue(TimeValidity.TYPE_ID, fieldTypeSearchFieldLoader), IsSuperset.of(period))),
                ctx
        );
    }

}
