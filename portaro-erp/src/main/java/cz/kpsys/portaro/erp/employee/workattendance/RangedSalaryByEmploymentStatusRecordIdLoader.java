package cz.kpsys.portaro.erp.employee.workattendance;

import cz.kpsys.portaro.commons.date.NotEmptyBoundedDatetimeRange;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.LinkedRecordSearchLoader;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondSalaryCost.Validity.TimeValidity;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.search.restriction.FieldTypeSearchField;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Overlaps;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RangedSalaryByEmploymentStatusRecordIdLoader {

    @NonNull LinkedRecordSearchLoader salaryByEmploymentStatusIdSearchLoader;
    @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader;

    public List<Record> load(@NonNull List<Record> employmentStatuses, @NonNull NotEmptyBoundedDatetimeRange period, @NonNull Department ctx) {
        var employmentStatusIds = ListUtil.convertStrict(employmentStatuses, Record::getId);
        return salaryByEmploymentStatusIdSearchLoader.loadByTargetRecordIds(
                employmentStatusIds,
                List.of(new Term<>(FieldTypeSearchField.loadOfValue(TimeValidity.TYPE_ID, fieldTypeSearchFieldLoader), Overlaps.with(period))),
                ctx
        );
    }

}
