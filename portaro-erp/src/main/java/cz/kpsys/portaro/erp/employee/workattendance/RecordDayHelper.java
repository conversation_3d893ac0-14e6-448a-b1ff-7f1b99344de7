package cz.kpsys.portaro.erp.employee.workattendance;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.date.HolidaysFactory;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.Holiday;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.Weekend;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.RecordDetailConstants;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.load.RecordDayIdLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.Day.Date;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordDayHelper {

    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull RecordDayIdLoader recordDayIdLoader;
    @NonNull Converter<List<UUID>, List<? extends Record>> idsToRecordsConverter;
    @NonNull Provider<@NonNull Fond> dayFondProvider;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull Provider<@NonNull ZoneId> timeZoneProvider;

    @Transactional
    public List<Record> createDaysFor(int year, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        var from = LocalDate.of(year, 1, 1);
        var to = LocalDate.of(year, 12, 31).plusDays(1);
        var holidays = HolidaysFactory.getHolidaysFor(year);
        Fond dayFond = dayFondProvider.get();

        var existingRecordDays = idsToRecordsConverter.convert(recordDayIdLoader.load(from, to, timeZoneProvider));
        Set<LocalDate> existingDays = Objects.requireNonNull(existingRecordDays).stream()
                .map(Date.FIELD_FINDER::getFirstIn)
                .collect(Collectors.toUnmodifiableSet());

        int countDaysToCreate = 366 - existingDays.size();
        var createdDays = new ArrayList<Record>(countDaysToCreate);
        for (var day = from; day.isBefore(to); day = day.plusDays(1)) {
            if (!existingDays.contains(day)) {
                createdDays.add(createDay(day, dayFond, holidays, ctx, currentAuth));
            }
        }

        return createdDays;
    }

    @Transactional
    public List<Record> enrichExistingDaysIn(int year, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        var from = LocalDate.of(year, 1, 1);
        var to = LocalDate.of(year, 12, 31).plusDays(1);
        var holidays = HolidaysFactory.getHolidaysFor(year);

        List<Record> enrichedDays = new ArrayList<>();
        var existingDayRecords = Objects.requireNonNull(
                idsToRecordsConverter.convert(recordDayIdLoader.load(from, to, timeZoneProvider)));
        for (var dayRecord : existingDayRecords) {
            var editation = createExistingRecordEditation(dayRecord, ctx, currentAuth);
            var date = dayRecord.getFirst(Date.FIELD_FINDER);

            fillWeekendIfNotPresent(dayRecord, date, editation, ctx, currentAuth);
            fillHolidayIfNotPresent(dayRecord, date, holidays, editation, ctx, currentAuth);
            if (!editation.isRevisionSaved()) { // There were changes
                enrichedDays.add(dayRecord);
            }
            editation.saveIfModified(ctx, currentAuth);
        }

        return enrichedDays;
    }

    private Record createDay(LocalDate date, Fond dayFond, HolidaysFactory.Holidays holidays, Department ctx, UserAuthentication currentAuth) {
        RecordEditation creation = recordEditationFactory
                .on(ctx)
                .ofNew(dayFond)
                .build(currentAuth);

        var dateString = RecordDetailConstants.RAW_LOCAL_DATE_FORMAT.format(date);
        var entrySubfield = recordEntryFieldTypeIdResolver.getEntryNativeSubfield(dayFond);
        recordEditationHelper.setStringSubfieldValue(dateString, false, entrySubfield.existingParent(), false, entrySubfield, creation, ctx, currentAuth);

        fillWeekend(date, creation, ctx, currentAuth);
        fillHoliday(date, holidays, creation, ctx, currentAuth);

        return creation.publish(ctx, currentAuth).getRecord();
    }

    private void fillWeekendIfNotPresent(Record dayRecord, LocalDate date, RecordEditation editation, Department ctx, UserAuthentication currentAuth) {
        var maybeWeekend = dayRecord.findFirst(Weekend.Value.FIELD_FINDER);
        if (maybeWeekend.isPresent()) {
            return;
        }
        fillWeekend(date, editation, ctx, currentAuth);
    }

    private void fillWeekend(LocalDate date, RecordEditation editation, Department ctx, UserAuthentication currentAuth) {
        var dayOfWeek = date.getDayOfWeek();
        boolean isWeekend = dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;
        recordEditationHelper.setBooleanSubfieldValue(isWeekend, false, Weekend.TYPE_ID, false, Weekend.Value.TYPE_ID, editation, ctx, currentAuth);
    }

    private void fillHolidayIfNotPresent(Record dayRecord, LocalDate date, HolidaysFactory.Holidays holidays, RecordEditation editation, Department ctx, UserAuthentication currentAuth) {
        var maybeHoliday = dayRecord.findFirst(Holiday.Value.FIELD_FINDER);
        if (maybeHoliday.isPresent()) {
            return;
        }
        fillHoliday(date, holidays, editation, ctx, currentAuth);
    }

    private void fillHoliday(LocalDate date, HolidaysFactory.Holidays holidays, RecordEditation editation, Department ctx, UserAuthentication currentAuth) {
        recordEditationHelper.setBooleanSubfieldValue(holidays.isHoliday(date), false, Holiday.TYPE_ID, false, Holiday.Value.TYPE_ID, editation, ctx, currentAuth);
    }

    private RecordEditation createExistingRecordEditation(Record record, Department ctx, UserAuthentication currentAuth) {
        return recordEditationFactory
                .on(ctx)
                .ofExisting(record)
                .build(currentAuth);
    }

}
