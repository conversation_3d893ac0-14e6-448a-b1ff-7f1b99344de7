package cz.kpsys.portaro.erp.employee.workattendance;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.Day;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.load.RecordDayIdLoader;
import cz.kpsys.portaro.record.search.restriction.FieldTypeSearchField;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Between;
import cz.kpsys.portaro.search.restriction.matcher.In;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SearchLoadingRecordDayIdLoader implements RecordDayIdLoader {

    @NonNull ParameterizedSearchLoader<MapBackedParams, UUID> documentIdSearchLoader;
    @NonNull Provider<@NonNull Fond> dayFondProvider;
    @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader;

    @Override
    public List<UUID> load(@NonNull Instant from, @NonNull Instant exclusiveTo) {
        return documentIdSearchLoader.getContent(RangePaging.forAll(), p -> {
            p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
            p.set(CoreSearchParams.FACETS_ENABLED, false);
            p.set(CoreSearchParams.INCLUDE_DRAFT, false);
            p.set(CoreSearchParams.INCLUDE_DELETED, false);
            p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
            p.set(RecordConstants.SearchParams.ROOT_FOND, List.of(dayFondProvider.get()));
            p.set(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION, new Conjunction<>(
                    new Term<>(FieldTypeSearchField.loadOfValue(Day.Date.TYPE_ID, fieldTypeSearchFieldLoader), Between.ofExclusiveTo(from, exclusiveTo))
            ));
        });
    }

    @Override
    public List<UUID> load(@NonNull Collection<LocalDate> dates) {
        return documentIdSearchLoader.getContent(RangePaging.forAll(), p -> {
            p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
            p.set(CoreSearchParams.FACETS_ENABLED, false);
            p.set(CoreSearchParams.INCLUDE_DRAFT, false);
            p.set(CoreSearchParams.INCLUDE_DELETED, false);
            p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
            p.set(RecordConstants.SearchParams.ROOT_FOND, List.of(dayFondProvider.get()));
            p.set(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION, new Conjunction<>(
                    new Term<>(FieldTypeSearchField.loadOfValue(Day.Date.TYPE_ID, fieldTypeSearchFieldLoader), new In(dates))
            ));
        });
    }

}
