package cz.kpsys.portaro.erp.employee.workattendance;

import lombok.NonNull;

import java.util.ArrayList;
import java.util.List;

public record WorkAtendanceReport(

        @NonNull
        WorkAtendanceReportSummary summary,

        @NonNull
        List<WorkAtendanceReportDay> days

) {

        public static final WorkAtendanceReport EMPTY = new WorkAtendanceReport(WorkAtendanceReportSummary.EMPTY, List.of());

        public WorkAtendanceReport plus(WorkAtendanceReport other) {
                var catList = new ArrayList<>(days);
                catList.addAll(other.days);
                return new WorkAtendanceReport(
                        summary.plus(other.summary),
                        catList
                );
        }

}
