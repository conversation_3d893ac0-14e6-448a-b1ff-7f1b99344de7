package cz.kpsys.portaro.erp.employee.workattendance;

import lombok.NonNull;

import java.time.Duration;

public record WorkAtendanceReportSummary(

        @NonNull
        Duration cumulativeFond,

        @NonNull
        Duration presence,

        @NonNull
        Duration overtimeWorkday,

        @NonNull
        Duration overtimeWeekendAndHoliday,

        @NonNull
        Duration totalOvertime,

        @NonNull
        Duration overtimeBank,

        @NonNull
        Duration absenceDoctor,

        @NonNull
        Duration absenceVacation,

        @NonNull
        Duration absenceIllness,

        @NonNull
        Duration absenceOcr,

        @NonNull
        Duration absenceParagraph,

        @NonNull
        Duration absenceCompensatory,

        @NonNull
        Duration absenceUnpaid,

        @NonNull
        Duration absenceUnexcused,

        @NonNull
        Duration absenceMaternity,

        @NonNull
        Duration absencePaternity,

        @NonNull
        Duration absenceObstacles,

        @NonNull
        Duration absenceChildrenAndYouth,


        @NonNull
        Duration planComputed,

        @NonNull
        Duration allAbsencesSumComputed

) {

    public static final WorkAtendanceReportSummary EMPTY = new WorkAtendanceReportSummary(
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO,
            Duration.ZERO
    );

    public WorkAtendanceReportSummary plus(WorkAtendanceReportSummary other) {
        return new WorkAtendanceReportSummary(
                cumulativeFond.plus(other.cumulativeFond),
                presence.plus(other.presence),
                overtimeWorkday.plus(other.overtimeWorkday),
                overtimeWeekendAndHoliday.plus(other.overtimeWeekendAndHoliday),
                totalOvertime.plus(other.totalOvertime),
                overtimeBank.plus(other.overtimeBank),
                absenceDoctor.plus(other.absenceDoctor),
                absenceVacation.plus(other.absenceVacation),
                absenceIllness.plus(other.absenceIllness),
                absenceOcr.plus(other.absenceOcr),
                absenceParagraph.plus(other.absenceParagraph),
                absenceCompensatory.plus(other.absenceCompensatory),
                absenceUnpaid.plus(other.absenceUnpaid),
                absenceUnexcused.plus(other.absenceUnexcused),
                absenceMaternity.plus(other.absenceMaternity),
                absencePaternity.plus(other.absencePaternity),
                absenceObstacles.plus(other.absenceObstacles),
                absenceChildrenAndYouth.plus(other.absenceChildrenAndYouth),

                planComputed.plus(other.planComputed),
                allAbsencesSumComputed.plus(other.allAbsencesSumComputed)
        );
    }

}
