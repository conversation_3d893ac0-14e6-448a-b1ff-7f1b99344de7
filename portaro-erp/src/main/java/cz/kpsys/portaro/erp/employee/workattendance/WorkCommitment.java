package cz.kpsys.portaro.erp.employee.workattendance;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledRecord;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import lombok.NonNull;

public record WorkCommitment(

        @DecimalMin("0.0")
        @DecimalMax("1.0")
        @NonNull
        Double ratio

) implements LabeledRecord {

    public static final int MANDAY_HOURS = 8;

    public static final WorkCommitment NONE = new WorkCommitment(0.0);
    public static final WorkCommitment FULL = new WorkCommitment(1.0);

    public static WorkCommitment fromWeeklyHours(double weeklyHours) {
        return new WorkCommitment(weeklyHours / (MANDAY_HOURS * 5));
    }

    @Override
    public @NonNull Text text() {
        if (ratio() == 1.0) {
            return Texts.ofNative("<PERSON>lný úvazek");
        }
        if (ratio() == 0.75) {
            return Texts.ofNative("3/4 úvazek");
        }
        if (ratio() == 0.5) {
            return Texts.ofNative("Půl úvazek");
        }
        if (ratio() == 0.25) {
            return Texts.ofNative("1/4 úvazek");
        }
        return Texts.ofNative(ratio + " úvazek");
    }
}
