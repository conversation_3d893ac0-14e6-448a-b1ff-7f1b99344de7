package cz.kpsys.portaro.erp.employee.workattendance.web;

import cz.kpsys.portaro.erp.employee.workattendance.AbsenceReason;
import lombok.NonNull;

public enum AbsenceReasonResponse {

    DOCTOR_VISIT,
    VACATION,
    ILLNESS,
    OCR,
    PARA<PERSON>APH,
    COMPENSATION,
    UNPAID,
    UNEXCUSED,
    MATERNITY,
    PATERNITY,
    EMPLOYER_OBSTACLES,
    CHILD_CAMP;

    public static @NonNull AbsenceReasonResponse fromAbsenceReason(AbsenceReason absenceReason) {
        return switch (absenceReason) {
            case DOCTOR_VISIT -> AbsenceReasonResponse.DOCTOR_VISIT;
            case VACATION -> AbsenceReasonResponse.VACATION;
            case ILLNESS -> AbsenceReasonResponse.ILLNESS;
            case OCR -> AbsenceReasonResponse.OCR;
            case PARAGRAPH -> AbsenceReasonResponse.PARAGRAPH;
            case COMPENSATION -> AbsenceReasonResponse.COMPENSATION;
            case UNPAID -> AbsenceReasonResponse.UNPAID;
            case UNEXCUSED -> AbsenceReasonResponse.UNEXCUSED;
            case MATERNITY -> AbsenceReasonResponse.MATERNITY;
            case PATERNITY -> AbsenceReasonResponse.PATERNITY;
            case EMPLOYER_OBSTACLES -> AbsenceReasonResponse.EMPLOYER_OBSTACLES;
            case EVENTS -> AbsenceReasonResponse.CHILD_CAMP;
        };
    }

}
