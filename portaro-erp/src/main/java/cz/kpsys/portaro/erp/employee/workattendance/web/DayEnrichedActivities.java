package cz.kpsys.portaro.erp.employee.workattendance.web;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.erp.employee.workattendance.AbsenceReason;
import cz.kpsys.portaro.erp.employee.workattendance.Activity;
import cz.kpsys.portaro.erp.employee.workattendance.EnrichedLocalDate;
import cz.kpsys.portaro.erp.employee.workattendance.EnrichedLocalDates;
import lombok.NonNull;

import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public record DayEnrichedActivities(

        @NonNull
        List<DayEnrichedActivity> works

) {

    public static @NonNull DayEnrichedActivities fromActivities(@NonNull List<Activity> activities, EnrichedLocalDates days) {
        List<DayEnrichedActivity> dayEnrichedActivities = ListUtil.convertStrict(activities,
                activity -> DayEnrichedActivity.from(activity, days.get(activity.startDay())));
        return new DayEnrichedActivities(dayEnrichedActivities);
    }

    public Stream<DayEnrichedActivity> stream() {
        return works.stream();
    }

    public @NonNull LinkedHashMap<EnrichedLocalDate, List<DayEnrichedActivity>> groupDays(@NonNull EnrichedLocalDates days) {
        return days.stream()
                .collect(Collectors.toMap(
                        d -> d,
                        d -> works.stream().filter(work -> work.startDay().equals(d)).toList(),
                        (e1, _) -> e1,
                        LinkedHashMap::new
                ));
    }

    public @NonNull Duration sumAll(@NonNull Function<? super DayEnrichedActivity, Duration> durationFunction) {
        return stream()
                .map(durationFunction)
                .reduce(Duration.ZERO, Duration::plus);
    }

    public @NonNull Duration sumOvertime(@NonNull Predicate<DayEnrichedActivity> filter) {
        return stream()
                .filter(filter)
                .map(DayEnrichedActivity::overtimeDuration)
                .reduce(Duration.ZERO, Duration::plus);
    }

    public @NonNull Duration sumAbsence(@NonNull AbsenceReason absenceReason) {
        return stream()
                .filter(work -> work.absenceReason().map(abs -> abs.equals(absenceReason)).orElse(false))
                .map(DayEnrichedActivity::absenceDuration)
                .reduce(Duration.ZERO, Duration::plus);
    }

}
