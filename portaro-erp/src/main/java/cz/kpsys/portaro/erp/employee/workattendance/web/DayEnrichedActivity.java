package cz.kpsys.portaro.erp.employee.workattendance.web;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.erp.employee.workattendance.AbsenceReason;
import cz.kpsys.portaro.erp.employee.workattendance.Activity;
import cz.kpsys.portaro.erp.employee.workattendance.EnrichedLocalDate;
import lombok.NonNull;

import java.time.Duration;
import java.util.Optional;
import java.util.UUID;

public record DayEnrichedActivity(

        @NonNull
        UUID id,

        @NonNull
        UUID userId,

        @NonNull
        EnrichedLocalDate startDay,

        @NonNull
        Duration regularDuration,

        @NonNull
        Duration overtimeDuration,

        @NonNull
        Duration onCallDutyDuration,

        @NonNull
        Duration absenceDuration,

        @NonNull
        Optional<AbsenceReason> absenceReason,

        @NonNull
        Duration idpDuration

) implements IdentifiedRecord<UUID> {

    public static @NonNull DayEnrichedActivity from(@NonNull Activity activity, @NonNull EnrichedLocalDate enrichedLocalDate) {
        return new DayEnrichedActivity(
                activity.id(),
                activity.userId(),
                enrichedLocalDate,
                activity.regularDuration(),
                activity.overtimeDuration(),
                activity.onCallDutyDuration(),
                activity.absenceDuration(),
                activity.absenceReason(),
                activity.idpDuration()
        );
    }

    public boolean isWorkday() {
        return startDay.isWorkday();
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, DayEnrichedActivity.class);
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }
}

