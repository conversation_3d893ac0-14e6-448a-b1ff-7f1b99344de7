package cz.kpsys.portaro.erp.employee.workattendance.web;

import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.date.NotEmptyBoundedDatetimeRange;
import cz.kpsys.portaro.commons.date.ZonedYearMonth;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.EmploymentStatus.EmploymentData.PersonalNumber;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.Holiday;
import cz.kpsys.portaro.erp.employee.workattendance.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.web.GenericApiController;
import cz.kpsys.portaro.web.swagger.SwaggerConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.Instant;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.TextStyle;
import java.util.*;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.Day.Date;
import static java.time.DayOfWeek.SATURDAY;
import static java.time.DayOfWeek.SUNDAY;

@Tag(name = "work-attendance", description = "ERP Work Attendance API")
@RequestMapping
@ResponseBody
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class WorkAttendanceApiController extends GenericApiController {

    public static final String OVERVIEW_PATH = "/api/work-attendance/overview";
    public static final String DAYS_PATH = "/api/work-attendance/days";

    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull ActivitySearchLoader activitySearchLoader;
    @NonNull Provider<ZoneId> httpRequestsTimeZoneProvider;
    @NonNull WorkAttendanceReportLoader workAttendanceReportLoader;
    @NonNull WorkAttendanceToResponseMapper workAttendanceToResponseMapper;
    @NonNull RecordDayLoader recordDayLoader;
    @NonNull RangedEmploymentStatusByUserRecordIdLoader rangedEmploymentStatusByUserRecordIdLoader;
    @NonNull RangedSalaryByEmploymentStatusRecordIdLoader rangedSalaryByEmploymentStatusRecordIdLoader;
    @NonNull RangedWorkCommitmentByUserRecordIdLoader rangedWorkCommitmentByUserRecordIdLoader;
    @NonNull Provider<@NonNull Fond> userPersonAuthorityFondProvider;

    @Operation(summary = "Get overview data for work-attendance table")
    @GetMapping(OVERVIEW_PATH)
    public WorkAttendanceOverviewResponse getOverview(@RequestParam("userRecordId")
                                                      @Parameter(description = "Record id of user, data will be generated for", example = "0193bb11-c572-7424-8743-************", required = true)
                                                      UUID userRecordId,

                                                      @RequestParam(value = "month", required = false)
                                                      @Parameter(schema = @Schema(implementation = ZonedYearMonth.class), description = "Month, data will be generated for", example = SwaggerConstants.SCHEMA_EXAMPLE_YEAR_MONTH)
                                                      ZonedYearMonth yearMonth,

                                                      @CurrentDepartment Department ctx) {

        YearMonth defaultedYearMonth = yearMonth == null
                ? YearMonth.now()
                : yearMonth.yearmonth();
        ZoneId zoneId = yearMonth == null
                ? httpRequestsTimeZoneProvider.get()
                : yearMonth.zoneId();
        NotEmptyBoundedDatetimeRange period = DatetimeRange.ofMonth(defaultedYearMonth, zoneId);
        Record userRecord = recordLoader.getById(userRecordId);
        Assert.isTrue(userRecord.getFond().equals(userPersonAuthorityFondProvider.get()), () -> "Loaded record is not from Osoby: %s".formatted(userRecord)); // TODO: udělat lepší kontrolu fondů načtených recordů?
        var daysInPeriod = recordDayLoader.load(period);
        EnrichedLocalDates days = enrichDays(daysInPeriod);

        List<Record> employmentStatuses = rangedEmploymentStatusByUserRecordIdLoader.load(userRecordId, period, ctx);
        Assert.state(employmentStatuses.size() <= 1, () -> "Too many employment statuses loaded! %s".formatted(employmentStatuses));
        if (employmentStatuses.isEmpty()) {
            return WorkAttendanceOverviewResponse.warning(days, "Uživatel %s nemá platný pracovní poměr pro měsíc %s"
                    .formatted(userRecord, defaultedYearMonth.getMonth().getDisplayName(TextStyle.FULL_STANDALONE, Locale.of("cs", "CZ"))));
        }

        List<Record> salaries = rangedSalaryByEmploymentStatusRecordIdLoader.load(employmentStatuses, period, ctx);
        if (salaries.size() > 1) {
            log.warn("Loaded > 1 salaries for {}: {}", userRecord, salaries);
        }
        List<Activity> activities = activitySearchLoader.load(userRecordId, salaries, daysInPeriod, ctx);

        Record jobFile = employmentStatuses.getFirst();
        String personalNumber = jobFile.getFirst(PersonalNumber.FIELD_FINDER);
        WorkCommitment workCommitment = rangedWorkCommitmentByUserRecordIdLoader.load(userRecordId, period, ctx);
        WorkAtendanceReport attendanceSystemReport = workAttendanceReportLoader.load(List.of(personalNumber), days, ctx);

        return workAttendanceToResponseMapper.mapAll(workCommitment, activities, attendanceSystemReport, days);
    }

    @Operation(summary = "Get days for work-attendance (days only) table")
    @GetMapping(DAYS_PATH)
    public List<DayAttendanceResponse> getDays(@RequestParam("userRecordId")
                                               @Parameter(description = "Record id of user, data will be generated for", example = "0193bb11-c572-7424-8743-************", required = true)
                                               UUID userRecordId,

                                               @RequestParam("fromDate")
                                               @Parameter(description = "Inclusive from-date", required = true, example = SwaggerConstants.SCHEMA_EXAMPLE_FROM_INSTANT)
                                               Instant fromDate,

                                               @RequestParam("toDate")
                                               @Parameter(description = "Exclusive to-date", required = true, example = SwaggerConstants.SCHEMA_EXAMPLE_TO_INSTANT)
                                               Instant toDate,

                                               @CurrentDepartment Department ctx) {

        NotEmptyBoundedDatetimeRange period = DatetimeRange.of(fromDate, toDate).toNotEmptyBounded();
        Record userRecord = recordLoader.getById(userRecordId);
        Assert.isTrue(userRecord.getFond().equals(userPersonAuthorityFondProvider.get()), () -> "Loaded record is not from Osoby: %s".formatted(userRecord)); // TODO: udělat lepší kontrolu fondů načtených recordů?
        var daysInPeriod = recordDayLoader.load(period);
        EnrichedLocalDates days = enrichDays(daysInPeriod);

        List<Record> employmentStatuses = rangedEmploymentStatusByUserRecordIdLoader.load(userRecordId, period, ctx);
        List<Record> salaries = rangedSalaryByEmploymentStatusRecordIdLoader.load(employmentStatuses, period, ctx);
        List<Activity> activities = activitySearchLoader.load(userRecordId, salaries, daysInPeriod, ctx);

        List<String> personalNumbers = employmentStatuses.stream()
                .map(jobFile -> jobFile.getFirst(PersonalNumber.FIELD_FINDER))
                .toList();
        if (personalNumbers.isEmpty()) {
            throw new ItemNotFoundException("Personal number", userRecord, Texts.ofNative("Uživatel %s nemá platné osobní číslo pro období %s - %s".formatted(userRecord, fromDate, toDate)));
        }

        WorkAtendanceReport attendanceSystemReport = workAttendanceReportLoader.load(personalNumbers, days, ctx);
        DayEnrichedActivities enrichedActivities = DayEnrichedActivities.fromActivities(activities, days);

        return workAttendanceToResponseMapper.mapDays(enrichedActivities, attendanceSystemReport.days(), days);
    }


    private EnrichedLocalDates enrichDays(List<? extends Record> recordDaysInPeriod) {
        return new EnrichedLocalDates(recordDaysInPeriod.stream()
                .map(WorkAttendanceApiController::enrichRecordDay)
                .sorted(Comparator.comparing(EnrichedLocalDate::value))
                .toList());
    }

    private static EnrichedLocalDate enrichRecordDay(Record recordDay) {
        var day = recordDay.getFirst(Date.FIELD_FINDER);
        var isHoliday = recordDay.findFirst(Holiday.Value.FIELD_FINDER).orElse(false);

        Set<DateLabel> dateLabels = new HashSet<>();
        if (isHoliday) {
            dateLabels.add(DateLabel.HOLIDAY);
        }
        if (day.getDayOfWeek() == SATURDAY) {
            dateLabels.add(DateLabel.SATURDAY);
        }
        if (day.getDayOfWeek() == SUNDAY) {
            dateLabels.add(DateLabel.SUNDAY);
        }
        return new EnrichedLocalDate(day, dateLabels);
    }

}
