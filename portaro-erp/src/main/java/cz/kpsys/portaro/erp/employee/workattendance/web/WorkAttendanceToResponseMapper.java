package cz.kpsys.portaro.erp.employee.workattendance.web;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.erp.employee.workattendance.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class WorkAttendanceToResponseMapper {

    @Transactional(readOnly = true)
    public @NonNull WorkAttendanceOverviewResponse mapAll(@NonNull WorkCommitment workCommitment,
                                                          @NonNull List<Activity> activities,
                                                          @Nullable WorkAtendanceReport attendanceSystemReport,
                                                          @NonNull EnrichedLocalDates days) {
        ActivitiesReport activitiesReport = ActivitiesReport.compute(activities, days, workCommitment);
        return new WorkAttendanceOverviewResponse(
                mapSummary(activitiesReport, ObjectUtil.elvis(attendanceSystemReport, WorkAtendanceReport::summary)),
                mapDays(activitiesReport.enrichedActivities(), ObjectUtil.elvis(attendanceSystemReport, WorkAtendanceReport::days), days),
                null
        );
    }

    private @NonNull SummaryResponse mapSummary(@NonNull ActivitiesReport activitiesReport,
                                                @Nullable WorkAtendanceReportSummary attendanceSystemSummary) {
        return new SummaryResponse(
                fond(activitiesReport.header()),
                holidays(activitiesReport.header()),
                workSummary(activitiesReport.header(), attendanceSystemSummary),
                overtimeSummary(activitiesReport.summary(), attendanceSystemSummary),
                absenceSummary(activitiesReport.summary(), attendanceSystemSummary),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesReport.summary().onCallDuty()),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesReport.summary().idp())
        );
    }

    private @NonNull WorkSummaryResponse workSummary(@NonNull ActivitiesReportHeader activitiesHeader,
                                                     @Nullable WorkAtendanceReportSummary attendanceSystemSummary) {
        return new WorkSummaryResponse(
                DescriptedDurationItemResponse.ofMandayDurationDescription(
                        activitiesHeader.workCommitment(),
                        activitiesHeader.presence(),
                        (durationText, commitment) -> MultiText.ofTexts("{} dní * {}",
                                durationText, commitment.text())
                ),
                DescriptedDurationItemResponse.ofPossibleMissingMandayDuration(
                        activitiesHeader.workCommitment(),
                        attendanceSystemSummary,
                        WorkAtendanceReportSummary::presence,
                        (durationText, commitment) -> MultiText.ofTexts("{} dní * {}",
                                durationText, commitment.text())
                ),
                DescriptedDurationItemResponse.ofMandayDurationDescription(
                        activitiesHeader.workCommitment(),
                        activitiesHeader.plan(),
                        (durationText, commitment) -> MultiText.ofTexts("{} dní (fond - svátky - absence) * {}",
                                durationText, commitment.text())
                ),
                DescriptedDurationItemResponse.ofPossibleMissingMandayDuration(
                        activitiesHeader.workCommitment(),
                        attendanceSystemSummary,
                        WorkAtendanceReportSummary::planComputed,
                        (durationText, commitment) -> MultiText.ofTexts("{} dní (fond - svátky - PwK absence) * {}",
                                durationText, commitment.text())
                )
        );
    }

    private @NonNull SummaryOvertimeResponse overtimeSummary(@NonNull ActivitiesSummary activitiesSummary,
                                                             @Nullable WorkAtendanceReportSummary attendanceSystemSummary) {
        return new SummaryOvertimeResponse(
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.overtimeWorkday()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::overtimeWorkday),
                DescriptedDurationItemResponse.ofDurationDescription(unknown()),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.overtimeWeekendAndHoliday()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::overtimeWeekendAndHoliday),
                DescriptedDurationItemResponse.ofDurationDescription(unknown()),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.totalOvertime()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::totalOvertime),
                DescriptedDurationItemResponse.ofDurationDescription(unknown()),
                DescriptedDurationItemResponse.ofDurationDescription(unknown()),
                DescriptedDurationItemResponse.ofDurationDescription(unknown())
        );
    }

    private @NonNull SummaryAbsenceResponse absenceSummary(@NonNull ActivitiesSummary activitiesSummary,
                                                           @Nullable WorkAtendanceReportSummary attendanceSystemSummary) {
        return new SummaryAbsenceResponse(
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceDoctor()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceDoctor),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceVacation()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceVacation),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceIllness()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceIllness),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceOcr()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceOcr),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceParagraph()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceParagraph),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceCompensatory()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceCompensatory),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceUnpaid()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceUnpaid),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceUnexcused()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceUnexcused),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceMaternity()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceMaternity),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absencePaternity()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absencePaternity),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceObstacles()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceObstacles),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.absenceChildrenAndYouth()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::absenceChildrenAndYouth),
                DescriptedDurationItemResponse.ofDurationDescription(activitiesSummary.totalAbsence()),
                DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemSummary, WorkAtendanceReportSummary::allAbsencesSumComputed)
        );
    }

    private @NonNull DescriptedDurationItemResponse fond(@NonNull ActivitiesReportHeader header) {
        return DescriptedDurationItemResponse.ofMandayDurationDescription(
                header.workCommitment(),
                header.fond(),
                (durationText, commitment) -> MultiText.ofTexts("{} dní * {}",
                        durationText, commitment.text())
        );
    }

    private static @NonNull DescriptedDurationItemResponse holidays(@NonNull ActivitiesReportHeader header) {
        return DescriptedDurationItemResponse.ofMandayDurationDescription(
                header.workCommitment(),
                header.holidays(),
                (durationText, commitment) -> MultiText.ofTexts("{} dní * {}",
                        durationText, commitment.text())
        );
    }

    @Transactional(readOnly = true)
    public @NonNull List<DayAttendanceResponse> mapDays(@NonNull DayEnrichedActivities activities,
                                                        @Nullable List<WorkAtendanceReportDay> attendanceSystemDays,
                                                        @NonNull EnrichedLocalDates days) {
        Map<EnrichedLocalDate, List<DayEnrichedActivity>> dayGroupedWorks = activities.groupDays(days);

        List<DayAttendanceResponse> dayResponses = new ArrayList<>();

        for (Map.Entry<EnrichedLocalDate, List<DayEnrichedActivity>> worksAtDay : dayGroupedWorks.entrySet()) {
            EnrichedLocalDate day = worksAtDay.getKey();
            DayEnrichedActivities dayWorks = new DayEnrichedActivities(worksAtDay.getValue());
            WorkAtendanceReportDay attendanceSystemDay = attendanceSystemDays == null ? null :
                    ListUtil.getSingleMatchingOrThrow(attendanceSystemDays, reportDay -> reportDay.day().equals(day), "workAtendanceReportDay", day);

            Duration regularWork = DateUtils.sum(
                    dayWorks.sumAll(DayEnrichedActivity::regularDuration),
                    dayWorks.sumAll(DayEnrichedActivity::idpDuration)
            );
            Duration overtimeWork = dayWorks.sumAll(DayEnrichedActivity::overtimeDuration);
            Duration onCallDuty = dayWorks.sumAll(DayEnrichedActivity::onCallDutyDuration);
            Duration absence = dayWorks.sumAll(DayEnrichedActivity::absenceDuration);
            Duration sum = Duration.ZERO
                    .plus(regularWork)
                    .plus(overtimeWork)
                    .plus(onCallDuty)
                    .plus(absence);

            DayAttendanceResponse response = new DayAttendanceResponse(
                    EnrichedLocalDateResponse.from(day),
                    DescriptedDurationItemResponse.ofDurationDescription(regularWork),
                    DescriptedDurationItemResponse.ofDurationDescription(overtimeWork),
                    DescriptedDurationItemResponse.ofDurationDescription(onCallDuty),
                    AbsenceDayAttendanceItemResponse.fromActivities(absence, dayWorks),
                    DescriptedDurationItemResponse.ofDurationDescription(sum),
                    DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemDay, WorkAtendanceReportDay::regularWorkDuration),
                    DescriptedDurationItemResponse.ofPossibleMissingDuration(attendanceSystemDay, WorkAtendanceReportDay::overtimeWorkDuration),
                    DescriptedDurationItemResponse.ofPossibleMissingDataResponse(attendanceSystemDay,
                            AbsenceDayAttendanceItemResponse::fromDay,
                            AbsenceDayAttendanceItemResponse::ofMissingData
                    )
            );
            dayResponses.add(response);
        }
        return dayResponses;
    }

    private static @NonNull Duration unknown() {
        return Duration.ZERO;
    }

}
