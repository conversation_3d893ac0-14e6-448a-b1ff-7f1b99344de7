package cz.kpsys.portaro.erp.reports.reportoptimisation;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields;
import cz.kpsys.portaro.erp.reports.ReportLoader;
import cz.kpsys.portaro.erp.reports.reportoptimisation.web.response.OptimisationFinancialSumsResponse;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.search.restriction.FieldTypeSearchField;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ReportsOptimisationFinancialSumsLoader extends ReportLoader {

    public ReportsOptimisationFinancialSumsLoader(@NonNull HierarchyLoader<Department> contextHierarchyLoader,
                                                  @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader,
                                                  @NonNull Provider<@NonNull List<Fond>> reportOptimisationFondsProvider,
                                                  @NonNull Provider<@NonNull Fond> optimisationReportFondProvider,
                                                  @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader) {
        super(contextHierarchyLoader, detailedRecordSearchLoader, reportOptimisationFondsProvider, optimisationReportFondProvider, fieldTypeSearchFieldLoader);
    }

    public OptimisationFinancialSumsResponse load(UUID projectRecordId, Department ctx) {
        List<Record> reportRecords = loadReportRecords(ctx, new Conjunction<>(
                new Term<>(FieldTypeSearchField.loadOfOrigin(RecordSutinSpecificFields.FondWorkReport.ParentProject.Main.TYPE_ID, fieldTypeSearchFieldLoader), new Eq(projectRecordId)))
        );

        var reportedPrice = sumNumberFieldValuesHolders(reportRecords, RecordSutinSpecificFields.FondWorkReport.ReportedPrice.Value.FIELD_FINDER);
        var chargedPrice = sumNumberFieldValuesHolders(reportRecords, RecordSutinSpecificFields.FondWorkReport.ChargedPrice.Value.FIELD_FINDER);

        return new OptimisationFinancialSumsResponse(
                createCzkPrice(reportedPrice),
                createCzkPrice(chargedPrice)
        );
    }
}
