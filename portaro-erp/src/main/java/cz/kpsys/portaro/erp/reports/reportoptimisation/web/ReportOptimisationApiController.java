package cz.kpsys.portaro.erp.reports.reportoptimisation.web;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.reports.reportoptimisation.GroupedProjectItemsLoader;
import cz.kpsys.portaro.erp.reports.reportoptimisation.ReportOptimisationItemOverviewLoader;
import cz.kpsys.portaro.erp.reports.reportoptimisation.ReportOptimisationOverallOverviewLoader;
import cz.kpsys.portaro.erp.reports.reportoptimisation.ReportsOptimisationFinancialSumsLoader;
import cz.kpsys.portaro.erp.reports.reportoptimisation.web.response.*;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Locale;
import java.util.UUID;

@RequestMapping("/api/report-optimisation")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ReportOptimisationApiController extends GenericApiController {

    private static final String DAY_OVERVIEW_PATH = "/overview/item";
    private static final String OVERALL_OVERVIEW_PATH = "/overview/overall";
    private static final String REPORT_FINANCIAL_SUMS_PATH = "/financial-sums";
    private static final String GROUPED_PROJECT_ITEMS_PATH = "/grouped-project-items";

    @NonNull ReportOptimisationItemOverviewLoader reportOptimisationItemOverviewLoader;
    @NonNull ReportOptimisationOverallOverviewLoader reportOptimisationOverallOverviewLoader;
    @NonNull ReportsOptimisationFinancialSumsLoader reportsOptimisationFinancialSumsLoader;
    @NonNull GroupedProjectItemsLoader groupedProjectItemsLoader;

    @RequestMapping(DAY_OVERVIEW_PATH)
    public DayOptimisationOverviewResponse getItemOverview(@RequestParam("project") UUID projectRecordId,
                                                          @RequestParam("item") UUID projectItemId,
                                                          @CurrentDepartment Department ctx) {

        return reportOptimisationItemOverviewLoader.load(projectRecordId, projectItemId, ctx);
    }

    @RequestMapping(OVERALL_OVERVIEW_PATH)
    public OverallOptimisationOverviewResponse getOverallOverview(@RequestParam("project") UUID projectRecordId,
                                                                  @CurrentDepartment Department ctx) {

        return reportOptimisationOverallOverviewLoader.load(projectRecordId, ctx);
    }

    @RequestMapping(REPORT_FINANCIAL_SUMS_PATH)
    public OptimisationFinancialSumsResponse getReportSums(@RequestParam("project") UUID projectRecordId,
                                                           @CurrentDepartment Department ctx) {

        return reportsOptimisationFinancialSumsLoader.load(projectRecordId, ctx);
    }

    @RequestMapping(GROUPED_PROJECT_ITEMS_PATH)
    public GroupedProjectItemsResponse getGroupedProjectItems(@RequestParam("project") UUID projectRecordId,
                                                                   @CurrentDepartment Department ctx,
                                                                   UserAuthentication currentAuth,
                                                                   Locale locale) {

        return groupedProjectItemsLoader.load(projectRecordId, ctx, currentAuth, locale);
    }
}