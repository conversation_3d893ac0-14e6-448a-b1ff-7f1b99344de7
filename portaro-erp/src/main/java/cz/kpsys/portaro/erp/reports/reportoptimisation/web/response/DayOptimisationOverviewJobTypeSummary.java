package cz.kpsys.portaro.erp.reports.reportoptimisation.web.response;

import lombok.NonNull;
import cz.kpsys.portaro.finance.Price;

import java.math.BigDecimal;

public record DayOptimisationOverviewJobTypeSummary(

        @NonNull String name,

        int reportedWorkersCount,
        int chargedWorkersCount,

        BigDecimal reportedStandardHours,
        BigDecimal reportedOvertimeHours,
        BigDecimal chargedStandardHours,
        BigDecimal chargedOvertimeHours,

        @NonNull Price reportedPrice,
        @NonNull Price chargedPrice
) {}