package cz.kpsys.portaro.erp.reports.reportoptimisation.web.response;

import cz.kpsys.portaro.grid.RecordRow;
import cz.kpsys.portaro.record.fond.SimpleFondResponse;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public record GroupedProjectItemsResponse(
        List<SimpleFondResponse> uniqueItemFonds,
        List<RecordRow> itemsWithoutDate,
        Map<LocalDate, List<RecordRow>> groupedInstallationLogbooks
) {}