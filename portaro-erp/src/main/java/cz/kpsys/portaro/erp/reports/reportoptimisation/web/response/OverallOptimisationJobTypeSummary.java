package cz.kpsys.portaro.erp.reports.reportoptimisation.web.response;

import cz.kpsys.portaro.finance.Price;
import lombok.NonNull;

import java.math.BigDecimal;

public record OverallOptimisationJobTypeSummary(

        @NonNull String name,

        BigDecimal reportedStandardHours,
        BigDecimal reportedOvertimeHours,
        BigDecimal chargedStandardHours,
        BigDecimal chargedOvertimeHours,

        @NonNull Price reportedPrice,
        @NonNull Price chargedPrice
) {}