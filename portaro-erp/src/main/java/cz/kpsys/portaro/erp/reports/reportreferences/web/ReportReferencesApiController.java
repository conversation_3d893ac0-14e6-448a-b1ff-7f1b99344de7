package cz.kpsys.portaro.erp.reports.reportreferences.web;

import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.reports.reportreferences.ReportReferencesReportedLoader;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.UUID;

@RequestMapping("/api/report-references")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ReportReferencesApiController extends GenericApiController {

    @NonNull ReportReferencesReportedLoader reportReferencesReportedLoader;

    @RequestMapping("/total-reported")
    public ReportReferenceReportedResponse getTotalReportedValues(@RequestParam("reportReference") UUID reportReferenceRecordId,
                                                                  @CurrentDepartment Department ctx) {

        return reportReferencesReportedLoader.load(reportReferenceRecordId, ctx);
    }
}
