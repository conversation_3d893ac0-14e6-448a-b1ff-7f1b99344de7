package cz.kpsys.portaro.exemplar.export;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadableByIdLoaderAdapter;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.*;
import cz.kpsys.portaro.exemplar.exemplarstatus.ExemplarStatus;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.export.ExportedDepartmentSearchHelper;
import cz.kpsys.portaro.record.export.RecordExport;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.PageSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.security.ActionPermittedFilter;
import cz.kpsys.portaro.security.SecurityManager;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ExportedExemplarsLoader {

    @NonNull PageSearchLoader<MapBackedParams, Exemplar, RangePaging> exemplarSearchLoader;
    @NonNull ExemplarListSorter exemplarListSorter;
    @NonNull ExportedDepartmentSearchHelper exportedDepartmentSearchHelper;
    @NonNull ContextualProvider<Department, @NonNull @NotEmpty List<Integer>> exportableExemplarStatusesProvider;
    @NonNull ByIdLoadable<ExemplarStatus, Integer> exemplarStatusLoader;
    @NonNull SecurityManager securityManager;

    public List<Exemplar> loadExemplarsOfDocuments(@NonNull List<UUID> recordIds,
                                                   @NonNull UserAuthentication currentAuth,
                                                   @NonNull RecordExport recordExport,
                                                   @NonNull Department exemplarsRootDepartment,
                                                   @NonNull Department currentDepartment) {
        MapBackedParams params = MapBackedParams.build(p -> {
            p.set(RecordConstants.SearchParams.RECORD, recordIds);
            p.set(ExemplarConstants.SearchParams.EXEMPLAR_STATUS, exportableExemplarStatusesProvider.andThen(new AllByIdsLoadableByIdLoaderAdapter<>(exemplarStatusLoader)::getAllByIds).getOn(currentDepartment));
            p.set(ExemplarConstants.SearchParams.EXEMPLAR_TYPE, List.of(ExemplarType.EXEMPLAR, ExemplarType.BINDING));
            p.set(CoreSearchParams.DEPARTMENT, exportedDepartmentSearchHelper.getDepartments(exemplarsRootDepartment, recordExport));
        });
        List<Exemplar> nonFilteredList = exemplarSearchLoader.getPage(RangePaging.forAll(), params).getItems();
        List<Exemplar> filteredList = ListUtil.filter(nonFilteredList, new ActionPermittedFilter<>(securityManager, ExemplarSecurityActions.EXEMPLAR_EXPORT, currentAuth, currentDepartment));
        List<Exemplar> sortedFilteredList = exemplarListSorter.sort(filteredList);

        if (sortedFilteredList.size() < nonFilteredList.size()) {
            log.info("Loaded {} exemplars of documents {} (filtered-out {} after applying '{}' security restriction filter)", sortedFilteredList.size(), recordIds, nonFilteredList.size() - sortedFilteredList.size(), ExemplarSecurityActions.EXEMPLAR_EXPORT);
        } else {
            log.debug("Loaded {} exemplars of documents {} (none of them were filtered-out after applying '{}' security restriction filter)", sortedFilteredList.size(), recordIds, ExemplarSecurityActions.EXEMPLAR_EXPORT);
        }

        return sortedFilteredList;
    }

}
