package cz.kpsys.portaro.exemplar.export.marc;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.exemplar.ExemplarComparators;
import cz.kpsys.portaro.marcxml.model.StrictDatafieldMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictSubfieldMarcDto;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldHelper;
import cz.kpsys.portaro.record.detail.IndicatorType;
import cz.kpsys.portaro.record.export.marc.DatafieldsMarcGenerator;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.record.RecordWellKnownFields.LocationStatements;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class D910FieldsMarcGenerator implements DatafieldsMarcGenerator<Provider<List<Exemplar>>> {

    private static final String NO_SIGLA_VALUE = "";

    @NonNull ContextualProvider<Department, @NullableNotBlank String> nullableSiglaProvider;

    @NonNull
    @Override
    public List<StrictDatafieldMarcDto> generate(@NonNull Record record, @NonNull Provider<List<Exemplar>> items, @NonNull Department ctx) {
        Map<String, List<Exemplar>> siglaGroupedExemplars = items.get().stream().collect(Collectors.groupingBy(this::getSiglaOfExemplar));
        return siglaGroupedExemplars.entrySet().stream()
                .filter(siglaExemplarsEntry -> !siglaExemplarsEntry.getKey().equals(NO_SIGLA_VALUE))
                .map(siglaExemplarsEntry -> {
                    String sigla = siglaExemplarsEntry.getKey();
                    List<Exemplar> exemplars = siglaExemplarsEntry.getValue();
                    return generateField(sigla, exemplars);
                })
                .toList();
    }

    @NonNull
    private static StrictDatafieldMarcDto generateField(@NonNull String sigla, @NonNull @NotEmpty List<Exemplar> exemplars) {
        List<StrictSubfieldMarcDto> subfields = new ArrayList<>(2);

        subfields.add(StrictSubfieldMarcDto.ofWithoutRecord(LocationStatements.Sigla.CODE, sigla));

        getFirstSignature(exemplars).ifPresent(firstSignature -> subfields.add(StrictSubfieldMarcDto.ofWithoutRecord(LocationStatements.Signature.CODE, firstSignature)));

        return new StrictDatafieldMarcDto(FieldHelper.fieldCodeToMarc21Tag(LocationStatements.CODE), IndicatorType.WIDE_SUPPORTED_EMPTY_IND, IndicatorType.WIDE_SUPPORTED_EMPTY_IND, subfields);
    }

    @NonNull
    private String getSiglaOfExemplar(Exemplar exemplar) {
        @NullableNotBlank String sigla = nullableSiglaProvider.getOn(exemplar.getDepartment());
        return ObjectUtil.firstNotNull(sigla, NO_SIGLA_VALUE);
    }

    private static Optional<String> getFirstSignature(List<Exemplar> exemplars) {
        return exemplars.stream()
                .sorted(ExemplarComparators.bySignature())
                .map(Exemplar::getSignature)
                .filter(StringUtil::hasLength)
                .findFirst();
    }
}
