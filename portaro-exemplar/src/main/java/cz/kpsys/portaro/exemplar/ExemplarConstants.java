package cz.kpsys.portaro.exemplar;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Ordered;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.exemplar.acquisitionway.AcquisitionWay;
import cz.kpsys.portaro.exemplar.customvalue.CustomValue;
import cz.kpsys.portaro.exemplar.exchangeset.ExchangeExemplarSet;
import cz.kpsys.portaro.exemplar.exemplarstatus.ExemplarStatus;
import cz.kpsys.portaro.exemplar.thematicgroup.ThematicGroup;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.property.JavatypedDatatypedProperty;
import cz.kpsys.portaro.property.PropertyFactory;
import cz.kpsys.portaro.search.SearchParamsConstants;
import cz.kpsys.portaro.search.field.BasicSearchField;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cz.kpsys.portaro.datatype.Datatype.scalar;

public class ExemplarConstants {

    public static final Integer FAKE_EXEMPLAR_ID = 0;

    public static final Map<String, String> COLUMNS_AND_PROPS_MAP = new HashMap<>();

    public static final String PRIR_CISLO = "PRIR_CISLO";
    public static final String SIGNATURA = "SIGNATURA";
    public static final String BAR_COD = "BAR_COD";

    static {
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("ID", Identified.Fields.id);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(Identified.PROPERTY_ID, Identified.PROPERTY_ID);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(ExemplarConstants.PRIR_CISLO, BasicExemplar.Fields.accessNumber);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.accessNumber, BasicExemplar.Fields.accessNumber);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(ExemplarConstants.SIGNATURA, BasicExemplar.Fields.signature);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.signature, BasicExemplar.Fields.signature);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(ExemplarConstants.BAR_COD, BasicExemplar.Fields.barCode);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.barCode, BasicExemplar.Fields.barCode);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("LOKACE", BasicExemplar.Fields.location);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.location, BasicExemplar.Fields.location);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("KATEGORIE", BasicExemplar.Fields.loanCategory);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.loanCategory, BasicExemplar.Fields.loanCategory);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("DOSTUPNOST", Exemplar.AVAILABILITY);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(Exemplar.AVAILABILITY, Exemplar.AVAILABILITY);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("BUDOVA", BasicExemplar.Fields.department);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.department, BasicExemplar.Fields.department);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("TEM_SKUP", BasicExemplar.Fields.thematicGroup);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.thematicGroup, BasicExemplar.Fields.thematicGroup);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("STATUS", BasicExemplar.Fields.status);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.status, BasicExemplar.Fields.status);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("POZNAMKA", BasicExemplar.Fields.note);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.note, BasicExemplar.Fields.note);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("internalNote", BasicExemplar.Fields.internalNote);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("CENA", BasicExemplar.Fields.price);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.price, BasicExemplar.Fields.price);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("UZIVATEL", BasicExemplar.Fields.owner);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.owner, BasicExemplar.Fields.owner);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("ROK_PRIR", BasicExemplar.Fields.acquisitionYear);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.acquisitionYear, BasicExemplar.Fields.acquisitionYear);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("ZPUSOB_NABYTI", BasicExemplar.Fields.acquisitionWay);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.acquisitionWay, BasicExemplar.Fields.acquisitionWay);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("FULLTEXT", BasicExemplar.Fields.fileDirectory);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("INDIVIDUALNI_HODNOTA", BasicExemplar.Fields.customValue);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.customValue, BasicExemplar.Fields.customValue);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("PORADI", Ordered.PROPERTY_ORDER);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(Ordered.PROPERTY_ORDER, Ordered.PROPERTY_ORDER);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("DATUM", BasicExemplar.Fields.creationDate);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.creationDate, BasicExemplar.Fields.creationDate);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("UMISTENI", Exemplar.PLACEMENT);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(Exemplar.PLACEMENT, Exemplar.PLACEMENT);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("HOLDER", Exemplar.HOLDER);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(Exemplar.HOLDER, Exemplar.HOLDER);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("ROCNIK", BasicExemplar.Fields.bundledVolumeNumber);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.bundledVolumeNumber, BasicExemplar.Fields.bundledVolumeNumber);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("ROK", BasicExemplar.Fields.bundledVolumeYear);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.bundledVolumeYear, BasicExemplar.Fields.bundledVolumeYear);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("ROZMEZI_CISEL", BasicExemplar.Fields.bundledVolumeIssueRange);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.bundledVolumeIssueRange, BasicExemplar.Fields.bundledVolumeIssueRange);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("UCET", BasicExemplar.Fields.account);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.account, BasicExemplar.Fields.account);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("CISLO_FAKTURY", BasicExemplar.Fields.invoiceNumber);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.invoiceNumber, BasicExemplar.Fields.invoiceNumber);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("DODAVATEL", BasicExemplar.Fields.supplier);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.supplier, BasicExemplar.Fields.supplier);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("TYP_CISLA", BasicExemplar.Fields.type);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.type, BasicExemplar.Fields.type);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("POCET_KUSU", BasicExemplar.Fields.quantity);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.quantity, BasicExemplar.Fields.quantity);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("PRILOHY", BasicExemplar.Fields.attachments);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.attachments, BasicExemplar.Fields.attachments);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("CISLO", BasicExemplar.Fields.issueName);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.issueName, BasicExemplar.Fields.issueName);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("EV_CISLO", BasicExemplar.Fields.issueEvidenceNumber);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.issueEvidenceNumber, BasicExemplar.Fields.issueEvidenceNumber);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("ROZSAH", BasicExemplar.Fields.bindingIssueRange);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(BasicExemplar.Fields.bindingIssueRange, BasicExemplar.Fields.bindingIssueRange);

        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put("IDENTIFIKATORY", Exemplar.IDENTIFIERS);
        ExemplarConstants.COLUMNS_AND_PROPS_MAP.put(Exemplar.IDENTIFIERS, Exemplar.IDENTIFIERS);
    }

    public static class Datatype {
        public static final String DATATYPE_CUSTOM_VALUE = "VAL_STAVZ";

        public static final ScalarDatatype LOCATION = scalar("LOKACE", Location.class, Integer.class);
        public static final ScalarDatatype TYPE = scalar("EXEMPLAR_TYPE", ExemplarType.class, Integer.class);
        public static final ScalarDatatype SIGNATURE = scalar("SIGNATURA", String.class);
        public static final ScalarDatatype ACCESS_NUMBER = scalar("PRIR_CISLO", String.class);
        public static final ScalarDatatype EXCHANGE_EXEMPLAR_SET = scalar("EXCHANGE_EXEMPLAR_SET", ExchangeExemplarSet.class, Integer.class);
        public static final ScalarDatatype CUSTOM_VALUE = scalar("CUSTOM_VALUE", CustomValue.class, String.class);
        public static final ScalarDatatype CUSTOM_VALUE_OLD = scalar(DATATYPE_CUSTOM_VALUE, CustomValue.class, String.class);
        public static final ScalarDatatype EXEMPLAR_STATUS = scalar("STATUS_EX", ExemplarStatus.class, Integer.class);
        public static final ScalarDatatype EXEMPLAR = scalar("EXEMPLAR", Exemplar.class, Integer.class);
        public static final ScalarDatatype THEMATIC_GROUP = scalar("TEMSKUP", ThematicGroup.class, String.class);
        public static final ScalarDatatype ACQUISITION_WAY = scalar("ZP_NAB", AcquisitionWay.class, String.class);
    }

    public static class SearchFields {
        public static final BasicSearchField LOCATION = BasicSearchField.createBuiltIn("location", Datatype.LOCATION, Texts.ofMessageCoded("commons.lokace"));
        public static final BasicSearchField DOCUMENT_SIGNATURE = BasicSearchField.createBuiltIn("documentSignature", Datatype.SIGNATURE, Texts.ofMessageCoded("detail.signatura"));
        public static final BasicSearchField DOCUMENT_ACCESS_NUMBER = BasicSearchField.createBuiltIn("documentAccessNumber", Datatype.ACCESS_NUMBER, Texts.ofMessageCoded("search.builtinfield.documentAccessNumber"));
    }

    public static class SearchParams implements SearchParamsConstants {

        public static final JavatypedDatatypedProperty<Boolean> INCLUDE_FAKE = PropertyFactory.<Boolean>ofSearchProperty("includeFakeItem", Texts.ofNative("Včetně fiktivních"), CoreConstants.Datatype.BOOLEAN, TypeDescriptor.valueOf(Boolean.class))
                .withDefault(Boolean.FALSE);

        public static final JavatypedDatatypedProperty<List<Location>> LOCATION = PropertyFactory.ofSearchProperty("location", Texts.ofNative("Lokace"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.LOCATION), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Location.class)));

        public static final JavatypedDatatypedProperty<List<ExemplarType>> EXEMPLAR_TYPE = PropertyFactory.ofSearchProperty("exemplarType", Texts.ofNative("Typ exempláře"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.TYPE), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(ExemplarType.class)));

        public static final JavatypedDatatypedProperty<List<ExemplarStatus>> EXEMPLAR_STATUS = PropertyFactory.ofSearchProperty("exemplarStatus", Texts.ofMessageCoded("hledani.SpecimenStatuses"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.EXEMPLAR_STATUS), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(ExemplarStatus.class)));

        public static final JavatypedDatatypedProperty<List<Integer>> FORBIDDEN_EXEMPLAR = PropertyFactory.ofSearchProperty("forbiddenExemplar", Texts.ofMessageCoded("hledani.ForbidenSpecimens"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.EXEMPLAR), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Integer.class)));

        public static final JavatypedDatatypedProperty<List<String>> ACQUISITION_WAY = PropertyFactory.ofSearchProperty("acquisitionWay", Texts.ofMessageCoded("hledani.AcquisitionWay"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.ACQUISITION_WAY), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(String.class)));

        public static final JavatypedDatatypedProperty<Integer> INCREASE_YEAR = PropertyFactory.<Integer>ofSearchProperty("increaseYear", Texts.ofMessageCoded("exemplar.IncreaseYear"), CoreConstants.Datatype.YEAR, TypeDescriptor.valueOf(Integer.class))
                .checkNotnullModifiedValue(buyYear -> Assert.isTrue(buyYear >= 0, "Rok přírůstku cannot be negative number"));

        public static final JavatypedDatatypedProperty<String> BAR_CODE = PropertyFactory.<String>ofSearchProperty("barCode", Texts.ofNative("Čár. kód"), CoreConstants.Datatype.BAR_CODE, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<String> RFID = PropertyFactory.<String>ofSearchProperty("rfid", Texts.ofNative("RFID"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<String> ACCESS_NUMBER = PropertyFactory.<String>ofSearchProperty("accessNumber", Texts.ofNative("Čár. kód"), Datatype.ACCESS_NUMBER, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> ACCESS_NUMBER_START = PropertyFactory.<String>ofSearchProperty("accessNumberStart", Texts.ofNative("Od přír. čísla"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> ACCESS_NUMBER_END = PropertyFactory.<String>ofSearchProperty("accessNumberEnd", Texts.ofNative("Po přír. číslo"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> SIGNATURE = PropertyFactory.<String>ofSearchProperty("signature", Texts.ofNative("Signatura"), Datatype.SIGNATURE, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> SIGNATURE_START = PropertyFactory.<String>ofSearchProperty("signatureStart", Texts.ofNative("Od signatury"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> SIGNATURE_END = PropertyFactory.<String>ofSearchProperty("signatureEnd", Texts.ofNative("Po signaturu"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> DISCARD_NUMBER_START = PropertyFactory.<String>ofSearchProperty("discardNumberStart", Texts.ofNative("Od úbyt. čísla"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> DISCARD_NUMBER_END = PropertyFactory.<String>ofSearchProperty("discardNumberEnd", Texts.ofNative("Po úbyt. číslo"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);
        /**
         * String value - can contain "2000-2001"
         */
        public static final JavatypedDatatypedProperty<String> BUNDLED_VOLUME_YEAR = PropertyFactory.<String>ofSearchProperty("bundledVolumeYear", Texts.ofMessageCoded("exemplar.BundledVolumeYear"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);
    }

}
