package cz.kpsys.portaro.exemplar;

import cz.kpsys.portaro.commons.barcode.BarCodeValidator;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.SorterStringBuilder;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.exemplar.accessnumber.AccessNumberSequenceLoader;
import cz.kpsys.portaro.exemplar.discard.Discardion;
import cz.kpsys.portaro.exemplar.signature.SignatureSequenceLoader;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.isbn.Isbn;
import cz.kpsys.portaro.record.isbn.IsbnChecker;
import cz.kpsys.portaro.search.AbstractSingleColumnSpringDbSearchLoader;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sequence.Sequence;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.Brackets;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import cz.kpsys.portaro.sql.generator.SelectQueryUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.lang.Nullable;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.KAT1_5.*;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.UBYTKY;
import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_4;
import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD;
import static cz.kpsys.portaro.exemplar.ExemplarConstants.FAKE_EXEMPLAR_ID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbExemplarIdSearchLoader extends AbstractSingleColumnSpringDbSearchLoader<MapBackedParams, Integer, RangePaging> {

    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull AccessNumberSequenceLoader accessNumberSequenceLoader;
    @NonNull SignatureSequenceLoader signatureSequenceLoader;
    @NonNull Provider<BarCodeValidator> exemplarBarCodeValidatorProvider;


    private static final Map<String, String> PROPS_TO_DB_COLUMNS_MAP = new HashMap<>();

    static {
        PROPS_TO_DB_COLUMNS_MAP.put(BasicExemplar.Fields.creationDate, DATUM);
        PROPS_TO_DB_COLUMNS_MAP.put(BasicExemplar.Fields.accessNumber, TRIDPRIC);
        PROPS_TO_DB_COLUMNS_MAP.put(BasicExemplar.Fields.signature, TRIDSIGN);
        PROPS_TO_DB_COLUMNS_MAP.put(BasicExemplar.Fields.barCode, BAR_COD);
        PROPS_TO_DB_COLUMNS_MAP.put(Discardion.Fields.discardNumber, UBYTKY.TRIDUBY);
    }

    public SpringDbExemplarIdSearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                          @NonNull QueryFactory queryFactory,
                                          @NonNull DepartmentAccessor departmentAccessor,
                                          @NonNull AccessNumberSequenceLoader accessNumberSequenceLoader,
                                          @NonNull SignatureSequenceLoader signatureSequenceLoader,
                                          @NonNull Provider<BarCodeValidator> exemplarBarCodeValidatorProvider) {
        super(jdbcTemplate, queryFactory, TABLE, ID_EX, new SelectedColumnRowMapper<>(Integer.class, ID_EX));
        this.departmentAccessor = departmentAccessor;
        this.accessNumberSequenceLoader = accessNumberSequenceLoader;
        this.signatureSequenceLoader = signatureSequenceLoader;
        this.exemplarBarCodeValidatorProvider = exemplarBarCodeValidatorProvider;
    }

    @Override
    protected void select(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @org.jspecify.annotations.Nullable SortingItem customSorting) {
        Optional<String> sortingColumn = defaultOrCustomSorting(customSorting).map(SortingItem::field);
        if (sortingColumn.isEmpty()) {
            sq.selectDistinct(
                    TC(TABLE, ID_EX),
                    TC(TABLE, PORADI)
            );
        } else {
            sq.selectDistinct(
                    TC(TABLE, ID_EX),
                    TC(TABLE, PORADI),
                    sortingColumn.get()
            );
        }
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @org.jspecify.annotations.Nullable SortingItem customSorting) {
        Optional<String> sortingColumn = defaultOrCustomSorting(customSorting).map(SortingItem::field);

        sq.from(TABLE);

        if (!count && sortingColumn.isPresent() && sortingColumn.get().equals(TC(UBYTKY.UBYTKY, UBYTKY.TRIDUBY))) {
            sq.joins().addLeft(UBYTKY.UBYTKY, COLSEQ(TC(TABLE, FK_UBYTEK), TC(UBYTKY.UBYTKY, UBYTKY.ID_UBYTEK)));
        }

        if (!p.get(ExemplarConstants.SearchParams.INCLUDE_FAKE)) {
            sq.where().and().notEq(TC(TABLE, ID_EX), FAKE_EXEMPLAR_ID);
        }

        if (p.hasNotNull(ExemplarConstants.SearchParams.EXEMPLAR_STATUS)) {
            if (!p.hasLength(ExemplarConstants.SearchParams.EXEMPLAR_STATUS)) {
                return false;
            }
            sq.where().and().in(TC(TABLE, FK_STATUS), ListUtil.getListOfIds(p.get(ExemplarConstants.SearchParams.EXEMPLAR_STATUS)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.RECORD)) {
            if (!p.hasLength(RecordConstants.SearchParams.RECORD)) {
                return false;
            }
            sq.where().and().in(TC(TABLE, RECORD_ID), p.get(RecordConstants.SearchParams.RECORD));
        }

        if (p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD)) {
            sq.where().and().notIn(TC(TABLE, RECORD_ID), p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD));
        }

        if (p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS)) {
            sq.joins().add(RECORD.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(TABLE, RECORD_ID)));
            sq.where().and().notIn(TC(RECORD.TABLE, RECORD.RECORD_STATUS_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS)));
        }

        if (p.hasNotNull(CoreSearchParams.DEPARTMENT) && !departmentAccessor.coversAllDepartments(p.get(CoreSearchParams.DEPARTMENT))) {
            if (!p.hasLength(CoreSearchParams.DEPARTMENT)) {
                return false;
            }
            sq.where().and().in(TC(TABLE, FK_PUJC), ListUtil.getListOfIds(p.get(CoreSearchParams.DEPARTMENT)));
        }

        if (p.hasNotNull(ExemplarConstants.SearchParams.LOCATION)) {
            if (!p.hasLength(ExemplarConstants.SearchParams.LOCATION)) {
                return false;
            }
            sq.where().and().in(TC(TABLE, FK_LOKACE), ListUtil.getListOfIds(p.get(ExemplarConstants.SearchParams.LOCATION)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.FOND)) {
            if (!p.hasLength(RecordConstants.SearchParams.FOND)) {
                return false;
            }
            sq.where().and().in(TC(TABLE, FK_DOKFOND), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.FOND)));
        }

        if (p.hasNotNull(ExemplarConstants.SearchParams.EXEMPLAR_TYPE)) {
            if (!p.hasLength(ExemplarConstants.SearchParams.EXEMPLAR_TYPE)) {
                return false;
            }
            sq.where().and().in(TC(TABLE, TYP_CISLA), ListUtil.getListOfIds(p.get(ExemplarConstants.SearchParams.EXEMPLAR_TYPE)));
        }

        if (p.hasNotNull(CoreSearchParams.Q)) {
            String q = p.get(CoreSearchParams.Q);
            if (q.length() > Exemplar.SIGNATURE_MAX_LENGTH && q.length() > Exemplar.ACCESS_NUMBER_MAX_LENGTH && q.length() > exemplarBarCodeValidatorProvider.get().getMaximumLength()) {
                return false;
            }

            Brackets brackets = sq.where().and().brackets();

            if (q.length() <= Exemplar.SIGNATURE_MAX_LENGTH) {
                brackets.or().eq(SIGNATURA, q);
            }

            if (q.length() <= Exemplar.ACCESS_NUMBER_MAX_LENGTH) {
                brackets.or().eq(PRIR_CISLO, q);
            }

            if (exemplarBarCodeValidatorProvider.get().isValid(StringUtil.diacriticsToDigits(q))) {
                String bc = StringUtil.diacriticsToDigits(q);
                brackets.or().in(BAR_COD, exemplarBarCodeValidatorProvider.get().streamAllValidVariants(bc).toList());
            }

            if (IsbnChecker.isValidIsbn(q) || IsbnChecker.isValidIssn(q)) {
                Isbn isxn = new Isbn(q);
                brackets.or().exists(SelectQueryUtils.existsQueryWhere(queryFactory, TABLE, RECORD_ID, KAT1_4.TABLE, KAT1_4.RECORD_ID,
                        where -> where.and().eq(KAT1_4.ISBN_CORE, isxn.getCoreValue())));
            }
        }

        if (p.hasNotNull(ExemplarConstants.SearchParams.SIGNATURE)) {
            sq.where().and().eq(SIGNATURA, p.get(ExemplarConstants.SearchParams.SIGNATURE));
        }

        if (p.hasNotNull(ExemplarConstants.SearchParams.ACCESS_NUMBER)) {
            sq.where().and().eq(PRIR_CISLO, p.get(ExemplarConstants.SearchParams.ACCESS_NUMBER));
        }

        if (p.hasNotNull(ExemplarConstants.SearchParams.BAR_CODE)) {
            String bc = StringUtil.diacriticsToDigits(p.get(ExemplarConstants.SearchParams.BAR_CODE));
            if (!exemplarBarCodeValidatorProvider.get().isValid(bc)) {
                return false;
            }
            sq.where().and().in(BAR_COD, exemplarBarCodeValidatorProvider.get().streamAllValidVariants(bc).toList());
        }

        if (p.hasNotNull(ExemplarConstants.SearchParams.RFID)) {
            if (p.get(ExemplarConstants.SearchParams.RFID).isBlank()) {
                return false;
            }
            sq.where().and().eq(RFID, p.get(ExemplarConstants.SearchParams.RFID));
        }

        if (p.hasNotNull(CoreSearchParams.FROM_DATE)) {
            sq.where().and().gtEq(DATUM, p.get(CoreSearchParams.FROM_DATE));
        }

        if (p.hasNotNull(CoreSearchParams.TO_DATE)) {
            sq.where().and().lt(DATUM, p.get(CoreSearchParams.TO_DATE));
        }

        if (!p.get(CoreSearchParams.INCLUDE_ACTIVE)) {
            sq.where().and().isNotNull(FK_UBYTEK);
        }

        if (!p.get(CoreSearchParams.INCLUDE_DELETED)) {
            sq.where().and().isNull(withoutIndex(FK_UBYTEK));
        }

        if (p.hasNotNull(ExemplarConstants.SearchParams.BUNDLED_VOLUME_YEAR)) {
            sq.where().and().eq(ROK_VOL, p.get(ExemplarConstants.SearchParams.BUNDLED_VOLUME_YEAR));
        }


        boolean an = false;
        if (p.hasNotNull(ExemplarConstants.SearchParams.ACCESS_NUMBER_START)) {
            String accessNumberStartSorter = getAccessNumberSequenceSorter(p.get(ExemplarConstants.SearchParams.ACCESS_NUMBER_START));
            sq.where().and().gtEq(TRIDPRIC, accessNumberStartSorter);
            an = true;
        }
        if (p.hasNotNull(ExemplarConstants.SearchParams.ACCESS_NUMBER_END)) {
            String accessNumberEndSorter = getAccessNumberSequenceSorter(p.get(ExemplarConstants.SearchParams.ACCESS_NUMBER_END));
            sq.where().and().ltEq(TRIDPRIC, accessNumberEndSorter);
            an = true;
        }
        if (an) {
            sq.where().and().isNotNull(PRIR_CISLO);
        }


        boolean signNotNul = false;
        if (p.hasNotNull(ExemplarConstants.SearchParams.SIGNATURE_START)) {
            String signatureSequenceStartSorter = getSignatureSequenceSorter(p.get(ExemplarConstants.SearchParams.SIGNATURE_START));
            sq.where().and().gtEq(TRIDSIGN, signatureSequenceStartSorter);
            signNotNul = true;
        }
        if (p.hasNotNull(ExemplarConstants.SearchParams.SIGNATURE_END)) {
            String signatureSequenceEndSorter = getSignatureSequenceSorter(p.get(ExemplarConstants.SearchParams.SIGNATURE_END));
            sq.where().and().ltEq(TRIDSIGN, signatureSequenceEndSorter);
            signNotNul = true;
        }
        if (signNotNul) {
            sq.where().and().isNotNull(SIGNATURA);
        }


        if (p.hasNotNull(ExemplarConstants.SearchParams.INCREASE_YEAR) && p.get(ExemplarConstants.SearchParams.INCREASE_YEAR) > 0) {
            sq.where().and().eq(ROK_PRIR, p.get(ExemplarConstants.SearchParams.INCREASE_YEAR));
        }


        if (p.hasNotNull(ExemplarConstants.SearchParams.DISCARD_NUMBER_START) || p.hasNotNull(ExemplarConstants.SearchParams.DISCARD_NUMBER_END)) {
            sq.joinOrExists(queryFactory, TABLE, FK_UBYTEK, UBYTKY.UBYTKY, UBYTKY.ID_UBYTEK, "discard_number_start_end", (where, alias) -> {
                if (p.hasNotNull(ExemplarConstants.SearchParams.DISCARD_NUMBER_START)) {
                    where.and().gtEq(TC(alias, UBYTKY.UBYT_CISLO), p.get(ExemplarConstants.SearchParams.DISCARD_NUMBER_START));
                }
                if (p.hasNotNull(ExemplarConstants.SearchParams.DISCARD_NUMBER_END)) {
                    where.and().ltEq(TC(alias, UBYTKY.UBYT_CISLO), p.get(ExemplarConstants.SearchParams.DISCARD_NUMBER_END));
                }
            });
        }

        return true;
    }

    @Override
    protected Optional<SortingItem> defaultOrCustomSorting(@Nullable SortingItem customSorting) {
        if (customSorting == null) {
            return Optional.empty();
        }
        String sortingColumn = PROPS_TO_DB_COLUMNS_MAP.get(customSorting.field());
        if (sortingColumn.equals(UBYTKY.TRIDUBY)) {
            sortingColumn = TC(UBYTKY.UBYTKY, UBYTKY.TRIDUBY);
        } else {
            sortingColumn = TC(TABLE, sortingColumn);
        }
        return Optional.of(SortingItem.ofSimpleFieldAndOrder(sortingColumn, customSorting.asc()));
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        return Sorting.ofAsc(PORADI, ID_EX);
    }

    @Override
    protected @NonNull Sorting finalDbSorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams mapBackedParams) {
        Optional<SortingItem> sortingItem = defaultOrCustomSorting(customSorting);
        if (sortingItem.isPresent()) {
            return Sorting.of(sortingItem.orElseThrow()).append(mandatorySorting(customSorting, mapBackedParams));
        }
        return mandatorySorting(customSorting, mapBackedParams);
    }

    private String getAccessNumberSequenceSorter(@NonNull String enteredAccessNumber) {
        return getSequenceSorter(enteredAccessNumber, accessNumberSequenceLoader);
    }


    private String getSignatureSequenceSorter(@NonNull String enteredSignature) {
        return getSequenceSorter(enteredSignature, signatureSequenceLoader);
    }


    private String getSequenceSorter(@NonNull String enteredValue, AllValuesProvider<? extends Sequence<?>> sequenceDefinitionAllValuesProvider) {
        List<? extends Sequence<?>> defs = sequenceDefinitionAllValuesProvider.getAll();
        Sequence<?> firstMatchingDef = ListUtil.findFirstMatching(defs, object -> object.matches(enteredValue)).orElse(null);
        if (firstMatchingDef != null) {
            return firstMatchingDef.createKnown(enteredValue).getSorter();
        }
        return SorterStringBuilder.parseAndBuild(enteredValue);
    }

}
