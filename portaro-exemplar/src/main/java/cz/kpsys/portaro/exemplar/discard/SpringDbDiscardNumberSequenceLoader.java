package cz.kpsys.portaro.exemplar.discard;

import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.sequence.SequenceLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import static cz.kpsys.portaro.databasestructure.ExemplarDb.DEF_UBYT_RADY.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbDiscardNumberSequenceLoader implements SequenceLoader<DiscardNumberSequence>, RowMapper<DiscardNumberSequence> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public List<DiscardNumberSequence> getAll() {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(TABLE);
        sq.orderBy().addDesc(UBYT_TELO);
        return ListUtil.filterNulls(jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this));
    }

    @Override
    public DiscardNumberSequence getById(@NonNull Long id) throws ItemNotFoundException {
        return ListUtil.getById(getAll(), id);
    }

    @Override
    public DiscardNumberSequence mapRow(ResultSet rs, int rowNum) throws SQLException {
        Long id = DbUtils.getLongNotNull(rs, ID_UBYT_RADY);
        String prefix = StringUtil.notNullString(rs.getString(UBYT_PREF)).trim();
        String suffix = StringUtil.notNullString(rs.getString(UBYT_SUFF)).trim();
        Integer bodyLength = DbUtils.getIntegerNotNull(rs, UBYT_DELKA);
        Integer lastSavedBodyValue = DbUtils.getIntegerNotNull(rs, UBYT_TELO);
        return new DiscardNumberSequence(id, prefix, suffix, bodyLength, lastSavedBodyValue);
    }
}
