package cz.kpsys.portaro.exemplar.edit;

import cz.kpsys.portaro.commons.barcode.BarCodeValidator;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.Exemplar;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class BarCodeTrimmingExemplarSaverProxy implements Saver<EditedExemplar, Exemplar> {

    @NonNull Saver<EditedExemplar, Exemplar> target;
    @NonNull ContextualProvider<Department, BarCodeValidator> exemplarBarCodeValidatorProvider;

    @Override
    public @NonNull Exemplar save(@NonNull EditedExemplar ee) {
        if (ee.getBarCode() != null) {
            BarCodeValidator barCodeValidator = exemplarBarCodeValidatorProvider.getOn(ee.getCtx());
            ee.setBarCode(barCodeValidator.getCore(ee.getBarCode()));
        }
        return target.save(ee);
    }
}
