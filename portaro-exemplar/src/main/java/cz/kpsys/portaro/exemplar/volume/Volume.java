package cz.kpsys.portaro.exemplar.volume;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.NonFinal;
import org.springframework.lang.Nullable;

import java.io.Serializable;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
@FieldNameConstants
public class Volume extends LabeledId<Integer> implements Serializable, Identified<Integer>, IdSettable<Integer>, LabeledIdentified<Integer>, SoftDeletable {

    public static final int DEFAULT_PERIODICITY_MULTIPLIER = 1;
    public static final int PROPERTY_VOLUME_NUMBER_MAX_LENGTH = 10;
    public static final int PROPERTY_DESCRIPTION_MAX_LENGTH = 80;
    public static final int PROPERTY_YEAR_MAX_LENGTH = 20;

    @NonNull
    UUID recordId;

    @Nullable
    String volumeNumber;

    @NonNull
    Integer issueQuantity;

    @Nullable
    String year;

    @NonNull
    VolumePeriodicity periodicity;

    @NonNull
    Integer periodicityMultiplier = DEFAULT_PERIODICITY_MULTIPLIER;

    @NonNull
    Instant date;

    @Nullable
    String description;

    @NonNull
    Boolean withIssues;

    @Nullable
    @NonFinal
    Instant deleteDate;

    public Volume() {
    }

    public Volume(@NonNull Integer id) {
        super(id);
    }

    public Volume(@NonNull Integer id, Text text) {
        super(id, text);
    }

    public Volume(@NonNull Integer id,
                  @NonNull UUID recordId,
                  @Nullable String volumeNumber,
                  @NonNull Integer issueQuantity,
                  @Nullable String year,
                  @NonNull VolumePeriodicity periodicity,
                  @NonNull Integer periodicityMultiplier,
                  @NonNull Instant date,
                  @Nullable String description,
                  @NonNull Boolean withIssues,
                  @Nullable Instant deleteDate) {
        super(id);
        this.recordId = recordId;
        this.volumeNumber = volumeNumber;
        this.issueQuantity = issueQuantity;
        this.year = year;
        this.periodicity = periodicity;
        this.periodicityMultiplier = periodicityMultiplier;
        this.date = date;
        this.description = description;
        this.withIssues = withIssues;
        this.deleteDate = deleteDate;
    }

    public static Volume testing(@NonNull Integer id, @NonNull Integer year) {
        Volume v = new Volume(id);
        v.setYear(String.valueOf(year));
        v.setIssueQuantity(1);
        v.setDescription("Volume " + year);
        return v;
    }


    /// Workaround kvuli jacksonu - ambiguous setId
    @Override
    public Integer getId() {
        return super.getId();
    }

    /// Workaround kvuli jacksonu - ambiguous setId
    @Override
    public void setId(Integer integer) {
        super.setId(integer);
    }


    public Optional<Instant> getDeleteDate() {
        return Optional.ofNullable(deleteDate);
    }

    public void setDeleted(Instant when) {
        deleteDate = when;
    }
}
