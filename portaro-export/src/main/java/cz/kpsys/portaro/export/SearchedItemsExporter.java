package cz.kpsys.portaro.export;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.io.FileStreamConsumer;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.TypeDescriptor;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class SearchedItemsExporter<E> extends CacheDeletingExporter<Search<MapBackedParams, E, RangePaging>, E> implements Exporter<Search<MapBackedParams, E, RangePaging>> {

    public static final int PAGE_SIZE = 200;
    private static final int DEFAULT_LIMIT_ITEMS = 5000;

    @NonNull Exporter<List<E>> recordListExporter;
    int limitItems;

    public static <E> SearchedItemsExporter<E> of(@NonNull Exporter<List<E>> recordListExporter, int limitItems) {
        return new SearchedItemsExporter<E>(recordListExporter, limitItems);
    }

    public static <E> SearchedItemsExporter<E> ofUnlimited(Exporter<List<E>> recordListExporter) {
        return of(recordListExporter, SearchIterator.LIMIT_ITEMS_NO_LIMIT);
    }

    public static <E> SearchedItemsExporter<E> ofDefaultLimit(Exporter<List<E>> recordListExporter) {
        return of(recordListExporter, DEFAULT_LIMIT_ITEMS);
    }

    @Override
    public void export(@NonNull Search<MapBackedParams, E, RangePaging> search, @NonNull UserAuthentication currentAuth, Department ctx, Locale locale, FileStreamConsumer streamConsumer) {
        Objects.requireNonNull(search.getLastResult(), "Search is without any precending searching");

        SearchIterator<MapBackedParams, E> iterator = new SearchIterator<>(search, ctx, currentAuth);
        iterator.setPageSize(PAGE_SIZE);
        int maxPages = limitItems == SearchIterator.LIMIT_ITEMS_NO_LIMIT ? -1 : (int) Math.ceil(limitItems / (double) PAGE_SIZE);

        List<E> all = new ArrayList<>();
        Predicate<E> exportPredicate = new ExporterTypeDescriptorExportPredicate<>(recordListExporter);

        TimeMeter tm = TimeMeter.start();
        iterator.iterate(limitItems, maxPages, cacheDeleter, c -> {
            List<? extends E> filtered = ListUtil.filter(c, exportPredicate);
            all.addAll(filtered);
            log.debug("loaded page {}", iterator.getLastResult().pageNumber());
        });

        recordListExporter.export(all, currentAuth, ctx, locale, streamConsumer);

        log.info("Export finished in {}", tm.elapsedTimeString());
    }


    @Override
    public TypeDescriptor getType() {
        return TypeDescriptor.valueOf(Search.class);
    }
}
