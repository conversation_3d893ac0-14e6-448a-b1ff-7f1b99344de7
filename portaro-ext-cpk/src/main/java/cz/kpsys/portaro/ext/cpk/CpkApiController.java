package cz.kpsys.portaro.ext.cpk;

import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.cpk.model.CpkSearchResult;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/api/cpk")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CpkApiController extends GenericApiController {

    @NonNull ContextualCpkClient<Department> contextualCpkClient;

    @GetMapping("/search")
    public CpkSearchResult search(@RequestParam("q") String q,
                                  @CurrentDepartment Department currentDepartment) {
        return contextualCpkClient.searchByQ(currentDepartment, q);
    }
}
