package cz.kpsys.portaro.ext.cpk;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PrefixProvidedCpkDedupIdLocalDbRecordIdResolver<CTX> implements CpkDedupIdLocalDbRecordIdResolver<CTX> {

    public static final String DELIMITER = ".";

    @NonNull ContextualProvider<CTX, @NonNull String> cpkInstitutionKeyProvider;

    public Optional<UUID> find(@NonNull String mainId, @NonNull CTX ctx) {
        return find(ctx, mainId);
    }

    public Optional<UUID> find(@NonNull Collection<String> dedupIds, @NonNull CTX ctx) {
        List<UUID> recordIds = dedupIds.stream()
                .flatMap(dedupId -> find(ctx, dedupId).stream())
                .toList();
        return DataUtils.requireMaxOne(recordIds, UUID.class, "CPK's dedupId with %s prefix".formatted(getPrefix(ctx)));
    }

    private Optional<UUID> find(@NonNull CTX ctx, String cpkId) {
        String prefix = getPrefix(ctx);
        return StringUtil.removePrefixOpt(cpkId, prefix)
                .map(UUID::fromString);
    }

    private @NonNull String getPrefix(@NonNull CTX ctx) {
        return cpkInstitutionKeyProvider.getOn(ctx) + DELIMITER;
    }

}
