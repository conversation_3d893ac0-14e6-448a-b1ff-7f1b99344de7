package cz.kpsys.portaro.ext.obalkyknih;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.databasestructure.RecordDb;
import cz.kpsys.portaro.ext.obalkyknih.db.Obalkyknih;
import cz.kpsys.portaro.ext.obalkyknih.db.ObalkyknihConstants;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbNextObalkyknihItemSupplier implements NextObalkyknihItemSupplier {

    private static final int PRIORITY_FETCH_COUNT = 150;
    private static final int OTHER_FETCH_COUNT = 15;
    private static final int OLD_FETCH_COUNT = 70;

    @NonNull ParameterizedSearchLoader<MapBackedParams, Obalkyknih> obalkyknihSearchLoader;
    @NonNull Provider<@NonNull Duration> obalkyknihRescanIntervalProvider;

    BackingoffFetcher<List<Obalkyknih>> priorityCovers = new ListBackingoffFetcher<>(Duration.ofMinutes(1),
            mkFetcher(this::priorityCovers, PRIORITY_FETCH_COUNT));
    BackingoffFetcher<List<Obalkyknih>> notFoundCovers = new ListBackingoffFetcher<>(Duration.ofHours(1),
            mkFetcher(this::notFoundCovers, OTHER_FETCH_COUNT));
    BackingoffFetcher<List<Obalkyknih>> erroredCovers = new ListBackingoffFetcher<>(Duration.ofDays(1),
            mkFetcher(this::erroredCovers, OTHER_FETCH_COUNT));
    BackingoffFetcher<List<Obalkyknih>> skippedCovers = new ListBackingoffFetcher<>(Duration.ofDays(1),
            mkFetcher(this::skippedCovers, OTHER_FETCH_COUNT));
    BackingoffFetcher<List<Obalkyknih>> oldCovers = new ListBackingoffFetcher<>(Duration.ofMinutes(1),
            mkFetcher(this::oldCovers, OLD_FETCH_COUNT)
    ).withBackoffAfterCountProcessed(OLD_FETCH_COUNT); // Do not load more than OLD_FETCH_COUNT in backoff period (1 min)

    public int getRecommendedStackSize() {
        return PRIORITY_FETCH_COUNT + OTHER_FETCH_COUNT * 3 + OLD_FETCH_COUNT;
    }

    public void fillStack(@NonNull List<Obalkyknih> stack) {
        TimeMeter tm = (log.isDebugEnabled()) ? TimeMeter.start() : null;

        // Načítání záznamů v opačném pořadí (na stack)
        oldCovers.tryFetch().ifPresent(list -> stack.addAll(list.reversed()));
        skippedCovers.tryFetch().ifPresent(list -> stack.addAll(list.reversed()));
        erroredCovers.tryFetch().ifPresent(list -> stack.addAll(list.reversed()));
        notFoundCovers.tryFetch().ifPresent(list -> stack.addAll(list.reversed()));
        priorityCovers.tryFetch().ifPresent(list -> stack.addAll(list.reversed()));

        if (tm != null) {
            log.debug("Obalkyknih DB fetch time: {}", tm.elapsedTimeString());
        }
    }

    /// Záznamy, které se mají načítat prioritně (typicky nové).
    private List<Obalkyknih> priorityCovers(int maxFetchCount) {
        MapBackedParams params = MapBackedParams.build(StaticParamsModifier.of(
                ObalkyknihConstants.SearchParams.DOWNLOAD_STATE, List.of(Obalkyknih.State.PRIORITY_FLAG),
                ObalkyknihConstants.SearchParams.INCLUDE_PRESENT_DATA, true,
                ObalkyknihConstants.SearchParams.INCLUDE_MISSING_DATA, true
        ));
        if (log.isDebugEnabled()) {
            log.debug("Priority covers to process: {}", obalkyknihSearchLoader.getTotalElements(params));
        }
        return obalkyknihSearchLoader.getPage(RangePaging.forFirstPage(maxFetchCount), SortingItem.ofSimpleDesc(RecordDb.OBALKYKNIH.CAS_HLEDANI), params).getItems();
    }

    /// Pokusy starší než jeden den, které nemají obálku.
    private List<Obalkyknih> notFoundCovers(int maxFetchCount) {
        Instant yesterday = Instant.now().minus(Duration.ofDays(1));
        MapBackedParams params = MapBackedParams.build(StaticParamsModifier.of(
                ObalkyknihConstants.SearchParams.DOWNLOAD_STATE, List.of(Obalkyknih.State.OK),
                ObalkyknihConstants.SearchParams.INCLUDE_PRESENT_DATA, false,
                ObalkyknihConstants.SearchParams.INCLUDE_MISSING_DATA, true,
                CoreSearchParams.TO_DATE, yesterday
        ));
        if (log.isDebugEnabled()) {
            log.debug("Not found covers to process: {}", obalkyknihSearchLoader.getTotalElements(params));
        }
        return obalkyknihSearchLoader.getPage(RangePaging.forFirstPage(maxFetchCount), SortingItem.ofSimpleDesc(RecordDb.OBALKYKNIH.CAS_HLEDANI), params).getItems();
    }

    /// Pokusy starší než jeden den, které skončily s chybou.
    private List<Obalkyknih> erroredCovers(int maxFetchCount) {
        Instant yesterday = Instant.now().minus(Duration.ofDays(1));
        MapBackedParams params = MapBackedParams.build(StaticParamsModifier.of(
                ObalkyknihConstants.SearchParams.DOWNLOAD_STATE, List.of(Obalkyknih.State.ERROR),
                ObalkyknihConstants.SearchParams.INCLUDE_PRESENT_DATA, true,
                ObalkyknihConstants.SearchParams.INCLUDE_MISSING_DATA, true,
                CoreSearchParams.TO_DATE, yesterday
        ));
        if (log.isDebugEnabled()) {
            log.debug("Errored covers to process: {}", obalkyknihSearchLoader.getTotalElements(params));
        }
        return obalkyknihSearchLoader.getPage(RangePaging.forFirstPage(maxFetchCount), SortingItem.ofSimpleDesc(RecordDb.OBALKYKNIH.CAS_HLEDANI), params).getItems();
    }

    /// Pokusy starší než jeden den, které byly přeskočeny na základě konfigurace.
    private List<Obalkyknih> skippedCovers(int maxFetchCount) {
        Instant yesterday = Instant.now().minus(Duration.ofDays(1));
        MapBackedParams params = MapBackedParams.build(StaticParamsModifier.of(
                ObalkyknihConstants.SearchParams.DOWNLOAD_STATE, List.of(Obalkyknih.State.SKIPPED),
                ObalkyknihConstants.SearchParams.INCLUDE_PRESENT_DATA, true,
                ObalkyknihConstants.SearchParams.INCLUDE_MISSING_DATA, true,
                CoreSearchParams.TO_DATE, yesterday
        ));
        if (log.isDebugEnabled()) {
            log.debug("Skipped covers to process: {}", obalkyknihSearchLoader.getTotalElements(params));
        }
        return obalkyknihSearchLoader.getPage(RangePaging.forFirstPage(maxFetchCount), SortingItem.ofSimpleDesc(RecordDb.OBALKYKNIH.CAS_HLEDANI), params).getItems();
    }

    /// Stažení metadat pro staré a úspěšně stažené obálky, které jsou starší než nastavený interval.
    private List<Obalkyknih> oldCovers(int maxFetchCount) {
        Instant rescanUpTo = Instant.now().minus(obalkyknihRescanIntervalProvider.get());
        MapBackedParams params = MapBackedParams.build(StaticParamsModifier.of(
                ObalkyknihConstants.SearchParams.DOWNLOAD_STATE, List.of(Obalkyknih.State.OK, Obalkyknih.State.NO_METADATA),
                ObalkyknihConstants.SearchParams.INCLUDE_PRESENT_DATA, true,
                ObalkyknihConstants.SearchParams.INCLUDE_MISSING_DATA, false,
                CoreSearchParams.TO_DATE, rescanUpTo
        ));
        if (log.isDebugEnabled()) {
            log.debug("Old covers to process: {}", obalkyknihSearchLoader.getTotalElements(params));
        }
        return obalkyknihSearchLoader.getPage(RangePaging.forFirstPage(maxFetchCount), SortingItem.ofSimpleDesc(RecordDb.OBALKYKNIH.CAS_HLEDANI), params).getItems();
    }

    private <R> Supplier<R> mkFetcher(Function<Integer, R> fetcherMaxCount, int maxFetchCount) {
        return () -> fetcherMaxCount.apply(maxFetchCount);
    }

}
