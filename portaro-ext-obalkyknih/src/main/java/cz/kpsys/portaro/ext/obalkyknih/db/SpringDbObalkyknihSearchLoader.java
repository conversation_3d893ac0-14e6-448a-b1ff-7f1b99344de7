package cz.kpsys.portaro.ext.obalkyknih.db;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.database.RangePagingResultSetExtractor;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

import static cz.kpsys.portaro.commons.db.QueryUtils.AS;
import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.RecordDb.OBALKYKNIH.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbObalkyknihSearchLoader
        extends AbstractSpringDbSearchLoader<MapBackedParams, Obalkyknih, RangePaging>
        implements RowMapper<Obalkyknih> {

    public SpringDbObalkyknihSearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                          @NonNull QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory);
    }

    @Override
    protected void select(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.select(
                TC(TABLE, RECORD_ID),
                TC(TABLE, CAS_HLEDANI),
                TC(TABLE, VYSLEDEK),
                // Column "citace" is VARCHAR(2000) and Firebird is pretty slow when this column is part of filtering
                // and ordering. So it is fetched additionally after main query is filtered and ordered.
                AS("(SELECT citace FROM obalkyknih okInner where okInner.record_id = obalkyknih.record_id)", CITACE),
                TC(TABLE, URL_COVER),
                TC(TABLE, FK_FULLTEXT),
                TC(TABLE, URL_BACKLINK),
                TC(TABLE, URL_TOC_PDF),
                TC(TABLE, TOC),
                TC(TABLE, BOOK_ID)
        );
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.from(TABLE);

        if (p.hasNotNull(RecordConstants.SearchParams.RECORD)) {
            if (!p.hasLength(RecordConstants.SearchParams.RECORD)) {
                return false;
            }
            sq.where().and().in(RECORD_ID, p.get(RecordConstants.SearchParams.RECORD));
        }

        List<Obalkyknih.State> coverState = p.getOrThrow(ObalkyknihConstants.SearchParams.DOWNLOAD_STATE);
        sq.where().and().in(TC(TABLE, VYSLEDEK), ListUtil.getListOfIds(coverState));

        if (!p.getOrThrow(ObalkyknihConstants.SearchParams.INCLUDE_MISSING_DATA)) {
            sq.where().and().isNotNull(TC(TABLE, FK_FULLTEXT));
        }

        if (!p.getOrThrow(ObalkyknihConstants.SearchParams.INCLUDE_PRESENT_DATA)) {
            sq.where().and().isNull(TC(TABLE, FK_FULLTEXT));
        }

        if (p.hasNotNull(CoreSearchParams.TO_DATE)) {
            sq.where().and().ltEq(TC(TABLE, CAS_HLEDANI), p.get(CoreSearchParams.TO_DATE));
        }

        return true;
    }

    @Override
    protected Optional<SortingItem> defaultOrCustomSorting(@Nullable SortingItem customSorting) {
        SortingItem sorting = ObjectUtil.firstNotNull(customSorting, SortingItem.ofSimpleDesc(CAS_HLEDANI));
        return Optional.of(SortingItem.ofSimpleFieldAndOrder(TC(TABLE, sorting.field()), sorting.asc()));
    }

    @Override
    protected ResultSetExtractor<Chunk<Obalkyknih, RangePaging>> createResultSetExtractor(@NonNull SelectQuery sq, @NonNull MapBackedParams searchParams, @NonNull RangePaging paging, @NonNull Sorting sorting) {
        return new RangePagingResultSetExtractor<>(this, paging);
    }

    @Override
    public Obalkyknih mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        return new Obalkyknih(
                DbUtils.uuidNotNull(rs, RECORD_ID),
                DbUtils.instantOrNull(rs, CAS_HLEDANI),
                Obalkyknih.State.CODEBOOK.getById(DbUtils.getShortNotNull(rs, VYSLEDEK)),
                rs.getString(CITACE),
                rs.getString(URL_COVER),
                DbUtils.getLong(rs, FK_FULLTEXT),
                rs.getString(URL_BACKLINK),
                rs.getString(URL_TOC_PDF),
                rs.getString(TOC),
                rs.getString(BOOK_ID)
        );
    }

}
