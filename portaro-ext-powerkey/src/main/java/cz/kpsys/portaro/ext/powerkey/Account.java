package cz.kpsys.portaro.ext.powerkey;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.erp.employee.workattendance.AbsenceReason;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.Optional;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public enum Account implements Identified<Integer> {

    A7_NEPR_LEKAR(7, "neprL - Lékař", AbsenceReason.DOCTOR_VISIT),
    A8_NEPR_DOVOLENA(8, "neprD - Do<PERSON>lená", AbsenceReason.VACATION),
    A9_NEPR_PARAGRAF(9, "neprP - Paragraf", AbsenceReason.PARAGRAPH),
    A10_NEPR_NEMOC(10, "neprN - Nemoc", AbsenceReason.ILLNESS),
    A11_NEPR_OCR(11, "neprOcr - Ošetřování člena rodiny", AbsenceReason.OCR),
    A12_NEPR_NAHR(12, "neprNa - Náhradní volno", AbsenceReason.COMPENSATION),
    A13_NEPR_NEPL_VOLNO(13, "neprNe - Neplacené volno", AbsenceReason.UNPAID),
    A14_SKOLENI(14, "školení"),
    A21_PRESCAS(21, "Přesčas (?? pravdepodobne pro pracovni den)"),
    A24_PRESCAS_SV(24, "Přesčas svátek"),
    A25_NEPR_NEOML_ABSENCE(25, "neprA - Neomluvená absence", AbsenceReason.UNEXCUSED),
    A26_SVATEK(26, "Svátek"),
    A62_FOND_PRAC_DOBY(62, "fond - Fond pracovní doby"),
    A63_BANK(63, "Bank"),
    A118_PRACE_SVATEK(118, "Práce svátek"),
    A154_PRESCAS_SOBOTA(154, "Přesčas - sobota"),
    A155_PRESCAS_NEDELE(155, "Přesčas - neděle"),
    A211_ODPRACOVANO_PAMICA(211, "Odpracováno Pamica"),
    A222_NEPR_PREKAZKY(222, "neprPz - Překážka zam.", AbsenceReason.EMPLOYER_OBSTACLES),
    A225_NEPR_ODT(225, "neprOdt - Otcovská dovolená", AbsenceReason.PATERNITY),
    A234_NAHR_VOLNO_ZA_SVATEK(234, "Náhradní volno za svátek", AbsenceReason.COMPENSATION),
    A251_NEPR_MATERSKA(251, "Mateřská dovolená", AbsenceReason.MATERNITY),
    A254_NEPR_RODICOVSKA_PRO_MATKU(254, "Rodičovská dovolená pro matku", AbsenceReason.MATERNITY),
    A257_NEPR_DOVOLENA_H(257, "Dovolená H", AbsenceReason.VACATION),
    A263_NEPR_ADM(263, "neprAdm - Pionýrák", AbsenceReason.EVENTS);

    public static final Codebook<Account, Integer> CODEBOOK = new StaticCodebook<>(values());

    @Getter @NonNull Integer id;
    @NonNull String desc;
    @Nullable AbsenceReason absenceReason;

    Account(@NonNull Integer id, @NonNull String desc) {
        this(id, desc, null);
    }

    public Optional<AbsenceReason> absenceReason() {
        return Optional.ofNullable(absenceReason);
    }
}
