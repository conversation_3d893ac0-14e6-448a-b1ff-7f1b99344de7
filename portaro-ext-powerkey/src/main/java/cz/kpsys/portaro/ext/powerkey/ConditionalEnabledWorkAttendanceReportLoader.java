package cz.kpsys.portaro.ext.powerkey;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.employee.workattendance.EnrichedLocalDates;
import cz.kpsys.portaro.erp.employee.workattendance.WorkAtendanceReport;
import cz.kpsys.portaro.erp.employee.workattendance.WorkAttendanceReportLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ConditionalEnabledWorkAttendanceReportLoader implements WorkAttendanceReportLoader {

    @NonNull ContextualProvider<@NonNull Department, @NonNull Boolean> enabledProvider;
    @NonNull WorkAttendanceReportLoader delegate;

    @Override
    public @Nullable WorkAtendanceReport load(@NonNull List<String> personalNumbers, @NonNull EnrichedLocalDates days, @NonNull Department ctx) {
        if (enabledProvider.getOn(ctx)) {
            return delegate.load(personalNumbers, days, ctx);
        } else {
            return null;
        }
    }

}
