package cz.kpsys.portaro.ext.powerkey;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.database.AdhocQueryer;
import cz.kpsys.portaro.databaseproperties.AutodetectedDatabaseConnectionSettingsContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.employee.workattendance.WorkAttendanceReportLoader;
import cz.kpsys.portaro.setting.SettingLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.ZoneId;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PowerkeyConfig {

    public static final String FEATURE_NAME = "PowerKey";

    @NonNull SettingLoader settingLoader;

    @Bean
    public StaticProvider<@NonNull ZoneId> powerkeyTimeZoneIdProvider() {
        return StaticProvider.of(CoreConstants.CZECH_TIME_ZONE_ID);
    }

    @Bean
    public WorkAttendanceReportLoader workAttendanceReportLoader() {
        return new ConditionalEnabledWorkAttendanceReportLoader(
                settingLoader.getDepartmentedProvider(PowerkeySettingKeys.POWERKEY_ENABLED),
                new PowerkeyViewWorkAttendanceReportLoader(powerkeyAdhodQuerier()));
    }

    @Bean
    public AdhocQueryer<Department> powerkeyAdhodQuerier() {
        return AdhocQueryer.create(
                powerkeyTimeZoneIdProvider(),
                settingLoader.getDepartmentedProvider(PowerkeySettingKeys.POWERKEY_ENABLED).toEnabledAsserter(value -> value, FEATURE_NAME, null),
                new AutodetectedDatabaseConnectionSettingsContextualProvider<>(
                        settingLoader.getDepartmentedProvider(PowerkeySettingKeys.POWERKEY_DB_CONNECTION_STRING).throwingWhenNull(),
                        settingLoader.getDepartmentedProvider(PowerkeySettingKeys.POWERKEY_DB_USER).throwingWhenNull(),
                        settingLoader.getDepartmentedProvider(PowerkeySettingKeys.POWERKEY_DB_PASSWORD).throwingWhenNull()
                )
        );
    }

}
