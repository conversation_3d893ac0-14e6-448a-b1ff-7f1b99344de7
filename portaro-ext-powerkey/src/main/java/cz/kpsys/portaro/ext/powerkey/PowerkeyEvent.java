package cz.kpsys.portaro.ext.powerkey;

import cz.kpsys.portaro.erp.employee.workattendance.EnrichedLocalDate;
import lombok.NonNull;

import java.time.Duration;

public record PowerkeyEvent(

        @NonNull
        String osCislo,

        @NonNull
        EnrichedLocalDate day,

        @NonNull
        Account account,

        @NonNull
        Duration duration

) {

    public boolean matches(@NonNull Account account) {
        return this.account == account;
    }

    public boolean matches(@NonNull AccountDayType refDayType) {
        return switch (refDayType) {
            case WORK_DAY -> day.isWorkday();
            case SATURDAY -> day.isSaturday();
            case SUNDAY -> day.isSunday();
            case HOLIDAY -> day.isWorkdayHoliday();
            case WHENEVER -> true;
        };
    }

    public boolean matches(@NonNull Account account, @NonNull AccountDayType dayType) {
        return matches(account) && matches(dayType);
    }

}
