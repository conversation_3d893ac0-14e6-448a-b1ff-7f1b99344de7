package cz.kpsys.portaro.ext.powerkey;

import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.AdhocQueryer;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.employee.workattendance.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.DataClassRowMapper;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.ext.powerkey.AccountValueFormulas.*;
import static cz.kpsys.portaro.ext.powerkey.PowerkeyTables.ViewKontaDen.*;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PowerkeyViewWorkAttendanceReportLoader implements WorkAttendanceReportLoader {

    @NonNull AdhocQueryer<Department> adhocQueryer;
    @NonNull LocalDateToPowerkeyDayNumberConverter toPowerkeyDayConverter = new LocalDateToPowerkeyDayNumberConverter();

    @Override
    public WorkAtendanceReport load(@NonNull List<String> personalNumbers, @NonNull EnrichedLocalDates days, @NonNull Department ctx) {
        return personalNumbers.stream()
                .map(personalNumber -> load(personalNumber, days, ctx))
                .reduce(WorkAtendanceReport.EMPTY, WorkAtendanceReport::plus);
    }

    private @NonNull WorkAtendanceReport load(@NonNull String personalNumber, @NonNull EnrichedLocalDates days, @NonNull Department ctx) {
        personalNumber = normalizePersonalNumber(personalNumber);

        List<PowerkeyEvent> pwkEvents = getPowerkeyEvents(days, ctx, personalNumber);

        Duration mesFond = MES_FOND.calculate(pwkEvents);
        Duration mesOdprCelkem = MES_ODPR_CELKEM.calculate(pwkEvents);

        Duration mesPrescasPd = MES_PRESCAS_PD.calculate(pwkEvents);
        Duration mesPrescasVikSv = MES_PRESCAS_VIK_SV.calculate(pwkEvents);
        Duration mesPrescasBank = MES_PRESCAS_BANK.calculate(pwkEvents);

        Duration mesNeprLekar = MES_NEPR_LEKAR.calculate(pwkEvents);
        Duration mesNeprDovolena = MES_NEPR_DOVOLENA.calculate(pwkEvents);

        Duration mesNeprNemoc = MES_NEPR_NEMOC.calculate(pwkEvents);
        Duration mesNeprOcr = MES_NEPR_OCR.calculate(pwkEvents);
        Duration mesNeprParagraf = MES_NEPR_PARAGRAF.calculate(pwkEvents);
        Duration mesNeprNahrVolno = MES_NEPR_NAHR_VOLNO.calculate(pwkEvents);
        Duration mesNeprNeplVolno = MES_NEPR_NEPL_VOLNO.calculate(pwkEvents);
        Duration mesNeprNeomlAbsence = MES_NEPR_NEOML_ABSENCE.calculate(pwkEvents);
        Duration mesNeprMaterska = MES_NEPR_MATERSKA.calculate(pwkEvents);
        Duration mesNeprOtcovska = MES_NEPR_OTCOVSKA.calculate(pwkEvents);
        Duration mesNeprPrekazky = MES_NEPR_PREKAZKY.calculate(pwkEvents);
        Duration mesNeprAdm = MES_NEPR_ADM.calculate(pwkEvents);


        Duration allAbsencesSum = DateUtils.sum(
                mesNeprLekar,
                mesNeprDovolena,
                mesNeprNemoc,
                mesNeprOcr,
                mesNeprParagraf,
                mesNeprNahrVolno,
                mesNeprNeplVolno,
                mesNeprNeomlAbsence,
                mesNeprMaterska,
                mesNeprOtcovska,
                mesNeprPrekazky,
                mesNeprAdm
        );

        Duration planComputed = (mesFond.isZero())
                ? mesOdprCelkem // TODO: tímhle si nejsem jistý, ale pokud zaměstnanec nemá fond data, tak sutin ukazuje počet odpracovaných
                : mesFond.minus(MES_FOND_SVATEK.calculate(pwkEvents)).minus(allAbsencesSum);

        Duration totalOvertime = mesOdprCelkem
                .minus(planComputed)
                .minus(mesNeprNahrVolno);

        return new WorkAtendanceReport(
                new WorkAtendanceReportSummary(
                        mesFond,
                        mesOdprCelkem,
                        mesPrescasPd,
                        mesPrescasVikSv,
                        totalOvertime,
                        mesPrescasBank,
                        mesNeprLekar,
                        mesNeprDovolena,
                        mesNeprNemoc,
                        mesNeprOcr,
                        mesNeprParagraf,
                        mesNeprNahrVolno,
                        mesNeprNeplVolno,
                        mesNeprNeomlAbsence,
                        mesNeprMaterska,
                        mesNeprOtcovska,
                        mesNeprPrekazky,
                        mesNeprAdm,
                        planComputed,
                        allAbsencesSum
                ),
                days.stream().map(day -> getDayReport(day, pwkEvents)).toList()
        );
    }

    private List<PowerkeyEvent> getPowerkeyEvents(EnrichedLocalDates days, Department ctx, String personalNumber) {
        List<Row> rows = fetchRows(days, ctx, personalNumber);
        return ListUtil.convertStrict(rows, source -> {
            LocalDate localDate = toPowerkeyDayConverter.reverseApply(source.den());
            EnrichedLocalDate day = days.get(localDate);
            return new PowerkeyEvent(
                    source.osCislo(),
                    day,
                    Account.CODEBOOK.getById(source.kontoId()),
                    Duration.ofMinutes(source.hodnotaMinuty())
            );
        });
    }

    private List<Row> fetchRows(@NonNull EnrichedLocalDates days, Department ctx, String personalNumber) {
        return adhocQueryer.select(ctx, (jdbc, sq) -> {
            sq.select(OS_CISLO, DEN, KONTO_ID, HODNOTA_MINUTY);
            sq.from(VIEW);
            sq.where()
                    .eq(OS_CISLO, personalNumber)
                    .and()
                    .in(KONTO_ID, ListUtil.getListOfIds(Account.CODEBOOK.getAll()))
                    .and()
                    .in(DEN, days.streamDates().map(toPowerkeyDayConverter).toList());
            return jdbc.query(sq.getSql(), sq.getParamMap(), DataClassRowMapper.newInstance(Row.class));
        });
    }

    private static WorkAtendanceReportDay getDayReport(EnrichedLocalDate day, List<PowerkeyEvent> pwkEvents) {
        Set<PowerkeyEvent> thisDayPwkEvents = pwkEvents.stream().filter(work -> work.day().equals(day)).collect(Collectors.toSet());
        AccountValueFormula.ComputedDuration computedAbsence = DEN_NEPRITOMNOST.calculateRich(thisDayPwkEvents);
        return new WorkAtendanceReportDay(
                day,
                DEN_PRAC_STD.calculate(thisDayPwkEvents),
                DEN_PRAC_PRESCAS.calculate(thisDayPwkEvents),
                new WorkAtendanceAbsenceSummary(
                        computedAbsence.duration(),
                        computedAbsence.positiveDurationAccounts().stream().map(Account::absenceReason).filter(Optional::isPresent).map(Optional::get).toList()
                )
        );
    }

    /// Due to some magic casting and processing in DB leading zeroes are removed from personal numbers, e.g.
    /// {@code "012" -> "12"}
    private static String normalizePersonalNumber(String personalNumber) {
        return StringUtils.trimLeadingCharacter(personalNumber, '0');
    }

}
