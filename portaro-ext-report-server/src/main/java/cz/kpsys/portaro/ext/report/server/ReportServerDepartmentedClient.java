package cz.kpsys.portaro.ext.report.server;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.report.server.datatypes.PrintParameterServerResponse;
import cz.kpsys.portaro.ext.report.server.datatypes.ReportTemplateReportServerResponse;
import cz.kpsys.portaro.ext.report.server.datatypes.ServerFolderReportServerResponse;
import lombok.NonNull;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Locale;
import java.util.Map;

public interface ReportServerDepartmentedClient {

    List<ReportTemplateReportServerResponse> getTemplates(Department department, UserAuthentication currentAuth);

    ServerFolderReportServerResponse getFolders(Department department, UserAuthentication currentAuth);

    List<PrintParameterServerResponse> getTemplateParameters(Department department, UserAuthentication currentAuth, String id);

    ResponseEntity<@NonNull Resource> getExportReportTemplate(Department department, UserAuthentication currentAuth, String id, Locale locale);

    ResponseEntity<@NonNull Resource> getExportReportTemplateWithParameters(Department department, UserAuthentication currentAuth, String id, Locale locale, Map<String, Object> props);
}
