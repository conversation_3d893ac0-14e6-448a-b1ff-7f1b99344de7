package cz.kpsys.portaro.ext.report.server;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.commons.api.ActionRequestMethod;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import lombok.With;

import java.util.Map;
import java.util.UUID;


@Form(id = "templateParametersRequest",
        title = "Parametry vybrané sestavy")
@FormSubmit(path = CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.REPORT_SERVER_URL_PART + "/reports/#{formObject.templateId}/export", method = ActionRequestMethod.GET, asPage = true)
@With
public record TemplateParametersRequest(

        UUID templateId,

        Map<String, Object> props

) {}
