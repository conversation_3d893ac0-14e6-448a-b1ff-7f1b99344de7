package cz.kpsys.portaro.ext.sol;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sol.datatypes.NormalizedSolUser;
import cz.kpsys.portaro.ext.sol.datatypes.SolUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class NormalizedSolUserLoader implements ContextualProvider<Department, List<NormalizedSolUser>> {

    @NonNull DynamicDepartmentedSolClient dynamicDepartmentedSolClient;

    @Override
    public List<NormalizedSolUser> getOn(Department ctx) throws ItemNotFoundException {
        // Input data from external Sol system
        List<SolUser> usersFromSolSystem = dynamicDepartmentedSolClient.getSolPersons(ctx);
        // Create new list of Normalized person with multiple personType
        return getNormalizedSolUsers(usersFromSolSystem);
    }

    @NonNull
    private List<NormalizedSolUser> getNormalizedSolUsers(List<SolUser> usersFromSolSystem) {
        List<NormalizedSolUser> normalizedUsersFromSolSystem = new ArrayList<>();

        usersFromSolSystem
                .forEach(solUser -> {
                    Optional<NormalizedSolUser> normalizedUser = ListUtil.findById(normalizedUsersFromSolSystem, solUser.getId());
                    if (normalizedUser.isPresent()) {
                        normalizedUser.get().getPersonType().add(solUser.getPersonType());
                    } else {
                        if (StringUtil.hasTrimmedLength(solUser.getFirstName()) && StringUtil.hasTrimmedLength(solUser.getLastName())) {
                            normalizedUsersFromSolSystem.add(NormalizedSolUser.mapNewUser(solUser));
                        }
                    }
                });

        return normalizedUsersFromSolSystem;
    }
}
