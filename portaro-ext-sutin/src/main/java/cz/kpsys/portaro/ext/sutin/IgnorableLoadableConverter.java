package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.commons.object.repo.ByIdAndByIdOptLoadable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class IgnorableLoadableConverter<ID, TARGET extends ImportIgnorable> implements Converter<ID, TARGET> {

    @NonNull ByIdAndByIdOptLoadable<TARGET, ID> loadable;

    @Override
    public TARGET convert(@NonNull ID source) {
        var converted = loadable.getById(source);
        if (converted.isImported()) {
            return converted;
        } else {
            log.info("Ignoring converted null value {} by loader {}", source, loadable);
            return null;
        }
    }

}
