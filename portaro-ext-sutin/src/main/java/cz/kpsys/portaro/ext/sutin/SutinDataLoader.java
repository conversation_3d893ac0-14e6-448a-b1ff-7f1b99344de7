package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.commons.convert.IdToObjectConverter;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.AdhocQueryer;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SelectStatementsDef;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinCompanyResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinPropertyResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinWorkerResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.companies.Spolecnost;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.contacts.*;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.property.Majetek;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.property.MajetekSkupina;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.property.MajetekZaznam;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.jdbc.core.DataClassRowMapper;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SuppressWarnings("SqlSourceToSinkFlow")
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SutinDataLoader {

    @NonNull AdhocQueryer<Department> adhocQueryer;

    public List<SutinWorkerResponse> loadSutinWorkers(Department ctx) {
        return adhocQueryer.select(ctx, (jdbc) -> {
            DefaultConversionService conversionService = getConversionService();
            List<PersonDto> persons = jdbc.query(SelectStatementsDef.getPracovnikSelectStatement(), DataClassRowMapper.newInstance(PersonDto.class, conversionService));
            log.atDebug().setMessage("Loaded {} person rows").addArgument(persons::size).log();

            Map<String, List<PersonDto>> mappedPersons = persons.stream()
                    .collect(Collectors.groupingBy(PersonDto::elementId));
            log.atDebug().setMessage("Loaded {} persons").addArgument(mappedPersons::size).log();

            return mappedPersons.entrySet().stream().map(personEntry -> {
                String elementId = personEntry.getKey();
                List<PersonDto> employeeData = personEntry.getValue();
                List<WorkCatalogLinkDto> workTypes = jdbc.query(SelectStatementsDef.getPracKatalogCinnostiVazby(elementId), DataClassRowMapper.newInstance(WorkCatalogLinkDto.class, conversionService));
                List<SalaryDto> salary = jdbc.query(SelectStatementsDef.getMzdySelectStatement(elementId), DataClassRowMapper.newInstance(SalaryDto.class, conversionService));
                List<MonthAttendanceDto> monthlyAttendances = jdbc.query(SelectStatementsDef.getMonthAttendanceSelectStatement(elementId), DataClassRowMapper.newInstance(MonthAttendanceDto.class, conversionService));

                List<Kontakt> contacts = jdbc.query(SelectStatementsDef.getContactSelectStatement(elementId), DataClassRowMapper.newInstance(Kontakt.class, conversionService));
                List<Adresa> address = jdbc.query(SelectStatementsDef.getAddressSelectStatement(elementId), DataClassRowMapper.newInstance(Adresa.class, conversionService));
                List<AdresaWithType> addressWithType = address.stream()
                        .map(adresa -> {
                            List<AdresaTyp> typeList = jdbc.query(SelectStatementsDef.getAddressTypeStatement(adresa.id()), DataClassRowMapper.newInstance(AdresaTyp.class, conversionService));
                            return new AdresaWithType(adresa, typeList);
                        })
                        .toList();

                return new SutinWorkerResponse(elementId, employeeData, workTypes, salary, monthlyAttendances, addressWithType, contacts);
            }).toList();
        });
    }

    public List<SutinCompanyResponse> loadSutinCompanies(Department ctx) {
        return adhocQueryer.select(ctx, (jdbc) -> {
            DefaultConversionService conversionService = getConversionService();
            List<Spolecnost> sutinCompanies = jdbc.query(SelectStatementsDef.getCompanySelectStatement(), (rs, _) -> new Spolecnost(
                    rs.getString("elementId"),
                    rs.getString("name"),
                    null,
                    rs.getString("companyIdNumber"),
                    rs.getString("taxIdNumber"),
                    rs.getString("linkedDivisionElementId"),
                    rs.getString("workReportEnabled"),
                    null,
                    null
            ));
            List<Spolecnost> centerCompanies = jdbc.query(SelectStatementsDef.getCentersSelectStatement(), (rs, _) -> new Spolecnost(
                    rs.getString("elementId"),
                    rs.getString("name") + " - " + rs.getString("customer"),
                    rs.getString("id"),
                    null,
                    null,
                    null,
                    null,
                    rs.getString("code"),
                    rs.getString("customer")
            ));

            return ListUtil.convertStrict(ListUtil.concat(sutinCompanies, centerCompanies), companyData -> {
                List<Kontakt> contacts = jdbc.query(SelectStatementsDef.getContactSelectStatement(companyData.realElementId()), DataClassRowMapper.newInstance(Kontakt.class, conversionService));

                List<Adresa> address = jdbc.query(SelectStatementsDef.getAddressSelectStatement(companyData.realElementId()), DataClassRowMapper.newInstance(Adresa.class, conversionService));
                List<AdresaWithType> addressWithType = ListUtil.convertStrict(address, adresa -> {
                    List<AdresaTyp> typeList = jdbc.query(SelectStatementsDef.getAddressTypeStatement(adresa.id()), DataClassRowMapper.newInstance(AdresaTyp.class, conversionService));
                    return new AdresaWithType(adresa, typeList);
                });

                return new SutinCompanyResponse(companyData, addressWithType, contacts);
            });
        });
    }

    public List<SutinPropertyResponse> loadSutinProperty(Department ctx) {
        return adhocQueryer.select(ctx, (jdbcTemplate) -> {
            DefaultConversionService conversionService = getConversionService();
            List<Majetek> sutinProperty = jdbcTemplate.query(SelectStatementsDef.getPropertySelectStatement(), DataClassRowMapper.newInstance(Majetek.class, conversionService));

            return ListUtil.convertStrict(sutinProperty, propertyData -> {
                List<MajetekZaznam> propertyRecords = jdbcTemplate.query(SelectStatementsDef.getPropertyStatusDataSelectStatement(propertyData.elementId()), DataClassRowMapper.newInstance(MajetekZaznam.class, conversionService));
                List<WorkCatalogLinkDto> workTypes = jdbcTemplate.query(SelectStatementsDef.getPracKatalogCinnostiVazby(propertyData.elementId()), DataClassRowMapper.newInstance(WorkCatalogLinkDto.class, conversionService));
                return new SutinPropertyResponse(propertyData, propertyRecords, workTypes);
            });
        });
    }

    private DefaultConversionService getConversionService() {
        DefaultConversionService conversionService = new DefaultConversionService();

        // Safeguard when forgetting to convert enum by codebook
        conversionService.removeConvertible(Integer.class, Enum.class);
        conversionService.removeConvertible(String.class, Enum.class);

        conversionService.addConverter(Integer.class, PracZdravPojistovnaKat.class, new IgnorableLoadableConverter<>(PracZdravPojistovnaKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracVzdelaniKat.class, new IgnorableLoadableConverter<>(PracVzdelaniKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracVypovedTypKat.class, new IgnorableLoadableConverter<>(PracVypovedTypKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracVypovedStranaKat.class, new IgnorableLoadableConverter<>(PracVypovedStranaKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracStatniPrislusnostKat.class, new IgnorableLoadableConverter<>(PracStatniPrislusnostKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracSmlouvaTypKat.class, new IgnorableLoadableConverter<>(PracSmlouvaTypKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracMistoPraceKat.class, new IgnorableLoadableConverter<>(PracMistoPraceKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracKategorieKat.class, new IgnorableLoadableConverter<>(PracKategorieKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracInvaliditaKat.class, new IgnorableLoadableConverter<>(PracInvaliditaKat.CODEBOOK));
        conversionService.addConverter(Integer.class, KontaktTypKat.class, new IdToObjectConverter<>(KontaktTypKat.CODEBOOK));
        conversionService.addConverter(Integer.class, AdresaTypKat.class, new IdToObjectConverter<>(AdresaTypKat.CODEBOOK));
        conversionService.addConverter(Integer.class, MajetekSkupina.class, new IdToObjectConverter<>(MajetekSkupina.CODEBOOK));
        conversionService.addConverter(String.class, WorkCatalogLinkDto.Typ.class, new IdToObjectConverter<>(WorkCatalogLinkDto.Typ.CODEBOOK));
        conversionService.addConverter(String.class, SalaryType.class, new IdToObjectConverter<>(SalaryType.CODEBOOK));
        conversionService.addConverter(String.class, Gender.class, new NullFallbackLoadableConverter<>(Gender.CODEBOOK));
        conversionService.addConverter(String.class, Exekuce.class, new IdToObjectConverter<>(Exekuce.CODEBOOK));
        conversionService.addConverter(Integer.class, IdpCompensationType.class, new IdToObjectConverter<>(IdpCompensationType.CODEBOOK));
        conversionService.addConverter(Integer.class, MonthAttendanceState.class, new IdToObjectConverter<>(MonthAttendanceState.CODEBOOK));
        conversionService.addConverter(Integer.class, OnCallCompensationType.class, new IdToObjectConverter<>(OnCallCompensationType.CODEBOOK));
        conversionService.addConverter(Integer.class, OvertimeCompensationType.class, new IdToObjectConverter<>(OvertimeCompensationType.CODEBOOK));
        return conversionService;
    }

}
