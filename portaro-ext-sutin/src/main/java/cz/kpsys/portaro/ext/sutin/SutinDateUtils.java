package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SutinDateUtils {

    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull ZoneId zoneId;

    public @NonNull DatetimeRange validityRange(@Nullable LocalDate validFrom, @Nullable LocalDate validToInclusive) {
        var from = toInstant(validFrom);
        var exclusiveTo = validToInclusive == null
                ? null
                : toInstant(validToInclusive.plusDays(1));
        return DatetimeRange.of(from, exclusiveTo);
    }

    public @Nullable Instant toInstant(@Nullable LocalDate date) {
        if (date == null || isDateSkipped(date, 2030)) {
            return null;
        }
        return date.atStartOfDay(zoneId).toInstant();
    }

    public void setDay(@Nullable LocalDate localDate, @NonNull FieldTypeId subfieldTypeId, @NonNull RecordEditation recordEditation, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        if (localDate == null || isDateSkipped(localDate, 2200)) {
            return;
        }
        recordEditationHelper.setDateSubfieldValue(localDate, true, subfieldTypeId.existingParent(), true, subfieldTypeId, recordEditation, ctx, currentAuth);
    }

    public static boolean isDateSkipped(@NonNull LocalDate date, int yearSkipThreshold) {
        if (date.getYear() < 2000) {
            log.warn("Got strange date with year < 2000: {}", date);
        }
        if (date.getYear() > yearSkipThreshold) {
            log.info("Skipping date {} > year {}", date, yearSkipThreshold);
            return true;
        }
        return false;
    }

}
