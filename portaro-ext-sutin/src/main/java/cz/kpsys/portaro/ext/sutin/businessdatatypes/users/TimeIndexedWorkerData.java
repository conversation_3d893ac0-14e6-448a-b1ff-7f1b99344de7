package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.ImportedValue;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class TimeIndexedWorkerData {

    public static TimeIndexedWorkerData build(List<JobData> jobData, List<SalaryData> salaryData, PersonData personData) {
        if (salaryData.isEmpty()) {
            log.warn("Missing salary data for {}!", personData);
        }
        return new TimeIndexedWorkerData(
                DurationIndex.of(jobData.stream().map(DataWithRecord::new).toList()),
                DurationIndex.of(salaryData.stream().map(DataWithRecord::new).toList())
        );
    }


    @NonNull DurationIndex<DataWithRecord<JobData>> employmentStatusIndex;
    @NonNull DurationIndex<DataWithRecord<SalaryData>> salaryIndex;

    public @NonNull Collection<DataWithRecord<JobData>> getEmploymentStates() {
        return employmentStatusIndex.getAll();
    }

    public @NonNull Collection<DataWithRecord<SalaryData>> getSalaries() {
        return salaryIndex.getAll();
    }

    public @NonNull Optional<DataWithRecord<JobData>> findEmploymentStatusFor(@NonNull SalaryData salaryData) {
        var employmentStates = employmentStatusIndex.getValidThrough(salaryData);
        return switch (employmentStates.size()) {
            case 1 -> Optional.of(ListUtil.getFirst(employmentStates));
            case 0 -> {
                log.warn("Falling back to ID pairing because no employment status was found for salary {} in {}", salaryData, employmentStatusIndex);
                // Try pairing salary based on ID of employment status (adr_pracovnik_mzdy_zaz.zaznam_id = adr_pracovnik_kat.id)
                yield employmentStatusIndex.getAll().stream()
                        .filter(employmentState -> employmentState.getData().rowId().equals(salaryData.jobFileId()))
                        .findAny();
            }
            default -> {
                log.warn("Too many employment states found for {} in {}", salaryData, employmentStatusIndex);
                // Select active employment status or one with greatest row ID
                DataWithRecord<JobData> result = null;
                var it = employmentStates.iterator();
                for (var next = it.next(); it.hasNext(); next = it.next()) {
                    if (result == null || Integer.parseInt(next.getData().rowId()) > Integer.parseInt(result.getData().rowId())) {
                        result = next;
                    }
                    if (next.getData().isValid()) {
                        result = next;
                        break;
                    }
                }
                yield Optional.ofNullable(result);
            }
        };
    }

    public @NonNull List<DataWithRecord<SalaryData>> findSalariesFor(@NonNull MonthAttendance monthAttendance) {
        return salaryIndex.getValidThrough(monthAttendance.parseYearMonth());
    }

    public @NonNull ImportedValue<BigDecimal> findWeeklyHoursFor(@NonNull JobData jobData) {
        var salaries = salaryIndex.getValidThrough(jobData);
        if (salaries.isEmpty()) {
            log.warn("Falling back to ID pairing because no weekly hours based on validity time were found for job data {} in {}", jobData, salaryIndex);
            var idPairedSalaries = salaryIndex.getAll().stream()
                    .filter(salary -> salary.getData().jobFileId().equals(jobData.rowId()))
                    .toList();

            return ensureSameValues(jobData, idPairedSalaries, SalaryData::weeklyHours, "weekly hours");
        }

        return ensureSameValues(jobData, salaries, SalaryData::weeklyHours, "weekly hours");
    }

    private static <C extends TimeValidited, T> ImportedValue<T> ensureSameValues(Object queryObject, List<DataWithRecord<C>> salaries, Function<C, T> accessor, String valueName) {
        if (salaries.isEmpty()) {
            log.warn("No {} can be recovered for {} from {}", valueName, queryObject, salaries);
            return ImportedValue.Problems.of("No %s could be recovered.".formatted(valueName));
        }
        var data = accessor.apply(salaries.getFirst().getData());
        for (int i = 1; i < salaries.size(); i++) {
            var nextData = accessor.apply(salaries.get(i).getData());
            if (!data.equals(nextData)) {
                log.warn("Too many different {} found for {} in {}", valueName, queryObject, salaries);
                return ImportedValue.Problems.of("Too many different %s found. Chosen none.".formatted(valueName));
            }
        }
        return ImportedValue.Ok.of(data);
    }

    public @NonNull String findDivisionIdFor(@NonNull JobData jobData) {
        return jobData.divisionId();
    }

}
