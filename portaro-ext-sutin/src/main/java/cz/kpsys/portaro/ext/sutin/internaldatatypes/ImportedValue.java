package cz.kpsys.portaro.ext.sutin.internaldatatypes;

import lombok.NonNull;

import java.util.ArrayList;
import java.util.List;

public sealed interface ImportedValue<T> permits ImportedValue.Ok, ImportedValue.Problems {

    record Ok<T>(@NonNull T value) implements ImportedValue<T> {
        public static <T> ImportedValue<T> of(T value) {
            return new ImportedValue.Ok<>(value);
        }
    }

    record Problems<T>(List<String> problems) implements ImportedValue<T> {
        public static <T> ImportedValue<T> of(String problem) {
            var ret = new ArrayList<String>();
            ret.add(problem);
            return new Problems<>(ret);
        }

        public String collect() {
            return String.join("\n", problems);
        }
    }

}
