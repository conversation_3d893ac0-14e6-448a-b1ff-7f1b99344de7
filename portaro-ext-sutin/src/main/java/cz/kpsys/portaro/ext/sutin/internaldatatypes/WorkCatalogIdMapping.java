package cz.kpsys.portaro.ext.sutin.internaldatatypes;

public class WorkCatalogIdMapping {

    public record MappedWorkCatalogId(int fondId, String newId) {
        MappedWorkCatalogId(int fondId, int newId) {
            this(fondId, Integer.toString(newId));
        }
    }

    // input = row ID katalogu činností (skl_katalog_kat.id)
    public static MappedWorkCatalogId map(String workCatalogId) {
        int id = Integer.parseInt(workCatalogId);
        return switch (id) {
            case 500, 379, 209, 423 -> new MappedWorkCatalogId(34, 500);  // Brus na kulato do 1000 mm
            case 44, 550, 210, 94 -> new MappedWorkCatalogId(34, 550);  // Brus na kulato do 1500 mm
            case 501, 377, 211, 421, 380, 424 -> new MappedWorkCatalogId(34, 501);  // Brus na kulato do 2000 mm
            case 502, 551, 212 -> new MappedWorkCatalogId(34, 551);  // Brus na kulato do 2500 mm
            case 492, 378, 200, 422, 98 -> new MappedWorkCatalogId(34, 492);  // Brus na plocho do 800 mm
            case 508, 381, 220 -> new MappedWorkCatalogId(34, 508);  // Drátořez
            case 510, 382, 222 -> new MappedWorkCatalogId(34, 510);  // Elektroerozivní hloubička
            case 40, 92, 383, 343, 344, 198 -> new MappedWorkCatalogId(34, 383);  // Horizontální fréza WH10 - CNC
            case 43, 384, 345, 91 -> new MappedWorkCatalogId(34, 384);  // Horizontální fréza WHQ13 - CNC
            case 509, 385, 221 -> new MappedWorkCatalogId(34, 509);  // Laserové navařování
            case 526, 66, 412, 178, 177 -> new MappedWorkCatalogId(34, 526);  // LIEBHERR LTM 1055
            case 15, 64, 413, 179, 180, 139 -> new MappedWorkCatalogId(34, 413);  // LIEBHERR LTM 1070
            case 503, 386, 213, 81 -> new MappedWorkCatalogId(34, 503);  // Ohraňování do 400 t
            case 506, 552, 218 -> new MappedWorkCatalogId(34, 552);  // Pila
            case 507, 387, 219 -> new MappedWorkCatalogId(34, 507);  // Pila CNC
            case 41, 389, 352, 86 -> new MappedWorkCatalogId(34, 389);  // Řezání vodním paprskem
            case 16, 388, 215, 83 -> new MappedWorkCatalogId(34, 388);  // Rovnaní na lisu do 400 t
            case 31, 390, 193, 93 -> new MappedWorkCatalogId(34, 390);  // Soustruh do 2000 mm
            case 46, 391, 194, 95 -> new MappedWorkCatalogId(34, 391);  // Soustruh do 3000 mm
            case 47, 392, 195, 96 -> new MappedWorkCatalogId(34, 392);  // Soustruh do 4000 mm
            case 468, 549, 196 -> new MappedWorkCatalogId(34, 549);  // Soustruh do 5000 mm
            case 469, 393, 197 -> new MappedWorkCatalogId(34, 469);  // Soustruh do 6000 mm
            case 504, 394, 214, 99 -> new MappedWorkCatalogId(34, 504);  // Stříhání do 13 mm
            case 45, 395, 350, 203, 90 -> new MappedWorkCatalogId(34, 395);  // Vertikální fréza CNC do 1250 mm
            case 494, 396, 349, 202 -> new MappedWorkCatalogId(34, 494);  // Vertikální fréza CNC do 750 mm
            case 493, 397, 348 -> new MappedWorkCatalogId(34, 493);  // Vertikální fréza do 1500 mm
            case 505, 398, 217 -> new MappedWorkCatalogId(34, 505);  // Vrtání CNC
            case 495, 400, 204 -> new MappedWorkCatalogId(34, 495);  // Zakružování plechů do 10 mm
            case 496, 401, 205 -> new MappedWorkCatalogId(34, 496);  // Zakružování plechů do 20 mm
            case 497, 402, 206 -> new MappedWorkCatalogId(34, 497);  // Zakružování plechů do 30 mm
            case 498, 403, 207 -> new MappedWorkCatalogId(34, 498);  // Zakružování plechů do 40 mm
            case 499, 404, 208 -> new MappedWorkCatalogId(34, 499);  // Zakružování profilů
            case 347 -> new MappedWorkCatalogId(34, 347);  // Vertikální fréza do 1000 mm
            case 346 -> new MappedWorkCatalogId(34, 346);  // Vertikální fréza do 500 mm
            case 372 -> new MappedWorkCatalogId(34, 372);  // Vytahovák svazků s obsluhou
            case 353 -> new MappedWorkCatalogId(34, 353);  // Vrtání
            case 351 -> new MappedWorkCatalogId(34, 351);  // Brus na kulato do 500 mm
            case 359 -> new MappedWorkCatalogId(34, 359);  // Hytorc
            case 342, 138 -> new MappedWorkCatalogId(34, 342);  // Soustruh do 1000 mm
            case 466 -> new MappedWorkCatalogId(34, 466);  // Elektroerozivní vyvrtávání
            case 82 -> new MappedWorkCatalogId(34, 82);  // Zakružování plechů
            case 581 -> new MappedWorkCatalogId(34, 581);  // TA 2025 POLYOLEFINY Obráběcí práce I
            case 583 -> new MappedWorkCatalogId(34, 583);  // TA 2025 POLYOLEFINY Obráběcí práce II
            case 584 -> new MappedWorkCatalogId(34, 584);  // TA 2025 POLYOLEFINY Obráběcí práce III

            case 485, 252 -> new MappedWorkCatalogId(16, 485);  // Autorizovaný inženýr
            case 484, 251 -> new MappedWorkCatalogId(16, 484);  // CAD designer
            case 37, 241 -> new MappedWorkCatalogId(16, 241);  // Dělník elektro
            case 8, 236 -> new MappedWorkCatalogId(16, 236);  // Elektrikář NN
            case 477, 237 -> new MappedWorkCatalogId(16, 477);  // Elektrikář VN
            case 38, 112 -> new MappedWorkCatalogId(16, 112);  // Izolatér
            case 29, 113 -> new MappedWorkCatalogId(16, 113);  // Klempíř
            case 483, 250 -> new MappedWorkCatalogId(16, 483);  // Konstruktér Junior
            case 482, 249, 100 -> new MappedWorkCatalogId(16, 482);  // Konstruktér Senior
            case 480, 245 -> new MappedWorkCatalogId(16, 480);  // Kopač
            case 515, 568 -> new MappedWorkCatalogId(16, 568);  // Korozní inspektor
            case 514, 567 -> new MappedWorkCatalogId(16, 567);  // Korozní technik
            case 13, 117 -> new MappedWorkCatalogId(16, 117);  // Lešenář
            case 122 -> new MappedWorkCatalogId(16, 122);  // Lešenář parťák
            case 512, 326 -> new MappedWorkCatalogId(16, 512);  // Lešenář – příplatek za IDP
            case 21, 119 -> new MappedWorkCatalogId(16, 119);  // Manipulační dělník
            case 14, 101 -> new MappedWorkCatalogId(16, 101);  // Mechanik rotačních strojů
            case 488, 263 -> new MappedWorkCatalogId(16, 488);  // Měřič geodezie
            case 475, 233 -> new MappedWorkCatalogId(16, 475);  // Montážník kritické přírubové spoje
            case 470, 227 -> new MappedWorkCatalogId(16, 470);  // Montážník vyhrazené plynové zařízení
            case 19, 235, 73 -> new MappedWorkCatalogId(16, 235);  // Montér potrubář
            case 26, 105, 275 -> new MappedWorkCatalogId(16, 105);  // Natěrač
            case 511, 325 -> new MappedWorkCatalogId(16, 511);  // Nezávislý kontrolor
            case 478, 243 -> new MappedWorkCatalogId(16, 478);  // Obkladač
            case 473, 231 -> new MappedWorkCatalogId(16, 473);  // Obsluha – strojní egalizace mobilní
            case 22, 229 -> new MappedWorkCatalogId(16, 229);  // Obsluha – strojní úkosování mobilní
            case 472, 230 -> new MappedWorkCatalogId(16, 472);  // Obsluha – strojní utahování mobilní
            case 474, 232 -> new MappedWorkCatalogId(16, 474);  // Obsluha – žíhací zařízení mobilní
            case 490, 269 -> new MappedWorkCatalogId(16, 490);  // Palič
            case 23, 240 -> new MappedWorkCatalogId(16, 240);  // PLC programátor
            case 24, 239 -> new MappedWorkCatalogId(16, 239);  // PLC údržba
            case 36, 355, 246 -> new MappedWorkCatalogId(16, 355);  // Pokrývač
            case 517, 273 -> new MappedWorkCatalogId(16, 517);  // Použití IDP nad rámec HZS
            case 491, 270 -> new MappedWorkCatalogId(16, 491);  // Požární hlídka
            case 516, 465 -> new MappedWorkCatalogId(16, 516);  // Práce s horolezeckou výbavou
            case 20, 238 -> new MappedWorkCatalogId(16, 238);  // Pracovník MaR
            case 518, 274 -> new MappedWorkCatalogId(16, 518);  // Pracovník v ČVV nad rámec HZS
            case 12, 226, 108 -> new MappedWorkCatalogId(16, 226);  // Předák (parťák)
            case 17, 260 -> new MappedWorkCatalogId(16, 260);  // Programátor CNC
            case 28, 248 -> new MappedWorkCatalogId(16, 248);  // Projektant Junior
            case 481, 247 -> new MappedWorkCatalogId(16, 481);  // Projektant Senior
            case 11, 257 -> new MappedWorkCatalogId(16, 257);  // Projektový manažer
            case 18, 265 -> new MappedWorkCatalogId(16, 265);  // Referent fakturace
            case 30, 268 -> new MappedWorkCatalogId(16, 268);  // Referent nákupu
            case 6, 267 -> new MappedWorkCatalogId(16, 267);  // Referent skladu
            case 4, 124 -> new MappedWorkCatalogId(16, 124);  // Revizní technik
            case 486, 253 -> new MappedWorkCatalogId(16, 486);  // Statik
            case 479, 244 -> new MappedWorkCatalogId(16, 479);  // Stavební dělník
            case 7, 223, 123, 585 -> new MappedWorkCatalogId(16, 223);  // Svářeč – tř. oceli 11 a 12
            case 32, 224, 136, 586 -> new MappedWorkCatalogId(16, 224);  // Svářeč – tř. oceli 15 a 17
            case 42, 254 -> new MappedWorkCatalogId(16, 254);  // Svářeč inženýr IWE
            case 27, 255 -> new MappedWorkCatalogId(16, 255);  // Svářeč technolog EWT
            case 25, 266 -> new MappedWorkCatalogId(16, 266);  // Technický referent
            case 35, 110 -> new MappedWorkCatalogId(16, 110);  // Technik BOZP
            case 513, 327 -> new MappedWorkCatalogId(16, 513);  // Technik lešení
            case 3, 256, 85, 588, 582 -> new MappedWorkCatalogId(16, 256);  // Technik OTK
            case 34, 261 -> new MappedWorkCatalogId(16, 261);  // Technik OZO BOZP
            case 5, 258 -> new MappedWorkCatalogId(16, 258);  // Technik realizace
            case 487, 262 -> new MappedWorkCatalogId(16, 487);  // Technik žíhání
            case 33, 106 -> new MappedWorkCatalogId(16, 106);  // Tryskač
            case 10, 225 -> new MappedWorkCatalogId(16, 225);  // Vedoucí pracovní skupiny
            case 476, 234 -> new MappedWorkCatalogId(16, 476);  // Vibrodiagnostik
            case 489, 264 -> new MappedWorkCatalogId(16, 489);  // Vykazování SAP
            case 2, 71, 587 -> new MappedWorkCatalogId(16, 71);  // Zámečník
            case 9, 242 -> new MappedWorkCatalogId(16, 242);  // Zedník
            case 339, 356 -> new MappedWorkCatalogId(16, 356);  // Technik zařízení budov
            case 376 -> new MappedWorkCatalogId(16, 376);  // Externista
            case 521, 128 -> new MappedWorkCatalogId(16, 521);  // Manipulátor
            case 109, 115, 116 -> new MappedWorkCatalogId(16, 109);  // Referent
            case 39, 259 -> new MappedWorkCatalogId(16, 259);  // Technolog obrábění
            case 89, 77, 69 -> new MappedWorkCatalogId(16, 77);  // Administrativa
            case 121, 80 -> new MappedWorkCatalogId(16, 80);  // Skladník
            case 519 -> new MappedWorkCatalogId(16, 519);  // Provoz tryskacího boxu
            case 580 -> new MappedWorkCatalogId(16, 580);  // Pohotovost
            case 335 -> new MappedWorkCatalogId(16, 335);  // Autorizovaný inženýr geodézie
            case 336 -> new MappedWorkCatalogId(16, 336);  // Autorizovaný inženýr pozemních staveb
            case 130 -> new MappedWorkCatalogId(16, 130);  // Bezpečnostní technik
            case 131 -> new MappedWorkCatalogId(16, 131);  // Elektrikář
            case 332 -> new MappedWorkCatalogId(16, 332);  // ITI
            case 111 -> new MappedWorkCatalogId(16, 111);  // Izolatér technik
            case 228, 471 -> new MappedWorkCatalogId(16, 471);  // Jeřábník, vazač
            case 340 -> new MappedWorkCatalogId(16, 340);  // Kreslič
            case 120 -> new MappedWorkCatalogId(16, 120);  // Lešenář technik
            case 278 -> new MappedWorkCatalogId(16, 278);  // Metalizace Zn100
            case 360 -> new MappedWorkCatalogId(16, 360);  // Obsluha mechanizace
            case 330 -> new MappedWorkCatalogId(16, 330);  // Pracovník směnové údržby elektro
            case 331 -> new MappedWorkCatalogId(16, 331);  // Pracovník směnové údržby MaR
            case 333 -> new MappedWorkCatalogId(16, 333);  // Projektant
            case 282 -> new MappedWorkCatalogId(16, 282);  // Souprava mobilního tryskacího zařízení
            case 341 -> new MappedWorkCatalogId(16, 341);  // Statik ocelových konstrukcí
            case 134 -> new MappedWorkCatalogId(16, 134);  // Stavbyvedoucí
            case 334 -> new MappedWorkCatalogId(16, 334);  // Stavební inženýr
            case 328 -> new MappedWorkCatalogId(16, 328);  // Svářeč – autogen
            case 132 -> new MappedWorkCatalogId(16, 132);  // Technik elektro
            case 338 -> new MappedWorkCatalogId(16, 338);  // Technik geodezie
            case 337 -> new MappedWorkCatalogId(16, 337);  // Technik hydrauliky
            case 107 -> new MappedWorkCatalogId(16, 107);  // Technik povrchová úprava
            case 84 -> new MappedWorkCatalogId(16, 84);  // Technik strojní
            case 329 -> new MappedWorkCatalogId(16, 329);  // Žíhač
            case 97 -> new MappedWorkCatalogId(16, 97);  // CNC nástrojář
            case 75 -> new MappedWorkCatalogId(16, 75);  // Brigádník
            case 76, 118 -> new MappedWorkCatalogId(16, 76);  // Mistr
            case 87 -> new MappedWorkCatalogId(16, 87);  // Pomocné práce
            case 79 -> new MappedWorkCatalogId(16, 79);  // Řidič
            case 104 -> new MappedWorkCatalogId(16, 104);  // Soustružník
            case 72 -> new MappedWorkCatalogId(16, 72);  // Svářeč
            case 70 -> new MappedWorkCatalogId(16, 70);  // Svářecí dozor
            case 103 -> new MappedWorkCatalogId(16, 103);  // Technik
            case 88 -> new MappedWorkCatalogId(16, 88);  // Technolog
            case 74 -> new MappedWorkCatalogId(16, 74);  // THP
            case 78 -> new MappedWorkCatalogId(16, 78);  // Uklízečka
            case 68, 102 -> new MappedWorkCatalogId(16, 68);  // Vedoucí divize
            case 129 -> new MappedWorkCatalogId(16, 129);  // Zámečník v IDP
            case 589 -> new MappedWorkCatalogId(16, 589);  // TA 2025 – Vypracování WPS, protokol

            case 528, 147, 146, 559 -> new MappedWorkCatalogId(17, 559);  // Autoplošina
            case 536, 170 -> new MappedWorkCatalogId(17, 536);  // Čerpadlo
            case 410, 65, 523, 150, 151 -> new MappedWorkCatalogId(17, 523);  // Dodávka - valník
            case 543, 184, 183, 561 -> new MappedWorkCatalogId(17, 561);  // Doprovodné vozidlo
            case 405, 176 -> new MappedWorkCatalogId(17, 405);  // Elektrocentrála bez obsluhy a PHM
            case 530, 560, 160, 159 -> new MappedWorkCatalogId(17, 560);  // Fekální vůz
            case 539, 375 -> new MappedWorkCatalogId(17, 539);  // Hytorc bez obsluhy
            case 562, 544, 186, 185 -> new MappedWorkCatalogId(17, 562);  // Jeřáb do 100 t
            case 188, 187, 563, 545 -> new MappedWorkCatalogId(17, 563);  // Jeřáb do 120 t
            case 190, 189, 564, 546 -> new MappedWorkCatalogId(17, 564);  // Jeřáb do 140 t
            case 191, 547, 565 -> new MappedWorkCatalogId(17, 565);  // Jeřáb do 200 t
            case 192, 548, 566 -> new MappedWorkCatalogId(17, 566);  // Jeřáb do 350 t
            case 406, 374 -> new MappedWorkCatalogId(17, 406);  // Kloubová plošina
            case 531, 163 -> new MappedWorkCatalogId(17, 531);  // Kolové nebo pásové rýpadlo do 10 t
            case 532, 164 -> new MappedWorkCatalogId(17, 532);  // Kolové nebo pásové rýpadlo do 16 t
            case 451, 174 -> new MappedWorkCatalogId(17, 451);  // Kompresor bez obsluhy a PHM
            case 529, 158 -> new MappedWorkCatalogId(17, 529);  // Kontejner na suť
            case 61, 414, 142, 143 -> new MappedWorkCatalogId(17, 414);  // Manipulátor
            case 557, 571 -> new MappedWorkCatalogId(17, 571);  // Mytí povrchu tlakovou vodou do 230 bar
            case 415, 63, 525, 155, 154 -> new MappedWorkCatalogId(17, 525);  // Nákladní auto - hydraulická ruka, tahač
            case 460, 67, 157, 156 -> new MappedWorkCatalogId(17, 460);  // Nákladní auto nad 10 t
            case 541, 181 -> new MappedWorkCatalogId(17, 541);  // Nástavec LTM 1055
            case 542, 182 -> new MappedWorkCatalogId(17, 542);  // Nástavec LTM 1070
            case 527, 144 -> new MappedWorkCatalogId(17, 527);  // Nůžková plošina
            case 58, 522, 417, 149, 361, 148 -> new MappedWorkCatalogId(17, 522);  // Osobní automobil / dodávka
            case 538, 172 -> new MappedWorkCatalogId(17, 538);  // Pažicí box ocelový 3x2 m
            case 418, 59, 162, 161, 357 -> new MappedWorkCatalogId(17, 418);  // Podval 8,5 m + 5,5 m + 3,5 m
            case 553, 578 -> new MappedWorkCatalogId(17, 578);  // Provoz tryskacího boxu
            case 537, 171 -> new MappedWorkCatalogId(17, 537);  // Řezačka
            case 452, 167 -> new MappedWorkCatalogId(17, 452);  // Smykový nakladač
            case 554, 576 -> new MappedWorkCatalogId(17, 576);  // Souprava mobilního tryskacího zařízení
            case 419, 57, 152, 153, 524 -> new MappedWorkCatalogId(17, 524);  // Traktor + valník, nosnost 8 t
            case 409, 165 -> new MappedWorkCatalogId(17, 409);  // Traktorbagr
            case 533, 166 -> new MappedWorkCatalogId(17, 533);  // Traktorbagr s pneukladivem
            case 535, 169 -> new MappedWorkCatalogId(17, 535);  // Vibrační deska
            case 534, 168 -> new MappedWorkCatalogId(17, 534);  // Vibrační válec
            case 555, 569 -> new MappedWorkCatalogId(17, 569);  // Vysokotlaké stříkací zařízení do 230 bar
            case 62, 420, 140, 141, 520 -> new MappedWorkCatalogId(17, 520);  // Vysokozdvižný vozík, nosnost 5 t
            case 556, 570 -> new MappedWorkCatalogId(17, 570);  // Vzduchové stříkací zařízení s kompresorem
            case 365, 364, 454, 60, 411, 126, 137 -> new MappedWorkCatalogId(17, 365);  // Dodávka valník nosnost 1,1 t
            case 367, 366 -> new MappedWorkCatalogId(17, 367);  // Dodávka valník nosnost 2,2 t
            case 370, 371, 125 -> new MappedWorkCatalogId(17, 371);  // Nákladní auto hydraulická ruka nosnost 12,5 t
            case 368, 369, 135, 127 -> new MappedWorkCatalogId(17, 369);  // Traktor + valník, nosnost 12 t
            case 540, 175 -> new MappedWorkCatalogId(17, 540);  // Žíhací zařízení bez obsluhy
            case 558 -> new MappedWorkCatalogId(17, 558);  // Očištění povrchu ručně mechanizované
            case 354 -> new MappedWorkCatalogId(17, 354);  // Dělení materiálu CNC
            case 173 -> new MappedWorkCatalogId(17, 173);  // Hytorc včetně protokolu
            case 145 -> new MappedWorkCatalogId(17, 145);  // Kloubová plošina 15 m
            case 358 -> new MappedWorkCatalogId(17, 358);  // Nůžková plošina 17 m
            case 272 -> new MappedWorkCatalogId(17, 272);  // Lahev 1 ks
            case 373 -> new MappedWorkCatalogId(17, 373);  // Nakladač - rýpadlo
            case 577 -> new MappedWorkCatalogId(17, 577);  // Očištění povrchu Bristle Blasting
            case 362 -> new MappedWorkCatalogId(17, 362);  // Přístroj 1 ks / 1 osoba

            default -> throw new IllegalArgumentException("Unknown work catalog id: " + workCatalogId);
        };
    }

}
