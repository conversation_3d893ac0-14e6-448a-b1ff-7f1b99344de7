package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum MonthAttendanceState implements Identified<Integer> {

    IN_PROGRESS(1, "inprogress"),
    TO_CHECK(2, "tocheck"),
    RETURNED(3, "returned"),
    CHECKED(4, "checked"),
    CLOSED(5, "closed"),
    RETURNED_CHECKED(6, "returned_checked"),
    TOCHECK_FIXED(7, "tocheck_fixed"),
    CHECKED_FIXED(8, "checked_fixed");

    public static final Codebook<MonthAttendanceState, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;

    @Getter
    @NonNull String portaroVal;

    @Override
    public Integer getId() {
        return sutorId;
    }

}
