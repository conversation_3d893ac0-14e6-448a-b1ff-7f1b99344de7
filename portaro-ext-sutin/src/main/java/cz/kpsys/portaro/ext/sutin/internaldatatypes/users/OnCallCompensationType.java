package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum OnCallCompensationType implements Identified<Integer> {

    NOTHING(1, "nothing"),
    D(2, "d"),
    FA(3, "fa"),
    OF(4, "of");

    public static final Codebook<OnCallCompensationType, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;

    @Getter
    @NonNull String portaroVal;

    @Override
    public Integer getId() {
        return sutorId;
    }

}
