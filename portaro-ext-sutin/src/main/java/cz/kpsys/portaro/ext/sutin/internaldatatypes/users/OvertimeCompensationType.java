package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum OvertimeCompensationType implements Identified<Integer> {

    NOTHING(1, "nothing"),
    D(2, "d"),
    OF(3, "of"),
    OF_IN_BONUS(4, "of_in_bonus");

    public static final Codebook<OvertimeCompensationType, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;

    @Getter
    @NonNull String portaroVal;

    @Override
    public Integer getId() {
        return sutorId;
    }

}
