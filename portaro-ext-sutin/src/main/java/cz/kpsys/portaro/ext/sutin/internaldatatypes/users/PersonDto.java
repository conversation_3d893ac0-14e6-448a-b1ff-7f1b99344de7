package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;

public record PersonDto(

        @NonNull String rowId, // Row ID

        @NonNull String elementId,

        String firstName,

        String lastName,

        String firstName2,

        String lastName2,

        String degreeBefore,

        String degreeAfter,

        LocalDate birthDate,

        String birthPlace,

        String personalIdentificationNumber,

        PracStatniPrislusnostKat nationality,

        PracVzdelaniKat educationLevel,

        String educationInstitution,

        Gender gender,

        String extension,

        String pohodaId,


        String personalNumber,

        // v sutinu pouze hodnoty Null nebo 1
        Integer professionId,

        LocalDate jobBeginDate,

        LocalDate testTime,

        LocalDate jobEndDate,

        @Nullable PracSmlouvaTypKat contractType,

        Integer workPositionId,

        @Nullable String superiorPersonId,

        @Nullable PracKategorieKat workCategory,

        PracVypovedStranaKat employmentTerminationSide,

        PracVypovedTypKat employmentTerminationType,

        String employmentTerminationReason,

        String identityCardNumber,

        LocalDate identityCardValidity,

        @Nullable PracMistoPraceKat jobPlace,

        String employerId,

        LocalDate contractDate,

        String vat,

        Integer employmentOrder,

        String identificationCardNumber,

        @Nullable LocalDate jobFileValidFrom,

        LocalDate jobFileValidTo,

        String divisionId,

        PracZdravPojistovnaKat healthInsuranceCode,

        PracInvaliditaKat handicap,

        LocalDate maternityBeginDate,

        LocalDate maternityEndDate,

        LocalDate parentalBeginDate,

        LocalDate parentalEndDate,

        Integer claimedChildren,

        Integer unclaimedChildren,

        Exekuce foreclosure,

        LocalDate workPlacementEndDate,


        Character valid

) {

    public boolean isValid() {
        return valid != null && valid.equals('A');
    }

}
