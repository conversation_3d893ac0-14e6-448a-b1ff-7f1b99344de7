package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.ext.sutin.ImportIgnorable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum PracInvaliditaKat implements Identified<Integer>, ImportIgnorable {

    NOT_DEFINED(1, "---", false),
    EMPTY(2, "", false),
    PRVNI_STUPEN(3, "i. stupeň", true),
    DRUHY_STUPEN(4, "ii. stupeň", true),
    NE(5, "ne", true),
    DUCHODCE(6, "důchodce", true),
    ZPT(7, "ztp", true),
    TRETI_STUPEN(8, "iii. stupeň", true);

    public static final Codebook<PracInvaliditaKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;
    @NonNull String value;
    @Getter boolean imported;

    public @NonNull Integer getId() {
        return sutorId;
    }

    public @NonNull String portaroVal() {
        return sutorId.toString();
    }

}
