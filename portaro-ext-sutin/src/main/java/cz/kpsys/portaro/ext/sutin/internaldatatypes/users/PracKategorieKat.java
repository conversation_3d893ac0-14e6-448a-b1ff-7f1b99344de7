package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.ext.sutin.ImportIgnorable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum PracKategorieKat implements Identified<Integer>, ImportIgnorable {

    NOT_DEFINED(1, "---", false),
    EMPTY(4, "", false),
    D(5, "d", true),
    T(6, "t", true),
    EXT_D(7, "ext. - d", true),
    EXT_T(8, "ext. - t", true),
    BOZP(9, "vedoucí oddělení bozp", true);

    public static final Codebook<PracKategorieKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;
    @NonNull String value;
    @Getter boolean imported;

    public @NonNull Integer getId() {
        return sutorId;
    }

    public @NonNull String portaroVal() {
        return sutorId.toString();
    }

}
