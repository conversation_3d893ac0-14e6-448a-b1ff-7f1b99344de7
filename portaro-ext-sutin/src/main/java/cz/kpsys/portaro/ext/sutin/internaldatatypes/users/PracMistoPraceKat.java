package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.ext.sutin.ImportIgnorable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum PracMistoPraceKat implements Identified<Integer>, ImportIgnorable {

    EMPTY(1, "---", false),
    EMPTY4(4, "#ODKAZ!", false),
    EMPTY5(5, "#NENÍ_K_DISPOZICI!", false),
    CHEMICKA(6, "chemička", true),
    KOPISTY(7, "kopisty", true),
    EMPTY8(8, "", false),
    RUZODOL(9, "růžodol", true),
    EMPTY10(10, "00.01.1900", false),
    ZALUZI(11, "záluží", true);

    public static final Codebook<PracMistoPraceKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;
    @NonNull String value;
    @Getter boolean imported;

    public @NonNull Integer getId() {
        return sutorId;
    }

    public @NonNull String portaroVal() {
        return sutorId.toString();
    }

}
