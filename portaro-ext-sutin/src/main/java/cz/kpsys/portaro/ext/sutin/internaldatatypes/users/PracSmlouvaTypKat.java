package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.ext.sutin.ImportIgnorable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum PracSmlouvaTypKat implements Identified<Integer>, ImportIgnorable {

    NOT_DEFINED(1, "---", false),
    HPP_NEURCITA(3, "hpp neurčitá", true),
    HPP_URCITA(4, "hpp určitá", true),
    ZIVNOST(5, "živnost", true),
    SUBDODAVATEL(6, "subdodavatel", true),
    TMA(7, "tma", true),
    DPP(8, "dpp", true),
    DPC(9, "dpč", true),
    PRAXE(10, "praxe", true);

    public static final Codebook<PracSmlouvaTypKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;
    @NonNull String value;
    @Getter boolean imported;

    public @NonNull Integer getId() {
        return sutorId;
    }

    public @NonNull String portaroVal() {
        return sutorId.toString();
    }

}
