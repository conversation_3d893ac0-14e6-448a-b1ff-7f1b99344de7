package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum PracStatniPrislusnostKat implements Identified<Integer> {

    NOT_DEFINED(1, "---"),
    CZECH(2, "Česk<PERSON>"),
    SLOVAK(3, "Slovenská"),
    MOLDAVIAN(4, "Moldavsk<PERSON>"),
    POLISH(5, "Polská"),
    UKRAINE(6, "<PERSON>krajinsk<PERSON>"),
    BULGARIAN(7, "<PERSON><PERSON>harsk<PERSON>"),
    EMPTY(8, "Nevyplněno"),
    ROMANE(9, "Rum<PERSON>k<PERSON>"),
    RUSSIAN(10, "Rusk<PERSON>"),
    HUNGARIAN(11, "<PERSON><PERSON>arsk<PERSON>"),
    SERBIAN(12, "Srbská"),
    MACEDONIAN(13, "Makedonská"),
    VIETNAMESE(14, "Vietnamská"),
    LITHUANIAN(15, "Litevská"),
    INDIAN(16, "Indická"),
    PHILIPPINE(17, "Filipíny"),
    UZBEK(18, "Uzbecká"),
    GEORGIAN(19, "Gruzijská");

    public static final Codebook<PracStatniPrislusnostKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer id;
    @NonNull String value;

    public @NonNull String getPortaroVal() {
        return id.toString();
    }

}
