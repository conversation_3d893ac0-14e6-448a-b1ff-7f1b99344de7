package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.ext.sutin.ImportIgnorable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum PracStatniPrislusnostKat implements Identified<Integer>, ImportIgnorable {

    NOT_DEFINED(1, "---", false),
    CZECH(2, "česká", true),
    SLOVAK(3, "slovenská", true),
    MOLDAVIAN(4, "moldavská", true),
    POLISH(5, "polská", true),
    UKRAINE(6, "ukrajinská", true),
    BULGARIAN(7, "bulharská", true),
    EMPTY(8, "", false),
    ROMANE(9, "rumunská", true),
    RUSSIAN(10, "ruská", true),
    HUNGARIAN(11, "maďarská", true),
    SERBIAN(12, "srbská", true),
    MACEDONIAN(13, "makedonská", true),
    VIETNAMESE(14, "vietnamská", true),
    LITHUANIAN(15, "litevská", true),
    INDIAN(16, "indická", true),
    PHILIPPINE(17, "filipíny", true),
    UZBEK(18, "uzbecká", true),
    GEORGIAN(19, "gruzijská", true);

    public static final Codebook<PracStatniPrislusnostKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer id;
    @NonNull String value;
    @Getter boolean imported;

    public @NonNull String getPortaroVal() {
        return id.toString();
    }

}
