package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.ext.sutin.ImportIgnorable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum PracVypovedStranaKat implements Identified<Integer>, ImportIgnorable {

    EMPTY1(1, "---", false),
    EMPTY3(3, "", false),
    EMPLOYEE(4, "zaměstnanec", true),
    EMPLOYER(5, "zaměstnavatel", true);

    public static final Codebook<PracVypovedStranaKat, Integer> CODEBOOK = new StaticCodebook<>(values()) {
        @Override
        public Optional<PracVypovedStranaKat> findById(@NonNull Integer integer) {
            if (integer == 6) {
                return Optional.of(EMPLOYEE);
            }
            return super.findById(integer);
        }
    };

    @NonNull Integer sutorId;
    @NonNull String value;
    @Getter boolean imported;

    @Override
    public Integer getId() {
        return sutorId;
    }

    public @NonNull String getPortaroVal() {
        return sutorId.toString();
    }

}
