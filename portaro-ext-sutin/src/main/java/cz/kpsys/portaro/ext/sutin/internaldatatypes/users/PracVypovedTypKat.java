package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.ext.sutin.ImportIgnorable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum PracVypovedTypKat implements Identified<Integer>, ImportIgnorable {

    EMPTY1(1, "---", false),
    EMPTY3(3, "", false),
    DOHODA(4, "dohoda", true),
    DOBA_URCITA(5, "doba určitá", true),
    ZKUSEBKA(6, "ve zku<PERSON><PERSON><PERSON><PERSON> době", true),
    VYPOVED(7, "výpověď", true),
    ZRUSENI_SMLOUVY(8, "zrušení smlouvy", true);

    public static final Codebook<PracVypovedTypKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;
    @NonNull String value;
    @Getter boolean imported;

    @Override
    public Integer getId() {
        return sutorId;
    }

    public @NonNull String getPortaroVal() {
        return sutorId.toString();
    }

}
