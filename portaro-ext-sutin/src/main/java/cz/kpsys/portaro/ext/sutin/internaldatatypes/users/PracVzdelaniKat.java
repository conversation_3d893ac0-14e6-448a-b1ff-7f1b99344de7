package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.ext.sutin.ImportIgnorable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum PracVzdelaniKat implements Identified<Integer>, ImportIgnorable {

    NOT_DEFINED(1, "---", false),
    VYUCEN(3, "vyučen", true),
    SS(4, "střední škola", true),
    VS(5, "vysoká škola", true),
    ZAK(6, "základn<PERSON>", true),
    EMPTY(7, "", false),
    VOS(8, "vy<PERSON><PERSON><PERSON> od<PERSON> škola", true),
    VYUCEN_S_MAT(9, "vyučen s maturitou", true);

    public static final Codebook<PracVzdelaniKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer id;
    @NonNull String value;
    @Getter boolean imported;

    public @NonNull String getPortaroVal() {
        return id.toString();
    }

}
