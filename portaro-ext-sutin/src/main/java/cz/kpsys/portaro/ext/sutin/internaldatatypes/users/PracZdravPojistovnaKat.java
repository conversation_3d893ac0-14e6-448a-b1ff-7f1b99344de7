package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.ext.sutin.ImportIgnorable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum PracZdravPojistovnaKat implements Identified<Integer>, ImportIgnorable {

    NOT_DEFINED(1, "---", false),
    EMPTY(11, "", false),
    VZP(12, "vzp", true),
    CPZP(13, "čpzp", true),
    ZPMV(14, "zp mv čr", true),
    OZP(15, "ozp", true),
    VOZP(16, "vozp", true),
    RBP(17, "rbp", true),
    PMV(18, "pmv", true);

    public static final Codebook<PracZdravPojistovnaKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;
    @NonNull String value;
    @Getter boolean imported;

    public @NonNull Integer getId() {
        return sutorId;
    }

    public @NonNull String portaroVal() {
        return sutorId.toString();
    }

}
