package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import lombok.NonNull;

import java.time.LocalDate;

public record PracovnikCoreData(

        @NonNull String elementId,

        String firstName,

        String lastName,

        String firstName2,

        String lastName2,

        String degreeBefore,

        String degreeAfter,

        LocalDate birthDate,

        String birthPlace,

        String personalIdentificationNumber,

        PracStatniPrislusnostKat nationality,

        PracVzdelaniKat educationLevel,

        String educationInstitution,

        Gender gender,

        String extension

) {

    public static PracovnikCoreData fromPracovnik(PersonDto personDto) {
        return new PracovnikCoreData(
                personDto.elementId(),
                personDto.firstName(),
                personDto.lastName(),
                personDto.firstName2(),
                personDto.lastName2(),
                personDto.degreeBefore(),
                personDto.degreeAfter(),
                personDto.birthDate(),
                personDto.birthPlace(),
                personDto.personalIdentificationNumber(),
                personDto.nationality(),
                personDto.educationLevel(),
                personDto.educationInstitution(),
                personDto.gender(),
                personDto.extension()
        );
    }

}
