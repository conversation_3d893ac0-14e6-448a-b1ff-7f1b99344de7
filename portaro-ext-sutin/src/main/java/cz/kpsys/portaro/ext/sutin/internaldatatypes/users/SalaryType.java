package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum SalaryType implements Identified<String> {

    HOURLY("H", "hour"),
    MONTHLY("M", "month");

    public static final Codebook<SalaryType, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String sutorId;
    @Getter
    @NonNull String portaroVal;

    @Override
    public String getId() {
        return sutorId;
    }

}
