package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;

public record WorkCatalogLinkDto(

        @NonNull String rowId,

        @NonNull String elementId,

        @NonNull String katalogId,

        @Nullable LocalDate validFrom,

        @Nullable LocalDate validTo,

        @NonNull Typ type,

        @Nullable String divisionId

) {

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public enum Typ implements Identified<String> {

        COST("K", "cost"),
        SELL("P", "sell");

        public static final Codebook<Typ, String> CODEBOOK = new StaticCodebook<>(values());

        @NonNull String sutorId;
        @NonNull String portaroVal;

        @Override
        public String getId() {
            return sutorId;
        }

        public @NonNull String portaroVal() {
            return portaroVal;
        }
    }

}
