package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondJobFile.AdditionalData;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondJobFile.JobFile;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondJobFile.JobFileValidity;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondJobFile.PersonalData;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.PersonLink;
import cz.kpsys.portaro.ext.sutin.SutinDateUtils;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.JobData;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.RowId;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class JobFileRecordEditationFiller {

    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull SutinDateUtils sutinDateUtils;

    public RecordEditation fill(@NonNull JobData jobData,
                                @NonNull RecordEditation recordEditation,
                                @NonNull Record userRecord,
                                @NonNull Department ctx,
                                @NonNull UserAuthentication currentAuth) {

        recordEditationHelper.setStringTopFieldValue(jobData.rowId(), RowId.TYPE_ID, recordEditation, ctx, currentAuth);

        DetailedRecordValueCommand userRecordLinkCmd = new DetailedRecordValueCommand(userRecord, ctx, currentAuth);
        recordEditationHelper.setRecordIdSubfieldValue(userRecordLinkCmd, true, PersonLink.TYPE_ID, true, PersonLink.Main.TYPE_ID, recordEditation);


        // PersonalData
        if (jobData.birthDate() != null) {
            recordEditationHelper.setDateSubfieldValue(jobData.birthDate(), true, PersonalData.TYPE_ID, true, PersonalData.BirthDate.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.personalIdentificationNumber() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.personalIdentificationNumber(), true, PersonalData.TYPE_ID, true, PersonalData.PersonalIdentificationNumber.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.birthPlace() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.birthPlace(), true, PersonalData.TYPE_ID, true, PersonalData.BirthPlace.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.nationality() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.nationality().getPortaroVal(), true, PersonalData.TYPE_ID, true, PersonalData.Nationality.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.educationLevel() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.educationLevel().getPortaroVal(), true, PersonalData.TYPE_ID, true, PersonalData.EducationLevel.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.educationInstitution() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.educationInstitution(), true, PersonalData.TYPE_ID, true, PersonalData.EducationInstitution.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.gender() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.gender().getPortaroVal(), true, PersonalData.TYPE_ID, true, PersonalData.Gender.TYPE_ID, recordEditation, ctx, currentAuth);
        }


        // Job file
        if (jobData.identificationCardNumber() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.identificationCardNumber(), true, JobFile.TYPE_ID, true, JobFile.IdentificationCardNumber.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.jobPlace() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.jobPlace().portaroVal(), true, JobFile.TYPE_ID, true, JobFile.JobPlace.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.identityCardNumber() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.identityCardNumber(), true, JobFile.TYPE_ID, true, JobFile.IdentityCardNumber.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.identityCardValidity() != null) {
            recordEditationHelper.setDateSubfieldValue(jobData.identityCardValidity(), true, JobFile.TYPE_ID, true, JobFile.IdentityCardValidity.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.workPlacementEndDate() != null) {
            sutinDateUtils.setDay(jobData.workPlacementEndDate(), JobFile.WorkPlacementValidity.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.healthInsuranceCode() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.healthInsuranceCode().portaroVal(), true, JobFile.TYPE_ID, true, JobFile.HealthInsuranceCode.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.handicap() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.handicap().portaroVal(), true, JobFile.TYPE_ID, true, JobFile.Handicap.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        // TODO: boolean or string?
        recordEditationHelper.setBooleanSubfieldValue(jobData.isValid(), true, JobFileValidity.TYPE_ID, true, JobFileValidity.Validity.TYPE_ID, recordEditation, ctx, currentAuth);
        var validityRange = sutinDateUtils.validityRange(jobData.jobFileValidFrom(), jobData.jobFileValidTo());
        recordEditationHelper.setDatetimeRangeSubfieldValue(validityRange, true, JobFileValidity.TYPE_ID, true, JobFileValidity.TimeValidity.TYPE_ID, recordEditation, ctx, currentAuth);


        // Additional data
        if (jobData.maternityBeginDate() != null) {
            recordEditationHelper.setDateSubfieldValue(jobData.maternityBeginDate(), true, AdditionalData.TYPE_ID, true, AdditionalData.MaternityBeginDate.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.maternityEndDate() != null) {
            recordEditationHelper.setDateSubfieldValue(jobData.maternityEndDate(), true, AdditionalData.TYPE_ID, true, AdditionalData.MaternityEndDate.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.parentalBeginDate() != null) {
            recordEditationHelper.setDateSubfieldValue(jobData.parentalBeginDate(), true, AdditionalData.TYPE_ID, true, AdditionalData.ParentalBeginDate.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.parentalEndDate() != null) {
            recordEditationHelper.setDateSubfieldValue(jobData.parentalEndDate(), true, AdditionalData.TYPE_ID, true, AdditionalData.ParentalEndDate.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.claimedChildren() != null) {
            recordEditationHelper.setNumberSubfieldValue(BigDecimal.valueOf(jobData.claimedChildren()), true, AdditionalData.TYPE_ID, true, AdditionalData.ClaimedChildren.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.unclaimedChildren() != null) {
            recordEditationHelper.setNumberSubfieldValue(BigDecimal.valueOf(jobData.unclaimedChildren()), true, AdditionalData.TYPE_ID, true, AdditionalData.UnclaimedChildren.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.foreclosure() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.foreclosure().getPortaroVal(), true, AdditionalData.TYPE_ID, true, AdditionalData.Foreclosure.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        return recordEditation;
    }

}
