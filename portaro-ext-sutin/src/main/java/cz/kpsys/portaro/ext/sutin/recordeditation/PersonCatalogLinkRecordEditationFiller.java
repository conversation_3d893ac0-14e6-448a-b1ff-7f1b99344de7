package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondWorkCatalog;
import cz.kpsys.portaro.ext.sutin.SutinDateUtils;
import cz.kpsys.portaro.ext.sutin.SutinRecordDataLoader;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.WorkCatalogIdMapping;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.WorkCatalogLinkDto;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.RowId;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class PersonCatalogLinkRecordEditationFiller {

    @NonNull SutinRecordDataLoader sutinRecordDataLoader;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull CacheDeletableById recordCache;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull SutinDateUtils sutinDateUtils;

    public RecordEditation fill(@NonNull WorkCatalogLinkDto workCatalogLink,
                                @NonNull RecordEditation recordEditation,
                                @NonNull Record userRecord,
                                @NonNull Department ctx,
                                @NonNull UserAuthentication currentAuth) {

        recordEditationHelper.setStringTopFieldValue(workCatalogLink.rowId(), RowId.TYPE_ID, recordEditation, ctx, currentAuth);

        recordEditationHelper.setStringSubfieldValue(workCatalogLink.type().portaroVal(), true, FondWorkCatalog.WorkType.TYPE_ID, true, FondWorkCatalog.WorkType.Type.TYPE_ID, recordEditation, ctx, currentAuth);

        var catalogIdMapping = WorkCatalogIdMapping.map(workCatalogLink.katalogId());
        Optional<UUID> catalogItemId = sutinRecordDataLoader.getRecordIdByExternalId(catalogIdMapping.newId(), RowId.CODE, catalogIdMapping.fondId());
        if (catalogItemId.isPresent()) {
            var workCatalogItem = recordLoader.getById(catalogItemId.get());
            DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(workCatalogItem, ctx, currentAuth);
            recordEditationHelper.setRecordIdSubfieldValue(cmd, true, FondWorkCatalog.WorkPersonCatalog.TYPE_ID, true, FondWorkCatalog.WorkPersonCatalog.Main.TYPE_ID, recordEditation);
            recordCache.deleteFromCacheById(catalogItemId.get());
        } else {
            log.error("Cannot find price list item for {}", workCatalogLink);
        }

        DetailedRecordValueCommand userRecordLinkCmd = new DetailedRecordValueCommand(userRecord, ctx, currentAuth);
        recordEditationHelper.setRecordIdSubfieldValue(userRecordLinkCmd, true, FondWorkCatalog.Person.TYPE_ID, true, FondWorkCatalog.Person.Main.TYPE_ID, recordEditation);

        var validityRange = sutinDateUtils.validityRange(workCatalogLink.validFrom(), workCatalogLink.validTo());
        recordEditationHelper.setDatetimeRangeSubfieldValue(validityRange, true, FondWorkCatalog.Validity.TYPE_ID, true, FondWorkCatalog.Validity.TimeValidity.TYPE_ID, recordEditation, ctx, currentAuth);

        return recordEditation;
    }

}
