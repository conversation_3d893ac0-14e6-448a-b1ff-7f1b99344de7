package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.NonFatalImportException;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.DataWithRecord;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.JobData;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.SalaryData;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SalaryRecordEditationCreator {

    @NonNull SalaryRecordEditationFiller salaryRecordEditationFiller;
    @NonNull Provider<@NonNull Fond> monthlySalaryFondProvider;
    @NonNull Provider<@NonNull Fond> hourlySalaryFondProvider;
    @NonNull RecordEditationFactory recordEditationFactory;

    public RecordEditation ofNewRecord(@NonNull SalaryData salaryData,
                                       @NonNull DataWithRecord<JobData> employmentStatus,
                                       @NonNull List<Department> holdingDepartments,
                                       @NonNull Department ctx,
                                       @NonNull UserAuthentication currentAuth) {
        Fond fond;
        if (salaryData.salaryType() != null) {
            fond = switch (salaryData.salaryType()) {
                case MONTHLY -> monthlySalaryFondProvider.get();
                case HOURLY -> hourlySalaryFondProvider.get();
            };
        } else {
            throw new NonFatalImportException("Cannot determine salary fond. Missing salary type in %s".formatted(salaryData));
        }

        RecordEditation recordEditation = recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofNew(fond)
                .build(currentAuth);

        return salaryRecordEditationFiller.fill(salaryData, recordEditation, employmentStatus, ctx, currentAuth);
    }

    public RecordEditation ofExistingRecord(@NonNull Record salaryRecord,
                                            @NonNull SalaryData salaryData,
                                            @NonNull DataWithRecord<JobData> employmentStatus,
                                            @NonNull List<Department> holdingDepartments,
                                            @NonNull Department ctx,
                                            @NonNull UserAuthentication currentAuth) {
        RecordEditation recordEditation = recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofExisting(salaryRecord)
                .build(currentAuth);

        return salaryRecordEditationFiller.fill(salaryData, recordEditation, employmentStatus, ctx, currentAuth);
    }


}
