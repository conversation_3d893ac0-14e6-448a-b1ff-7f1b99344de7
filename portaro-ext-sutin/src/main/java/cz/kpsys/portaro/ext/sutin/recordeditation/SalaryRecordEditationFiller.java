package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.SutinDateUtils;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.DataWithRecord;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.JobData;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.SalaryData;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondSalaryCost.EmploymentStatus;
import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondSalaryCost.Validity;
import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.RowId;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SalaryRecordEditationFiller {

    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull SutinDateUtils sutinDateUtils;

    public RecordEditation fill(@NonNull SalaryData salaryData,
                                @NonNull RecordEditation recordEditation,
                                @NonNull DataWithRecord<JobData> employmentStatus,
                                @NonNull Department ctx,
                                @NonNull UserAuthentication currentAuth) {

        recordEditationHelper.setStringTopFieldValue(salaryData.rowId(), RowId.TYPE_ID, recordEditation, ctx, currentAuth);

        Record employmentStatusRecord = Objects.requireNonNull(employmentStatus.getRecord());
        DetailedRecordValueCommand employmentStatusRecordLinkCmd = new DetailedRecordValueCommand(employmentStatusRecord, ctx, currentAuth);
        recordEditationHelper.setRecordIdSubfieldValue(employmentStatusRecordLinkCmd, true, EmploymentStatus.TYPE_ID, true, EmploymentStatus.Main.TYPE_ID, recordEditation);

        // TODO: sensitive. Uncomment in prod.
        //if (salaryData.note() != null) {
        //    recordEditationHelper.setStringSubfieldValue(salaryData.note(), true, FondSalaryCost.Note.TYPE_ID, true, FondSalaryCost.Note.Value.TYPE_ID, recordEditation);
        //}

        var validityRange = sutinDateUtils.validityRange(salaryData.validFrom(), salaryData.validTo());
        recordEditationHelper.setDatetimeRangeSubfieldValue(validityRange, true, Validity.TYPE_ID, true, Validity.TimeValidity.TYPE_ID, recordEditation, ctx, currentAuth);

        if (salaryData.salaryType() != null) {
            switch (salaryData.salaryType()) {
                case MONTHLY -> fillMonthlySalaryData(salaryData, recordEditation, ctx, currentAuth);
                case HOURLY -> fillHourlySalaryData(salaryData, recordEditation, ctx, currentAuth);
            }
        } else {
            log.error("Missing salary type information, cannot determine salary values! {}", salaryData);
        }

        return recordEditation;
    }

    private void fillMonthlySalaryData(@NonNull SalaryData salaryData, @NonNull RecordEditation recordEditation, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        // FIXME: Do not comment out yet! It is sensitive information!
        //recordEditationHelper.setNumberSubfieldValue(salaryData.basicSalary(), true, FondSalaryCost.BasicSalary.TYPE_ID, true, FondSalaryCost.BasicSalary.Value.TYPE_ID, recordEditation, ctx, currentAuth);
        //recordEditationHelper.setNumberSubfieldValue(salaryData.floatingSalary(), true, FondSalaryCost.FloatingSalary.TYPE_ID, true, FondSalaryCost.FloatingSalary.Value.TYPE_ID, recordEditation, ctx, currentAuth);
        //recordEditationHelper.setNumberSubfieldValue(salaryData.rewardMoney(), true, FondSalaryCost.RewardMoney.TYPE_ID, true, FondSalaryCost.RewardMoney.Value.TYPE_ID, recordEditation, ctx, currentAuth);
    }

    private void fillHourlySalaryData(@NonNull SalaryData salaryData, @NonNull RecordEditation recordEditation, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        // FIXME: Do not comment out yet! It is sensitive information!
        //recordEditationHelper.setNumberSubfieldValue(salaryData.basicSalary(), true, FondSalaryCost.HourlyRate.TYPE_ID, true, FondSalaryCost.HourlyRate.Value.TYPE_ID, recordEditation, ctx, currentAuth);
    }

}
