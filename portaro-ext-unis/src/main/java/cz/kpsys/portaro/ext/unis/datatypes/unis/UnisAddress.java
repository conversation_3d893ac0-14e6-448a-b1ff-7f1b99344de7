package cz.kpsys.portaro.ext.unis.datatypes.unis;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public class UnisAddress {

    @JacksonXmlProperty(localName = "street")
    @Nullable
    String street;

    @JacksonXmlProperty(localName = "number")
    @Nullable
    String number;

    @JacksonXmlProperty(localName = "city")
    @Nullable
    String city;

    @JacksonXmlProperty(localName = "zipCode")
    @Nullable
    String zipCode;

    @JacksonXmlProperty(localName = "country")
    @Nullable
    String country;

    @JacksonXmlProperty(localName = "countryCode")
    @Nullable
    String countryCode;
}
