package cz.kpsys.portaro.ext.unis.datatypes.unis;

import lombok.NonNull;
import lombok.Value;
import org.springframework.lang.Nullable;

import java.time.LocalDate;

@Value
public class ValidUnisPersonRelation {

    @Nullable
    String relationId;

    @Nullable
    UnisRelationDefinition relationType;

    @Nullable
    LocalDate beginDate;

    @NonNull
    LocalDate endDate;

    @Nullable
    String orgUnitAbbrev;

    @Nullable
    String orgUnitTitle;

    @Nullable
    String workplaceAbbrev;

    @Nullable
    String employeeSubgroupCode;

    @Nullable
    String employeeStatusCode;

    @Nullable
    String workContractTypeCode;

    @Nullable
    String studyTypeId;

    @Nullable
    String studyTypeCode;

    @Nullable
    String studyFormId;

    @Nullable
    String studyFormCode;

    @Nullable
    String studyProgrammeCode;

    @Nullable
    String studyProgrammeTitle;

    @Nullable
    String studyBranchCode;

    @Nullable
    String studyBranchTitle;

    @Nullable
    String studyResidency;

    @Nullable
    boolean studyActive;

    @Nullable
    String studyInterruption;

    @Nullable
    String courseId;

    @Nullable
    String courseCode;

    @Nullable
    String courseTypeId;

    @Nullable
    String courseTypeCode;
}
