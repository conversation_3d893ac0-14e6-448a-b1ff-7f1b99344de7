package cz.kpsys.portaro.ext.wallet;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.prop.UserServicePropertyHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CardIdProvider implements ContextualFunction<User, Department, @NonNull String> {

    private static final String WALLET_SERVICE = "wallet";
    private static final String WALLET_CARD_ID = "cardId";

    @NonNull Provider<@NonNull String> rootSerialCodeProvider;
    @NonNull UserServicePropertyHelper userServicePropertyHelper;

    @Override
    public @NonNull String getOn(User user, Department department) {
        return userServicePropertyHelper.getPropertyOrCreateNew(WALLET_SERVICE, user.getId(), WALLET_CARD_ID, () -> {
            String serialCode = rootSerialCodeProvider.get();
            String userUuid = UuidGenerator.forRandomString();
            return "%s.%s.%s".formatted(serialCode, department.getId(), userUuid);
        });
    }
}
