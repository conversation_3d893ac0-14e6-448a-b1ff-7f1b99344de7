package cz.kpsys.portaro.ext.ziskej.impl;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.cpk.impl.CpkDatasource;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.AfterIntegrityValidationViolation;
import cz.kpsys.portaro.formannotation.annotations.validation.IntegrityValidation;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorAlias;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorAliasType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.searchoredit.SearchOrEditEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.searchoredit.StaticSearchParameters;
import cz.kpsys.portaro.loan.ill.Seeking;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.export.RecordViewFunctions;
import cz.kpsys.portaro.search.BasicMapSearchParams;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

@Form(id = "seekingZiskejLinkCreation", title = "Odeslat do Získej")
@FormSubmit(path = ZiskejSeekingApiController.CREATE_LINK_PATH)
@AfterIntegrityValidationViolation(bean = "seekingZiskejLinkCreationRequestDefaulter")
@With
public record SeekingZiskejLinkCreationRequest(

        @Schema(implementation = String.class, example = Seeking.SCHEMA_EXAMPLE_ID)
        @NotNull(groups = IntegrityValidation.class)
        Seeking id,

        @Schema(implementation = UUID.class, example = Record.SCHEMA_EXAMPLE_DOCUMENT_ID, description = "Requested record id. **Record must have CPK id field filled (field 4001.a)**")
        @FormPropertyLabel("Dokument z CPK")
        @SearchOrEditEditor(staticSearchParameters = @StaticSearchParameters(kind = BasicMapSearchParams.KIND_RECORD, subkind = BasicMapSearchParams.SUBKIND_DOCUMENT, type = SearchViewConstants.TYPE_SEARCH_SELECTION, datasourceGroup = CpkDatasource.DATASOURCE_EXTERNAL_CPK_GROUP))
        @ValueEditorAlias(ValueEditorAliasType.RECORD_SEARCH)
        @NotNull(groups = IntegrityValidation.class)
        Record document

) {

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class SeekingZiskejLinkCreationRequestDefaulter implements TypedAuthenticatedContextualObjectModifier<SeekingZiskejLinkCreationRequest> {

        @Override
        public SeekingZiskejLinkCreationRequest modify(SeekingZiskejLinkCreationRequest request, Department ctx, UserAuthentication currentAuth) {
            if (request.document() == null) {
                Record document = request.id().getDesiredExemplar().document();
                if (RecordViewFunctions.primaryCpkId(document.getDetail()).isPresent()) {
                    request = request.withDocument(document);
                }
            }
            return request;
        }
    }
}
