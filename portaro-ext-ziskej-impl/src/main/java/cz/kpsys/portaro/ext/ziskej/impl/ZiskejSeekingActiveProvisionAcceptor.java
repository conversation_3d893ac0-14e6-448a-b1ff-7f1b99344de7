package cz.kpsys.portaro.ext.ziskej.impl;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.ziskej.ContextualZiskejClient;
import cz.kpsys.portaro.loan.ill.*;
import cz.kpsys.portaro.loan.ill.process.SeekingActiveProvisionAcceptCommand;
import cz.kpsys.portaro.loan.ill.process.SeekingActiveProvisionAcceptor;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneId;
import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ZiskejSeekingActiveProvisionAcceptor implements Consumer<@NonNull SeekingActiveProvisionAcceptCommand> {

    @NonNull SeekingActiveProvisionAcceptor standardAcceptor;
    @NonNull ContextualZiskejClient<Department> contextualZiskejClient;
    @NonNull Provider<@NonNull ZoneId> ziskejTimeZoneIdProvider;

    @Transactional
    @Override
    public void accept(@NonNull SeekingActiveProvisionAcceptCommand command) {
        standardAcceptor.accept(command);

        String ziskejSubticketId = command.seeking().getExistingActiveProvision().ziskejSubticketSyncId();
        performZiskejAccept(command, ziskejSubticketId, command.ctx());
    }

    private void performZiskejAccept(@NonNull SeekingActiveProvisionAcceptCommand command, @NonNull String ziskejSubticketId, @NonNull Department ctx) {
        if (command.acceptCondition() == null) {
            contextualZiskejClient.performDkSubticketAccept(ctx, ziskejSubticketId);
            return;
        }
        if (command.acceptCondition() instanceof HigherFeeAcceptCondition cond) {
            contextualZiskejClient.performDkSubticketAcceptWithHigherFeeCondition(ctx, ziskejSubticketId, cond.price().amount().intValueExact());
            return;
        }
        if (command.acceptCondition() instanceof AnotherEditionAcceptCondition cond) {
            contextualZiskejClient.performDkSubticketAcceptWithAnotherEditionCondition(ctx, ziskejSubticketId, cond.edition());
            return;
        }
        if (command.acceptCondition() instanceof LaterSendDateAcceptCondition cond) {
            contextualZiskejClient.performDkSubticketAcceptWithLaterSendDateCondition(ctx, ziskejSubticketId, DateUtils.instantToLocalDate(cond.sendDate(), ziskejTimeZoneIdProvider.get()));
            return;
        }
        if (command.acceptCondition() instanceof InPlaceOnlyAcceptCondition) {
            contextualZiskejClient.performDkSubticketAcceptWithInPlaceOnlyCondition(ctx, ziskejSubticketId);
            return;
        }
        if (command.acceptCondition() instanceof ShorterLoanPeriodAcceptCondition cond) {
            contextualZiskejClient.performDkSubticketAcceptWithShorterLoanPeriodCondition(ctx, ziskejSubticketId, cond.loanPeriodDays());
            return;
        }
        throw new IllegalArgumentException("Unsupported condition: %s".formatted(command.acceptCondition()));
    }
}
