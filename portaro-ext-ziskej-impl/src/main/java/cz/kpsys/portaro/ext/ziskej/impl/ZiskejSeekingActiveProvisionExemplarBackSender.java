package cz.kpsys.portaro.ext.ziskej.impl;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.ziskej.ContextualZiskejClient;
import cz.kpsys.portaro.ext.ziskej.json.ZkSubticketTransitionIdZiskejRequest;
import cz.kpsys.portaro.ext.ziskej.model.ZiskejZkSubticketTransitionPerformCommand;
import cz.kpsys.portaro.loan.ill.Seeking;
import cz.kpsys.portaro.loan.ill.process.SeekingActiveProvisionExemplarBackSender;
import cz.kpsys.portaro.loan.ill.process.SeekingActiveProvisionExemplarSendBackCommand;
import cz.kpsys.portaro.loan.returning.ReturningResult;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ZiskejSeekingActiveProvisionExemplarBackSender implements Function<@NonNull SeekingActiveProvisionExemplarSendBackCommand, @NonNull ReturningResult> {

    @NonNull SeekingActiveProvisionExemplarBackSender standardBackSender;
    @NonNull ContextualZiskejClient<Department> contextualZiskejClient;

    @Transactional
    @Override
    public @NonNull ReturningResult apply(@NonNull SeekingActiveProvisionExemplarSendBackCommand command) {
        ReturningResult returningResult = standardBackSender.apply(command);

        if (command.seeking().wasLent()) {
            performZiskejTicketReturnAfterReaderReturn(command.seeking(), command.ctx());
        } else {
            performZiskejTicketCloseAfterReaderDidNotLent(command, command.ctx());
        }
        performZiskejSubticketSendBack(command, command.ctx());

        return returningResult;
    }

    private void performZiskejTicketReturnAfterReaderReturn(@NonNull Seeking seeking, @NonNull Department ctx) {
        String ziskejTicketId = seeking.getZiskejTicketId();
        contextualZiskejClient.performTicketLendSync(ctx, ziskejTicketId, seeking.wasLent(), seeking.isReturned());
    }

    private void performZiskejTicketCloseAfterReaderDidNotLent(@NonNull SeekingActiveProvisionExemplarSendBackCommand command, @NonNull Department ctx) {
        String ziskejTicketId = command.seeking().getZiskejTicketId();
        contextualZiskejClient.performTicketClose(ctx, ziskejTicketId, null);
    }

    private void performZiskejSubticketSendBack(@NonNull SeekingActiveProvisionExemplarSendBackCommand command, @NonNull Department ctx) {
        ZiskejZkSubticketTransitionPerformCommand transitionPerformCommand = new ZiskejZkSubticketTransitionPerformCommand(
                command.seeking().getActiveProvision().ziskejSubticketSyncId(),
                ZkSubticketTransitionIdZiskejRequest.SEND_BACK,
                null
        );
        contextualZiskejClient.performZkSubticketTransition(ctx, transitionPerformCommand);
    }
}
