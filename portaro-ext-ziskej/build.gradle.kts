dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.junit.jupiter:junit-jupiter-params:+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-finance"))
    implementation(project(":portaro-integ-feign"))
    implementation(project(":portaro-web"))

    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign:4.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("io.github.openfeign:feign-jackson:+")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.+")
}
