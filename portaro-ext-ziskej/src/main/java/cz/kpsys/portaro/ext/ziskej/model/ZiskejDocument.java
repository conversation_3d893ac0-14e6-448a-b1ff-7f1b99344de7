package cz.kpsys.portaro.ext.ziskej.model;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import lombok.NonNull;

public record ZiskejDocument(

        /**
         * ID dokumentu v CPK, je-li <PERSON>.
         * nullable: true (ve swaggeru)
         */
        @NullableNotBlank
        String cpkId,

        /**
         * ID dokumentu v AKS dožádané knihovny, je-l<PERSON>, přev<PERSON>to z aktivního požadavku.
         * nullable: true (ve swaggeru)
         */
        @NullableNotBlank
        String dkId,

        /**
         * Citace ve tvaru používaném v ZÍSKEJ.
         */
        @NonNull
        String bibliographicReference

) {}
