package cz.kpsys.portaro.ext.ziskej.model;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.ext.ziskej.json.ZkSubticketTransitionIdZiskejRequest;
import lombok.NonNull;

public record ZiskejZkSubticketTransitionPerformCommand(

        @NonNull
        String subticketId,

        @NonNull
        ZkSubticketTransitionIdZiskejRequest transitionId,

        /**
         * nullable: true (ve swaggeru)
         */
        @NullableNotBlank
        String transitionMessage

) {}
