package cz.kpsys.portaro.file;

import cz.kpsys.portaro.commons.object.IdSettable;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.id.IDdFile;
import jakarta.annotation.Nullable;
import lombok.NonNull;

public interface IdentifiedFile extends Resource, IDdFile, IdSettable<Long>, LabeledIdentified<Long> {

    String SCHEMA_EXAMPLE_ID = "42069";

    @NonNull FileProcessingState getFileProcessingState();

    void setFileProcessingState(@NonNull FileProcessingState fileProcessingState);

    @Nullable Integer getOrder();

    void setOrder(@Nullable Integer order);
}
