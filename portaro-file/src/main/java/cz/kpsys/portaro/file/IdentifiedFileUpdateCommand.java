package cz.kpsys.portaro.file;

import cz.kpsys.portaro.file.directory.Directory;
import cz.kpsys.portaro.file.filecategory.FileCategory;
import jakarta.annotation.Nullable;
import lombok.NonNull;

public record IdentifiedFileUpdateCommand(
        @NonNull IdentifiedFile identifiedFile,
        @NonNull Directory directory,
        @NonNull String name,
        @NonNull String fileName,
        @NonNull Integer order,
        @NonNull FileCategory category,
        @Nullable FileAccessType accessType
) {
}
