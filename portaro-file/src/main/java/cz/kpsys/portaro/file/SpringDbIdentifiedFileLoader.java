package cz.kpsys.portaro.file;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.List;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SKUPINY;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbIdentifiedFileLoader implements ByIdLoadable<IdentifiedFile, Long> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull RowMapper<IdentifiedFile> identifiedFileRowMapper;


    @Override
    public IdentifiedFile getById(@NonNull Long id) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.ID_FULLTEXT),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.POPIS),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.EXTENSION),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.FILENAME),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.VELIKOST),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.IS_INDEX_LUCENE),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.DATUM_ZAL),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.FK_UZIV),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.DATUM_OPRAV),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.FK_UZIV_OPRAV),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.ZDROJ),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.TEXT),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.FK_FULLTEXT_PRISTUP),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.PORADI),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.FK_TYP_FULLTEXT),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.WIDTH),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.HEIGHT),
                TC(FULLTEXT_SOUBORY.TABLE, FULLTEXT_SOUBORY.SERVICE),
                AS(substring(FULLTEXT_SOUBORY.ORIGINAL, 1, 100), FULLTEXT_SOUBORY.ORIGINAL_PREVIEW),

                TC(FULLTEXT_SKUPINY.TABLE, FULLTEXT_SKUPINY.ID_FULLTEXT_SKUPINY),
                TC(FULLTEXT_SKUPINY.TABLE, FULLTEXT_SKUPINY.NAZEV),
                TC(FULLTEXT_SKUPINY.TABLE, FULLTEXT_SKUPINY.FK_NADR),
                TC(FULLTEXT_SKUPINY.TABLE, FULLTEXT_SKUPINY.PORADI),
                TC(FULLTEXT_SKUPINY.TABLE, FULLTEXT_SKUPINY.FK_PRISTUP)
        );
        sq.from(FULLTEXT_SOUBORY.TABLE);
        sq.joins().addLeft(FULLTEXT_SKUPINY.TABLE, COLSEQ(FULLTEXT_SOUBORY.FK_FULLTEXT_SKUPINY, FULLTEXT_SKUPINY.ID_FULLTEXT_SKUPINY));
        sq.where()
                .eq(FULLTEXT_SOUBORY.ID_FULLTEXT, id)
                .and()
                .notIn(FULLTEXT_SOUBORY.IS_INDEX_LUCENE, List.of(
                        FileProcessingState.READY_TO_DELETE.getId(),
                        FileProcessingState.DELETED.getId()));
        return jdbcTemplate.queryForObject(sq.getSql(), sq.getParamMap(), identifiedFileRowMapper);
    }

}
