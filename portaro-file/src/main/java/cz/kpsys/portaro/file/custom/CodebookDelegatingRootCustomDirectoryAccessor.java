package cz.kpsys.portaro.file.custom;

import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.EmptyResultDataAccessException;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CodebookDelegatingRootCustomDirectoryAccessor implements RootCustomDirectoryAccessor {

    @NonNull Codebook<RootCustomDirectory, Integer> codebook;

    @Override
    public List<RootCustomDirectory> getAll() {
        return codebook.getAll();
    }

    @Override
    public RootCustomDirectory getById(@NonNull Integer id) throws ItemNotFoundException {
        return codebook.getById(id);
    }

    @Override
    public RootCustomDirectory getByDep(Department dep) {
        return getById(dep.getId());
    }

    @Override
    public Optional<RootCustomDirectory> getByDepOptional(Department dep) {
        try {
            return Optional.of(getByDep(dep));
        } catch (EmptyResultDataAccessException | ItemNotFoundException e) {
            return Optional.empty();
        }
    }
}
