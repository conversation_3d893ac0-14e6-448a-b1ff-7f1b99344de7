package cz.kpsys.portaro.file.directory;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.BasicNamedIdentified;
import cz.kpsys.portaro.file.FileAccessType;

import java.util.Optional;

/**
 * Basic directory class without information about parent directory
 */
public class BasicDirectory extends BasicNamedIdentified<Integer> implements Directory {

    private Optional<Text> customText = Optional.empty();

    public BasicDirectory(Integer id, String name) {
        super(id, name);
    }

    public static BasicDirectory createOnlyWithId(Integer id) {
        return new BasicDirectory(id, null);
    }

    public static BasicDirectory testing(Integer id, String name) {
        return new BasicDirectory(id, name);
    }

    public static BasicParentableDirectory testing(Integer id) {
        return new BasicParentableDirectory(id, "Dir_" + id, 0, null, FileAccessType.createPermitting());
    }

    public static BasicParentableDirectory testing(String name) {
        return new BasicParentableDirectory(null, name, 0, null, FileAccessType.createPermitting());
    }

    public BasicDirectory withTextDifferentFromName(Text customText) {
        this.customText = Optional.of(customText);
        return this;
    }

    @Override
    public Text getText() {
        return customText.orElse(Directory.super.getText());
    }

}
