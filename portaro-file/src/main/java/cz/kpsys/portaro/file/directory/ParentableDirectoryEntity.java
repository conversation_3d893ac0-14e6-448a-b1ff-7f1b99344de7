package cz.kpsys.portaro.file.directory;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.Objects;

import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SKUPINY.*;

@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Entity
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = TABLE)
public class ParentableDirectoryEntity implements Identified<Integer> {

    @EqualsAndHashCode.Include
    @Column(name = ID_FULLTEXT_SKUPINY)
    @NonNull
    @Id
    Integer id;

    @NonNull
    @Column(name = NAZEV)
    String name;

    @Nullable
    @Column(name = FK_NADR)
    Integer parentId;

    @NonNull
    @Column(name = PORADI)
    Integer order;

    @NonNull
    @Column(name = FK_PRISTUP)
    Integer accessTypeId;

    public boolean isSubdirectoryOf(ParentableDirectoryEntity otherDirectory) {
        return Objects.equals(parentId, otherDirectory.getId());
    }
}
