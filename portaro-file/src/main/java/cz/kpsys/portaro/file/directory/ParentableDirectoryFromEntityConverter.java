package cz.kpsys.portaro.file.directory;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.file.FileAccessType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ParentableDirectoryFromEntityConverter implements Converter<ParentableDirectoryEntity, ParentableDirectory> {

    @NonNull ByIdLoadable<FileAccessType, Integer> fileAccessTypeLoader;

    @Override
    public ParentableDirectory convert(@NonNull ParentableDirectoryEntity parentableDirectoryEntity) {
        return new BasicParentableDirectory(
                parentableDirectoryEntity.getId(),
                parentableDirectoryEntity.getName(),
                parentableDirectoryEntity.getOrder(),
                parentableDirectoryEntity.getParentId(),
                fileAccessTypeLoader.getById(parentableDirectoryEntity.getAccessTypeId())
        );
    }
}
