package cz.kpsys.portaro.file.security;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.commons.io.BytesRange;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.io.FileStreamConsumer;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.security.Action;
import cz.kpsys.portaro.security.SecurityManager;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SecuredFileDataStreamer implements FileDataStreamer {

    @NonNull FileDataStreamer target;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull ByIdLoadable<IdentifiedFile, Long> identifiedFileLoader;
    @NonNull SecurityManager securityManager;
    @NonNull Provider<Department> currentDepartmentProvider;
    @NonNull Action<IdentifiedFile> identifiedFileAction;

    @Override
    public void streamData(@NonNull Long id, @Nullable BytesRange range, @NonNull FileStreamConsumer reader) {
        throwIfDenied(id);
        target.streamData(id, range, reader);
    }

    private void throwIfDenied(Long id) {
        Department department = null;
        try {
            department = currentDepartmentProvider.get();
        } catch (Exception e) {
            log.error("Cannot get current department from current thread", e);
        }
        if (department != null) {
            securityManager.throwIfCannot(FileSecurityActions.FILES_SHOW, authenticationHolder.getCurrentAuth(), department);
        }
        IdentifiedFile file = identifiedFileLoader.getById(id);
        if (department != null) {
            securityManager.throwIfCannot(identifiedFileAction, authenticationHolder.getCurrentAuth(), department, file);
        }
    }

}
