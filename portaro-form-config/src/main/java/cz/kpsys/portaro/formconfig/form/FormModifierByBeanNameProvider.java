package cz.kpsys.portaro.formconfig.form;

import cz.kpsys.portaro.form.form.FormModifier;
import cz.kpsys.portaro.form.form.FormModifierProvider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.BeanFactory;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FormModifierByBeanNameProvider implements FormModifierProvider {

    @NonNull BeanFactory beanFactory;

    @Override
    public <FORM> FormModifier<FORM> getModifier(String beanName) {
        return (FormModifier<FORM>) beanFactory.getBean(beanName, FormModifier.class);
    }
}
