package cz.kpsys.portaro.form.editedproperty;

import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

import static java.lang.String.format;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CompositeEditedPropertyResolver implements EditedPropertyResolver {

    @NonNull List<EditedPropertyResolver> resolvers;

    @Override
    public Optional<EditedProperty<?>> resolve(Object formObject, String fieldName, Department department) {
        EditedProperty<?> editedProperty = resolvers.stream()
                .map(editedPropertyResolver -> editedPropertyResolver.resolve(formObject, fieldName, department))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst()
                .orElseThrow(() -> new ItemNotFoundException(EditedProperty.class, format("formObject=%s and propertyName=%s", formObject.getClass().getSimpleName(), fieldName)));
        return Optional.of(editedProperty);
    }
}
