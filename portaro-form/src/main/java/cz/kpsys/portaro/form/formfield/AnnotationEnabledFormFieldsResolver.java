package cz.kpsys.portaro.form.formfield;

import cz.kpsys.portaro.commons.util.AnnotationUtil;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.ValidationUtils;
import cz.kpsys.portaro.formannotation.annotations.formfield.EnabledFormFieldWhenAnotherFilled;
import cz.kpsys.portaro.formannotation.annotations.formfield.EnabledFormFields;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditor;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AnnotationEnabledFormFieldsResolver implements EnabledFormFieldsResolver {

    @NonNull FormFieldsNamesProvider formFieldsNamesProvider;

    @Override
    public boolean resolve(Object formObject, String fieldName) {
        Predicate<String> predicate = getHasEditorFieldFilter(formObject)
                .and(getEnabledFormFieldFilter(formObject))
                .and(getDependsOnFieldFilter(formObject));
        return predicate
                .test(fieldName);
    }

    private Predicate<String> getHasEditorFieldFilter(@NonNull Object formObject) {
        return fieldName -> AnnotationUtil.findAnnotation(formObject, fieldName, ValueEditor.class).isPresent();
    }

    private Predicate<String> getEnabledFormFieldFilter(Object formObject) {
        Optional<EnabledFormFields> annotation = AnnotationUtil.findClassLevelAnnotation(formObject, EnabledFormFields.class);
        if (annotation.isEmpty()) {
            return fieldName -> true;
        }

        Collection<Field> involvedFields = ObjectUtil.getFieldStream(formObject.getClass()).collect(Collectors.toUnmodifiableSet());
        Assert.notEmpty(involvedFields, "FormObject %s is annotated with %s, but class has no fields".formatted(formObject.getClass().getSimpleName(), EnabledFormFields.class.getSimpleName()));
        if (annotation.get().resolveOnlyExplicitlyInvolved()) {
            involvedFields = ListUtil.filter(involvedFields, field -> field.isAnnotationPresent(EnabledFormFields.Involve.class));
            Assert.notEmpty(involvedFields, "FormObject %s is annotated with %s, but class has no field with %s annotation".formatted(formObject.getClass().getSimpleName(), EnabledFormFields.class.getSimpleName(), EnabledFormFields.Involve.class.getSimpleName()));
        }
        List<String> involvedFieldNames = ListUtil.convert(involvedFields, Field::getName);

        return fieldName -> {
            if (!involvedFieldNames.contains(fieldName)) {
                return true;
            }
            Set<String> providedFields = formFieldsNamesProvider.getFieldsNames(annotation.get().bean(), formObject);
            return involvedFieldNames.stream()
                    .filter(providedFields::contains)
                    .anyMatch(fieldName::equals);
        };
    }

    private Predicate<String> getDependsOnFieldFilter(@NonNull Object formObject) {
        return fieldName -> {
            Optional<EnabledFormFieldWhenAnotherFilled> annotation = AnnotationUtil.findAnnotation(formObject, fieldName, EnabledFormFieldWhenAnotherFilled.class);
            if (annotation.isEmpty()) {
                return true;
            }
            String mandatoryField = annotation.get().value();
            Object mandatoryFieldValue = ObjectUtil.getFieldValue(formObject, mandatoryField);
            return ValidationUtils.hasValue(mandatoryFieldValue);
        };
    }
}
