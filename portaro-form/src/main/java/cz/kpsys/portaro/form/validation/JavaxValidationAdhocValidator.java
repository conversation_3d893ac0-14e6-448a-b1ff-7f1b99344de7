package cz.kpsys.portaro.form.validation;

import cz.kpsys.portaro.department.Department;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class JavaxValidationAdhocValidator implements AdhocValidator {

    @NonNull Validator validator;

    @Override
    public <FORM_OBJECT> FORM_OBJECT validate(@NonNull FORM_OBJECT formObject, @NonNull Department currentDepartment) throws ConstraintViolationException {
        var constraintViolations = validator.validate(formObject);
        if (!constraintViolations.isEmpty()) {
            throw new ConstraintViolationException(constraintViolations);
        }
        return formObject;
    }

}
