package cz.kpsys.portaro.form.valueeditor.autocomplete;

import cz.kpsys.portaro.form.valueeditor.ValueEditorOptions;
import lombok.Value;
import lombok.With;

import java.util.Optional;

@With
@Value
public class AutocompleteValueEditorOptions implements ValueEditorOptions {

    public static AutocompleteValueEditorOptions getEmptyOptions() {
        return new AutocompleteValueEditorOptions(null, null);
    }

    Integer minLength;

    AutocompleteValueEditorStaticParams staticParams;

    public Optional<Integer> getMinLength() {
        return Optional.ofNullable(minLength);
    }

    public Optional<AutocompleteValueEditorStaticParams> getStaticParams() {
        return Optional.ofNullable(staticParams);
    }
}
