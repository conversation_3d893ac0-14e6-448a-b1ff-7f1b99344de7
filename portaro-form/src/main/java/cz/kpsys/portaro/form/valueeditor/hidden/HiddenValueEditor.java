package cz.kpsys.portaro.form.valueeditor.hidden;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.form.valueeditor.DefaultValueEditorValidations;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.ValueEditorOptions;
import cz.kpsys.portaro.form.valueeditor.ValueEditorValidations;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.BasicValueEditorType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorType;
import lombok.NonNull;
import lombok.Value;
import lombok.With;

import java.util.Optional;

@With
@Value
public class HiddenValueEditor implements ValueEditor<ValueEditorOptions, ValueEditorValidations, HiddenValueEditor> {

    public static HiddenValueEditor getEmptyEditor() {
        return new HiddenValueEditor(BasicValueEditorType.HIDDEN, null, null, null, null, null, null, null);
    }

    @NonNull
    ValueEditorType type;

    String editorId;

    String editorName;

    Text placeholder;

    Boolean disabled;

    @JsonProperty("isVisible")
    Boolean visible;

    ValueEditorOptions options;

    ValueEditorValidations validations;

    public Optional<String> getEditorId() {
        return Optional.ofNullable(editorId);
    }

    public Optional<String> getEditorName() {
        return Optional.ofNullable(editorName);
    }

    public Optional<Text> getPlaceholder() {
        return Optional.ofNullable(placeholder);
    }

    @Override
    public Optional<Boolean> getDisabled() {
        return Optional.ofNullable(disabled);
    }

    @Override
    public Optional<Boolean> getVisible() {
        return Optional.ofNullable(visible);
    }

    public Optional<ValueEditorOptions> getOptions() {
        return Optional.ofNullable(options);
    }

    public Optional<ValueEditorValidations> getValidations() {
        return Optional.ofNullable(validations);
    }

    /* --- */

    @Override
    public HiddenValueEditor withRequired(boolean required) {
        return this.withValidations(this.getValidations().orElseGet(DefaultValueEditorValidations::getEmptyValidations).withRequired(required));
    }
}
