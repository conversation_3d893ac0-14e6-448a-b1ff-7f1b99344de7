package cz.kpsys.portaro.form.valueeditor.list;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.form.valueeditor.ValueEditorValidations;
import cz.kpsys.portaro.form.validation.ValueEditorAsyncValidation;
import lombok.Value;
import lombok.With;

import java.util.Optional;

@With
@Value
public class ListValueEditorValidations implements ValueEditorValidations {

    public static ListValueEditorValidations getEmptyValidations() {
        return new ListValueEditorValidations(null, null, null);
    }

    Boolean required;

    ValueEditorAsyncValidation async;

    Provider<Integer> maxCountProvider;


    @Override
    public Optional<Boolean> getRequired() {
        return Optional.ofNullable(required);
    }

    @Override
    public Optional<ValueEditorAsyncValidation> getAsync() {
        return Optional.ofNullable(async);
    }

    public Optional<Integer> getMaxCount() {
        return Optional.ofNullable(maxCountProvider).map(Provider::get);
    }

}
