package cz.kpsys.portaro.form.valueeditor.password;

import cz.kpsys.portaro.commons.util.AnnotationUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.valueeditor.*;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.password.PasswordEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.EditorOptions;
import cz.kpsys.portaro.form.validation.ValidationsResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PasswordValueEditorResolver implements AnnotationsAwareValueEditorResolver {

    @NonNull ValidationsResolver validationsResolver;
    @NonNull AnnotationValueEditorModifier<Department> annotationValueEditorModifier;

    @Override
    public Optional<? extends ValueEditor<?, ?, ?>> resolve(Object formObject, String fieldName, Department currentDepartment) {
        return this.resolveProperty(formObject, fieldName, currentDepartment, false);
    }

    @Override
    public Optional<? extends ValueEditor<?, ?, ?>> resolveByTypeAnnotation(Object formObject, String fieldName, Department currentDepartment) {
        return this.resolveProperty(formObject, fieldName, currentDepartment, true);
    }

    @SneakyThrows
    private Optional<PasswordValueEditor> resolveProperty(Object formObject, String fieldName, Department department, boolean searchInTypeAnnotation) {
        if (!AnnotationUtil.hasAnnotationIncludingTypeAnnotations(formObject, fieldName, PasswordEditor.class, searchInTypeAnnotation)) {
            return Optional.empty();
        }

        if (!ObjectUtil.getField(formObject, fieldName).getType().isAssignableFrom(String.class)) {
            throw new RuntimeException("@PasswordEditor can be used only on String interface");
        }

        Optional<PasswordEditor> annotation = AnnotationUtil.findAnnotationIncludingTypeAnnotations(formObject, fieldName, PasswordEditor.class, searchInTypeAnnotation);

        PasswordValueEditorOptions passwordValueEditorOptions = PasswordValueEditorOptions.getEmptyOptions();

        Optional<EditorOptions> editorOptions = annotation.map(PasswordEditor::editorOptions);

        PasswordValueEditor passwordValueEditor = PasswordValueEditor.getEmptyEditor()
                .withEditorName(fieldName)
                .withOptions(passwordValueEditorOptions);

        passwordValueEditor = ValueEditor.fillValueEditorWithOptions(passwordValueEditor, editorOptions);

        passwordValueEditor = validationsResolver.processTextValueEditorValidations(formObject, fieldName, passwordValueEditor, searchInTypeAnnotation);

        passwordValueEditor = ValueEditorDescriptionResolver.processDescription(passwordValueEditor, formObject, fieldName, searchInTypeAnnotation);

        passwordValueEditor = ValueEditorAliasHelper.aliasEditor(formObject, fieldName, searchInTypeAnnotation, passwordValueEditor);

        passwordValueEditor = annotationValueEditorModifier.modifyEditorIfModifierPresent(formObject, fieldName, searchInTypeAnnotation, passwordValueEditor, department);

        return Optional.of(passwordValueEditor);
    }
}
