package cz.kpsys.portaro.grid;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.LabeledReference;
import cz.kpsys.portaro.commons.object.ValuableRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.detail.FieldId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.UUID;

public record EmptyGridField(

        @NonNull
        FieldId fieldId,

        @NonNull
        Text text,

        @Nullable
        LabeledReference<UUID> recordReference,

        @Nullable
        LabeledReference<UUID> origin

) implements GridField, ValuableRecord<Object> {

    @Override
    public Object value() {
        return null;
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, EmptyGridField.class, EmptyGridField::fieldId);
    }

    @Override
    public int hashCode() {
        return fieldId().hashCode();
    }
}
