package cz.kpsys.portaro.grid;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.LabeledReference;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.frontend.ErroredFieldResponse;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.UUID;


public record ErrorGridField(

        @NonNull
        FieldId fieldId,

        @NonNull
        Text text,

        @NonNull
        ErroredFieldResponse error,

        @Nullable
        LabeledReference<UUID> recordReference,

        @Nullable
        LabeledReference<UUID> origin

) implements GridField {

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, ErrorGridField.class, ErrorGridField::fieldId);
    }

    @Override
    public int hashCode() {
        return fieldId().hashCode();
    }
}
