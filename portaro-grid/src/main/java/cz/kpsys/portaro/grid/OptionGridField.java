package cz.kpsys.portaro.grid;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.LabeledIdentifiedRecord;
import cz.kpsys.portaro.commons.object.LabeledReference;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.LabeledRecordRef;
import cz.kpsys.portaro.record.detail.FieldId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.UUID;

public record OptionGridField<ID>(

        @NonNull
        FieldId fieldId,

        @NonNull
        ID id,

        @NonNull
        Text text,

        @Nullable
        LabeledRecordRef recordReference,

        @Nullable
        LabeledReference<UUID> origin

) implements GridField, LabeledIdentifiedRecord<ID> {

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, OptionGridField.class, OptionGridField::fieldId);
    }

    @Override
    public int hashCode() {
        return fieldId().hashCode();
    }

}
