package cz.kpsys.portaro.inventory.match;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.inventory.InventoryConstants;
import cz.kpsys.portaro.search.AbstractSingleColumnSpringDbSearchLoader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.Map;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.COLSEQ;
import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.KAT1_5;
import static cz.kpsys.portaro.databasestructure.InventoryDb.REVI_MATCH.*;
import static cz.kpsys.portaro.exemplar.ExemplarConstants.SearchParams.LOCATION;

public class SpringDbMatchIdSearchLoader extends AbstractSingleColumnSpringDbSearchLoader<MapBackedParams, UUID, RangePaging> {

    private static final Map<String, String> PROPS_TO_DB_COLUMNS_MAP = Map.of(
            Match.Fields.accessNumber, KAT1_5.TRIDPRIC,
            Match.Fields.signature, KAT1_5.TRIDSIGN,
            Match.Fields.barCode, KAT1_5.BAR_COD
    );

    public SpringDbMatchIdSearchLoader(NamedParameterJdbcOperations jdbcTemplate, QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory, TABLE, ID, new SelectedColumnRowMapper<>(UUID.class, ID));
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.from(TABLE);

        if (!count && customSorting != null) {
            sq.joins().addLeft(KAT1_5.TABLE, COLSEQ(TC(TABLE, CIS_EX), TC(KAT1_5.TABLE, KAT1_5.ID_EX)));
        }

        if (p.hasNotNull(InventoryConstants.SearchParams.INVENTORY)) {
            if (!p.hasLength(InventoryConstants.SearchParams.INVENTORY)) {
                return false;
            }
            sq.where().and().in(FK_REVI, p.get(InventoryConstants.SearchParams.INVENTORY));
        }

        if (p.hasNotNull(InventoryConstants.SearchParams.MATCH_STATE)) {
            if (!p.hasLength(InventoryConstants.SearchParams.MATCH_STATE)) {
                return false;
            }
            sq.where().and().in(STAV_REVI, ListUtil.getListOfIds(p.get(InventoryConstants.SearchParams.MATCH_STATE)));
        }

        if (p.hasNotNull(InventoryConstants.SearchParams.PREVIOUS_MATCH_STATE)) {
            if (!p.hasLength(InventoryConstants.SearchParams.PREVIOUS_MATCH_STATE)) {
                return false;
            }
            sq.where().and().in(POSL_REVI, ListUtil.getListOfIds(p.get(InventoryConstants.SearchParams.PREVIOUS_MATCH_STATE)));
        }

        if (p.hasNotNull(LOCATION)) {
            if (!p.hasLength(LOCATION)) {
                return false;
            }
            sq.where().and().in(CIS_LOKACE, ListUtil.getListOfIds(p.get(LOCATION)));
        }

        return true;
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        if (customSorting == null) {
            return Sorting.empty();
        }
        SortingItem sorting = SortingItem.ofSimpleFieldAndOrder(TC(KAT1_5.TABLE, PROPS_TO_DB_COLUMNS_MAP.get(customSorting.field())), customSorting.asc());
        return Sorting.of(sorting, SortingItem.ofSimpleAsc(KAT1_5.PORADI), SortingItem.ofSimpleAsc(KAT1_5.ID_EX));
    }
}