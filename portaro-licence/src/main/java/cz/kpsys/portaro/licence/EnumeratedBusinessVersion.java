package cz.kpsys.portaro.licence;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Arrays;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum EnumeratedBusinessVersion implements NumberBasedBusinessVersion {

    V1_0(1.0),
    V1_1(1.1),
    V1_2(1.2),
    V1_3(1.3),
    V2_0(2.0),
    V2_1(2.1),
    V2_2(2.2),
    V2_3(2.3),
    V2_4(2.4),
    V3_0(3.0),
    V3_1(3.1),
    V3_2(3.2);

    @NonNull Double value;

    public boolean isAtLeast(BusinessVersion minimal) {
        if (minimal instanceof NumberBasedBusinessVersion) {
            return value >= ((NumberBasedBusinessVersion) minimal).getNumberValue();
        }
        throw new UnsupportedOperationException("Cannot compare %s with %s".formatted(this, minimal));
    }

    public String getValue() {
        return String.valueOf(value);
    }

    @Override
    public double getNumberValue() {
        return value;
    }

    public static EnumeratedBusinessVersion fromValue(@NonNull String stringValue) {
        return Arrays.stream(EnumeratedBusinessVersion.values())
                .filter(version -> version.getValue().equals(stringValue))
                .findFirst()
                .orElseThrow(() -> new UnsupportedOperationException("Unknown licence version value " + stringValue));
    }

    public static EnumeratedBusinessVersion fromMajorMinor(@NonNull String major, @NonNull String minor) {
        return fromValue("%s.%s".formatted(major, minor));
    }

    public static EnumeratedBusinessVersion fromFullVersionString(@NonNull String versionString) {
        if (!versionString.contains(".")) {
            throw new IllegalArgumentException("Invalid full version string %s".formatted(versionString));
        }
        String major = versionString.split("[.]")[0];
        String minor = versionString.split("[.]")[1];
        return fromMajorMinor(major, minor);
    }

    @Override
    public String toString() {
        return "Licence version " + value;
    }
}
