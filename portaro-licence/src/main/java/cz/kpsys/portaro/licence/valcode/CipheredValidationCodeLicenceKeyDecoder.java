package cz.kpsys.portaro.licence.valcode;

import cz.kpsys.portaro.licence.Licence;
import cz.kpsys.portaro.licence.LicenceKeyDecoder;
import cz.kpsys.portaro.licence.LicenceKeyExpiredException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CipheredValidationCodeLicenceKeyDecoder implements LicenceKeyDecoder {

    @NonNull LicenceKeyDecoder decipheredValidationCodeLicenceKeyDecoder;

    @Override
    public @NonNull Licence decode(@NonNull String ciphered) throws LicenceKeyExpiredException {
        String deciphered = decipher(ciphered);
        log.debug("Ciphered validation code \"{}\" deciphered to \"{}\"", ciphered, deciphered);
        return decipheredValidationCodeLicenceKeyDecoder.decode(deciphered);
    }

    public static String decipher(String cipher) {
        int x = charToInt(cipher, 0);
        int y = charToInt(cipher, 1);

        String decoded = "";
        for (int i = 2; i < cipher.length(); i++) {
            int cislo = charToInt(cipher, i);
            if (isEven(i)) {
                cislo = (10 - ((x + cislo) % 10)) % 10;
            } else {
                cislo = (10 - ((y + cislo) % 10)) % 10;
            }
            decoded = decoded + cislo;
        }
        return decoded;
    }

    private static boolean isEven(int i) {
        return i % 2 == 0;
    }

    private static int charToInt(String input, int idx) {
        return Integer.parseInt(String.valueOf(input.charAt(idx)));
    }

}
