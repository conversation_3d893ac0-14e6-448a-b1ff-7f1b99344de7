package cz.kpsys.portaro.loan.ill;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum SeekedProvisionState implements LabeledIdentified<String> {

    REQUESTED("requested", Texts.ofMessageCoded("commons.neprijata")),
    ACCEPTED_BY_PROVIDER("accepted-by-provider", Texts.ofNative("Schváleno DK")),
    ACCEPTED_BY_SEEKER("accepted-by-seeker", Texts.ofNative("Schváleno ŽK")),
    RESERVED("reserved", Texts.ofNative("Zarezervováno")),
    SENT("sent", Texts.ofNative("Odesláno do DK")),
    RECEIVED("received", Texts.ofNative("Přijato DK")),
    SENT_BACK("sent-back", Texts.ofNative("Odesláno zpět")),
    RECEIVED_BACK("received-back", Texts.ofNative("Přijato zpět"));

    public static final Codebook<SeekedProvisionState, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String id;
    @NonNull Text text;

}
