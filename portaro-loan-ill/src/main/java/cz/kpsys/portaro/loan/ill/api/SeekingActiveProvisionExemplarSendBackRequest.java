package cz.kpsys.portaro.loan.ill.api;

import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.loan.ill.Seeking;
import cz.kpsys.portaro.loan.returning.LoanReturnIgnores;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.With;
import org.springframework.lang.Nullable;

@Form(id = "seekingActiveProvisionExemplarSendBack")
@FormSubmit(path = SeekingApiController.SEND_BACK_ACTIVE_PROVISION_PATH)
@With
public record SeekingActiveProvisionExemplarSendBackRequest(

        @Schema(implementation = String.class, example = Seeking.SCHEMA_EXAMPLE_ID)
        @NotNull
        Seeking id,

        @Nullable Boolean ignoreReturnOnDifferentDepartment,
        @Nullable Boolean ignoreSpecialInformationOnLocation,
        @Nullable Boolean ignoreDocumentIsFromDifferentLocation,
        @Nullable Boolean ignoreSpecialInformationOnCategory,
        @Nullable Boolean ignoreReturningMoreItemsThenIsRented,
        @Deprecated @Nullable Boolean ignoreReturningIll,
        @Nullable Boolean ignoreDocumentInBackwardCataloging,
        @Nullable Boolean ignoreSpecialInformationOnStatus,
        @Nullable Boolean ignoreDocumentHasAttachment,
        @Nullable Boolean ignoreSpecialInformationOnHelperField,
        @Nullable Boolean ignoreLoanHasNote,
        @Nullable Boolean ignorePenaltyGeneration

) implements LoanReturnIgnores {}
