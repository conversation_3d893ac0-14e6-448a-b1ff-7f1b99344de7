package cz.kpsys.portaro.loan.ill.api;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formfield.EnabledFormFields;
import cz.kpsys.portaro.formannotation.annotations.formfield.RequiredFormFields;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.BeforeIntegrityValidation;
import cz.kpsys.portaro.formannotation.annotations.validation.IntegrityValidation;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.scannabletext.ScannableTextEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.singleacceptable.SingleAcceptableEditor;
import cz.kpsys.portaro.formconfig.formfield.FormFieldsNamesContextualFunction;
import cz.kpsys.portaro.loan.ill.Seeking;
import cz.kpsys.portaro.loan.ill.SeekingProvisionProvisionWayType;
import cz.kpsys.portaro.loan.lending.webapi.LendingRequestIgnores;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

import java.util.List;

@Form(id = "seekingActiveProvisionExemplarSend")
@FormSubmit(path = SeekingApiController.SEND_ACTIVE_PROVISION_EXEMPLAR_PATH)
@BeforeIntegrityValidation(bean = "seekingActiveProvisionExemplarSendRequestPreValidationModifier")
@EnabledFormFields(bean = "seekingActiveProvisionExemplarSendRequestEnabledPropsProvider", resolveOnlyExplicitlyInvolved = true)
@RequiredFormFields(bean = "seekingActiveProvisionExemplarSendRequestEnabledPropsProvider", resolveOnlyExplicitlyInvolved = true)
@With
@FieldNameConstants
public record SeekingActiveProvisionExemplarSendRequest(

        @Schema(implementation = String.class, example = Seeking.SCHEMA_EXAMPLE_ID)
        @NotNull
        Seeking id,

        @Schema(implementation = String.class, example = SeekingProvisionProvisionWayType.SCHEMA_EXAMPLE_ID, description = "Způsob dodání exempláře")
        @FormPropertyLabel("Způsob dodání")
        @SingleAcceptableEditor(valuesSourceBean = "seekingProvisionProvisionWayTypeLoader")
        @EnabledFormFields.Involve
        @RequiredFormFields.Involve
        @NotNull(groups = IntegrityValidation.class)
        SeekingProvisionProvisionWayType provisionWay,

        @FormPropertyLabel("Čár.kód/RFID exempláře")
        @ScannableTextEditor(autosubmit = false)
        @EnabledFormFields.Involve
        @RequiredFormFields.Involve
        @Size(min = 1, max = Exemplar.ANY_IDENTIFIER_MAX_LENGTH)
        @NullableNotBlank
        String exemplarIdentifier,

        @Nullable Boolean ignoreUserHasDebt,
        @Nullable Boolean ignoreUserHasNotValidRegistration,
        @Nullable Boolean ignoreDocumentWasLoanedInPast,
        @Nullable Boolean ignoreSpecialInformationOnLocation,
        @Nullable Boolean ignoreSpecialInformationOnStatus,
        @Nullable Boolean ignoreSpecialInformationOnCategory,
        @Nullable Boolean ignoreLendingOnDifferentDepartment,
        @Nullable Boolean ignoreLendingOnDifferentLocation,
        @Nullable Boolean ignoreSpecialInformationOnHelperField,
        @Nullable Boolean ignoreDocumentHasAttachment,
        @Nullable Boolean ignoreOverdueNotices,
        @Nullable Boolean ignoreBlockedTransactions,
        @Nullable Boolean ignoreOtherUserUnprocessedOrder,
        @Nullable Boolean ignoreOtherUserUnsentReservation,
        @Nullable Boolean ignoreExceededLoanLimit,
        @Nullable Boolean ignoreExceededLoanLimitInCategory,
        @Nullable Boolean ignoreDocumentIsAlreadyLentBySameUser,
        @Nullable Boolean ignoreReaderHasReservationOnAnotherItem,
        @Nullable Boolean ignoreDocumentInBackwardCataloging

) implements LendingRequestIgnores {

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class SeekingActiveProvisionExemplarSendRequestPreValidationModifier implements TypedAuthenticatedContextualObjectModifier<SeekingActiveProvisionExemplarSendRequest> {

        @Override
        public SeekingActiveProvisionExemplarSendRequest modify(SeekingActiveProvisionExemplarSendRequest request, Department ctx, UserAuthentication currentAuth) {
            if (request.provisionWay() == null) {
                Seeking seeking = request.id();
                if (!seeking.isEventuallyPhotocopyable()) {
                    request = request.withProvisionWay(SeekingProvisionProvisionWayType.ORIGINAL_EXEMPLAR);
                }
            }
            return request;
        }
    }

    public static class SeekingActiveProvisionExemplarSendRequestEnabledPropsProvider implements FormFieldsNamesContextualFunction<SeekingActiveProvisionExemplarSendRequest, Department> {

        @Override
        public List<String> getOn(SeekingActiveProvisionExemplarSendRequest request, Department ctx) {
            if (request.provisionWay() == null) {
                return List.of(Fields.provisionWay);
            }
            return switch (request.provisionWay()) {
                case ORIGINAL_EXEMPLAR -> List.of(Fields.exemplarIdentifier);
                case PHOTOCOPY -> List.of();
            };
        }
    }
}
