package cz.kpsys.portaro.loan.ill.api;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class SeekingCreationNotValidException extends RuntimeException implements UserFriendlyException {

    @NonNull Text text;

    public SeekingCreationNotValidException(@NonNull String message, @NonNull Text text) {
        super(message);
        this.text = text;
    }
}
