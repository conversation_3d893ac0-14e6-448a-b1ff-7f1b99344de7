package cz.kpsys.portaro.loan.ill.persist;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.LoanSetting;
import cz.kpsys.portaro.loan.ill.Seeking;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ConditionalIllSeekingsLoader implements IllSeekingsLoader {

    @NonNull ContextualProvider<Department, @NonNull LoanSetting> loanSettingProvider;
    @NonNull IllSeekingsLoader delegate;

    private boolean isDisabled(Department currentDepartment) {
        return !loanSettingProvider.getOn(currentDepartment).isEnabled();
    }

    @Override
    public List<Seeking> getSeekingsByRequester(User lender, RangePaging paging, Department currentDepartment, UserAuthentication currentAuth) {
        if (isDisabled(currentDepartment)) {
            return null;
        }
        return delegate.getSeekingsByRequester(lender, paging, currentDepartment, currentAuth);
    }

    @Override
    public int getSeekingsByRequesterSize(User lender, Department currentDepartment, UserAuthentication currentAuth) {
        if (isDisabled(currentDepartment)) {
            return 0;
        }
        return delegate.getSeekingsByRequesterSize(lender, currentDepartment, currentAuth);
    }

    @Override
    public List<Seeking> getSeekingsByProvider(User lender, RangePaging paging, Department currentDepartment, UserAuthentication currentAuth) {
        if (isDisabled(currentDepartment)) {
            return null;
        }
        return delegate.getSeekingsByProvider(lender, paging, currentDepartment, currentAuth);
    }

    @Override
    public int getSeekingsByProviderSize(User lender, Department currentDepartment, UserAuthentication currentAuth) {
        if (isDisabled(currentDepartment)) {
            return 0;
        }
        return delegate.getSeekingsByProviderSize(lender, currentDepartment, currentAuth);
    }

    @Override
    public List<Seeking> getProvidedSeekingsBySeeker(User seekerUser, RangePaging paging, Department ctx, UserAuthentication currentAuth) {
        if (isDisabled(ctx)) {
            return null;
        }
        return delegate.getProvidedSeekingsBySeeker(seekerUser, paging, ctx, currentAuth);
    }

    @Override
    public int getProvidedSeekingsBySeekerSize(User seekerUser, Department ctx, UserAuthentication currentAuth) {
        if (isDisabled(ctx)) {
            return 0;
        }
        return delegate.getProvidedSeekingsBySeekerSize(seekerUser, ctx, currentAuth);
    }
}
