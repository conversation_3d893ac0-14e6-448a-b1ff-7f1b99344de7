package cz.kpsys.portaro.loan.ill.persist;

import cz.kpsys.portaro.database.RangePagingResultSetExtractor;
import cz.kpsys.portaro.loan.ill.SeekingConstants;
import cz.kpsys.portaro.search.AbstractSpringDbSearchLoader;
import cz.kpsys.portaro.search.Chunk;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.List;

import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.LoanDb.ILL_PROVISION.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbIllProvisionEntitySearchLoader extends AbstractSpringDbSearchLoader<MapBackedParams, IllProvisionEntity, RangePaging> {

    @NonNull RowMapper<IllProvisionEntity> rowMapper;

    public SpringDbIllProvisionEntitySearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                                  @NonNull QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory);
        this.rowMapper = new IllProvisionEntityRowMapper();
    }


    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.from(TABLE);

        if (p.hasNotNull(SeekingConstants.SearchParams.SEEKING_ID)) {
            List<Integer> realizationIds = p.get(SeekingConstants.SearchParams.SEEKING_ID);
            if (realizationIds.isEmpty()) {
                return false;
            }
            sq.where().and().in(TC(TABLE, SEEKING_ID), realizationIds);
        }

        return true;
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        return Sorting.ofAsc(ORDER_NUMBER);
    }

    @Override
    protected ResultSetExtractor<Chunk<IllProvisionEntity, RangePaging>> createResultSetExtractor(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @NonNull RangePaging paging, @NonNull Sorting sorting) {
        return new RangePagingResultSetExtractor<>(rowMapper, paging);
    }

}
