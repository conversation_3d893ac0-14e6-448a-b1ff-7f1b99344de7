package cz.kpsys.portaro.loan.ill.process;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.ill.IllSeekingProvisionList;
import cz.kpsys.portaro.loan.ill.Seeking;
import cz.kpsys.portaro.loan.ill.SeekingDesiredExemplar;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.user.Institution;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SeekedSeekingCreator implements Saver<SeekedSeekingCreationCommand, Seeking> {

    @NonNull ContextualProvider<Department, @NonNull Integer> seekingIdGenerator;
    @NonNull ContextualProvider<Department, Institution> nullableInstitutionContextualProvider;
    @NonNull Saver<Seeking, ?> seekingSaver;
    @NonNull RecordEditationFactory recordEditationFactory;

    @Transactional
    @Override
    public @NonNull Seeking save(@NonNull SeekedSeekingCreationCommand command) throws DataAccessException {
        command = saveRecordIfNotPersisted(command);

        Seeking seeking = createSeeking(command);
        seekingSaver.save(seeking);
        return seeking;
    }

    private SeekedSeekingCreationCommand saveRecordIfNotPersisted(@NonNull SeekedSeekingCreationCommand command) {
        Record document = command.document();
        if (!DataUtils.isPersistedId(document.getKindedId())) {
            RecordEditation editation = recordEditationFactory
                    .on(command.department())
                    .ofExisting(document)
                    .build(command.currentAuth());
            editation.saveIfModified(command.department(), command.currentAuth());
            command = command.withDocument(editation.getRecord());
        }
        return command;
    }

    private Seeking createSeeking(SeekedSeekingCreationCommand command) {
        Integer seekingId = seekingIdGenerator.getOn(command.department());

        return new Seeking(
                seekingId,
                seekingId,
                false,
                command.requester(),
                getMeAsSeeker(command.department()),
                null,
                new SeekingDesiredExemplar(
                        command.document(),
                        command.volume(),
                        command.page(),
                        command.article()
                ),
                command.eventuallyPhotocopyable(),
                command.eventuallyReservable(),
                command.eventuallyOnSiteLendable(),
                command.eventuallyAbroadDeliverable(),
                command.note(),
                command.seekerReferenceId(),
                command.requesterObtainDeadlineDate(),
                command.department(),
                Instant.now(),
                Instant.now(),
                false,
                null,
                IllSeekingProvisionList.ofEmpty(seekingId)
        );
    }

    @Nullable
    private Institution getMeAsSeeker(Department ctx) {
        return nullableInstitutionContextualProvider.getOn(ctx);
    }

}
