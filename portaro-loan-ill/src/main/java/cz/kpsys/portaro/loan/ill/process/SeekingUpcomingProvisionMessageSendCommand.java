package cz.kpsys.portaro.loan.ill.process;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.ill.Seeking;
import lombok.NonNull;
import lombok.With;
import org.springframework.lang.Nullable;

import java.time.Instant;

@With
public record SeekingUpcomingProvisionMessageSendCommand(

        @NonNull
        Seeking seeking,

        @Nullable
        @NullableNotBlank
        String message,

        @NonNull
        Instant date,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth

) {}
