package cz.kpsys.portaro.loan;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class ByExemplarQLoanPageSearchLoader implements PageSearchLoader<MapBackedParams, Loan, RangePaging> {

    @NonNull ParameterizedSearchLoader<MapBackedParams, Loan> loanSearchLoader;
    @NonNull PageSearchLoader<MapBackedParams, Integer, RangePaging> exemplarIdSearchLoader;

    @Override
    public Chunk<Loan, RangePaging> getPage(RangePaging paging, SortingItem customSorting, MapBackedParams params) {
        customSorting = ObjectUtil.firstNotNull(customSorting, SortingItem.ofSimpleDesc(Loan.LEND_DATE));
        return loanSearchLoader.getPage(paging.pageSize() == 0 ? RangePaging.forAll() : paging, customSorting, getLoansMapBackedParams(params, paging));
    }

    @Override
    public int getTotalElements(MapBackedParams params) {
        return loanSearchLoader.getTotalElements(getLoansMapBackedParams(params, RangePaging.forAll()));
    }

    private MapBackedParams getLoansMapBackedParams(MapBackedParams params, RangePaging rangePaging) {
        String exemplarQ = params.get(LoanConstants.SearchParams.EXEMPLAR_Q);

        MapBackedParams exemplarParams = MapBackedParams.build(StaticParamsModifier.of(CoreSearchParams.Q, exemplarQ));
        List<Integer> exemplarIds = exemplarIdSearchLoader.getPage(rangePaging.pageSize() == 0 ? RangePaging.forAll() : RangePaging.of(rangePaging.pageNumber(), rangePaging.pageSize() * 2), exemplarParams).getItems();

        return MapBackedParams.build(StaticParamsModifier.of(
                LoanConstants.SearchParams.EXEMPLAR, exemplarIds,
                LoanConstants.SearchParams.LOAN_STATE, LoanState.allExemplarAssignedActive() //we want only active loans with assigned exemplar
        ));
    }
}
