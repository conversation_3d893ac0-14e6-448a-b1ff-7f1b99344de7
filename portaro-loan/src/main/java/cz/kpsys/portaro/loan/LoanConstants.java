package cz.kpsys.portaro.loan;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.exemplar.ExemplarConstants;
import cz.kpsys.portaro.exemplar.loancategory.LoanCategory;
import cz.kpsys.portaro.loan.lending.extern.ExternalLoanService;
import cz.kpsys.portaro.property.JavatypedDatatypedProperty;
import cz.kpsys.portaro.property.PropertyFactory;
import cz.kpsys.portaro.search.SearchParamsConstants;
import cz.kpsys.portaro.search.field.BasicSearchField;
import cz.kpsys.portaro.template.BasicTemplateDescriptor;
import cz.kpsys.portaro.template.TemplateDescriptor;
import cz.kpsys.portaro.user.BasicUser;
import org.springframework.core.convert.TypeDescriptor;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.datatype.Datatype.scalar;

public class LoanConstants {

    public static class Datatype {
        public static final ScalarDatatype LOAN_STATE = scalar("LOAN_STATE", LoanState.class, String.class);
        public static final ScalarDatatype LOAN_REQUEST_ID = scalar("LOAN_REQUEST_ID", Integer.class);
        public static final ScalarDatatype LOAN_CATEGORY = scalar("VYP_KAT", LoanCategory.class, String.class);
        public static final ScalarDatatype EXTERNAL_LOAN_SERVICE = scalar("EXTERNAL_LOAN_SERVICE", ExternalLoanService.class, String.class);
    }

    public static class SearchFields {

        public static final BasicSearchField RECORD_LOAN_SOURCE = BasicSearchField.createBuiltIn("recordLoanSource", Datatype.EXTERNAL_LOAN_SERVICE, Texts.ofMessageCoded("loan.RecordLoanSource"));
    }

    public static class SearchParams implements SearchParamsConstants {
        public static final JavatypedDatatypedProperty<List<BasicUser>> LENDER = PropertyFactory.ofSearchProperty("lender", Texts.ofMessageCoded("exemplar.holder"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.USER), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(BasicUser.class)));

        public static final JavatypedDatatypedProperty<List<LoanState>> LOAN_STATE = PropertyFactory.ofSearchProperty("loanState", Texts.ofMessageCoded("loan.State"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.LOAN_STATE), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(LoanState.class)));

        public static final JavatypedDatatypedProperty<List<Integer>> EXEMPLAR = PropertyFactory.ofSearchProperty("exemplar", Texts.ofMessageCoded("loan.Exemplar"), cz.kpsys.portaro.datatype.Datatype.listOf(ExemplarConstants.Datatype.EXEMPLAR), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Integer.class)));

        public static final JavatypedDatatypedProperty<List<Integer>> LOAN_REQUEST_ID = PropertyFactory.ofSearchProperty("loanRequestId", Texts.ofMessageCoded("loan.Request"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.LOAN_REQUEST_ID), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Integer.class)));

        public static final JavatypedDatatypedProperty<List<UUID>> SHIPMENT_ITEM = PropertyFactory.ofSearchProperty("shipmentItem", Texts.ofMessageCoded("loan.Shipment"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.UUID), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(UUID.class)));

        public static final JavatypedDatatypedProperty<Instant> LEND_FROM_DATE = PropertyFactory.ofSearchProperty("lendFromDate", Texts.ofMessageCoded("loan.search.LendFromDate"), CoreConstants.Datatype.DATETIME, TypeDescriptor.valueOf(Instant.class));
        public static final JavatypedDatatypedProperty<Instant> LEND_TO_DATE = PropertyFactory.ofSearchProperty("lendToDate", Texts.ofMessageCoded("loan.search.LendToDate"), CoreConstants.Datatype.DATETIME, TypeDescriptor.valueOf(Instant.class));

        public static final JavatypedDatatypedProperty<Boolean> INCLUDE_NOT_SHIPPING = PropertyFactory.ofSearchProperty("includeNotShipping", Texts.ofMessageCoded("loan.IncludingNonBox"), CoreConstants.Datatype.BOOLEAN, TypeDescriptor.valueOf(Boolean.class));
        public static final JavatypedDatatypedProperty<Boolean> INCLUDE_REMINDED = PropertyFactory.ofSearchProperty("includeReminded", Texts.ofNative("loan.IncludingReminded"), CoreConstants.Datatype.BOOLEAN, TypeDescriptor.valueOf(Boolean.class));
        public static final JavatypedDatatypedProperty<Boolean> INCLUDE_NOT_REMINDED = PropertyFactory.ofSearchProperty("includeNotReminded", Texts.ofNative("loan.IncludingNotReminded"), CoreConstants.Datatype.BOOLEAN, TypeDescriptor.valueOf(Boolean.class));
        public static final JavatypedDatatypedProperty<String> EXEMPLAR_Q = PropertyFactory.ofSearchProperty("exemplarQ", Texts.ofNative("loan.Identifier"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.valueOf(String.class));
    }

    public static class Template {
        public static final TemplateDescriptor TEMPLATE_ACTIVE_LOANS_PRINT = new BasicTemplateDescriptor("html", "active-loans-print", "vtl");
        public static final TemplateDescriptor TEMPLATE_LOAN_CONFIRMATION_PRINT = new BasicTemplateDescriptor("html", "loan-confirmation-print", "vtl");
        public static final TemplateDescriptor TEMPLATE_EXTERNAL_LOAN_READY_MAIL = new BasicTemplateDescriptor("html", "external-loan-ready-mail", "vtl");
    }
}
