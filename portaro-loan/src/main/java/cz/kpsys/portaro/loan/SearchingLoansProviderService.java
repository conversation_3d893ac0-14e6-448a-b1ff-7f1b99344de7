package cz.kpsys.portaro.loan;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.department.AuthContextualDepartmentsLoader;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SearchingLoansProviderService implements LoansProviderService {

    @NonNull ContextualProvider<Department, @NonNull LoanSetting> loanSettingProvider;
    @NonNull PageSearchLoader<MapBackedParams, Loan, RangePaging> loanSearchLoader;
    @NonNull AuthContextualDepartmentsLoader loanAuthContextualDepartmentsLoader;
    @NonNull List<UUID> forbiddenRecordIds;


    private List<Loan> getContent(MapBackedParams p, Range range, SortingItem customSorting) {
        if (p == null) {
            return List.of();
        }
        return loanSearchLoader.getPage(Paging.ofRange(range), customSorting, p).getItems();
    }

    private int getSize(MapBackedParams p) {
        if (p == null) {
            return 0;
        }
        return loanSearchLoader.getTotalElements(p);
    }



    @Override
    public List<Loan> getLoans(@NonNull User lender, @NonNull List<LoanState> loanStates, @NonNull SortingItem customSorting, @NonNull Range range, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return getContent(getParams(lender, loanStates, currentDepartment, currentAuth), range, customSorting);
    }

    @Override
    public int getLoansSize(@NonNull User lender, @NonNull List<LoanState> loanStates, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return getSize(getParams(lender, loanStates, currentDepartment, currentAuth));
    }

    private MapBackedParams getParams(@NonNull User lender, @NonNull List<LoanState> loanStates, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return MapBackedParams.build(p -> {
            p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, forbiddenRecordIds);
            p.set(CoreSearchParams.DEPARTMENT, loanAuthContextualDepartmentsLoader.getAll(currentAuth, currentDepartment));
            p.set(LoanConstants.SearchParams.LENDER, List.of(lender));
            p.set(LoanConstants.SearchParams.LOAN_STATE, loanStates);
        });
    }
    
    
    
    @Override
    public List<Loan> getActiveLoans(User lender, Range range, Department currentDepartment, UserAuthentication currentAuth) {
        return getContent(getLoansParams(lender, currentDepartment, currentAuth), range, SortingItem.ofSimpleDesc(Loan.LEND_DATE));
    }

    @Override
    public int getActiveLoansSize(User lender, Department currentDepartment, UserAuthentication currentAuth) {
        return getSize(getLoansParams(lender, currentDepartment, currentAuth));
    }

    private MapBackedParams getLoansParams(User lender, Department currentDepartment, UserAuthentication currentAuth) {
        if (!loanSetting(currentDepartment).isEnabled()) {
            return null;
        }
        return getParams(lender, List.of(LoanState.LENT), currentDepartment, currentAuth);
    }





    @Override
    public List<Loan> getWaitingLoans(User lender, Range range, Department currentDepartment, UserAuthentication currentAuth) {
        return getContent(getWaitingLoansParams(lender, currentDepartment, currentAuth), range, SortingItem.ofSimpleDesc(Loan.REQUEST_DATE));
    }

    @Override
    public int getWaitingLoansSize(User lender, Department currentDepartment, UserAuthentication currentAuth) {
        return getSize(getWaitingLoansParams(lender, currentDepartment, currentAuth));
    }

    private MapBackedParams getWaitingLoansParams(User lender, Department currentDepartment, UserAuthentication currentAuth) {
        if (!loanSetting(currentDepartment).isStandardReservingEnabled() &&
            !loanSetting(currentDepartment).isMailReservingEnabled() &&
            !loanSetting(currentDepartment).isStandardOrderingEnabled() &&
            !loanSetting(currentDepartment).isMailOrderingEnabled()) {
            return null;
        }
        return getParams(lender, LoanState.allWaiting(), currentDepartment, currentAuth);
    }

    @Override
    public List<Loan> getEndedLoans(User lender, Range range, Department currentDepartment, UserAuthentication currentAuth) {
        return getContent(getRelicsParams(lender, currentDepartment, currentAuth), range, SortingItem.ofSimpleDesc(Loan.END_DATE));
    }

    @Override
    public int getEndedLoansSize(User lender, Department currentDepartment, UserAuthentication currentAuth) {
        return getSize(getRelicsParams(lender, currentDepartment, currentAuth));
    }

    private MapBackedParams getRelicsParams(User lender, Department currentDepartment, UserAuthentication currentAuth) {
        if (!loanSetting(currentDepartment).isEnabled()) {
            return null;
        }
        return getParams(lender, List.of(LoanState.RETURNED, LoanState.CANCELLED_RESERVATION), currentDepartment, currentAuth);
    }

    private LoanSetting loanSetting(Department currentDepartment) {
        return loanSettingProvider.getOn(currentDepartment);
    }
    
}
