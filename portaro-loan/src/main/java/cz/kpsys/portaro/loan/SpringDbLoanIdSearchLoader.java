package cz.kpsys.portaro.loan;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.database.RangePagingResultSetExtractor;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.search.AbstractSpringDbSearchLoader;
import cz.kpsys.portaro.search.Chunk;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.Brackets;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static cz.kpsys.portaro.commons.db.QueryUtils.AS;
import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.commons.util.ListUtil.getListOfIds;
import static cz.kpsys.portaro.databasestructure.LoanDb.POZ_REZ;
import static cz.kpsys.portaro.databasestructure.LoanDb.VYPUC;
import static cz.kpsys.portaro.exemplar.ExemplarConstants.SearchParams.LOCATION;
import static cz.kpsys.portaro.record.RecordConstants.SearchParams.FORBIDDEN_RECORD;
import static cz.kpsys.portaro.record.RecordConstants.SearchParams.RECORD;
import static cz.kpsys.portaro.search.CoreSearchParams.DEPARTMENT;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbLoanIdSearchLoader extends AbstractSpringDbSearchLoader<MapBackedParams, LoanId, RangePaging> {

    private static final Map<String, String> PROPS_TO_DB_COLUMNS_MAP = new HashMap<>();
    private static final String V = "v";

    static {
        PROPS_TO_DB_COLUMNS_MAP.put(Loan.REQUEST_DATE, VYPUC.DAT_VYP);
        PROPS_TO_DB_COLUMNS_MAP.put(Loan.RESERVATION_DATE, VYPUC.DAT_VYP);
        PROPS_TO_DB_COLUMNS_MAP.put(Loan.LEND_DATE, VYPUC.DAT_VYP);
        PROPS_TO_DB_COLUMNS_MAP.put(Loan.END_DATE, VYPUC.DAT_VR);
        PROPS_TO_DB_COLUMNS_MAP.put(Loan.QUEUE_NUMBER, VYPUC.CIS_REZE);
    }

    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull DatabaseProperties databaseProperties;

    public SpringDbLoanIdSearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                      @NonNull QueryFactory queryFactory,
                                      @NonNull DepartmentAccessor departmentAccessor,
                                      @NonNull DatabaseProperties databaseProperties) {
        super(jdbcTemplate, queryFactory);
        this.departmentAccessor = departmentAccessor;
        this.databaseProperties = databaseProperties;
    }


    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        boolean useVypuc2 = resolveVypuc2Usage(p);
        double vypuc2ConditionSufficiency = 0; //heuristic value for check if we can search in vypuc2 that it will not take whole life to get results
        String idColumn = useVypuc2 ? VYPUC.ID_VYPUC : VYPUC.FK_VYPUC;

        String sortingColumn = defaultOrCustomSorting(customSorting).orElseThrow().field();

        if (count) {
            sq.selectCount(TC(V, idColumn));
        } else {
            sq.selectDistinct(
                    TC(V, idColumn),
                    TC(V, VYPUC.DATCAS), // when we're sorting by this field, we must include it also in select expression
                    sortingColumn // when we're sorting by this field, we must include it also in select expression
            );
        }

        sq.from(AS(useVypuc2 ? VYPUC.TABLE_ALL : VYPUC.TABLE_ACTIVE, V));


        if (p.hasLength(FORBIDDEN_RECORD)) {
            sq.where().and().notIn(TC(V, VYPUC.RECORD_ID), p.get(FORBIDDEN_RECORD));
        }

        if (p.hasNotNull(RECORD)) {
            if (!p.hasLength(RECORD)) {
                return false;
            }
            vypuc2ConditionSufficiency += 1;
            sq.where().and().in(TC(V, VYPUC.RECORD_ID), p.get(RECORD));
        }

        if (p.hasNotNull(LoanConstants.SearchParams.EXEMPLAR)) {
            if (!p.hasLength(LoanConstants.SearchParams.EXEMPLAR)) {
                return false;
            }
            vypuc2ConditionSufficiency += 1;
            sq.where().and().in(TC(V, VYPUC.FK_EX), p.get(LoanConstants.SearchParams.EXEMPLAR));
        }

        if (p.hasNotNull(LoanConstants.SearchParams.LOAN_REQUEST_ID)) {
            if (!p.hasLength(LoanConstants.SearchParams.LOAN_REQUEST_ID)) {
                return false;
            }
            vypuc2ConditionSufficiency += 1;
            sq.joinOrExists(queryFactory, V, idColumn, POZ_REZ.TABLE, POZ_REZ.FK_VYPUC, "loan_request", (where, alias) ->
                    where.and().in(TC(alias, POZ_REZ.ID_POZREZ), p.get(LoanConstants.SearchParams.LOAN_REQUEST_ID)));
        }

        if (p.hasNotNull(LoanConstants.SearchParams.SHIPMENT_ITEM)) {
            if (!p.hasLength(LoanConstants.SearchParams.SHIPMENT_ITEM)) {
                return false;
            }
            vypuc2ConditionSufficiency += 1;
            sq.where().and().in(TC(V, VYPUC.SHIPMENT_ITEM_ID), p.get(LoanConstants.SearchParams.SHIPMENT_ITEM));
        }

        if (p.hasNotNull(LoanConstants.SearchParams.INCLUDE_NOT_SHIPPING) && !p.get(LoanConstants.SearchParams.INCLUDE_NOT_SHIPPING)) {
            vypuc2ConditionSufficiency += 0.25;
            sq.where().and().isNotNull(TC(V, VYPUC.SHIPMENT_ITEM_ID));
        }

        if (p.hasNotNull(LoanConstants.SearchParams.INCLUDE_REMINDED) && !p.get(LoanConstants.SearchParams.INCLUDE_REMINDED)) {
            vypuc2ConditionSufficiency += 0.25;
            sq.where().and().eq(TC(V, VYPUC.CIS_UPOM), 0);
        }

        if (p.hasNotNull(LoanConstants.SearchParams.INCLUDE_NOT_REMINDED) && !p.get(LoanConstants.SearchParams.INCLUDE_NOT_REMINDED)) {
            vypuc2ConditionSufficiency += 0.25;
            sq.where().and().gt(TC(V, VYPUC.CIS_UPOM), 0);
        }

        if (p.hasNotNull(LoanConstants.SearchParams.LEND_FROM_DATE)) {
            vypuc2ConditionSufficiency += 0.25;
            sq.where().and().gtEq(TC(V, VYPUC.DAT_VYP), p.get(LoanConstants.SearchParams.LEND_FROM_DATE));
        }

        if (p.hasNotNull(LoanConstants.SearchParams.LEND_TO_DATE)) {
            vypuc2ConditionSufficiency += 0.25;
            sq.where().and().ltEq(TC(V, VYPUC.DAT_VYP), p.get(LoanConstants.SearchParams.LEND_TO_DATE));
        }

        if (p.hasNotNull(DEPARTMENT) && !departmentAccessor.coversAllDepartments(p.get(DEPARTMENT))) {
            if (!p.hasLength(DEPARTMENT)) {
                return false;
            }
            vypuc2ConditionSufficiency += 0.5;
            sq.where().and().in(TC(V, VYPUC.FK_PUJC), getListOfIds(p.get(DEPARTMENT)));
        }

        if (p.hasNotNull(LOCATION)) {
            if (!p.hasLength(LOCATION)) {
                return false;
            }
            vypuc2ConditionSufficiency += 0.25;
            sq.where().and().in(TC(V, VYPUC.FK_LOKACE), getListOfIds(p.get(LOCATION)));
        }

        if (p.hasNotNull(LoanConstants.SearchParams.LENDER)) {
            if (!p.hasLength(LoanConstants.SearchParams.LENDER)) {
                return false;
            }
            vypuc2ConditionSufficiency += 1;
            sq.where().and().in(TC(V, VYPUC.FK_UZIV_CTEN), getListOfIds(p.get(LoanConstants.SearchParams.LENDER)));
        }

        if (p.hasNotNull(LoanConstants.SearchParams.LOAN_STATE)) {
            if (!p.hasLength(LoanConstants.SearchParams.LOAN_STATE)) {
                return false;
            }
            vypuc2ConditionSufficiency += (0.5 / p.get(LoanConstants.SearchParams.LOAN_STATE).size());
            Brackets disjunction = sq.where().and().brackets();
            p.get(LoanConstants.SearchParams.LOAN_STATE).forEach(state -> {
                switch (state) {
                    case ON_WAITING_LIST:
                        disjunction.or()
                                .gtEq(TC(V, VYPUC.CIS_REZE), Loan.CIS_REZE_REZE_MIN)
                                .and()
                                .ltEq(TC(V, VYPUC.CIS_REZE), Loan.CIS_REZE_REZE_MAX);
                        break;
                    case UNSENT_RESERVATION:
                        disjunction.or().eq(TC(V, VYPUC.CIS_REZE), Loan.CIS_REZE_UNSENT_RESERVATION);
                        break;
                    case SENT_RESERVATION:
                        disjunction.or().eq(TC(V, VYPUC.CIS_REZE), Loan.CIS_REZE_SENT_RESERVATION);
                        break;
                    case UNPROCESSED_ORDER:
                        disjunction.or().eq(TC(V, VYPUC.CIS_REZE), Loan.CIS_REZE_UNPROCESSED_ORDER);
                        break;
                    case PROCESSED_ORDER:
                        disjunction.or().eq(TC(V, VYPUC.CIS_REZE), Loan.CIS_REZE_PROCESSED_ORDER);
                        break;
                    case PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR:
                        disjunction.or().eq(TC(V, VYPUC.CIS_REZE), Loan.CIS_REZE_PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR);
                        break;
                    case LENT:
                        disjunction.or()
                                .eq(TC(V, VYPUC.CIS_REZE), Loan.CIS_REZE_LOAN)
                                .and()
                                .isNull(TC(V, VYPUC.DAT_VR));
                        break;
                    case RETURNED:
                        disjunction.or()
                                .eq(TC(V, VYPUC.CIS_REZE), Loan.CIS_REZE_LOAN)
                                .and()
                                .isNotNull(TC(V, VYPUC.DAT_VR));
                        break;
                    case CANCELLED_RESERVATION:
                        disjunction.or()
                                .notEq(TC(V, VYPUC.CIS_REZE), Loan.CIS_REZE_LOAN)
                                .and()
                                .isNotNull(TC(V, VYPUC.DAT_VR));
                        break;
                    default:
                        throw new UnsupportedOperationException("State %s is not supported in loan item search".formatted(state));
                }
            });
        }

        if (useVypuc2 && vypuc2ConditionSufficiency < 1) {
            throw new UnsupportedOperationException("Search conditions not sufficient for vypuc2 use, please narrow search criteria or search active loans only");
        }

        return true;
    }

    @Override
    protected Optional<SortingItem> defaultOrCustomSorting(@Nullable SortingItem customSorting) {
        SortingItem sorting = ObjectUtil.firstNotNull(customSorting, SortingItem.ofSimpleAsc(Loan.LEND_DATE));
        return Optional.of(SortingItem.ofSimpleFieldAndOrder(TC(V, PROPS_TO_DB_COLUMNS_MAP.get(sorting.field())), sorting.asc()));
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        boolean useVypuc2 = resolveVypuc2Usage(p);
        String idColumn = useVypuc2 ? VYPUC.ID_VYPUC : VYPUC.FK_VYPUC;

        if (customSorting == null || customSorting.asc()) {
            return Sorting.ofAsc(TC(V, VYPUC.DATCAS), TC(V, idColumn));
        }
        return Sorting.ofDesc(TC(V, VYPUC.DATCAS), TC(V, idColumn));
    }

    @Override
    protected @NonNull Sorting finalDbSorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams mapBackedParams) {
        return Sorting.of(defaultOrCustomSorting(customSorting).orElseThrow()).append(mandatorySorting(customSorting, mapBackedParams));
    }

    private boolean resolveVypuc2Usage(MapBackedParams p) {
        return databaseProperties.getType().equals(DatabaseProperties.DBTYPE_POSTGRES) || !p.hasNotNull(LoanConstants.SearchParams.LOAN_STATE) || p.get(LoanConstants.SearchParams.LOAN_STATE).stream().anyMatch(LoanState::isFinished);
    }

    @Override
    protected ResultSetExtractor<Chunk<LoanId, RangePaging>> createResultSetExtractor(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @NonNull RangePaging paging, @NonNull Sorting sorting) {
        return new RangePagingResultSetExtractor<>((rs, _) -> {
            boolean relic = DbUtils.containsColumn(rs, VYPUC.ID_VYPUC);
            Integer loanRealizationId = rs.getInt(relic ? VYPUC.ID_VYPUC : VYPUC.FK_VYPUC);
            return LoanId.ofSure(loanRealizationId, relic);
        }, paging);
    }

}
