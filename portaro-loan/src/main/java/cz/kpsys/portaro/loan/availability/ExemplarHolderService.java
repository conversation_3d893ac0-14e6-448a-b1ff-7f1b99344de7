package cz.kpsys.portaro.loan.availability;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanConstants;
import cz.kpsys.portaro.loan.LoanState;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ExemplarHolderService {

    @NonNull ParameterizedSearchLoader<MapBackedParams, Loan> loanSearchLoader;

    public Map<Integer, Loan> batchFindHoldingLoans(List<Integer> exemplarIds) {
        List<Loan> activeLoans = loanSearchLoader.getContent(RangePaging.forAll(), SortingItem.ofSimpleAsc(Loan.QUEUE_NUMBER), StaticParamsModifier.of(
                LoanConstants.SearchParams.EXEMPLAR, exemplarIds,
                LoanConstants.SearchParams.LOAN_STATE, LoanState.allNonRelic()
        ));

        Map<Integer, List<Loan>> groupedLoans = activeLoans.stream()
                .collect(Collectors.groupingBy(loan -> loan.getExemplar().getId()));

        // TODO: Tohle dvoufázové mapování by šlo zahodit, pokud bychom měli collector pro nalezení holdera
        var result = new HashMap<Integer, Loan>();
        for (Integer exemplarId : groupedLoans.keySet()) {
            var holder = selectHolder(groupedLoans.get(exemplarId));
            holder.ifPresent(loan -> result.put(exemplarId, loan));
        }
        return result;
    }

    /**
     * Najde výpůjčku/rezervaci/objednávku, která má nejvyšší prioritu pro zpracování.
     *
     * @param exemplarId id kontrolovaného exempláře
     *
     * @return výpůjčka/rezervace/objednávka s nejvyšší prioritou.
     */
    public Optional<Loan> findHoldingLoan(@NonNull Integer exemplarId) {
        List<Loan> activeLoans = loanSearchLoader.getContent(RangePaging.forAll(), SortingItem.ofSimpleAsc(Loan.QUEUE_NUMBER), StaticParamsModifier.of(
                LoanConstants.SearchParams.EXEMPLAR, List.of(exemplarId),
                LoanConstants.SearchParams.LOAN_STATE, LoanState.allNonRelic()
        ));
        if (activeLoans.isEmpty()) {
            return Optional.empty();
        }

        return selectHolder(activeLoans);
    }

    private static Optional<Loan> selectHolder(List<Loan> exemplarLoans) {
        Loan higherPriority = null;
        Loan lowerPriority = null;
        Loan queueMember = null;

        for (Loan loan : exemplarLoans) {
            switch (loan.getState()) {
                case LENT:
                    return Optional.of(loan); // LENT stav má absolutní přednost, netřeba hledat dál
                case PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR:
                case PROCESSED_ORDER:
                case SENT_RESERVATION:
                    higherPriority = maxQueueNumber(higherPriority, loan);
                    break;
                case UNPROCESSED_ORDER:
                case UNSENT_RESERVATION:
                    lowerPriority = maxQueueNumber(lowerPriority, loan);
                    break;
                case ON_WAITING_LIST:
                    queueMember = minQueueNumber(queueMember, loan);
                    break;
            }
        }

        return ObjectUtil.firstNotNullOptional(higherPriority, lowerPriority, queueMember);
    }

    private static Loan maxQueueNumber(Loan loan1, Loan loan2) {
        if (loan1 == null) {
            return loan2;
        }
        if (loan2 == null) {
            return loan1;
        }
        if (loan2.getQueueNumber() > loan1.getQueueNumber()) {
            return loan2;
        } else {
            return loan1;
        }
    }

    private static Loan minQueueNumber(Loan loan1, Loan loan2) {
        if (loan1 == null) {
            return loan2;
        }
        if (loan2 == null) {
            return loan1;
        }
        if (loan2.getQueueNumber() < loan1.getQueueNumber()) {
            return loan2;
        } else {
            return loan1;
        }
    }

}
