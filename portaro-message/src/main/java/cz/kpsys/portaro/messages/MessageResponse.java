package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.messages.constants.MessageSeverity;
import cz.kpsys.portaro.messages.constants.MessageTopic;
import cz.kpsys.portaro.messages.dto.ContentType;
import cz.kpsys.portaro.record.LabeledRecordRef;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

public record MessageResponse(

        @NonNull
        UUID id,

        @NonNull
        String content,

        @NonNull
        MessageTopic topic,

        @NonNull
        MessageSeverity severity,

        @NonNull
        BasicUser senderUser,

        @Nullable
        Record thread,

        @Nullable
        BasicUser targetUser,

        @Nullable
        ContentType contentType,

        @Nullable
        Integer directoryId,

        @NonNull
        Instant creationDate,

        @Nullable
        Instant activationDate,

        @NonNull
        List<LabeledRecordRef> mentions,

        @NonNull
        List<IdentifiedFile> attachments

) {
}
