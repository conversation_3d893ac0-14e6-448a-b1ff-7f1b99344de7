package cz.kpsys.portaro.messages.constants;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum MessageSendingsSendStatus  implements LabeledIdentified<String> {

    SENT("sent", Texts.ofMessageCoded("message.sentStatus.Sent")),
    UNSENT("unsent", Texts.ofMessageCoded("message.sentStatus.Unsent"));

    public static final Codebook<MessageSendingsSendStatus, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String id;
    @NonNull Text text;

}
