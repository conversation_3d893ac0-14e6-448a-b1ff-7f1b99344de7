package cz.kpsys.portaro.messages.participants;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.messages.thread.ThreadType;
import cz.kpsys.portaro.messages.thread.ThreadTypeResolver;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ThreadParticipantLoader {

    @NonNull ThreadParticipantEntityLoader threadParticipantRepository;
    @NonNull ThreadTypeResolver threadTypeResolver;

    public @NonNull ThreadParticipant getExceptMain(@NonNull Record thread, @NonNull BasicUser participant) {
        return convert(threadParticipantRepository.getByThreadAndParticipantAndTypeIn(thread.getId(), participant.getRid(), List.of(ThreadParticipantType.PARTICIPANT.getId(), ThreadParticipantType.MENTION.getId(), ThreadParticipantType.PRIVATE.getId())).orElseThrow());
    }

    public @NonNull Optional<ThreadParticipant> getOptionalParticipantOrMention(@NonNull Record thread, @NonNull BasicUser participant) {
        return threadParticipantRepository.getByThreadAndParticipantAndTypeIn(thread.getId(), participant.getRid(), List.of(ThreadParticipantType.PARTICIPANT.getId(), ThreadParticipantType.MENTION.getId())).map(this::convert);
    }

    public @NonNull List<ThreadParticipant> getAllByThreadId(@NonNull UUID threadId) {
        List<ThreadParticipantEntity> allByParticipant = threadParticipantRepository.getAllByThread(threadId);
        return ListUtil.convert(allByParticipant, this::convert);
    }

    public @NonNull List<ParticipantThreadParticipant> getParticipantsByThread(@NonNull UUID threadId) {
        List<ThreadParticipantEntity> participants = threadParticipantRepository.getAllByThreadAndType(threadId, ThreadParticipantType.PARTICIPANT.getId());
        return ListUtil.convert(participants, entity -> typedConvert(entity, ParticipantThreadParticipant.class));
    }

    public @NonNull List<ThreadParticipant> getAllExceptMainByThread(@NonNull UUID threadId) {
        List<ThreadParticipantEntity> allByParticipant = threadParticipantRepository.getAllByThreadAndTypeIn(threadId, List.of(ThreadParticipantType.PARTICIPANT.getId(), ThreadParticipantType.MENTION.getId(), ThreadParticipantType.PRIVATE.getId()));
        return ListUtil.convert(allByParticipant, this::convert);
    }

    public @NonNull List<ThreadParticipant> getParticipantByThreadAndType(@NonNull UUID threadId) {
        ThreadType threadType = threadTypeResolver.resolveThreadType(threadId);
        List<ThreadParticipantEntity> allByParticipant = switch (threadType) {
            case PRIVATE -> threadParticipantRepository.getAllByThreadAndType(threadId, ThreadParticipantType.PRIVATE.getId());
            case GROUP -> threadParticipantRepository.getAllByThreadAndTypeIn(threadId, List.of(ThreadParticipantType.PARTICIPANT.getId()));
            case RECORD -> threadParticipantRepository.getAllByThreadAndTypeIn(threadId, List.of(ThreadParticipantType.PARTICIPANT.getId(),  ThreadParticipantType.MENTION.getId()));
        };
        return ListUtil.convert(allByParticipant, this::convert);
    }

    public Optional<UUID> optionalPrivate(@NonNull BasicUser first, @NonNull BasicUser second) {
        return threadParticipantRepository.findPrivateThread(
                first.getRid(),
                second.getRid()
        );
    }

    public @NonNull List<MainThreadParticipant> getMainsByParticipantId(@NonNull UUID participantId) {
        List<ThreadParticipantEntity> mains = threadParticipantRepository.getAllByParticipantAndType(participantId, ThreadParticipantType.MAIN.getId());
        return ListUtil.convert(mains, entity -> typedConvert(entity, MainThreadParticipant.class));
    }

    private ThreadParticipant convert(@NonNull ThreadParticipantEntity entity) {
        return switch (ThreadParticipantType.CODEBOOK.getById(entity.getType())) {
            case MAIN -> new MainThreadParticipant(entity.getId(), entity.getThread(), entity.getParticipant(), entity.getCreateDate());
            case PARTICIPANT -> new ParticipantThreadParticipant(entity.getId(), entity.getThread(), entity.getParticipant(), entity.getCreateDate(), entity.getFirstUnreadMessageId(), entity.isAdministrator());
            case MENTION -> new MentionThreadParticipant(entity.getId(), entity.getThread(), entity.getParticipant(), entity.getCreateDate(), entity.getFirstUnreadMessageId());
            case PRIVATE -> new PrivateThreadParticipant(entity.getId(), entity.getThread(), entity.getParticipant(), entity.getCreateDate(), entity.getFirstUnreadMessageId());
        };
    }

    private <T extends ThreadParticipant> T typedConvert(@NonNull ThreadParticipantEntity entity, @NonNull Class<T> targetType) {
        ThreadParticipant participant = convert(entity);
        if (!targetType.isInstance(participant)) {
            throw new IllegalStateException("Expected type " + targetType.getSimpleName() + " but got " + participant.getClass().getSimpleName());
        }
        return (T) participant;
    }
}
