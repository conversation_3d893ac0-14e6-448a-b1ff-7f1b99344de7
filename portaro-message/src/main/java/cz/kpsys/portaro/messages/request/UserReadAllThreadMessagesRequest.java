package cz.kpsys.portaro.messages.request;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.messages.dto.UserReadAllThreadMessagesCommand;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.util.UUID;

public record UserReadAllThreadMessagesRequest(

        @Schema(implementation = UUID.class, description = "Thread UUID", example = cz.kpsys.portaro.record.Record.SCHEMA_EXAMPLE_DOCUMENT_ID)
        @NotNull
        Record thread,

        @Schema(implementation = Integer.class, example = User.SCHEMA_EXAMPLE_PERSON_ID, description = "user id")
        @NotNull
        BasicUser user

) {
    public UserReadAllThreadMessagesCommand toCommand(Department ctx, UserAuthentication currentAuth) {
        return new UserReadAllThreadMessagesCommand(thread(), user(), ctx, currentAuth);
    }
}
