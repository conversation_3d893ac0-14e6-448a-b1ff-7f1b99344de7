package cz.kpsys.portaro.ncip.impl;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.finance.AmountGroup;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanState;
import cz.kpsys.portaro.loan.LoansProviderService;
import cz.kpsys.portaro.ncip.NcipRequest;
import cz.kpsys.portaro.ncip.XmlUtil;
import cz.kpsys.portaro.ncip.handler.UnwrappedHandler;
import cz.kpsys.portaro.ncip.impl.convert.LoanToNcipLoanConverter;
import cz.kpsys.portaro.ncip.impl.convert.NcipUserIdToUserConverter;
import cz.kpsys.portaro.ncip.impl.convert.TransactionToNcipAmountConverter;
import cz.kpsys.portaro.ncip.schema.*;
import cz.kpsys.portaro.payment.Transaction;
import cz.kpsys.portaro.payment.TransactionLoader;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.contact.ContactType;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Optional;

import static java.util.Collections.singletonList;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LookupUserHandler implements UnwrappedHandler<LookupUser, LookupUserResponse> {

    @NonNull Authenticator authenticator;
    @NonNull LoansProviderService loansProviderService;
    @NonNull TransactionLoader transactionLoader;
    @NonNull ContextualFunction<BasicUser, Department, UserId> userToNcipUserIdConverter;
    @NonNull NcipAgencyIdProvider ncipAgencyIdProvider;
    @NonNull Converter<String, UnstructuredAddress> stringToNcipUnstructuredAddressConverter;
    @NonNull NcipUserIdToUserConverter ncipUserIdToUserConverter;
    @NonNull Converter<Loan, RequestedItem> loanToNcipRequestedItemConverter;
    @NonNull LoanToNcipLoanConverter loanToNcipLoanConverter;
    @NonNull TransactionToNcipAmountConverter amountToNcipAmountConverter;
    @NonNull Translator<Department> translator;


    @Override
    public LookupUserResponse handleRequestObject(NcipRequest<LookupUser> request) {
        LookupUser requestMessage = request.getMessage();
        Department ctx = request.getDepartment();

        User user = getUserFromRequest(request);
        UserAuthentication currentAuth = CurrentAuth.createWithAbsoluteAuthenticity(user);
        ReaderRole readerRole = user.roleStreamOn(ReaderRole.class, ctx)
                .findFirst()
                .orElseThrow(() -> new ItemNotFoundException(ReaderRole.class, user.getId(), Texts.ofNative("Given user %s has not reader role".formatted(user))));


        LookupUserResponse response = new LookupUserResponse();
        response.setExt(new Ext());

        AmountGroup<Transaction> transactionGroup = null;

        UserId userId = userToNcipUserIdConverter.getOn(user, ctx);
        response.setUserId(userId);

        if (requestMessage.getLoanedItemsDesired() != null) {
            List<Loan> loans = loansProviderService.getActiveLoans(user, Range.forAll(), ctx, currentAuth);
            loans.stream()
                    .map(loan -> loanToNcipLoanConverter.convert(loan, ctx, currentAuth))
                    .forEach(loanedItem -> response.getLoanedItem().add(loanedItem));;
        }

        if (requestMessage.getRequestedItemsDesired() != null) {
            List<Loan> waitingLoans = loansProviderService.getWaitingLoans(user, Range.forAll(), ctx, currentAuth);
            waitingLoans.stream()
                    .map(loanToNcipRequestedItemConverter::convert)
                    .forEach(requestedItem -> response.getRequestedItem().add(requestedItem));
        }

        if (requestMessage.getUserFiscalAccountDesired() != null) {
            transactionGroup = new AmountGroup<>(transactionLoader.getAll(user));
            UserFiscalAccount userFiscalAccount = amountToNcipAmountConverter.convert(transactionGroup, ctx, request.getLocale());
            response.getUserFiscalAccount().add(userFiscalAccount);
        }

        Optional<Integer> desiredLoanHistoryPage = NcipUtil.findExtObject(request.getMessage().getExt(), HistoryDesired.class)
                .map(HistoryDesired::getPage);
        if (desiredLoanHistoryPage.isPresent()) {
            Integer desiredLoanHistoryPageNumber = desiredLoanHistoryPage.get();
            response.getExt().getAny().add(getLoanedItemsHistory(ctx, user, currentAuth, desiredLoanHistoryPageNumber));
        }

        response.setUserOptionalFields(new UserOptionalFields());

        for (SchemeValuePair requestedElement : requestMessage.getUserElementType()) {
            switch (requestedElement.getValue()) {

                case "Authentication Input":
                    //nic
                    break;

                case "User Id":
                    if (readerRole.getBarCode() != null) {
                        UserId ncipBC = new UserId();
                        ncipBC.setUserIdentifierType(new SchemeValuePair());
                        ncipBC.getUserIdentifierType().setScheme("http://www.niso.org/ncip/v1_0/imp1/schemes/visibleuseridentifiertype/visibleuseridentifiertype.scm");
                        ncipBC.getUserIdentifierType().setValue("Barcode");
                        ncipBC.setUserIdentifierValue(String.valueOf(readerRole.getBarCode()));
                        response.getUserOptionalFields().getUserId().add(ncipBC);
                    }
                    if (readerRole.getCardNumber() != null) {
                        UserId ncipCN = new UserId();
                        ncipCN.setUserIdentifierType(new SchemeValuePair());
                        ncipCN.getUserIdentifierType().setScheme("http://www.niso.org/ncip/v1_0/imp1/schemes/visibleuseridentifiertype/visibleuseridentifiertype.scm");
                        ncipCN.getUserIdentifierType().setValue("Card Number");
                        ncipCN.setUserIdentifierValue(String.valueOf(readerRole.getCardNumber()));
                        response.getUserOptionalFields().getUserId().add(ncipCN);
                    }
                    break;

                case "Previous User Id":
                    //nic
                    break;

                case "Block Or Trap":
                    if (readerRole.getBlocked()) {
                        response.getUserOptionalFields().getBlockOrTrap().add(createBlockOrTrap("Zablokované operace"));
                    }
                    if (readerRole.isRegistrationExpired()) {
                        response.getUserOptionalFields().getBlockOrTrap().add(createBlockOrTrap("Skončená registrace"));
                    }
                    if (readerRole.getDeleted()) {
                        response.getUserOptionalFields().getBlockOrTrap().add(createBlockOrTrap("Smazaný čtenář"));
                    }
                    if (transactionGroup == null) {
                        transactionGroup = new AmountGroup<>(transactionLoader.getAll(user));
                    }
                    if (transactionGroup.isNegative()) {
                        response.getUserOptionalFields().getBlockOrTrap().add(createBlockOrTrap(String.format("Dluh %s Kč", transactionGroup.getSum().negate())));
                    }
                    break;

                case "Date Of Birth": {
                    Person p = Person.requireUserIsPerson(user);
                    if (p.getBirthDate() != null) {
                        response.getUserOptionalFields().setDateOfBirth(XmlUtil.toXmlGregorianCalendar(p.getBirthDate()));
                    }
                    break;
                }

                case "Name Information": {
                    Person p = Person.requireUserIsPerson(user);
                    NameInformation ni = new NameInformation();
                    ni.setPersonalNameInformation(new PersonalNameInformation());
                    StructuredPersonalUserName username = new StructuredPersonalUserName();
                    username.setGivenName(p.getFirstName());
                    username.setSurname(p.getLastName());
                    if (p.getDegree() != null) {
                        username.setPrefix(p.getDegree());
                    }
                    if (p.getSuffixDegree() != null) {
                        username.setSuffix(p.getSuffixDegree());
                    }
                    ni.getPersonalNameInformation().getContent().add(username);
                    response.getUserOptionalFields().setNameInformation(ni);
                    break;
                }

                case "User Address Information":
                    user.getAddresses().forEach(userAddress -> {
                        UserAddressInformation addr = new UserAddressInformation();
                        addr.setUserAddressRoleType(new SchemeValuePair());
                        addr.getUserAddressRoleType().setScheme("http://www.niso.org/ncip/v2_0/imp1/schemes/useraddressroletype/useraddressroletype.scm");
                        addr.getUserAddressRoleType().setValue("Ship To");
                        addr.setPhysicalAddress(new PhysicalAddress());
                        addr.getPhysicalAddress().setPhysicalAddressType(new SchemeValuePair());
                        addr.getPhysicalAddress().getPhysicalAddressType().setScheme("http://www.niso.org/ncip/v1_0/imp1/schemes/physicaladdresstype/physicaladdresstype.scm");
                        addr.getPhysicalAddress().getPhysicalAddressType().setValue("Street Address");
                        addr.getPhysicalAddress().setUnstructuredAddress(stringToNcipUnstructuredAddressConverter.convert(userAddress.address().getTextWithoutState().localize(translator, ctx, request.getLocale())));
                        response.getUserOptionalFields().getUserAddressInformation().add(addr);
                    });

                    user.getContacts().stream().filter(contact -> contact.type().isEmail() || contact.type().isPhone()).forEach(contact -> {
                        String addressRoleType = contact.type() == ContactType.EMAIL && user.getEmail().equals(contact.getValue()) ? "Notice" : "Multi-Purpose"; //pokud se jedna o ten email, ktery nam vraci user.getEmail, jedna se o defaultni
                        String addressType = switch (contact.type()) {
                            case EMAIL -> "mailto";
                            case PHONE -> "tel";
                            case SMS_PHONE -> "sms";
                            default -> "unknown";
                        };

                        UserAddressInformation addr = new UserAddressInformation();
                        addr.setUserAddressRoleType(new SchemeValuePair());
                        addr.getUserAddressRoleType().setScheme("http://www.niso.org/ncip/v2_0/imp1/schemes/useraddressroletype/useraddressroletype.scm");
                        addr.getUserAddressRoleType().setValue(addressRoleType);
                        addr.setElectronicAddress(new ElectronicAddress());
                        addr.getElectronicAddress().setElectronicAddressType(new SchemeValuePair());
                        addr.getElectronicAddress().getElectronicAddressType().setScheme("http://www.iana.org/assignments/uri-schemes");
                        addr.getElectronicAddress().getElectronicAddressType().setValue(addressType);
                        addr.getElectronicAddress().setElectronicAddressData(contact.getValue());
                        response.getUserOptionalFields().getUserAddressInformation().add(addr);
                    });
                    break;

                case "User Language":
                    //nic
                    break;

                case "User Privilege":
                    UserPrivilege up = new UserPrivilege();
                    up.setAgencyId(ncipAgencyIdProvider.get());
                    up.setAgencyUserPrivilegeType(new SchemeValuePair());
                    up.getAgencyUserPrivilegeType().setScheme("http://www.niso.org/ncip/v1_0/imp1/schemes/agencyuserprivilegetype/agencyuserprivilegetype.scm");
                    up.getAgencyUserPrivilegeType().setValue("loaning");
                    if (readerRole.getRegistrationDate() != null) {
                        up.setValidFromDate(XmlUtil.toXmlGregorianCalendar(readerRole.getRegistrationDate()));
                    }
                    readerRole.getRegistrationExpirationDate().map(XmlUtil::toXmlGregorianCalendar).ifPresent(up::setValidToDate);
                    up.setUserPrivilegeDescription("Loaning, Renewing and other reader actions");
                    response.getUserOptionalFields().getUserPrivilege().add(up);
                    break;

                default:
                    break;
            }
        }

        return response;
    }

    private User getUserFromRequest(NcipRequest<LookupUser> request) {
        Department currentDepartment = request.getDepartment();
        List<AuthenticationInput> authenticationInputs = request.getMessage().getAuthenticationInput();
        if (authenticationInputs.isEmpty()) {
            return ncipUserIdToUserConverter.convert(currentDepartment, request.getMessage().getUserId());
        }
        return authenticator.getAuthenticatedUser(currentDepartment, authenticationInputs);
    }

    private LoanedItemsHistory getLoanedItemsHistory(Department currentDepartment, User user, UserAuthentication currentAuth, Integer desiredLoanHistoryPageNumber) {
        int pageSize = 10;
        List<LoanState> loanStates = singletonList(LoanState.RETURNED);
        LoanedItemsHistory loanedItemsHistory = new LoanedItemsHistory();

        loanedItemsHistory.setPage(desiredLoanHistoryPageNumber);

        int loansSize = loansProviderService.getLoansSize(user, loanStates, currentDepartment, currentAuth);
        int lastPage = ((loansSize - 1) / pageSize) + 1;
        loanedItemsHistory.setLastPage(lastPage);

        if (loansSize > 0) {
            List<Loan> loans = loansProviderService.getLoans(user, loanStates, SortingItem.ofSimpleDesc(Loan.END_DATE), Range.of(desiredLoanHistoryPageNumber, pageSize), currentDepartment, currentAuth);
            loans.stream()
                    .map(loan -> loanToNcipLoanConverter.convert(loan, currentDepartment, currentAuth))
                    .forEach(loanedItem -> loanedItemsHistory.getLoanedItem().add(loanedItem));
        }

        return loanedItemsHistory;
    }

    private BlockOrTrap createBlockOrTrap(String value) {
        BlockOrTrap bot = new BlockOrTrap();
        bot.setAgencyId(ncipAgencyIdProvider.get());
        bot.setBlockOrTrapType(new SchemeValuePair());
        bot.getBlockOrTrapType().setScheme("http://www.niso.org/ncip/v1_0/imp1/schemes/blockortraptype/blockortraptype.scm");
        bot.getBlockOrTrapType().setValue(value);
        return bot;
    }

}
