package cz.kpsys.portaro.ncip;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.localization.UserFriendlyExceptionTextResolver;
import cz.kpsys.portaro.commons.util.StringEscapeUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ncip.schema.NCIPMessage;
import cz.kpsys.portaro.ncip.schema.Problem;
import cz.kpsys.portaro.ncip.schema.SchemeValuePair;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.Locale;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ProblemCreator {

    @NonNull Translator<Department> translator;
    @NonNull UserFriendlyExceptionTextResolver userFriendlyExceptionTextResolver;

    public NCIPMessage byException(Exception e, Department currentDepartment, Locale locale) {
        NCIPMessage errorResponse = new NCIPMessage();
        Problem p = new Problem();
        p.setProblemType(new SchemeValuePair());

        if (e instanceof NcipException) {
            p.getProblemType().setValue(((NcipException) e).getProblemType());
            p.setProblemDetail(((NcipException) e).getProblemDetail().localize(translator, currentDepartment, locale));
        } else {
            p.getProblemType().setValue(e.getClass().getSimpleName());
            p.setProblemDetail(getExceptionMessage(e, currentDepartment, locale));
        }

        errorResponse.getProblem().add(p);
        return errorResponse;
    }

    public String generateErrorXmlString(Exception e, Department currentDepartment, Locale locale) {
        return generateErrorXmlString(e.getClass().getSimpleName(), getExceptionMessage(e, currentDepartment, locale));
    }

    public String generateErrorXmlString(String problemType, @Nullable String problemDetail) {
        return ("""
                <?xml version="1.0" encoding="UTF-8"?>
                <ns1:NCIPMessage ns1:version="http://www.niso.org/schemas/ncip/v2_02/ncip_v2_02.xsd" xmlns:ns1="http://www.niso.org/2008/ncip" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.niso.org/2008/ncip ncip_v2_02.xsd">
                  <ns1:Problem>
                    <ns1:ProblemType ns1:Scheme="http://www.niso.org/ncip/v1_0/schemes/processingerrortype/generalprocessingerror.scm">%s</ns1:ProblemType>
                    <ns1:ProblemDetail>%s</ns1:ProblemDetail>
                  </ns1:Problem>
                </ns1:NCIPMessage>"""
        ).formatted(StringEscapeUtil.escapeXml10(problemType), StringEscapeUtil.escapeXml10(problemDetail != null ? problemDetail : "Unknown error"));
    }

    private String getExceptionMessage(Exception e, Department currentDepartment, Locale locale) {
        Text userFriendlyText = userFriendlyExceptionTextResolver.resolve(e);
        return userFriendlyText.localize(translator, currentDepartment, locale);
    }

}
