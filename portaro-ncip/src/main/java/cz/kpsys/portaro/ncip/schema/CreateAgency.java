
package cz.kpsys.portaro.ncip.schema;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}InitiationHeader" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}MandatedAction" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}AgencyId" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}OrganizationNameInformation" maxOccurs="unbounded"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}AgencyAddressInformation" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}AuthenticationPrompt" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ApplicationProfileSupportedType" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ConsortiumAgreement" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}AgencyUserPrivilegeType" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}Ext" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "initiationHeader",
    "mandatedAction",
    "agencyId",
    "organizationNameInformation",
    "agencyAddressInformation",
    "authenticationPrompt",
    "applicationProfileSupportedType",
    "consortiumAgreement",
    "agencyUserPrivilegeType",
    "ext"
})
@XmlRootElement(name = "CreateAgency")
@Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
public class CreateAgency {

    @XmlElement(name = "InitiationHeader")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected InitiationHeader initiationHeader;
    @XmlElement(name = "MandatedAction")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected MandatedAction mandatedAction;
    @XmlElement(name = "AgencyId")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected SchemeValuePair agencyId;
    @XmlElement(name = "OrganizationNameInformation", required = true)
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<OrganizationNameInformation> organizationNameInformation;
    @XmlElement(name = "AgencyAddressInformation")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<AgencyAddressInformation> agencyAddressInformation;
    @XmlElement(name = "AuthenticationPrompt")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<AuthenticationPrompt> authenticationPrompt;
    @XmlElement(name = "ApplicationProfileSupportedType")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<SchemeValuePair> applicationProfileSupportedType;
    @XmlElement(name = "ConsortiumAgreement")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<SchemeValuePair> consortiumAgreement;
    @XmlElement(name = "AgencyUserPrivilegeType")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<SchemeValuePair> agencyUserPrivilegeType;
    @XmlElement(name = "Ext")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected Ext ext;

    /**
     * Gets the value of the initiationHeader property.
     * 
     * @return
     *     possible object is
     *     {@link InitiationHeader }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public InitiationHeader getInitiationHeader() {
        return initiationHeader;
    }

    /**
     * Sets the value of the initiationHeader property.
     * 
     * @param value
     *     allowed object is
     *     {@link InitiationHeader }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setInitiationHeader(InitiationHeader value) {
        this.initiationHeader = value;
    }

    /**
     * Gets the value of the mandatedAction property.
     * 
     * @return
     *     possible object is
     *     {@link MandatedAction }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public MandatedAction getMandatedAction() {
        return mandatedAction;
    }

    /**
     * Sets the value of the mandatedAction property.
     * 
     * @param value
     *     allowed object is
     *     {@link MandatedAction }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setMandatedAction(MandatedAction value) {
        this.mandatedAction = value;
    }

    /**
     * Gets the value of the agencyId property.
     * 
     * @return
     *     possible object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public SchemeValuePair getAgencyId() {
        return agencyId;
    }

    /**
     * Sets the value of the agencyId property.
     * 
     * @param value
     *     allowed object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setAgencyId(SchemeValuePair value) {
        this.agencyId = value;
    }

    /**
     * Gets the value of the organizationNameInformation property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the organizationNameInformation property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOrganizationNameInformation().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link OrganizationNameInformation }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<OrganizationNameInformation> getOrganizationNameInformation() {
        if (organizationNameInformation == null) {
            organizationNameInformation = new ArrayList<OrganizationNameInformation>();
        }
        return this.organizationNameInformation;
    }

    /**
     * Gets the value of the agencyAddressInformation property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the agencyAddressInformation property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAgencyAddressInformation().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AgencyAddressInformation }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<AgencyAddressInformation> getAgencyAddressInformation() {
        if (agencyAddressInformation == null) {
            agencyAddressInformation = new ArrayList<AgencyAddressInformation>();
        }
        return this.agencyAddressInformation;
    }

    /**
     * Gets the value of the authenticationPrompt property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the authenticationPrompt property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAuthenticationPrompt().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AuthenticationPrompt }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<AuthenticationPrompt> getAuthenticationPrompt() {
        if (authenticationPrompt == null) {
            authenticationPrompt = new ArrayList<AuthenticationPrompt>();
        }
        return this.authenticationPrompt;
    }

    /**
     * Gets the value of the applicationProfileSupportedType property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the applicationProfileSupportedType property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getApplicationProfileSupportedType().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SchemeValuePair }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<SchemeValuePair> getApplicationProfileSupportedType() {
        if (applicationProfileSupportedType == null) {
            applicationProfileSupportedType = new ArrayList<SchemeValuePair>();
        }
        return this.applicationProfileSupportedType;
    }

    /**
     * Gets the value of the consortiumAgreement property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the consortiumAgreement property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getConsortiumAgreement().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SchemeValuePair }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<SchemeValuePair> getConsortiumAgreement() {
        if (consortiumAgreement == null) {
            consortiumAgreement = new ArrayList<SchemeValuePair>();
        }
        return this.consortiumAgreement;
    }

    /**
     * Gets the value of the agencyUserPrivilegeType property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the agencyUserPrivilegeType property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAgencyUserPrivilegeType().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SchemeValuePair }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<SchemeValuePair> getAgencyUserPrivilegeType() {
        if (agencyUserPrivilegeType == null) {
            agencyUserPrivilegeType = new ArrayList<SchemeValuePair>();
        }
        return this.agencyUserPrivilegeType;
    }

    /**
     * Gets the value of the ext property.
     * 
     * @return
     *     possible object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public Ext getExt() {
        return ext;
    }

    /**
     * Sets the value of the ext property.
     * 
     * @param value
     *     allowed object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setExt(Ext value) {
        this.ext = value;
    }

}
