
package cz.kpsys.portaro.ncip.schema;

import javax.annotation.Generated;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}FromSystemId" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}FromSystemAuthentication" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}FromAgencyId"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}FromAgencyAuthentication" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}OnBehalfOfAgency" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ToSystemId" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ToAgencyId"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ApplicationProfileType" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}Ext" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "fromSystemId",
    "fromSystemAuthentication",
    "fromAgencyId",
    "fromAgencyAuthentication",
    "onBehalfOfAgency",
    "toSystemId",
    "toAgencyId",
    "applicationProfileType",
    "ext"
})
@XmlRootElement(name = "InitiationHeader")
@Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
public class InitiationHeader {

    @XmlElement(name = "FromSystemId")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected SchemeValuePair fromSystemId;
    @XmlElement(name = "FromSystemAuthentication")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected String fromSystemAuthentication;
    @XmlElement(name = "FromAgencyId", required = true)
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected FromAgencyId fromAgencyId;
    @XmlElement(name = "FromAgencyAuthentication")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected String fromAgencyAuthentication;
    @XmlElement(name = "OnBehalfOfAgency")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected OnBehalfOfAgency onBehalfOfAgency;
    @XmlElement(name = "ToSystemId")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected SchemeValuePair toSystemId;
    @XmlElement(name = "ToAgencyId", required = true)
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected ToAgencyId toAgencyId;
    @XmlElement(name = "ApplicationProfileType")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected SchemeValuePair applicationProfileType;
    @XmlElement(name = "Ext")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected Ext ext;

    /**
     * Gets the value of the fromSystemId property.
     * 
     * @return
     *     possible object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public SchemeValuePair getFromSystemId() {
        return fromSystemId;
    }

    /**
     * Sets the value of the fromSystemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setFromSystemId(SchemeValuePair value) {
        this.fromSystemId = value;
    }

    /**
     * Gets the value of the fromSystemAuthentication property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public String getFromSystemAuthentication() {
        return fromSystemAuthentication;
    }

    /**
     * Sets the value of the fromSystemAuthentication property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setFromSystemAuthentication(String value) {
        this.fromSystemAuthentication = value;
    }

    /**
     * Gets the value of the fromAgencyId property.
     * 
     * @return
     *     possible object is
     *     {@link FromAgencyId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public FromAgencyId getFromAgencyId() {
        return fromAgencyId;
    }

    /**
     * Sets the value of the fromAgencyId property.
     * 
     * @param value
     *     allowed object is
     *     {@link FromAgencyId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setFromAgencyId(FromAgencyId value) {
        this.fromAgencyId = value;
    }

    /**
     * Gets the value of the fromAgencyAuthentication property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public String getFromAgencyAuthentication() {
        return fromAgencyAuthentication;
    }

    /**
     * Sets the value of the fromAgencyAuthentication property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setFromAgencyAuthentication(String value) {
        this.fromAgencyAuthentication = value;
    }

    /**
     * Gets the value of the onBehalfOfAgency property.
     * 
     * @return
     *     possible object is
     *     {@link OnBehalfOfAgency }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public OnBehalfOfAgency getOnBehalfOfAgency() {
        return onBehalfOfAgency;
    }

    /**
     * Sets the value of the onBehalfOfAgency property.
     * 
     * @param value
     *     allowed object is
     *     {@link OnBehalfOfAgency }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setOnBehalfOfAgency(OnBehalfOfAgency value) {
        this.onBehalfOfAgency = value;
    }

    /**
     * Gets the value of the toSystemId property.
     * 
     * @return
     *     possible object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public SchemeValuePair getToSystemId() {
        return toSystemId;
    }

    /**
     * Sets the value of the toSystemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setToSystemId(SchemeValuePair value) {
        this.toSystemId = value;
    }

    /**
     * Gets the value of the toAgencyId property.
     * 
     * @return
     *     possible object is
     *     {@link ToAgencyId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public ToAgencyId getToAgencyId() {
        return toAgencyId;
    }

    /**
     * Sets the value of the toAgencyId property.
     * 
     * @param value
     *     allowed object is
     *     {@link ToAgencyId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setToAgencyId(ToAgencyId value) {
        this.toAgencyId = value;
    }

    /**
     * Gets the value of the applicationProfileType property.
     * 
     * @return
     *     possible object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public SchemeValuePair getApplicationProfileType() {
        return applicationProfileType;
    }

    /**
     * Sets the value of the applicationProfileType property.
     * 
     * @param value
     *     allowed object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setApplicationProfileType(SchemeValuePair value) {
        this.applicationProfileType = value;
    }

    /**
     * Gets the value of the ext property.
     * 
     * @return
     *     possible object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public Ext getExt() {
        return ext;
    }

    /**
     * Sets the value of the ext property.
     * 
     * @param value
     *     allowed object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setExt(Ext value) {
        this.ext = value;
    }

}
