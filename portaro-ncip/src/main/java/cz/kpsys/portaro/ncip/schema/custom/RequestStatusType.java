package cz.kpsys.portaro.ncip.schema.custom;

import cz.kpsys.portaro.ncip.schema.SchemeValuePair;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

/**
 *
 * Pro funkcnost je nutne pridat toto do tridy, ktera tento element obaluje: <br/>
 * \@XmlElementRef(name = "RequestStatusType", namespace = "http://www.niso.org/2008/ncip", type = RequestStatusType.class, required = false)
 *
 * Created by Jan on 4. 8. 2015.
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "RequestStatusType")
public class RequestStatusType extends SchemeValuePair {

    public static final String AVAILABLE_FOR_PICKUP = "Available For Pickup";
    public static final String CANNOT_FULFILL_REQUEST = "Cannot Fulfill Request";
    public static final String EXPIRED = "Expired";
    public static final String IN_PROCESS = "In Process";
    public static final String NEED_TO_ACCEPT_CONDITIONS = "Need to Accept Conditions";
    public static final String REQUESTED_VIA_ILL = "Requested Via ILL";

    public RequestStatusType() {
        setScheme("http://www.niso.org/ncip/v1_0/imp1/schemes/requeststatustype/requeststatustype.scm");
    }

}
