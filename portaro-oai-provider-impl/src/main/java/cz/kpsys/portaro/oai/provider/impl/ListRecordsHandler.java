package cz.kpsys.portaro.oai.provider.impl;

import cz.kpsys.portaro.batch.ChunkLoader;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.SingleValueRepository;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.oai.model.ErrorCode;
import cz.kpsys.portaro.oai.model.ListRecordsResponse;
import cz.kpsys.portaro.oai.model.RecordResponse;
import cz.kpsys.portaro.oai.model.ResumptionTokenResponse;
import cz.kpsys.portaro.oai.provider.OaiException;
import cz.kpsys.portaro.oai.provider.handler.OaiCommand;
import cz.kpsys.portaro.oai.provider.handler.OaiMetadataFormat;
import cz.kpsys.portaro.oai.provider.handler.VerbHandler;
import cz.kpsys.portaro.oai.provider.impl.record.FormattedRecordResponseConverter;
import cz.kpsys.portaro.oai.provider.impl.record.UpdatedRecordId;
import cz.kpsys.portaro.record.export.RecordExport;
import cz.kpsys.portaro.record.export.listener.RecordExportListener;
import cz.kpsys.portaro.search.Chunk;
import cz.kpsys.portaro.search.SingleKeysetPaging;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ListRecordsHandler implements VerbHandler<ListRecordsResponse> {

    @NonNull FormattedRecordResponseConverter formattedRecordResponseConverter;
    @NonNull HarvestingToOaiResumptionTokenConverter harvestingToOaiResumptionTokenConverter;
    @NonNull ChunkLoader<UpdatedRecordId, HarvestingParams, SingleKeysetPaging<UUID>> updatedRecordIdChunkSearchLoader;
    @NonNull RecordExportListener recordExportListener;
    @NonNull SingleValueRepository<HarvestingParams, String> oaiParamsRepository;
    @NonNull OaiRequestToHarvestingParamsConverter oaiRequestToHarvestingParamsConverter;
    @NonNull ByIdLoadable<RecordExport, String> recordExportByOaiSetIdLoader;


    @Override
    public @NonNull ListRecordsResponse handle(@NonNull OaiCommand command) {
        HarvestingParams params = oaiRequestToHarvestingParamsConverter.requestToHarvestingParams(command);
        RecordExport recordExport = recordExportByOaiSetIdLoader.getById(params.getSet());

        Chunk<UpdatedRecordId, SingleKeysetPaging<UUID>> chunk = search(params);

        List<RecordResponse> recordResponses = formattedRecordResponseConverter.toRecordResponses(OaiMetadataFormat.CODEBOOK.getById(Objects.requireNonNull(params.getMetadataPrefix())), recordExport, chunk.getItems(), command.currentAuth(), command.department());
        ResumptionTokenResponse resumptionToken = getResumptionToken(params, chunk);
        ListRecordsResponse listRecordsResponse = new ListRecordsResponse(recordResponses, resumptionToken);

        recordExportListener.recordsExported(ListUtil.getListOfIds(chunk.getItems()), recordExport, command.requesterIp());

        return listRecordsResponse;
    }

    private Chunk<UpdatedRecordId, SingleKeysetPaging<UUID>> search(HarvestingParams params) {
        Chunk<UpdatedRecordId, SingleKeysetPaging<UUID>> chunk = params.getCursor() == null
                ? updatedRecordIdChunkSearchLoader.loadFirst(params)
                : updatedRecordIdChunkSearchLoader.loadNext(params, params.getCursor());
        OaiException.throwIf(chunk.getItems().isEmpty() && params.getCursor() == null && chunk.isDefinitelyFinal(), ErrorCode.NO_RECORDS_MATCH, "No records with this params");
        return chunk;
    }

    private ResumptionTokenResponse getResumptionToken(HarvestingParams params, Chunk<?, SingleKeysetPaging<UUID>> chunk) {
        String nextPageToken = null;
        if (!chunk.isDefinitelyFinal()) {
            HarvestingParams nextPageParams = params.createForNextPage(chunk.getNextChunkCursor());
            nextPageToken = oaiParamsRepository.store(nextPageParams);
        }
        return harvestingToOaiResumptionTokenConverter.convert(nextPageToken, params.getCursor() == null ? null : params.getCursor().lastValueNumber(), null);
    }

}
