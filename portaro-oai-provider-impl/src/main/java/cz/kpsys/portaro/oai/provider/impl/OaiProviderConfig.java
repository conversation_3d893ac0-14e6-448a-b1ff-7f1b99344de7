package cz.kpsys.portaro.oai.provider.impl;

import cz.kpsys.portaro.batch.ChunkLoader;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.NamedLabeledIdentified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import cz.kpsys.portaro.config.ConverterRegisterer;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.department.SystemInstitution;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.exemplar.ExemplarSettingKeys;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditorModifier;
import cz.kpsys.portaro.formconfig.valueeditor.AcceptableValuesResolver;
import cz.kpsys.portaro.licence.FeatureEnabledProvider;
import cz.kpsys.portaro.licence.FeatureManager;
import cz.kpsys.portaro.oai.model.*;
import cz.kpsys.portaro.oai.provider.OaiProviderController;
import cz.kpsys.portaro.oai.provider.handler.*;
import cz.kpsys.portaro.oai.provider.impl.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.export.ExportedDepartmentSearchHelper;
import cz.kpsys.portaro.record.export.RecordExport;
import cz.kpsys.portaro.record.export.RecordExportConfig;
import cz.kpsys.portaro.record.export.listener.RecordExportListener;
import cz.kpsys.portaro.record.export.marc.RecordsToMarcRecordsMapper;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.holding.RecordHoldingLoader;
import cz.kpsys.portaro.search.SingleKeysetPaging;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Configuration
@Lazy
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class OaiProviderConfig {

    @NonNull FeatureManager featureManager;
    @NonNull SettingLoader settingLoader;
    @NonNull ContextualProvider<Department, @NonNull SystemInstitution> systemInstitutionProvider;
    @NonNull TransactionTemplateFactory readonlyTransactionTemplateFactory;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull ExportedDepartmentSearchHelper exportedDepartmentSearchHelper;
    @NonNull ContextualProvider<Department, List<Fond>> showableFondsDepartmentedProvider;
    @NonNull Eventer eventer;
    @NonNull IdAndIdsLoadable<Record, UUID> exportedRecordLoader;
    @NonNull AllByIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;
    @NonNull ByIdLoadable<Record, Integer> nonDetailedDocumentByKindedIdLoader;
    @NonNull RecordsToMarcRecordsMapper recordsToMarcRecordsMapper;
    @NonNull Provider<@NonNull RecordExport> defaultRecordExportProvider;
    @NonNull AllValuesProvider<? extends RecordExport> recordExportLoader;
    @NonNull ByIdLoadable<RecordExport, String> recordExportByOaiSetIdLoader;
    @NonNull RecordExportListener recordExportListener;
    @NonNull RecordHoldingLoader includingDeletedRecordHoldingLoader;



    @Bean
    public OaiProviderController oaiProviderController() {
        return new OaiProviderController(httpMessageOaiRequestHandler(), defaultRecordExportProvider.andThen(RecordExport::getOaiSetId));
    }

    @Bean
    public AcceptableValuesResolver<OaiRequest, NamedLabeledIdentified<String>> oaiRequestAllowedSetsResolver() {
        return new OaiRequest.OaiRequestAllowedSetsResolver(recordExportLoader);
    }

    @Bean
    public TextValueEditorModifier<OaiRequest> oaiRequestIdentifierEditorModifier() {
        return new OaiRequest.OaiRequestIdentifierEditorModifier(
                settingLoader.getDepartmentedProvider(OaiSettingKeys.DOCUMENT_ID_PATTERN).throwingWhenNull(),
                RecordExportConfig.RECORD_ID_PLACEHOLDER,
                RecordExportConfig.KINDED_ID_PLACEHOLDER
        );
    }

    @Bean
    public Codebook<OaiVerbRequest, String> oaiVerbRequestLoader() {
        return OaiVerbRequest.CODEBOOK;
    }

    @Bean
    public Codebook<OaiMetadataFormat, String> oaiMetadataFormatLoader() {
        return OaiMetadataFormat.CODEBOOK;
    }

    @Bean
    public HttpOaiRequestHandler httpMessageOaiRequestHandler() {
        return new OptionallyEnabledLazyHttpOaiRequestHandler(
                ContextIgnoringContextualProvider.of(new FeatureEnabledProvider(featureManager, FeatureManager.FEATURE_OAI_PROVIDER)),
                () -> new JacksonHttpOaiRequestHandler(oaiHandler())
        );
    }

    @Bean
    public OaiHandler oaiHandler() {
        return new ExceptionHandlingOaiHandler(
                new VerbHandlerDispatchingOaiHandler(
                        createIdentifyHandler(),
                        createListMetadataFormatsHandler(),
                        createListSetsHandler(),
                        createListIdentifiersHandler(),
                        createGetRecordHandler(),
                        createListRecordsHandler()
                )
        );
    }

    private IdBase64ingSingleValueRepository<HarvestingParams> listRecordsOaiParamsRepository() {
        return new IdBase64ingSingleValueRepository<>(new InJsonTokenHarvestingParamsRepository());
    }

    private IdBase64ingSingleValueRepository<HarvestingParams> listIdentifiersOaiParamsRepository() {
        return new IdBase64ingSingleValueRepository<>(new InJsonTokenHarvestingParamsRepository());
    }

    private HarvestingToOaiResumptionTokenConverter harvestingToOaiResumptionTokenConverter() {
        Duration tokenExpirationTimeout = Duration.ofDays(1);
        return new HarvestingToOaiResumptionTokenConverterImpl(tokenExpirationTimeout);
    }

    private VerbHandler<IdentifyResponse> createIdentifyHandler() {
        return new EventLoggingVerbHandler<>(
                new TransactionalVerbHandler<>(new IdentifyHandler(systemInstitutionProvider), readonlyTransactionTemplateFactory.get()),
                eventer
        );
    }

    private VerbHandler<ListMetadataFormatsResponse> createListMetadataFormatsHandler() {
        return new EventLoggingVerbHandler<>(
                new ListMetadataFormatsHandler(),
                eventer
        );
    }

    private VerbHandler<ListSetsResponse> createListSetsHandler() {
        return new EventLoggingVerbHandler<>(
                new TransactionalVerbHandler<>(
                        new ListSetsHandler(recordExportLoader),
                        readonlyTransactionTemplateFactory.get()
                ),
                eventer
        );
    }

    private VerbHandler<ListIdentifiersResponse> createListIdentifiersHandler() {
        OaiRequestToHarvestingParamsConverter paramsConverter = new OaiRequestToHarvestingParamsConverter(false, listIdentifiersOaiParamsRepository(), recordExportByOaiSetIdLoader);
        return new EventLoggingVerbHandler<>(
                new TransactionalVerbHandler<>(
                        new ListIdentifiersHandler(updatedRecordIdsToOaiHeadersConverter(), harvestingToOaiResumptionTokenConverter(), updatedRecordIdChunkSearchLoader(), listIdentifiersOaiParamsRepository(), paramsConverter, recordExportByOaiSetIdLoader),
                        readonlyTransactionTemplateFactory.get()
                ),
                eventer
        );
    }

    private VerbHandler<GetRecordResponse> createGetRecordHandler() {
        ContextualProvider<Department, @NonNull String> documentKindedIdPattern = settingLoader.getDepartmentedProvider(OaiSettingKeys.DOCUMENT_ID_PATTERN).throwingWhenNull();
        ContextualProvider<Department, @NonNull String> recordIdPattern = settingLoader.getDepartmentedProvider(OaiSettingKeys.AUTHORITY_ID_PATTERN).throwingWhenNull();
        return new EventLoggingVerbHandler<>(
                new TransactionalVerbHandler<>(
                        new GetRecordHandler(RecordExportConfig.RECORD_ID_PLACEHOLDER, RecordExportConfig.KINDED_ID_PLACEHOLDER, documentKindedIdPattern, recordIdPattern, recordExportByOaiSetIdLoader, nonDetailedDocumentByKindedIdLoader, formattedRecordResponseConverter(), recordExportListener),
                        readonlyTransactionTemplateFactory.get()
                ),
                eventer
        );
    }

    private VerbHandler<ListRecordsResponse> createListRecordsHandler() {
        OaiRequestToHarvestingParamsConverter paramsConverter = new OaiRequestToHarvestingParamsConverter(true, listRecordsOaiParamsRepository(), recordExportByOaiSetIdLoader);
        return new EventLoggingVerbHandler<>(
                new TransactionalVerbHandler<>(
                        new ListRecordsHandler(formattedRecordResponseConverter(), harvestingToOaiResumptionTokenConverter(), updatedRecordIdChunkSearchLoader(), recordExportListener, listRecordsOaiParamsRepository(), paramsConverter, recordExportByOaiSetIdLoader),
                        readonlyTransactionTemplateFactory.get()
                ),
                eventer
        );
    }

    private FormattedRecordResponseConverter formattedRecordResponseConverter() {
        UpdatedRecordIdsToOaiRecordResponsesConverter dcConverter = new UpdatedRecordIdsToOaiRecordResponsesConverter(exportedRecordLoader, includingDeletedRecordHoldingLoader, new RecordsToOaiDcMetadataMapper(), nonDetailedRecordToOaiRecordIdConverter(), departmentAccessor);
        UpdatedRecordIdsToOaiRecordResponsesConverter marc21Converter = new UpdatedRecordIdsToOaiRecordResponsesConverter(exportedRecordLoader, includingDeletedRecordHoldingLoader, new RecordsToOaiMarcMetadataMapper(recordsToMarcRecordsMapper), nonDetailedRecordToOaiRecordIdConverter(), departmentAccessor);
        return new FormattedRecordResponseConverter(
                Map.of(
                        OaiMetadataFormat.OAI_DC, dcConverter,
                        OaiMetadataFormat.MARC21, marc21Converter
                ));
    }

    private UpdatedRecordIdsToOaiHeadersConverter updatedRecordIdsToOaiHeadersConverter() {
        return new UpdatedRecordIdsToOaiHeadersConverter(
                nonDetailedRichRecordLoader,
                includingDeletedRecordHoldingLoader,
                nonDetailedRecordToOaiRecordIdConverter(),
                departmentAccessor
        );
    }

    private Converter<Record, String> nonDetailedRecordToOaiRecordIdConverter() {
        Provider<@NonNull PlaceholderTemplate> documentPlaceholderTemplate = settingLoader.getOnRootProvider(OaiSettingKeys.DOCUMENT_ID_PATTERN).andThen(PlaceholderTemplate::new);
        Provider<@NonNull PlaceholderTemplate> authorityPlaceholderTemplate = settingLoader.getOnRootProvider(OaiSettingKeys.AUTHORITY_ID_PATTERN).andThen(PlaceholderTemplate::new);
        return new ItemIdToFormatedIdConverter(RecordExportConfig.RECORD_ID_PLACEHOLDER, RecordExportConfig.KINDED_ID_PLACEHOLDER, documentPlaceholderTemplate, authorityPlaceholderTemplate);
    }

    @Bean
    public ChunkLoader<UpdatedRecordId, HarvestingParams, SingleKeysetPaging<UUID>> updatedRecordIdChunkSearchLoader() {
        return new SpringDbUpdatedRecordIdChunkSearchLoader(
                notAutoCommittingJdbcTemplate,
                queryFactory,
                recordExportByOaiSetIdLoader,
                departmentLoader,
                exportedDepartmentSearchHelper,
                showableFondsDepartmentedProvider,
                settingLoader.getDepartmentedProvider(ExemplarSettingKeys.EXEMPLAR_STATUSES_EXPORT).throwingWhenNull().invalidWhen(Collection::isEmpty),
                settingLoader.getDepartmentedProvider(OaiSettingKeys.PAGE_SIZE)
        );
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        converterRegisterer
                .registerForStringId(OaiVerbRequest.class, oaiVerbRequestLoader())
                .registerForStringId(OaiMetadataFormat.class, oaiMetadataFormatLoader());
    }
}
