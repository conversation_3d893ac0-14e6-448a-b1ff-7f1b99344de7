package cz.kpsys.portaro.oai.provider.impl;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.setting.SettingKey;
import lombok.NonNull;

public class OaiSettingKeys {

    public static final String SECTION_OAI_PROVIDER = "oai.provider";
    public static final SettingKey<@NullableNotBlank String> DOCUMENT_ID_PATTERN = new SettingKey<>(SECTION_OAI_PROVIDER, "documentIdPattern");
    public static final SettingKey<@NullableNotBlank String> AUTHORITY_ID_PATTERN = new SettingKey<>(SECTION_OAI_PROVIDER, "authorityIdPattern");
    public static final SettingKey<@NonNull Integer> PAGE_SIZE = new SettingKey<>(SECTION_OAI_PROVIDER, "pageSize");

}
