package cz.kpsys.portaro.oai.provider.impl;

import cz.kpsys.portaro.batch.ChunkLoader;
import cz.kpsys.portaro.batch.db.SingleKeysetPagingResultSetExtractor;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.databasestructure.RecordDb.RECORD_HOLDING;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.ExemplarType;
import cz.kpsys.portaro.oai.provider.impl.record.UpdatedRecordId;
import cz.kpsys.portaro.record.export.ExportedDepartmentSearchHelper;
import cz.kpsys.portaro.record.export.RecordExport;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.Chunk;
import cz.kpsys.portaro.search.SingleKeysetPaging;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.lang.Nullable;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.commons.util.ListUtil.getListOfIds;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.KAT1_5;
import static cz.kpsys.portaro.databasestructure.RecordOperationDb.RECORD_OPERATION;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbUpdatedRecordIdChunkSearchLoader implements ChunkLoader<UpdatedRecordId, HarvestingParams, SingleKeysetPaging<UUID>> {

    public static final String CAS = "cas";

    @NonNull SelectedColumnRowMapper<UUID> cursorLastValueRowMapper = new SelectedColumnRowMapper<>(UUID.class, RECORD_OPERATION.RECORD_ID);
    @NonNull UpdatedRecordIdRowMapper rowMapper = new UpdatedRecordIdRowMapper();
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ByIdLoadable<RecordExport, String> recordExportByOaiSetIdLoader;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull ExportedDepartmentSearchHelper exportedDepartmentSearchHelper;
    @NonNull ContextualProvider<Department, List<Fond>> allowedFondsContextualProvider;
    @NonNull ContextualProvider<Department, @NonNull @NotEmpty List<Integer>> exportableExemplarStatusesProvider;
    @NonNull ContextualProvider<Department, @NonNull Integer> pageSizeProvider;


    @Override
    public Chunk<UpdatedRecordId, SingleKeysetPaging<UUID>> loadFirst(@NonNull HarvestingParams params) {
        return loadNext(params, SingleKeysetPaging.forFirstPage(pageSizeProvider.getOn(departmentLoader.getById(params.getDepartment()))));
    }

    @Override
    public Chunk<UpdatedRecordId, SingleKeysetPaging<UUID>> loadNext(@NonNull HarvestingParams params, @NonNull SingleKeysetPaging<UUID> paging) {
        RecordExport recordExport = recordExportByOaiSetIdLoader.getById(params.getSet());
        Department ctx = departmentLoader.getById(params.getDepartment());

        SelectQuery sq = queryFactory.newSelectQuery();

        sq.selectDistinct(
                TC(RECORD_OPERATION.TABLE, RECORD_OPERATION.RECORD_ID),
                AS(MAX(TC(RECORD_OPERATION.TABLE, RECORD_OPERATION.CREATION_DATE)), CAS)
        );
        sq.from(RECORD_OPERATION.TABLE);
        boolean valid = appendWhere(sq, params.getFromDate(), params.getToDate(), recordExport, ctx);
        if (!valid) {
            return Chunk.ofNothing();
        }
        sq.groupBy(TC(RECORD_OPERATION.TABLE, RECORD_OPERATION.RECORD_ID));
        sq.setKeysetRangeLookingBeyondHorizon(pageSizeProvider.getOn(ctx), TC(RECORD_OPERATION.TABLE, RECORD_OPERATION.RECORD_ID), paging.lastValue(), Sorting.ofAsc());

        return TimeMeter.measureAndLog(
                () -> jdbcTemplate.query(sq.getSql(), sq.getParamMap(), new SingleKeysetPagingResultSetExtractor<>(NumberUtil.zeroIfNull(paging.lastValueNumber()), cursorLastValueRowMapper, rowMapper, pageSizeProvider.getOn(ctx), true)),
                log,
                () -> "Fetch OAI document ids by params %s".formatted(params),
                Duration.ofSeconds(2),
                Duration.ofSeconds(5)
        );
    }


    public boolean appendWhere(@NonNull SelectQuery sq, @Nullable Instant fromDate, @Nullable Instant toDate, @NonNull RecordExport recordExport, @NonNull Department ctx) {
        if (fromDate != null) {
            sq.where().and().gtEq(TC(RECORD_OPERATION.TABLE, RECORD_OPERATION.CREATION_DATE), fromDate);
        }

        if (toDate != null) {
            sq.where().and().ltEq(TC(RECORD_OPERATION.TABLE, RECORD_OPERATION.CREATION_DATE), toDate);
        }

        if (recordExport.getRecordOperationTypes() != null) {
            if (recordExport.getRecordOperationTypes().isEmpty()) {
                return false;
            }
            sq.where().and().in(TC(RECORD_OPERATION.TABLE, RECORD_OPERATION.TYPE_ID), ListUtil.getListOfIds(recordExport.getRecordOperationTypes()));
        }

        List<Fond> fonds = getFonds(recordExport, ctx);
        if (fonds.isEmpty()) {
            return false;
        }
        sq.where().and().in(TC(RECORD_OPERATION.TABLE, RECORD_OPERATION.FOND_ID), ListUtil.getListOfIds(fonds));

        if (ListUtil.hasLength(recordExport.getForbiddenRecordIds())) {
            sq.where().and().notIn(TC(RECORD_OPERATION.TABLE, RECORD_OPERATION.RECORD_ID), recordExport.getForbiddenRecordIds());
        }

        if (recordExport.getSqlClause() != null) {
            sq.where().and().addStringCondition(recordExport.getSqlClause());
        }

        List<Department> departments = exportedDepartmentSearchHelper.getDepartments(ctx, recordExport);
        if (departments.isEmpty()) {
            return false;
        }
        if (recordExport.getWithNonexemplared()) {
            sq.joinOrExists(queryFactory, RECORD_OPERATION.TABLE, RECORD_OPERATION.RECORD_ID, RECORD_HOLDING.TABLE, RECORD_HOLDING.RECORD_ID, "exported_record_holdings", (where, alias) -> where
                    .and().in(TC(alias, RECORD_HOLDING.DEPARTMENT_ID), getListOfIds(departments))
            );
        } else {
            // should be same as in ExportedExemplarsLoader
            sq.joinOrExists(queryFactory, RECORD_OPERATION.TABLE, RECORD_OPERATION.RECORD_ID, KAT1_5.TABLE, KAT1_5.RECORD_ID, "exported_record_exemplars", (where, alias) -> where
                    .and().in(TC(alias, KAT1_5.FK_STATUS), exportableExemplarStatusesProvider.getOn(ctx))
                    .and().in(TC(alias, KAT1_5.TYP_CISLA), getListOfIds(List.of(ExemplarType.EXEMPLAR, ExemplarType.BINDING)))
                    .and().in(TC(alias, KAT1_5.FK_PUJC), getListOfIds(departments))
                    .and().isNull(withoutIndex(TC(alias, KAT1_5.FK_UBYTEK)))
            );
        }

        return true;
    }

    private List<Fond> getFonds(@NonNull RecordExport set, @NonNull Department ctx) {
        List<Fond> fonds = ObjectUtil.firstNotNull(set::getFonds, () -> allowedFondsContextualProvider.getOn(ctx));
        if (!set.getWithAuthorities()) {
            fonds = ListUtil.filter(fonds, fond -> !fond.isOfAuthority());
        }
        if (!set.getWithDocuments()) {
            fonds = ListUtil.filter(fonds, fond -> !fond.isOfDocument());
        }
        if (!set.getWithNonperio()) {
            fonds = Fond.filterPeriodical(fonds);
        }
        if (!set.getWithPerio()) {
            fonds = Fond.filterNonperiodical(fonds);
        }
        return fonds;
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class UpdatedRecordIdRowMapper implements RowMapper<UpdatedRecordId> {

        @Override
        public UpdatedRecordId mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
            UUID id = DbUtils.uuidNotNull(rs, RECORD_OPERATION.RECORD_ID);
            Instant date = DbUtils.instantNotNull(rs, CAS);
            return UpdatedRecordId.ofKnownUpdateDate(id, date);
        }
    }

}
