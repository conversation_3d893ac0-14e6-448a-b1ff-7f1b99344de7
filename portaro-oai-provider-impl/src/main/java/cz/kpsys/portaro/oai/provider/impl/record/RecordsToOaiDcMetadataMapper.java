package cz.kpsys.portaro.oai.provider.impl.record;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.IdentifiedValue;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.oai.dc.RecordDcDto;
import cz.kpsys.portaro.oai.dc.RecordDcMetadataResponse;
import cz.kpsys.portaro.oai.model.MetadataContainer;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.export.RecordExport;
import cz.kpsys.portaro.record.query.SingleValFieldGroup;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordsToOaiDcMetadataMapper implements RecordsToOaiMetadataMapper {

    @Override
    public List<IdentifiedValue<UUID, ? extends MetadataContainer>> mapRecords(@NonNull List<Record> records,
                                                                               @NonNull RecordExport recordExport,
                                                                               @NonNull UserAuthentication currentAuth,
                                                                               @NonNull Department currentDepartment) {
        return ListUtil.convert(records, this::convertSingle);
    }

    private IdentifiedValue<UUID, RecordDcMetadataResponse> convertSingle(@NonNull Record record) {
        RecordDcMetadataResponse recordDc = new RecordDcMetadataResponse(new RecordDcDto(
                getTitle(record),
                getCreators(record),
                getSubjects(record),
                getLanguages(record)
        ));
        return IdentifiedValue.of(record.getId(), recordDc);
    }

    private String getTitle(Record record) {
        return record.getName();
    }

    private List<String> getCreators(Record record) {
        return ListUtil.convert(record.query("d100.a,d700.a").purify(), SingleValFieldGroup::getRaw);
    }

    private List<String> getSubjects(Record record) {
        return ListUtil.convert(record.query("d650.a,d651.a,d653.a,d655.a").purify(), SingleValFieldGroup::getRaw);
    }

    private List<String> getLanguages(Record record) {
        return ListUtil.convert(record.query("d41.a").purify(), SingleValFieldGroup::getRaw);
    }
}
