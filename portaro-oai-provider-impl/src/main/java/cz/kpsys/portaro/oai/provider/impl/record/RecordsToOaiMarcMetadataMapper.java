package cz.kpsys.portaro.oai.provider.impl.record;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.IdentifiedValue;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.oai.marc.RecordMarcMetadataResponse;
import cz.kpsys.portaro.oai.model.MetadataContainer;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.export.RecordExport;
import cz.kpsys.portaro.record.export.marc.RecordsToMarcRecordsMapper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordsToOaiMarcMetadataMapper implements RecordsToOaiMetadataMapper {

    @NonNull RecordsToMarcRecordsMapper recordsToMarcRecordsMapper;

    @Override
    public List<IdentifiedValue<UUID, ? extends MetadataContainer>> mapRecords(@NonNull List<Record> records,
                                                                               @NonNull RecordExport recordExport,
                                                                               @NonNull UserAuthentication currentAuth,
                                                                               @NonNull Department currentDepartment) {
        return recordsToMarcRecordsMapper.map(records, recordExport, currentAuth, currentDepartment).stream()
                .map(identifiedRecordMarc -> identifiedRecordMarc.mapValue(RecordMarcMetadataResponse::new))
                .collect(Collectors.toUnmodifiableList());
    }
}
