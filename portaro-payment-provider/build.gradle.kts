dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.junit.jupiter:junit-jupiter-params:+")
    testImplementation("org.mockito:mockito-core:+")
    testImplementation("org.apache.commons:commons-lang3:3.+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-appserver"))
    implementation(project(":portaro-auth"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-commons-db"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-finance"))
    implementation(project(":portaro-payment"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-sql-generator"))

    implementation("org.springframework:spring-context:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework.security:spring-security-core:6.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
}
