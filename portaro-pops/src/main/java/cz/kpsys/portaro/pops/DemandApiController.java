package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Locale;
import java.util.Optional;

@RequestMapping("/api/demands")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DemandApiController extends GenericApiController {

    public static final String OFFERS_SCOPE_NONE = "none";
    public static final String OFFERS_SCOPE_SINGLE_SUPPLIER = "single-supplier";
    public static final String OFFERS_SCOPE_ALL_SUPPLIERS = "all-suppliers";

    @NonNull SecurityManager securityManager;
    @NonNull DemandLoaderSecurityHelper demandLoaderSecurityHelper;
    @NonNull TenderLoader tenderLoader;
    @NonNull ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableItemsConverter;

    @GetMapping
    public List<DemandResponse> getAllByTender(@RequestParam("tender") int tenderId,
                                               @RequestParam("offersScope") String offersScope,
                                               @RequestParam("offersSupplier") Optional<User> offersSupplier,
                                               @CurrentDepartment Department ctx,
                                               UserAuthentication currentAuth,
                                               Locale locale) {
        Tender tender = tenderLoader.getById(tenderId);
        securityManager.throwIfCannot(PopsSecurityActions.POPS_TENDER_SHOW, currentAuth, ctx, tender);

        List<Demand> demands = getDemands(tenderId, offersScope, offersSupplier, ctx, currentAuth);
        return ListUtil.convertStrict(demands, source -> mapDemandToResponse(currentAuth, ctx, locale, source));
    }

    private List<Demand> getDemands(int tenderId, String offersScope, Optional<User> offersSupplier, Department ctx, UserAuthentication currentAuth) {
        return switch (offersScope) {
            case OFFERS_SCOPE_NONE -> demandLoaderSecurityHelper.getAllByTenderWithoutOffers(ctx, currentAuth, tenderId);
            case OFFERS_SCOPE_SINGLE_SUPPLIER -> demandLoaderSecurityHelper.getAllByTenderWithSupplierOffers(ctx, currentAuth, tenderId, offersSupplier.orElseThrow(() -> new IllegalArgumentException("Supplier is required when offers scope is supplier")));
            case OFFERS_SCOPE_ALL_SUPPLIERS -> demandLoaderSecurityHelper.getAllByTenderWithAllOffers(ctx, currentAuth, tenderId);
            default -> throw new IllegalStateException("Unexpected offersScope value: " + offersScope);
        };
    }

    private DemandResponse mapDemandToResponse(@NonNull UserAuthentication currentAuth, Department ctx, Locale locale, Demand source) {
        return new DemandResponse(
                source.getId(),
                source.getText(),
                recordsToViewableItemsConverter.convertSingle(source.getDocument(), currentAuth, ctx, locale),
                source.getVolume(),
                source.getDesiredQuantity(),
                source.isElectronicForm(),
                source.getSupplyDeadline(),
                source.getOffers()
        );
    }

}
