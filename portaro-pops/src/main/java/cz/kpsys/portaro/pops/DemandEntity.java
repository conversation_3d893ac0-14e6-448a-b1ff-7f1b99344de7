package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.commons.object.Identified;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.util.UUID;

@Value
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class DemandEntity implements Identified<String> {

    public static final int SUPPLY_TYPE_ELECTRONIC = 1;

    @EqualsAndHashCode.Include
    @NonNull
    String id;
    @NonNull Integer tenderId;
    @NonNull UUID recordId;
    @Nullable Integer volumeId;
    @NonNull Integer desiredQuantity;
    @NonNull BigDecimal minEstimatedPrice;
    @NonNull BigDecimal maxEstimatedPrice;
    @Nullable Integer daysToSupply;
    @NonNull Integer supplyTypeId;

}
