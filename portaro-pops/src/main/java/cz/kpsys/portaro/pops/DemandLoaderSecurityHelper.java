package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;

import java.util.List;

public interface DemandLoaderSecurityHelper {

    List<Demand> getAllByTenderWithoutOffers(Department ctx, UserAuthentication currentAuth, int tenderId);

    List<Demand> getAllByTenderWithSupplierOffers(Department ctx, UserAuthentication currentAuth, int tenderId, User supplierCompany);

    List<Demand> getAllByTenderWithAllOffers(Department ctx, UserAuthentication currentAuth, int tenderId);

}
