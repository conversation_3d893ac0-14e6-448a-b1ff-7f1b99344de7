package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DemandLoaderSecurityHelperImpl implements DemandLoaderSecurityHelper {

    @NonNull SecurityManager securityManager;
    @NonNull DemandLoader demandLoader;

    @Override
    public List<Demand> getAllByTenderWithoutOffers(Department ctx, UserAuthentication currentAuth, int tenderId) {
        return demandLoader.getAllByTenderWithoutOffers(tenderId);
    }

    @Override
    public List<Demand> getAllByTenderWithSupplierOffers(Department ctx, UserAuthentication currentAuth, int tenderId, User supplierCompany) {
        securityManager.throwIfCannot(PopsSecurityActions.POPS_SUPPLIER_OFFERS_SHOW, currentAuth, ctx, supplierCompany);
        return demandLoader.getAllByTenderWithSupplierOffers(tenderId, supplierCompany.getRecordId());
    }

    @Override
    public List<Demand> getAllByTenderWithAllOffers(Department ctx, UserAuthentication currentAuth, int tenderId) {
        securityManager.throwIfCannot(PopsSecurityActions.POPS_ALL_OFFERS_SHOW, currentAuth, ctx);
        return demandLoader.getAllWithAllOffers(tenderId);
    }
}
