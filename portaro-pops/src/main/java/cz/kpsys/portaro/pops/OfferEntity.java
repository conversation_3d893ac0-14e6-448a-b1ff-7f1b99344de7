package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Min;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import static cz.kpsys.portaro.pops.PopsDb.POPS_NABIDKY.*;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = TABLE)
@NoArgsConstructor
@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OfferEntity implements Identified<Integer> {

    @NonNull
    @Id
    @Column(name = ID_NABIDKY)
    Integer id;

    @NonNull
    @Column(name = FK_POPTAVKY)
    String demandId;

    @NonNull
    @Column(name = FK_UZIV)
    Integer supplierRepresentativeId;

    @NonNull
    @Column(name = SUPPLIER_RECORD_ID)
    UUID supplierRecordId;

    @NonNull
    @Min(1)
    @Column(name = POC_DODATELNYCH)
    Integer quantity;

    @NonNull
    @Column(name = CENA_JEDN)
    BigDecimal priceAmount;

    @NonNull
    @Column(name = CAS)
    Instant createDate;

    @Nullable
    @Column(name = FK_UZIV_SMAZ)
    Integer deletedByUserId;

    @Nullable
    @Column(name = CAS_SMAZANI)
    Instant deleteDate;

    @NullableNotBlank
    @Column(name = POZNAMKA)
    String note;

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, OfferEntity.class);
    }

    @Override
    public int hashCode() {
        return getId().hashCode();
    }
}
