package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.security.Action;
import cz.kpsys.portaro.user.User;

public class PopsSecurityActions {
    //pops
    public static final Action<Tender> POPS_TENDER_SHOW = Action.withSubject("PopsTenderShow", Tender.class);
    public static final Action<Tender> POPS_TENDER_SAVE = Action.withSubject("PopsTenderSave", Tender.class);
    public static final Action<Tender> POPS_TENDER_DELETE = Action.withSubject("PopsTenderDelete", Tender.class);
    public static final Action<Void> POPS_ALL_OFFERS_SHOW = Action.withoutSubject("PopsAllOffersShow");
    public static final Action<User> POPS_SUPPLIER_OFFERS_SHOW = Action.withSubject("PopsSupplierOffersShow", User.class);
    public static final Action<Offer> POPS_OFFER_SHOW = Action.withSubject("PopsOfferShow", Offer.class);
    public static final Action<Offer> POPS_OFFER_SAVE = Action.withSubject("PopsOfferSave", Offer.class);
    public static final Action<Offer> POPS_OFFER_DELETE = Action.withSubject("PopsOfferDelete", Offer.class);
}
