package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;

import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.DAYS;

@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TenderLoaderDelegating implements TenderLoader {

    @NonNull Codebook<Tender, Integer> tenderCodebook;

    @Override
    public List<Tender> getAllActive() {
        return tenderCodebook.getAll().stream()
                .filter(tender -> !tender.isDeleted())
                .filter(tender -> tender.getOpenDate().isBefore(now()))
                .filter(tender -> tender.getEndDate().plus(180, DAYS).isAfter(now()))
                .toList();
    }

    @Override
    public Tender getById(@NonNull Integer id) throws ItemNotFoundException {
        return tenderCodebook.getById(id);
    }
}
