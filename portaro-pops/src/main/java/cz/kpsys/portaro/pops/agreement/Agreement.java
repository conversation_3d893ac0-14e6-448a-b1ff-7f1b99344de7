package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.SoftDeletable;
import org.springframework.lang.NonNull;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

public interface Agreement extends Identified<String>, SoftDeletable {

    @NonNull Integer getTenderId();

    @NonNull UUID getSupplierRecordId();

    @NonNull Instant getCreateDate();

    Optional<Instant> getConfirmDate();

    void confirm(Instant when);
}
