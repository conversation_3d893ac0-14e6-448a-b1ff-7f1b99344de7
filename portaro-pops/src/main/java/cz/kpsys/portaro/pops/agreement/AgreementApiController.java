package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.ActiveUser;
import cz.kpsys.portaro.commons.web.DownloadFileStreamConsumer;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.pops.Tender;
import cz.kpsys.portaro.pops.TenderLoader;
import cz.kpsys.portaro.security.AccessDeniedException;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.web.GenericApiController;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping("/api/tenders/{tender}/agreements")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AgreementApiController extends GenericApiController {

    @NonNull TenderLoader tenderLoader;
    @NonNull FileAgreementCreator fileAgreementCreator;
    @NonNull AgreementProcessor agreementProcessor;
    @NonNull SignedAgreementUploader signedAgreementUploader;


    @GetMapping("confirm")
    public ActionResponse confirmAgreement(@PathVariable("tender") int tenderId,
                                           @RequestParam("supplier") User supplier,
                                           @CurrentDepartment Department ctx,
                                           UserAuthentication currentAuth) {
        Agreement agreement = agreementProcessor.processAgreementConfirmation(new AgreementProcessConfirmationCommand(tenderId, supplier, currentAuth, ctx));
        return FinishedSaveResponse.saved(agreement);
    }


    @GetMapping("original")
    public void downloadOriginalAgreement(@PathVariable("tender") int tenderId,
                                          @RequestParam("supplier") User supplier,
                                          HttpServletResponse response,
                                          @CurrentDepartment Department ctx,
                                          UserAuthentication currentAuth) {
        Tender tender = tenderLoader.getById(tenderId);
        AccessDeniedException.throwIfNot(tender.isAcceptingSignedAgreement());

        DownloadFileStreamConsumer downloadStreamConsumer = new DownloadFileStreamConsumer(response)
                .forceAttachmentContentDisposition();
        fileAgreementCreator.createBlankAgreementFile(tender, supplier.getRecordId(), downloadStreamConsumer, currentAuth, ctx);
    }


    @PostMapping("signed")
    public ActionResponse uploadSignedAgreement(@PathVariable("tender") int tenderId,
                                                @RequestParam("supplier") User supplier,
                                                @RequestParam("fileData") MultipartFile fileData,
                                                @ActiveUser User activeUser,
                                                @CurrentDepartment Department ctx,
                                                UserAuthentication currentAuth) {
        Agreement agreement = signedAgreementUploader.uploadSignedAgreement(new SignedAgreementUploadCommand(
                tenderId,
                supplier,
                fileData,
                activeUser,
                currentAuth,
                ctx
        ));
        return FinishedSaveResponse.saved(agreement);
    }

}
