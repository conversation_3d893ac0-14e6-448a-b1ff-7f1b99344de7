package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.id.UuidGenerator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ButtonAgreementCreator implements AgreementCreator {

    @NonNull Saver<AgreementSaveCommand, Agreement> agreementSaver;
    @NonNull TransactionTemplate readwriteTransactiontemplate;

    public Agreement create(@NonNull AgreementCreationCommand agreementCreationCommand) {
        return readwriteTransactiontemplate.execute(_ ->
                agreementSaver.save(
                        new AgreementSaveCommand(
                                new ButtonAgreement(
                                        UuidGenerator.forIdentifierWithoutDashes(),
                                        agreementCreationCommand.tenderId(),
                                        StringToUuidConverter.fromString(agreementCreationCommand.supplierRecordId()),
                                        agreementCreationCommand.createDate(),
                                        agreementCreationCommand.confirmDate(),
                                        agreementCreationCommand.deleteDate()
                                ),
                                agreementCreationCommand.currentAuth(),
                                agreementCreationCommand.ctx()
                        )
                )
        );
    }

}
