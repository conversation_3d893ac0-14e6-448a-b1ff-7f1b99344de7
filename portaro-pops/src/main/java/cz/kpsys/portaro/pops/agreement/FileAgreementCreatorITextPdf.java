package cz.kpsys.portaro.pops.agreement;

import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.font.constants.StandardFonts;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.io.FileStreamConsumer;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.pops.Demand;
import cz.kpsys.portaro.pops.DemandLoader;
import cz.kpsys.portaro.pops.Offer;
import cz.kpsys.portaro.pops.Tender;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.prop.RecordPropertyKeys;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import lombok.SneakyThrows;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;

import java.io.ByteArrayOutputStream;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;


public class FileAgreementCreatorITextPdf implements FileAgreementCreator {

    public static final int STANDARD_FONT_SIZE = 12;
    public static final int TABLE_FONT_SIZE = 8;
    public static final int H1_FONT_SIZE = 18;
    private final DemandLoader demandLoader;
    private final Converter<Price, String> priceToStringConverter;
    private final Provider<Boolean> includeFooterWithSigningRequirements;
    private final IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;

    public FileAgreementCreatorITextPdf(DemandLoader demandLoader, Provider<Boolean> includeFooterWithSigningRequirements, IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader) {
        this.demandLoader = demandLoader;
        this.includeFooterWithSigningRequirements = includeFooterWithSigningRequirements;
        this.nonDetailedRichRecordLoader = nonDetailedRichRecordLoader;
        this.priceToStringConverter = new Converter<>() {
            final DecimalFormat currencyFormat = new DecimalFormat("0.00", DecimalFormatSymbols.getInstance(CatalogConstants.DEFAULT_LOCALE));

            @Override
            public String convert(@NonNull Price price) {
                return price.currency().format(price.amount(), currencyFormat);
            }
        };
    }

    private static void addEmptyLine(Paragraph paragraph, int number) {
        for (int i = 0; i < number; i++) {
            paragraph.add(new Paragraph(" "));
        }
    }

    @Override
    public void createBlankAgreementFile(@NonNull Tender tender,
                                         @NonNull UUID supplierCompanyAuthorityId,
                                         @NonNull FileStreamConsumer streamConsumer,
                                         @NonNull UserAuthentication currentAuth,
                                         @NonNull Department ctx) {
        List<Demand> demands = demandLoader.getAllByTenderWithSupplierOffers(tender.getId(), supplierCompanyAuthorityId);
        demands = ListUtil.filter(demands, object -> !object.getOffers().isEmpty());
        Assert.notEmpty(demands, "Supplier has not any offers");

        byte[] pdfFileBytes = createPdf(supplierCompanyAuthorityId, demands);

        String pdfFilename = SignedFileAgreement.FILENAME_PATTERN_ORIGINAL
                .withParameter("tenderId", tender.getId())
                .withParameter("supplierRecordId", supplierCompanyAuthorityId)
                .build();

        FileStreamConsumer.streamBytes(streamConsumer, pdfFilename, pdfFileBytes);
    }

    private byte[] createPdf(UUID supplierCompanyAuthorityId, List<Demand> demands) {
        ByteArrayOutputStream pdfWritting = new ByteArrayOutputStream();
        try (
                PdfDocument pdfDocument = new PdfDocument(new PdfWriter(pdfWritting));
                Document document = new Document(pdfDocument)) {

            fillPdf(pdfDocument, document, demands, supplierCompanyAuthorityId);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return pdfWritting.toByteArray();
    }

    @SneakyThrows
    private void fillPdf(PdfDocument pdfDocument, Document document, List<Demand> demands, UUID supplierCompanyId) {

        boolean atLeastOnePeriodical = demands.stream().anyMatch(this::isPerio);
        boolean atLeastOneMonography = demands.stream().anyMatch(d -> !isPerio(d));

        Record supplierCompany = nonDetailedRichRecordLoader.getById(supplierCompanyId);

        pdfDocument.getDocumentInfo().setCreator("Portaro, library system KP-SYS s.r.o.");
        pdfDocument.getDocumentInfo().setTitle(String.format("Agreement of offers by supplier %s", supplierCompany.getName()));
        pdfDocument.getDocumentInfo().setSubject("Agreement");
        pdfDocument.getDocumentInfo().setAuthor("Portaro, library system KP-SYS s.r.o.");

        PdfFont baseFont = PdfFontFactory.createFont(StandardFonts.HELVETICA, PdfEncodings.CP1250, PdfFontFactory.EmbeddingStrategy.PREFER_EMBEDDED);
        document.setFont(baseFont).setFontSize(STANDARD_FONT_SIZE);

        Paragraph preface = new Paragraph();
        addEmptyLine(preface, 1);
        preface.add(createH1Paragraph("Potvrzení nabídek dodavatele %s".formatted(supplierCompany.getName())));
        document.add(preface);


        Paragraph documentsAgreementPar = new Paragraph();
        addEmptyLine(documentsAgreementPar, 1);
        documentsAgreementPar.add(createStdParagraph("Dodavatel se seznámil a souhlasí se zněním obchodních podmínek / smlouvy."));
        documentsAgreementPar.add(createStdParagraph("Dodavatel čestně prohlašuje, že splňuje požadované kvalifikační předpoklady."));
        document.add(documentsAgreementPar);


        Paragraph upperTableInfo = new Paragraph();
        addEmptyLine(upperTableInfo, 2);
        upperTableInfo.add(createStdParagraph("Tímto dokumentem potvrzuji platnost následujících nabídek:"));
        addEmptyLine(upperTableInfo, 1);
        document.add(upperTableInfo);


        float[] columnWidths = getColumnWidths(atLeastOnePeriodical, atLeastOneMonography);
        Table table = new Table(UnitValue.createPercentArray(columnWidths)).useAllAvailableWidth();

        table.addHeaderCell(createHeaderCell("Dokument"));
        if (atLeastOneMonography) {
            table.addHeaderCell(createHeaderCell("Autor"));
        }
        if (atLeastOnePeriodical) {
            table.addHeaderCell(createHeaderCell("Ročník"));
        }
        table.addHeaderCell(createHeaderCell("Forma"));
        table.addHeaderCell(createHeaderCell("Termín dodání"));
        table.addHeaderCell(createHeaderCell("Množství"));
        table.addHeaderCell(createHeaderCell("Cena/ks"));
        table.addHeaderCell(createHeaderCell("Cena"));

        for (Demand d : demands) {
            table.addCell(createCellParagraph(d.getDocument().getName()));
            if (atLeastOneMonography) {
                table.addCell(createCellParagraph(d.getDocument().getProps().find(RecordPropertyKeys.PRIMARY_AUTHORS).orElseGet(List::of).stream().map(FileAgreementCreatorITextPdf::localize).collect(Collectors.joining(", "))));
            }
            if (atLeastOnePeriodical) {
                table.addCell(createCellParagraph(d.getVolume() == null ? null : d.getVolume().getYear()));
            }
            table.addCell(createCellParagraph(d.isElectronicForm() ? "Elektronická" : "Tištěná"));
            table.addCell(createCellParagraph(isPerio(d) ? "Dle smlouvy" : d.getDaysToSupply() + " dní ode dne odeslání objednávky"));
            Offer offer = d.getOffers().getFirst();
            table.addCell(createCellParagraph(String.valueOf(offer.getQuantity())));
            table.addCell(createCellParagraph(priceToStringConverter.convert(offer.getPrice())));
            table.addCell(createCellParagraph(priceToStringConverter.convert(offer.getTotalPrice())));
        }

        document.add(table);


        if (includeFooterWithSigningRequirements.get()) {
            Paragraph footer = new Paragraph();
            addEmptyLine(footer, 4);
            footer.add(createStdParagraph("Bez validního elektronického podpisu je tento dokument neplatný."));
            footer.add(createStdParagraph("Po změně, přidání nebo smazání některé nabídky v řízení je třeba elektronicky podepsat nově vygenerovaný dokument."));
            document.add(footer);
        }
    }

    private static String localize(Labeled labeled) {
        return labeled.getText().toString();
    }

    private Paragraph createH1Paragraph(String text) {
        return new Paragraph(text).setFontSize(H1_FONT_SIZE);
    }

    private Paragraph createStdParagraph(String text) {
        return new Paragraph(text);
    }

    private static float[] getColumnWidths(boolean atLeastOnePeriodical, boolean atLeastOneMonography) {
        if (atLeastOneMonography && atLeastOnePeriodical) {
            return new float[] {6, 4, 2, 3, 3, 2, 2, 2};
        }
        if (atLeastOneMonography) {
            return new float[] {6, 4, 3, 3, 2, 2, 2};
        }
        if (atLeastOnePeriodical) {
            return new float[] {6, 2, 3, 3, 2, 2, 2};
        }
        throw new IllegalStateException();
    }

    private Paragraph createCellParagraph(@Nullable String text) {
        if (text == null) {
            return new Paragraph();
        }
        return new Paragraph(text).setFontSize(TABLE_FONT_SIZE);
    }

    private boolean isPerio(Demand demand) {
        return demand.getVolume() != null;
    }

    private Cell createHeaderCell(String text) {
        return new Cell()
                .add(createCellParagraph(text))
                .setTextAlignment(TextAlignment.CENTER);
    }
}
