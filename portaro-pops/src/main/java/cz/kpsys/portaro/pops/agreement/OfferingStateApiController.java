package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.pops.Tender;
import cz.kpsys.portaro.pops.TenderLoader;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.Institution;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.role.supplier.SupplierRole;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/tenders/{tender}")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class OfferingStateApiController extends GenericApiController {

    @NonNull TenderLoader tenderLoader;
    @NonNull OfferingStateResolver offeringStateResolver;


    @GetMapping("offering-states")
    public List<OfferingState> getOfferingStates(@PathVariable("tender") int tenderId,
                                                 @RequestParam("supplier") User supplierCompanyUser,
                                                 UserAuthentication currentAuth) {
        if (!(supplierCompanyUser instanceof Institution supplierCompany)) {
            return List.of();
        }
        if ((!currentAuth.getActiveUser().equals(supplierCompany) && !currentAuth.getRole().contains(BasicUser.ROLE_ADMIN)) || !supplierCompany.hasRole(SupplierRole.class)) {
            return List.of();
        }
        Tender tender = tenderLoader.getById(tenderId);
        OfferingState validationResult = offeringStateResolver.getState(tender, supplierCompany);
        return List.of(validationResult);
    }

}
