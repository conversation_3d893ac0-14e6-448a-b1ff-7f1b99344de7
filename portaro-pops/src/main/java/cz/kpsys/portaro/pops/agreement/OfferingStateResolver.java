package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.pops.Offer;
import cz.kpsys.portaro.pops.OfferLoader;
import cz.kpsys.portaro.pops.Tender;
import cz.kpsys.portaro.user.Institution;
import cz.kpsys.portaro.user.role.supplier.SupplierRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static cz.kpsys.portaro.pops.agreement.AgreementStyle.FILE_SIGNING;
import static cz.kpsys.portaro.pops.agreement.OfferingStepType.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class OfferingStateResolver {

    private static final List<OfferingStepType> FILE_SIGNING_REQUIRED_STEPS = List.of(
            TENDER_START,
            OFFER_ADD,
            TERMS_AND_CONDITIONS_ACCEPT,
            AGREEMENT_FILE_DOWNLOAD,
            SIGNED_AGREEMENT_FILE_UPLOAD,
            TENDER_EVALUATION
    );
    private static final List<OfferingStepType> BUTTON_REQUIRED_STEPS = List.of(
            TENDER_START,
            OFFER_ADD,
            TERMS_AND_CONDITIONS_ACCEPT,
            AGREEMENT_CONFIRM,
            TENDER_EVALUATION
    );

    @NonNull AgreementLoader agreementLoader;
    @NonNull OfferLoader offerLoader;
    @NonNull Provider<AgreementStyle> agreementStyleProvider;


    public OfferingState getState(Tender tender, Institution supplierInstitution) {
        List<OfferingStepType> requiredSteps = agreementStyleProvider.get() == FILE_SIGNING ? FILE_SIGNING_REQUIRED_STEPS : BUTTON_REQUIRED_STEPS;
        List<OfferingStepType> finishedStepTypes = getFinishedSteps(tender, supplierInstitution);

        List<OfferingStep> steps = ListUtil.convert(requiredSteps, requiredStepType -> {
            boolean done = finishedStepTypes.contains(requiredStepType);
            boolean feasible = !requiredStepType.isRequiringNotFinishedTenderToBeFeasible() || tender.getPhase() < Tender.PHASE_FINISHED;
            return new OfferingStep(requiredStepType, done, feasible);
        });

        return new OfferingState(tender.getId(), steps);
    }


    private List<OfferingStepType> getFinishedSteps(Tender tender, Institution supplierInstitution) {
        Assert.state(supplierInstitution.hasRole(SupplierRole.class), "User has not supplier's role");
        ArrayList<OfferingStepType> successfulSteps = new ArrayList<>();

        // tendr musi byt zahajem
        if (tender.getPhase() >= Tender.PHASE_STARTED) {
            successfulSteps.add(TENDER_START);

            // zjistime, zda ma uzivatel vubec nejake nabidky
            List<? extends Offer> offersOfThisTenderAndUser = offerLoader.getAllByTenderAndSupplierCompany(tender, supplierInstitution.getRecordId(), false);
            if (!offersOfThisTenderAndUser.isEmpty()) { // ma nabidky
                successfulSteps.add(OFFER_ADD);

                // pokud nejake nabidky ma, nacteme stazeny agreement
                Optional<Agreement> agreement = agreementLoader.getBySupplierAndTender(supplierInstitution.getRecordId(), tender.getId());
                if (agreement.isPresent()) { // stahnul agreement, takze i odfajfkoval podminky
                    successfulSteps.add(TERMS_AND_CONDITIONS_ACCEPT);
                    successfulSteps.add(AGREEMENT_FILE_DOWNLOAD);

                    if (agreement.get().getConfirmDate().isPresent()) { // mame i potvrzeni, zjistime datum posledni zmeny tendru
                        // pokud mame i potvrzeni, zjistime datum posledni zmeny tendru
                        Instant lastTenderUpdateDate = getLastTenderUpdateDate(offersOfThisTenderAndUser);

                        // zjistime, zda je datum potvrzeni vetsi nez datum posledni zmeny tendru
                        if (agreement.get().getCreateDate().isAfter(lastTenderUpdateDate)) {
                            successfulSteps.add(SIGNED_AGREEMENT_FILE_UPLOAD);
                            successfulSteps.add(AGREEMENT_CONFIRM);

                            // zjistime, zda je tendr jiz ukoncen
                            if (tender.getPhase() >= Tender.PHASE_FINISHED) {
                                successfulSteps.add(TENDER_EVALUATION);
                            }
                        }
                    }
                }
            }
        }

        return successfulSteps;
    }


    private Instant getLastTenderUpdateDate(List<? extends Offer> offersOfThisTenderAndUser) {
        boolean seen = false;
        Instant best = null;
        for (Offer o : offersOfThisTenderAndUser) {
            Instant instant = o.getDeleteDate().orElseGet(o::getCreateDate);
            if (!seen || instant.compareTo(best) > 0) {
                seen = true;
                best = instant;
            }
        }
        return (seen ? Optional.of(best) : Optional.<Instant>empty())
                .orElseThrow(() -> new IllegalStateException("offersOfThisTenderAndUser list cannot be empty"));
    }

}
