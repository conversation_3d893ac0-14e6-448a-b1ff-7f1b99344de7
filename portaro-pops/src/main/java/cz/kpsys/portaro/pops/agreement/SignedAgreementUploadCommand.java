package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import lombok.NonNull;
import org.springframework.web.multipart.MultipartFile;

public record SignedAgreementUploadCommand(
        @NonNull Integer tenderId,
        @NonNull User supplier,
        @NonNull MultipartFile fileData,
        @NonNull User activeUser,
        @NonNull UserAuthentication currentAuth,
        @NonNull Department ctx
){
}
