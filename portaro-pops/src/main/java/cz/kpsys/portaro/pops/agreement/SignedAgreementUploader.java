package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.file.ByteArrayLoadedIdentifiedFile;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.IdentifiedFileImpl;
import cz.kpsys.portaro.file.LoadedIdentifiedFile;
import cz.kpsys.portaro.pops.Tender;
import cz.kpsys.portaro.pops.TenderLoader;
import cz.kpsys.portaro.security.AccessDeniedException;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SignedAgreementUploader {

    @NonNull TransactionTemplate readwriteTransactionTemplate;
    @NonNull AgreementConfirmer agreementConfirmer;
    @NonNull Saver<AgreementSaveCommand, Agreement> agreementSaver;
    @NonNull AgreementLoader agreementLoader;
    @NonNull TenderLoader tenderLoader;

    public Agreement uploadSignedAgreement(@NonNull SignedAgreementUploadCommand command) {
        return readwriteTransactionTemplate.execute(_ -> {
            Tender tender = tenderLoader.getById(command.tenderId());
            AccessDeniedException.throwIfNot(tender.isAcceptingSignedAgreement());
            if (command.fileData().isEmpty()) {
                throw new IllegalArgumentException();
            }

            IdentifiedFile signedAgreementFile = IdentifiedFileImpl.createNewFile(
                    SignedFileAgreement.FILENAME_PATTERN_SIGNED
                            .withParameter("tenderId", command.tenderId())
                            .withParameter("supplierRecordId", command.supplier().getRecordId())
                            .build(),
                    command.fileData().getSize(),
                    command.activeUser().getId()
            );
            LoadedIdentifiedFile loadedSignedAgreementFile = getLoadedIdentifiedFile(command, signedAgreementFile);
            LoadedSignedFileAgreement loadedAgreement = getOriginalLoadedAgreement(command.tenderId(), command.supplier());
            loadedAgreement.sign(loadedSignedAgreementFile);
            agreementSaver.save(new AgreementSaveCommand(loadedAgreement, command.currentAuth(), command.ctx()));

            agreementConfirmer.confirmAgreement(loadedAgreement, command.currentAuth(), command.ctx());
            return loadedAgreement;
        });
    }

    @SneakyThrows()
    LoadedIdentifiedFile getLoadedIdentifiedFile(SignedAgreementUploadCommand command, IdentifiedFile signedAgreementFile) {
        return new ByteArrayLoadedIdentifiedFile(signedAgreementFile, command.fileData().getBytes());
    }

    LoadedSignedFileAgreement getOriginalLoadedAgreement(int tenderId, User supplier) {
        return agreementLoader.getBySupplierAndTender(supplier.getRecordId(), tenderId)
                .filter(LoadedSignedFileAgreement.class::isInstance)
                .map(LoadedSignedFileAgreement.class::cast)
                .orElseThrow(() -> new ItemNotFoundException(LoadedSignedFileAgreement.class, String.format("supplierRecordId=%s, tenderId=%s", supplier.getRecordId(), tenderId)));
    }

}
