package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import cz.kpsys.portaro.file.IdentifiedFile;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SignedFileAgreement extends BasicAgreement implements Agreement {

    public static final PlaceholderTemplate FILENAME_PATTERN_ORIGINAL = new PlaceholderTemplate("Agreement_{tenderId}_{supplierRecordId}_original.pdf");
    public static final PlaceholderTemplate FILENAME_PATTERN_SIGNED = new PlaceholderTemplate("Agreement_{tenderId}_{supplierRecordId}_signed.pdf");

    @NonNull IdentifiedFile originalFile;
    @NonFinal @Nullable IdentifiedFile signedFile;

    public SignedFileAgreement(String id, @NonNull Integer tenderId, @NonNull UUID supplierRecordId, @NonNull Instant createDate, @Nullable Instant confirmDate, @Nullable Instant deleteDate, @NonNull IdentifiedFile originalFile, @Nullable IdentifiedFile signedFile) {
        super(id, tenderId, supplierRecordId, createDate, confirmDate, deleteDate);
        this.originalFile = originalFile;
        this.signedFile = signedFile;
    }


    public IdentifiedFile getOriginalFile() {
        return originalFile;
    }


    public Optional<? extends IdentifiedFile> getSignedFile() {
        return Optional.ofNullable(signedFile);
    }


    public void sign(IdentifiedFile signedFile) {
        this.signedFile = signedFile;
    }

}
