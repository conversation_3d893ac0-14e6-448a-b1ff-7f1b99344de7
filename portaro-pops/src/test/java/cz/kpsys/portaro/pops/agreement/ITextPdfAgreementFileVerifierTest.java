package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.localization.TestingTranslator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.ByteArrayLoadedIdentifiedFile;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;

import static org.junit.jupiter.api.Assertions.*;

@Tag("ci")
@Tag("unit")
public class ITextPdfAgreementFileVerifierTest {

    public static final String ORIGINAL_FILE_NAME = "Agreement_5_123.pdf";
    public static final String SIGNED_FILE_NAME = "Agreement_5_123_podepsany.pdf";

    @Test
    public void shouldPassOnSignedFile() {
        ByteArrayLoadedIdentifiedFile signedFile = ByteArrayLoadedIdentifiedFile.createFromResource(new ClassPathResource(SIGNED_FILE_NAME), 123);

        ITextPdfAgreementFileVerifier verifier = new ITextPdfAgreementFileVerifier();
        AgreementFileVerificationResult res = verifier.verifyAgreement(signedFile);

        assertTrue(res.success());
    }

    @Test
    public void shouldFailOnUnsignedFile() {
        ByteArrayLoadedIdentifiedFile notSignedFile = ByteArrayLoadedIdentifiedFile.createFromResource(new ClassPathResource(ORIGINAL_FILE_NAME), 123);

        ITextPdfAgreementFileVerifier verifier = new ITextPdfAgreementFileVerifier();
        AgreementFileVerificationResult res = verifier.verifyAgreement(notSignedFile);

        assertFalse(res.success());
        String message = res.message().localize(new TestingTranslator<>().with("file.PdfFileNotSigned"), Department.testingRoot(), CoreConstants.Locales.CS);
        assertEquals("file.PdfFileNotSigned", message);
    }
}