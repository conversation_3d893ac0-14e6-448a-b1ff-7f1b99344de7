package cz.kpsys.portaro.maps;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * Created by Jan on 12. 1. 2015.
 */
public class Coordinates {

    public static double round(double a) {
        return Math.round(a * 1000000.0) / 1000000.0;
    }

    /**
     * napr "54°42'56.4''N 20°30'50.9''E"
     *
     * @param coordinatesString
     * @return
     */
    public static Coordinates parseFromDegreesFormat(String coordinatesString) {
        String s = coordinatesString;

        //nejdrive vycistime string
        s = s.replace("\"", "''"); //nahrazeni vsech " za ''

        //rozdelime lat a lng
        int indexOfSpaceChar = Math.max(s.indexOf("N"), s.indexOf("W")) + 1;
        String lat = s.substring(0, indexOfSpaceChar);

        int indexOfLng = indexOfSpaceChar + (s.charAt(indexOfSpaceChar) == ' ' ? 1 : 0); //pokud je mezi lat a lng mezera, posunem se jeste dal
        String lng = s.substring(indexOfLng);

        return parseFromDegreesFormat(lat, lng);
    }

    /**
     * @param lat napr "54°42'56.4''N"
     * @param lng
     * @return
     */
    public static Coordinates parseFromDegreesFormat(String lat, String lng) {
        int idxStart;
        int idxEnd;


        idxStart = 0;
        idxEnd = lat.indexOf('°', idxStart);
        int latDeg = Integer.parseInt(lat.substring(idxStart, idxEnd));

        idxStart = idxEnd + 1;
        idxEnd = lat.indexOf('\'', idxStart);
        int latMin = Integer.parseInt(lat.substring(idxStart, idxEnd));

        idxStart = idxEnd + 1;
        idxEnd = lat.indexOf("\'\'", idxStart);
        double latSec = Double.parseDouble(lat.substring(idxStart, idxEnd));


        idxStart = 0;
        idxEnd = lng.indexOf('°', idxStart);
        int lngDeg = Integer.parseInt(lng.substring(idxStart, idxEnd));

        idxStart = idxEnd + 1;
        idxEnd = lng.indexOf('\'', idxStart);
        int lngMin = Integer.parseInt(lng.substring(idxStart, idxEnd));

        idxStart = idxEnd + 1;
        idxEnd = lng.indexOf("\'\'", idxStart);
        double lngSec = Double.parseDouble(lng.substring(idxStart, idxEnd));


        return new Coordinates(latDeg, latMin, latSec, lngDeg, lngMin, lngSec);
    }

    public static @Nullable
    Coordinates parseFromDecimalFormat(@NonNull String decimalFormat) {
        String[] parts = decimalFormat.split(",");

        if (parts.length != 2) {
            return null;
        }

        String longitude = null;
        String latitude = null;

        for (String part : parts) {
            String trimmed = part.trim();

            switch (trimmed.charAt(trimmed.length() - 1)) {
                case 'S':
                    latitude = "-" + trimmed.substring(0, trimmed.length() - 2);
                    break;
                case 'N':
                    latitude = trimmed.substring(0, trimmed.length() - 2);
                    break;
                case 'E':
                    longitude = trimmed.substring(0, trimmed.length() - 2);
                    break;
                case 'W':
                    longitude = "-" + trimmed.substring(0, trimmed.length() - 2);
                    break;
                default:
                    return null;
            }
        }

        if (longitude == null || latitude == null) {
            return null;
        }

        try {
            return new Coordinates(Double.parseDouble(latitude), Double.parseDouble(longitude));
        } catch (Throwable throwable) {
            return null;
        }
    }

    private final double lat;
    private final double lng;

    public Coordinates(double lat, double lng) {
        this.lat = lat;
        this.lng = lng;
    }

    public Coordinates(int latDeg, int latMin, double latSec, int lngDeg, int lngMin, double lngSec) {
        this.lat = round(latDeg + (latMin / (double) 60) + (latSec / (double) 3600));
        this.lng = round(lngDeg + (lngMin / (double) 60) + (lngSec / (double) 3600));
    }

    public double getLat() {
        return lat;
    }

    public double getLng() {
        return lng;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Coordinates)) {
            return false;
        }

        Coordinates that = (Coordinates) o;

        if (Double.compare(that.lat, lat) != 0) {
            return false;
        }
        return Double.compare(that.lng, lng) == 0;
    }

    @Override
    public int hashCode() {
        int result;
        long temp;
        temp = Double.doubleToLongBits(lat);
        result = (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(lng);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        return result;
    }

    @Override
    public String toString() {
        return lat + "," + lng;
    }
}
