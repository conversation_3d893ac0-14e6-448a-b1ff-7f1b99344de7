package cz.kpsys.portaro.proveniencesmap;

import cz.kpsys.portaro.commons.web.HttpHeaderConstants.ContentType;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.form.Form;
import cz.kpsys.portaro.form.form.FormByFormObjectFactory;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PlacesHistoryApiController extends GenericApiController {

    public static final String PLACES_HISTORY_URL = "/api/places-history";
    public static final String PLACES_HISTORY_URL_FILL = "/api/places-history/refill";
    public static final String PLACES_HISTORY_URL_FORM = "/api/places-history/form";

    @NonNull HistoricalPlaceLoader historicalPlaceLoader;
    @NonNull HistoricalPlaceUpdater historicalPlaceUpdater;
    @NonNull FormByFormObjectFactory formByFormObjectFactory;


    @PostMapping(PLACES_HISTORY_URL)
    public List<HistoricalPlace> getAll(@RequestBody(required = false) HistoricalPlacesRequest historicalPlacesRequest) {
        return historicalPlaceLoader.getAllByQuery(historicalPlacesRequest);
    }

    @RequestMapping(value = PLACES_HISTORY_URL_FILL, produces = ContentType.Value.JSON)
    public ActionResponse refill() {
        HistoricalPlaceUpdaterReport report = historicalPlaceUpdater.refill();
        return FinishedSaveResponse.ok(report);
    }

    @GetMapping(PLACES_HISTORY_URL_FORM)
    public Form getForm(@CurrentDepartment Department currentDepartment) {
        HistoricalPlacesRequest emptyRequest = HistoricalPlacesRequest.getEmptyRequest();

        Form formSetting = formByFormObjectFactory.createFormByFormObject(emptyRequest, currentDepartment);
        formSetting = formByFormObjectFactory.addFieldsByFormObject(formSetting, emptyRequest, currentDepartment);

        return formSetting;
    }

}
