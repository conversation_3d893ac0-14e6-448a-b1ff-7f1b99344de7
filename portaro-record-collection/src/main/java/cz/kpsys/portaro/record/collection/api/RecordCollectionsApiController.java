package cz.kpsys.portaro.record.collection.api;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.HierarchyTree;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.localization.LocalizationCodes;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.record.collection.*;
import cz.kpsys.portaro.record.collection.hierarchy.RecordCollectionHierarchyTreeLoader;
import cz.kpsys.portaro.record.collection.hierarchy.RecordCollectionHierarchyTreeNode;
import cz.kpsys.portaro.record.collection.recordcollection.RecordCollection;
import cz.kpsys.portaro.record.collection.recordcollectioncategory.RecordCollectionCategory;
import cz.kpsys.portaro.record.collection.recordcollectionitem.RecordCollectionItem;
import cz.kpsys.portaro.record.collection.recordcollectionitem.RecordCollectionItemEntity;
import cz.kpsys.portaro.record.collection.utils.SecuritySearchParamsProvider;
import cz.kpsys.portaro.record.collection.view.RecordCollectionItemResponse;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Locale;
import java.util.UUID;

import static cz.kpsys.portaro.record.collection.RecordCollectionConstants.SearchParams;
import static cz.kpsys.portaro.record.collection.api.RecordCollectionApiConstants.*;

@RequestMapping(RECORD_COLLECTIONS_URL_PART)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordCollectionsApiController extends GenericApiController {

    @NonNull SearchableRepository<MapBackedParams, RecordCollection, UUID> recordCollectionSearchableRepository;
    @NonNull SearchableRepository<MapBackedParams, RecordCollectionItem, UUID> recordCollectionItemSearchableRepository;
    @NonNull RecordCollectionItemInserter recordCollectionItemInserter;
    @NonNull RecordCollectionItemRemover recordCollectionItemRemover;
    @NonNull ParameterizedSearchLoader<MapBackedParams, RecordCollectionItemEntity> switchableDataSourceRecordCollectionItemEntitySearchLoader;
    @NonNull RecordCollectionCreator recordCollectionCreator;
    @NonNull RecordCollectionRemover recordCollectionRemover;
    @NonNull RecordCollectionUpdater recordCollectionUpdater;
    @NonNull SecuritySearchParamsProvider securitySearchParamsProvider;
    @NonNull ToViewableRecordCollectionConverter toViewableRecordCollectionConverter;
    @NonNull Provider<RecordCollectionCategory> recordCollectionPrivateCategoryProvider;
    @NonNull Provider<RecordCollectionCategory> recordCollectionFavouritesCategoryProvider;
    @NonNull AllValuesProvider<RecordCollectionCategory> recordCollectionPublicCategoriesAllValuesProvider;
    @NonNull ByIdLoadable<Event, UUID> eventLoader;
    @NonNull RecordCollectionHierarchyTreeLoader recordCollectionHierarchyTreeLoader;
    @NonNull Saver<List<RecordCollectionItem>, ?> recordCollectionItemBulkSaver;
    @NonNull ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordsConverter;


    @GetMapping(COLLECTIONS_URL_PART + "/{id}")
    public RecordCollection getCollection(@PathVariable("id") RecordCollection recordCollection,
                                          @RequestParam(defaultValue = "false") boolean viewable,
                                          UserAuthentication currentAuth,
                                          @CurrentDepartment Department currentDepartment) {

        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_SHOW_CONTENT, currentAuth, currentDepartment, recordCollection);
        if (viewable) {
            return toViewableRecordCollectionConverter.convert(recordCollection, currentAuth, currentDepartment);
        }
        return recordCollection;
    }

    @GetMapping(COLLECTIONS_URL_PART + "/{id}/detail")
    public RecordCollectionDetail getCollectionEvents(@PathVariable("id") RecordCollection recordCollection,
                                                UserAuthentication currentAuth,
                                                @CurrentDepartment Department currentDepartment) {

        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_SHOW_CONTENT, currentAuth, currentDepartment, recordCollection);

        Event creationEvent = eventLoader.getById(recordCollection.getCreationEventId());
        Event lastUpdateEvent = eventLoader.getById(recordCollection.getLastUpdateEventId());

        return new RecordCollectionDetail(creationEvent, lastUpdateEvent);
    }

    @GetMapping(COLLECTIONS_URL_PART + "/{id}/hierarchy")
    public HierarchyTree<UUID, RecordCollectionHierarchyTreeNode> getCollectionHierarchy(@PathVariable("id") RecordCollection recordCollection,
                                                                                         UserAuthentication currentAuth,
                                                                                         @CurrentDepartment Department currentDepartment) {

        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_SHOW_CONTENT, currentAuth, currentDepartment, recordCollection);

        return recordCollectionHierarchyTreeLoader.getHierarchyTreeOfCollection(recordCollection, currentAuth, currentDepartment);
    }

    @GetMapping(COLLECTIONS_URL_PART)
    public List<? extends RecordCollection> queryCollections(@RequestParam("user") User user,
                                                             @RequestParam(defaultValue = "") List<RecordCollectionCategory> categories,
                                                             @RequestParam(defaultValue = "false") boolean viewable,
                                                             UserAuthentication currentAuth,
                                                             @CurrentDepartment Department currentDepartment) {

        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTIONS_SHOW_USERS_COLLECTIONS, currentAuth, currentDepartment, user);
        if (categories.size() == 1 && categories.getFirst().isFavouritesCategory()) { // if requested favourites return only users collections
            return getFavourites(user, categories, currentAuth, currentDepartment, viewable);
        }

        var privateCategories = categories.stream().filter(RecordCollectionCategory::isPrivateCategory).toList();
        var publicCategories = categories.stream().filter(category -> !category.isFavouriteOrPrivateCategory()).toList();

        List<RecordCollection> results;
        if (privateCategories.isEmpty() || publicCategories.isEmpty()) {
            StaticParamsModifier params = StaticParamsModifier.of(
                    SearchParams.RECORD_COLLECTION_OWNER, List.of(user.getId()),
                    SearchParams.RECORD_COLLECTION_CATEGORY, categories);
            results = recordCollectionSearchableRepository.getContent(RangePaging.forAll(), params);
            results = ListUtil.filter(results, collection -> securityManager.can(RecordCollectionSecurityActions.RECORD_COLLECTION_SHOW_CONTENT, currentAuth, currentDepartment, collection));
        } else {
            StaticParamsModifier params = StaticParamsModifier.of(
                    SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER, securitySearchParamsProvider
                            .getParameter(SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER)
                            .create(user, currentDepartment, privateCategories, publicCategories));
            results = recordCollectionSearchableRepository.getContent(RangePaging.forAll(), params);
        }

        if (viewable) {
            return toViewableRecordCollectionConverter.convert(results, currentAuth, currentDepartment);
        }
        return results;
    }

    @GetMapping(COLLECTIONS_URL_PART + "/{id}/index")
    public List<RecordCollectionItemEntity> getCollectionIndex(@PathVariable("id") RecordCollection recordCollection,
                                                               UserAuthentication currentAuth,
                                                               @CurrentDepartment Department currentDepartment) {

        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_SHOW_CONTENT, currentAuth, currentDepartment, recordCollection);

        StaticParamsModifier params = StaticParamsModifier.of(
                SearchParams.RECORD_COLLECTION_ANCESTOR, recordCollection.getId(),
                SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER, securitySearchParamsProvider
                        .getParameter(SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER)
                        .create(currentAuth.getActiveUser(), currentDepartment, List.of(recordCollectionPrivateCategoryProvider.get(), recordCollectionFavouritesCategoryProvider.get()), recordCollectionPublicCategoriesAllValuesProvider.getAll())
        );
        return switchableDataSourceRecordCollectionItemEntitySearchLoader.getContent(RangePaging.forAll(), params);
    }

    @DeleteMapping(COLLECTIONS_URL_PART + "/{id}/clear")
    public ActionResponse removeAllFromCollection(@PathVariable("id") RecordCollection recordCollection,
                                                  @RequestParam(defaultValue = "false") boolean includingSubCollections,
                                                  UserAuthentication currentAuth,
                                                  @CurrentDepartment Department currentDepartment) {

        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_EDIT, currentAuth, currentDepartment, recordCollection);

        var collections = List.of(recordCollection);
        if (includingSubCollections) {
            collections = recordCollectionSearchableRepository.getContent(RangePaging.forAll(), StaticParamsModifier.of(SearchParams.RECORD_COLLECTION_ANCESTOR, recordCollection.getId()));
        }
        var toBeClearedCollections = collections
                .stream()
                .filter(collection -> securityManager.can(RecordCollectionSecurityActions.RECORD_COLLECTION_EDIT, currentAuth, currentDepartment, collection)) // clear only collections editable by user
                .toList();

        recordCollectionItemRemover.removeAllItemsFromCollection(toBeClearedCollections, currentDepartment, currentAuth);
        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Deleted));
    }

    @PostMapping(COLLECTIONS_URL_PART)
    public ActionResponse createCollection(@RequestBody @ValidFormObject RecordCollectionCreationRequest request,
                                           UserAuthentication currentAuth,
                                           @CurrentDepartment Department currentDepartment) {
        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_CREATE, currentAuth, currentDepartment, request);
        RecordCollectionCreationCommand command = new RecordCollectionCreationCommand(
                request.creator(),
                request.name(),
                request.parentCollection(),
                request.department(),
                request.note(),
                request.recordCollectionCategory()
        );
        RecordCollection recordCollection = recordCollectionCreator.create(command, currentDepartment, currentAuth);
        return FinishedSaveResponse.saved(recordCollection);
    }

    @PostMapping(COLLECTIONS_URL_PART + "/{id}")
    public ActionResponse editCollection(@PathVariable("id") RecordCollection recordCollection,
                                         @RequestBody @ValidFormObject RecordCollectionEditationRequest request,
                                         UserAuthentication currentAuth,
                                         @CurrentDepartment Department currentDepartment) {
        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_EDIT, currentAuth, currentDepartment, recordCollection);

        if (!recordCollection.getRecordCollectionCategory().equals(request.recordCollectionCategory())) { // if changing collections category, we need to update all subcollections categories
            var subcollections = recordCollectionSearchableRepository.getContent(RangePaging.forAll(), StaticParamsModifier.of(SearchParams.RECORD_COLLECTION_ANCESTOR, recordCollection.getId()));
            subcollections.forEach(subcollection -> securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_EDIT, currentAuth, currentDepartment, subcollection));
        }

        RecordCollectionEditationCommand command = new RecordCollectionEditationCommand(
                request.name(),
                request.parentCollection(),
                request.order(),
                request.note(),
                request.recordCollectionCategory());
        RecordCollection updatedRecordCollection = recordCollectionUpdater.update(recordCollection, command, currentDepartment, currentAuth);
        return FinishedSaveResponse.saved(updatedRecordCollection);
    }

    @DeleteMapping(COLLECTIONS_URL_PART + "/{id}")
    public ActionResponse deleteCollection(@PathVariable("id") RecordCollection collection,
                                           UserAuthentication currentAuth,
                                           @CurrentDepartment Department currentDepartment) {
        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_DELETE, currentAuth, currentDepartment, collection);
        recordCollectionRemover.delete(collection);
        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Deleted));
    }

    @GetMapping(ITEMS_URL_PART)
    public List<RecordCollectionItemResponse> queryItems(@RequestParam RecordCollection recordCollection,
                                                         @RequestParam(value = SearchViewConstants.PAGE_NUMBER_PARAM, defaultValue = "1") int page,
                                                         @RequestParam(value = SearchViewConstants.PAGE_SIZE_PARAM, defaultValue = "10") int size,
                                                         @RequestParam(defaultValue = "true") boolean ascending,
                                                         UserAuthentication currentAuth,
                                                         @CurrentDepartment Department ctx,
                                                         Locale locale) {
        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_SHOW_CONTENT, currentAuth, ctx, recordCollection);

        StaticParamsModifier params = StaticParamsModifier.of(
                SearchParams.RECORD_COLLECTION_ANCESTOR, recordCollection.getId(),
                SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER, securitySearchParamsProvider
                        .getParameter(SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER)
                        .create(currentAuth.getActiveUser(), ctx, List.of(recordCollectionPrivateCategoryProvider.get(), recordCollectionFavouritesCategoryProvider.get()), recordCollectionPublicCategoriesAllValuesProvider.getAll())
        );
        return ListUtil.convert(recordCollectionItemSearchableRepository.getContent(RangePaging.of(page, size), SortingItem.ofSimpleFieldAndOrder("order", ascending),  params), source -> mapCollectionItemToResponse(currentAuth, ctx, locale, source));
    }

    @PostMapping(value = ITEMS_URL_PART, params = "!bulk")
    public FinishedSaveResponse<RecordCollectionItemResponse> insertSingleCollectionItem(@RequestBody @ValidFormObject RecordCollectionInsertRequest request,
                                                                                         UserAuthentication currentAuth,
                                                                                         @CurrentDepartment Department ctx,
                                                                                         Locale locale) {
        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_INSERT, currentAuth, ctx, request.recordCollection());
        RecordCollectionItem insertedItem = recordCollectionItemInserter.insertSingle(new RecordCollectionInsertCommand(request.record(), request.recordCollection()), ctx, currentAuth);
        return FinishedSaveResponse.saved(mapCollectionItemToResponse(currentAuth, ctx, locale, insertedItem));
    }

    @PostMapping(value = ITEMS_URL_PART, params = "bulk=true")
    public FinishedSaveResponse<List<RecordCollectionItemResponse>> insertMultipleCollectionItems(@RequestBody @ValidFormObject RecordCollectionBulkInsertRequest request,
                                                                                                  UserAuthentication currentAuth,
                                                                                                  @CurrentDepartment Department ctx,
                                                                                                  Locale locale) {
        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_INSERT, currentAuth, ctx, request.recordCollection());
        var insertedItems = recordCollectionItemInserter.insertBulk(new RecordCollectionBulkInsertCommand(request.records(), request.recordCollection()), ctx, currentAuth);
        return FinishedSaveResponse.saved(ListUtil.convert(insertedItems, item -> mapCollectionItemToResponse(currentAuth, ctx, locale, item)));
    }

    @DeleteMapping(ITEMS_URL_PART + "/{id}")
    public ActionResponse removeCollectionItem(@PathVariable("id") RecordCollectionItem collectionItem,
                                               UserAuthentication currentAuth,
                                               @CurrentDepartment Department currentDepartment) {
        RecordCollection recordCollection = recordCollectionSearchableRepository.getById(collectionItem.getRecordCollectionId());
        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_EDIT, currentAuth, currentDepartment, recordCollection);
        recordCollectionItemRemover.removeSingleCollectionItem(collectionItem, recordCollection, currentDepartment, currentAuth);
        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Deleted));
    }

    @PutMapping(ITEMS_URL_PART)
    public FinishedSaveResponse<List<RecordCollectionItemResponse>> swapCollectionItemsOrder(@RequestParam("item1") RecordCollectionItem collectionItem1,
                                                                                             @RequestParam("item2") RecordCollectionItem collectionItem2,
                                                                                             UserAuthentication currentAuth,
                                                                                             @CurrentDepartment Department ctx,
                                                                                             Locale locale) {

        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_EDIT, currentAuth, ctx, recordCollectionSearchableRepository.getById(collectionItem1.getRecordCollectionId()));
        securityManager.throwIfCannot(RecordCollectionSecurityActions.RECORD_COLLECTION_EDIT, currentAuth, ctx, recordCollectionSearchableRepository.getById(collectionItem2.getRecordCollectionId()));

        var swapped = List.of(collectionItem1.withOrder(collectionItem2.getOrder()), collectionItem2.withOrder(collectionItem1.getOrder()));

        recordCollectionItemBulkSaver.save(swapped);
        return FinishedSaveResponse.saved(ListUtil.convert(swapped, item -> mapCollectionItemToResponse(currentAuth, ctx, locale, item)));
    }


    private List<? extends RecordCollection> getFavourites(User user, List<RecordCollectionCategory> categories, @NonNull UserAuthentication currentAuth, Department currentDepartment, boolean viewable) {
        StaticParamsModifier params = StaticParamsModifier.of(
                SearchParams.RECORD_COLLECTION_OWNER, List.of(user.getId()),
                SearchParams.RECORD_COLLECTION_CATEGORY, categories
        );
        var results = recordCollectionSearchableRepository.getContent(RangePaging.forSingle(), params);

        if (viewable) {
            return toViewableRecordCollectionConverter.convert(results, currentAuth, currentDepartment);
        }
        return results;
    }

    private RecordCollectionItemResponse mapCollectionItemToResponse(@NonNull UserAuthentication currentAuth, Department ctx, Locale locale, RecordCollectionItem source) {
        return new RecordCollectionItemResponse(
                source.getId(),
                source.getRecordCollectionId(),
                recordsToViewableRecordsConverter.convertSingle(source.getRecord(), currentAuth, ctx, locale),
                source.getOrder()
        );
    }
}
