package cz.kpsys.portaro.record.collection.recordcollectionitem;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Ordered;
import cz.kpsys.portaro.commons.object.repo.Repository;
import cz.kpsys.portaro.databasestructure.RecordCollectionDb;
import cz.kpsys.portaro.record.collection.RecordCollectionConstants.SearchParams;
import cz.kpsys.portaro.record.collection.recordcollection.RecordCollection;
import cz.kpsys.portaro.search.AbstractInMemorySearchLoader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.UUID;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PROTECTED, makeFinal = true)
public class InMemoryRecordCollectionItemSearchLoader extends AbstractInMemorySearchLoader<MapBackedParams, RecordCollectionItem> {

    @NonNull Repository<RecordCollection, UUID> collectionsRepository;

    public InMemoryRecordCollectionItemSearchLoader(@NonNull AllValuesProvider<RecordCollectionItem> source,
                                                    @NonNull Repository<RecordCollection, UUID> collectionsRepository) {
        super(source);
        this.collectionsRepository = collectionsRepository;
    }

    @Override
    protected Stream<RecordCollectionItem> applyParams(boolean count, Stream<RecordCollectionItem> recordCollectionItemStream, MapBackedParams params, SortingItem customSorting) {

        if (params.hasNotNull(SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER)) {
            var showableSearchParameter = params.get(SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER);
            recordCollectionItemStream = recordCollectionItemStream
                    .filter(item -> {
                        RecordCollection collection = collectionsRepository.getById(item.getRecordCollectionId());
                        boolean hasRequiredDepartment = showableSearchParameter.getDepartments().contains(collection.getDepartment());
                        boolean isPublic = showableSearchParameter.getPublicCategories().contains(collection.getRecordCollectionCategory());
                        boolean isPrivateAndUserIsOwner = showableSearchParameter.getPrivateCategories().contains(collection.getRecordCollectionCategory()) && showableSearchParameter.getOwner().equals(collection.getOwner());
                        return hasRequiredDepartment && (isPublic || isPrivateAndUserIsOwner);
                    });
        }

        if (params.hasNotNull(SearchParams.RECORD_COLLECTION_ANCESTOR)) {
            recordCollectionItemStream = recordCollectionItemStream.filter(item -> { // throw only if any subcollections exists
                RecordCollection collection = collectionsRepository.getById(item.getRecordCollectionId());
                if (collection.getParentId().isPresent()) { // can only throw in filter because stream is lazy evaluated and cannot access data before final .toList() collection;
                    throw new UnsupportedOperationException(String.format("%s does not support search by ancestor", InMemoryRecordCollectionItemSearchLoader.class));
                }
                return params.get(SearchParams.RECORD_COLLECTION_ANCESTOR).equals(collection.getId());
            });
        }

        if (params.hasNotNull(SearchParams.RECORD_COLLECTION) && params.hasLength(SearchParams.RECORD_COLLECTION)) {
            recordCollectionItemStream = recordCollectionItemStream.filter(item -> params.get(SearchParams.RECORD_COLLECTION).contains(item.getRecordCollectionId()));
        }

        if (params.hasNotNull(SearchParams.RECORD) && params.hasLength(SearchParams.RECORD)) {
            recordCollectionItemStream = recordCollectionItemStream.filter(item -> params.get(SearchParams.RECORD).contains(item.getRecord().getId()));
        }

        if (!count) {
            if (customSorting == null) {
                customSorting = SortingItem.ofSimpleAsc(RecordCollectionDb.RECORD_COLLECTION_ITEM.ORDER_NUMBER);
            }

            if (customSorting.asc()) {
                recordCollectionItemStream = recordCollectionItemStream.sorted(Ordered.COMPARATOR);
            } else {
                recordCollectionItemStream = recordCollectionItemStream.sorted(Ordered.COMPARATOR.reversed());
            }
        }

        return recordCollectionItemStream;
    }
}
