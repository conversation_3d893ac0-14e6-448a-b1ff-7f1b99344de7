package cz.kpsys.portaro.record.collection.recordcollectionitem;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.search.AbstractSingleColumnSpringDbSearchLoader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordCollectionDb.*;
import static cz.kpsys.portaro.record.collection.RecordCollectionConstants.SearchParams;

public class SpringDbRecordCollectionItemIdSearchLoader extends AbstractSingleColumnSpringDbSearchLoader<MapBackedParams, UUID, RangePaging> {

    private static final String COLLECTIONS_RESULT_SET_ALIAS = "collections_result_set_alias";

    public SpringDbRecordCollectionItemIdSearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate, @NonNull QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory, RECORD_COLLECTION_ITEM.TABLE, RECORD_COLLECTION_ITEM.ID, new SelectedColumnRowMapper<>(UUID.class, RECORD_COLLECTION_ITEM.ID));
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {

        if (p.hasNotNull(SearchParams.RECORD_COLLECTION_ANCESTOR)) {

            // anchor query selects ancestor collection
            SelectQuery anchorQuery = queryFactory.newSelectQuery();
            var anchorQueryTableAlias = "anchor_query_alias";
            anchorQuery
                    .select(WHOLE(anchorQueryTableAlias))
                    .from(AS(RECORD_COLLECTION.TABLE, anchorQueryTableAlias))
                    .where()
                    .eq(TC(anchorQueryTableAlias, RECORD_COLLECTION.ID), p.get(SearchParams.RECORD_COLLECTION_ANCESTOR));

            // recursive query works top down and finds children of collections added by anchor query and previous recursive query calls
            SelectQuery recursiveQuery = queryFactory.newSelectQuery();
            var recursiveQueryTableAlias = "recursive_query_alias";
            recursiveQuery
                    .select(WHOLE(recursiveQueryTableAlias))
                    .from(AS(RECORD_COLLECTION.TABLE, recursiveQueryTableAlias))
                    .joins().add(COLLECTIONS_RESULT_SET_ALIAS, COLSEQ(TC(recursiveQueryTableAlias, RECORD_COLLECTION.PARENT_ID), TC(COLLECTIONS_RESULT_SET_ALIAS, RECORD_COLLECTION.ID)));

            sq.fromWithRecursive(COLLECTIONS_RESULT_SET_ALIAS, anchorQuery, recursiveQuery);
            sq.joins().add(RECORD_COLLECTION_ITEM.TABLE, COLSEQ(TC(COLLECTIONS_RESULT_SET_ALIAS, RECORD_COLLECTION.ID), TC(RECORD_COLLECTION_ITEM.TABLE, RECORD_COLLECTION_ITEM.RECORD_COLLECTION_ID)));
        } else {
            sq.from(RECORD_COLLECTION_ITEM.TABLE);
        }

        if (p.hasNotNull(SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER)) {

            if (!p.hasNotNull(SearchParams.RECORD_COLLECTION_ANCESTOR)) {
                throw new RuntimeException(String.format("Parameter %s can only be used together with %s parameter", SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER, SearchParams.RECORD_COLLECTION_ANCESTOR));
            }

            var showableSearchParameter = p.get(SearchParams.SHOWABLE_RECORD_COLLECTIONS_SEARCH_PARAMETER);
            sq.joins().add(USER_RECORD_COLLECTION.TABLE, COLSEQ(TC(COLLECTIONS_RESULT_SET_ALIAS, RECORD_COLLECTION.ID), TC(USER_RECORD_COLLECTION.TABLE, USER_RECORD_COLLECTION.RECORD_COLLECTION_ID)));

            sq.where().and().in(RECORD_COLLECTION.DEPARTMENT_ID, ListUtil.convert(showableSearchParameter.getDepartments(), Identified::getId));
            var brackets = sq.where().and().brackets();
            brackets.or().in(RECORD_COLLECTION.RECORD_COLLECTION_CATEGORY_ID, ListUtil.convert(showableSearchParameter.getPublicCategories(), Identified::getId));
            brackets.or()
                    .brackets()
                        .and().in(RECORD_COLLECTION.RECORD_COLLECTION_CATEGORY_ID, ListUtil.convert(showableSearchParameter.getPrivateCategories(), Identified::getId))
                        .and().in(USER_RECORD_COLLECTION.USER_ID, showableSearchParameter.getOwner().getId());
        }


        if (p.hasNotNull(SearchParams.RECORD_COLLECTION)) {
            if (!p.hasLength(SearchParams.RECORD_COLLECTION)) {
                return false;
            }
            sq.where().and().in(RECORD_COLLECTION_ITEM.RECORD_COLLECTION_ID, p.get(SearchParams.RECORD_COLLECTION));
        }

        if (p.hasNotNull(SearchParams.RECORD)) {
            if (!p.hasLength(SearchParams.RECORD)) {
                return false;
            }
            sq.where().and().in(RECORD_COLLECTION_ITEM.RECORD_ID, p.get(SearchParams.RECORD));
        }

        return true;
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        if (customSorting == null || customSorting.asc()) {
            return Sorting.ofAsc(TC(RECORD_COLLECTION_ITEM.TABLE, RECORD_COLLECTION_ITEM.RECORD_COLLECTION_ID), TC(RECORD_COLLECTION_ITEM.TABLE, RECORD_COLLECTION_ITEM.ORDER_NUMBER));
        }
        return Sorting.ofDesc(TC(RECORD_COLLECTION_ITEM.TABLE, RECORD_COLLECTION_ITEM.RECORD_COLLECTION_ID), TC(RECORD_COLLECTION_ITEM.TABLE, RECORD_COLLECTION_ITEM.ORDER_NUMBER));
    }
}
