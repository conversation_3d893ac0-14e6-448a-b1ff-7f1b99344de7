package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.contextual.CompositeAllValuesContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.*;
import cz.kpsys.portaro.commons.object.provider.SingleMatchingProvider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.config.CodebookLoaderBuilderFactory;
import cz.kpsys.portaro.config.ConverterRegisterer;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.fond.*;
import cz.kpsys.portaro.search.BasicMapSearchParams;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordCodebookConfig {

    @NonNull SettingLoader settingLoader;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull TransactionTemplateFactory readonlyTransactionTemplateFactory;

    @Bean
    public Codebook<RecordStatus, Integer> recordStatusLoader() {
        return RecordStatus.CODEBOOK;
    }

    @Bean
    public AllValuesProvider<RecordStatus> allowedRecordStatusesProvider() {
        return RecordStatus.NOT_DEPRECATED_CODEBOOK;
    }

    @Bean
    public AllByIdsLoadable<RecordStatus, Integer> recordStatusesByIdsLoader() {
        return new AllByIdsLoadableByIdLoaderAdapter<>(recordStatusLoader());
    }

    @Bean
    public Provider<@NonNull Fond> defaultDocumentFondProvider() {
        return DefaultProvider.byFirst(enabledDocumentFondsProvider());
    }

    @Bean
    public Provider<@NonNull Fond> defaultNonPeriodicalDocumentFondProvider() {
        return new CompositeDefaultFondProvider(enabledFondsProvider(), fondLoader(), Fond.MONOGRAPHY_FOND_ID, fond -> !fond.isPeriodical(), "non-periodical");
    }

    @Bean
    public Provider<@NonNull Fond> defaultPeriodicalDocumentFondProvider() {
        return new CompositeDefaultFondProvider(enabledFondsProvider(), fondLoader(), Fond.PERIODICAL_FOND_ID, Fond::isPeriodical, "periodical");
    }

    @Bean
    public Provider<@NonNull Fond> defaultAuthorityFondProvider() {
        return DefaultProvider.byFirst(enabledAuthorityFondsProvider());
    }

    @Bean
    public Codebook<Fond, Integer> fondLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedByJpa(FondEntity.class, Identified.PROPERTY_ID)
                .convertedEachBy(new FondFromDtoConverter())
                .staticCached(Fond.class.getSimpleName())
                .build();
    }

    @Bean
    public Codebook<FondInclusion, UUID> fondInclusionLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedByJpa(FondInclusionEntity.class)
                .convertedEachBy(new EntityToFondInclusionConverter())
                .staticCached(FondInclusion.class.getSimpleName())
                .build();
    }

    @Bean
    public FondInheritanceLoader enabledFondInheritanceLoader() {
        return new FondInheritanceLoader(
                enabledFondsProvider(),
                readonlyTransactionTemplateFactory.get(),
                fondRelationsMapProvider()
        );
    }

    @Bean
    public FondRelationsMapProvider fondRelationsMapProvider() {
        return new FondRelationsMapProvider(enabledFondsProvider(), fondInclusionLoader());
    }

    @Bean
    public Codebook<Fond, Integer> enabledDocumentFondsProvider() {
        return FilteringCodebook.filteredAlways(enabledFondsProvider(), Fond::isOfDocument);
    }

    @Bean
    public Codebook<Fond, Integer> enabledAuthorityFondsProvider() {
        return FilteringCodebook.filteredAlways(enabledFondsProvider(), Fond::isOfAuthority);
    }

    @Bean
    public Codebook<Fond, Integer> enabledFondsProvider() {
        return FilteringCodebook.filteredAlways(fondLoader(), Fond::isEnabled);
    }

    @Bean
    public Function<Fond, List<Fond>> enabledLoadableFondsExpander() {
        return fond -> enabledFondInheritanceLoader().getThisAndIncludedAndChildren(fond);
    }

    @Bean
    public Function<String, List<Fond>> subkindToEnabledFondsExpander() {
        return subkind -> {
            List<Fond> allFonds = enabledFondsProvider().getAll();
            List<Fond> fondsBySubkind = new ArrayList<>();
            if (subkind.equals(BasicMapSearchParams.SUBKIND_DOCUMENT)) {
                fondsBySubkind.addAll(Fond.filterDocumentFonds(allFonds));
            }
            if (subkind.equals(BasicMapSearchParams.SUBKIND_AUTHORITY)) {
                fondsBySubkind.addAll(Fond.filterAuthorityFonds(allFonds));
            }
            return fondsBySubkind;
        };
    }

    @Bean
    public Function<String, List<Fond>> kindToEnabledFondsExpander() {
        return subkind -> {
            if (subkind.equals(BasicMapSearchParams.KIND_RECORD)) {
                return enabledFondsProvider().getAll();
            }
            return List.of();
        };
    }

    @Bean
    public ContextualProvider<Department, @NonNull List<Fond>> showableFondsDepartmentedProvider() {
        var readerShowableDocumentFondsDepartmentedProvider = settingLoader.getDepartmentedProvider(RecordSettingKeys.DOCUMENT_FONDS);
        var readerShowableAuthorityFondsDepartmentedProvider = settingLoader.getDepartmentedProvider(RecordSettingKeys.AUTHORITY_FONDS);
        var composite = new CompositeAllValuesContextualProvider<Department, Integer>()
                .add(readerShowableDocumentFondsDepartmentedProvider)
                .add(readerShowableAuthorityFondsDepartmentedProvider);
        return composite.andThen(new AllByIdsLoadableByIdLoaderAdapter<>(enabledFondsProvider())::getAllByIds);
    }

    @Bean
    public AllValuesProvider<@NonNull Fond> readerShowableFondsProvider() {
        return CompositeAllValuesProvider.of(
                readerShowableDocumentFondsProvider(),
                readerShowableAuthorityFondsProvider()
        );
    }

    @Bean
    public AllValuesProvider<Fond> readerShowableDocumentFondsProvider() {
        return new FilteredAndSortedAllValuesProvider<>(
                enabledDocumentFondsProvider(),
                settingLoader.getOnRootProvidedList(RecordSettingKeys.DOCUMENT_FONDS));
    }

    @Bean
    public AllValuesProvider<Fond> readerShowableAuthorityFondsProvider() {
        return new FilteredAndSortedAllValuesProvider<>(
                enabledAuthorityFondsProvider(),
                settingLoader.getOnRootProvidedList(RecordSettingKeys.AUTHORITY_FONDS));
    }

    @Bean
    public Provider<@Nullable Fond> supplierAuthorityFondProvider() {
        return SingleMatchingProvider.of(fondLoader(), FondType.SUPPLIER::matches);
    }

    @Bean
    public Provider<@Nullable Fond> desiderataFondProvider() {
        return SingleMatchingProvider.of(fondLoader(), FondType.ACQUISITION::matches);
    }

    @Bean
    public Provider<@NonNull Fond> dayFondProvider() {
        return SingleMatchingProvider.of(fondLoader(), FondType.DAY::matches).throwingWhenNull();
    }

    @Bean
    public ContextualProvider<Department, @NonNull Boolean> documentFondsDisabledProvider() {
        return department -> settingLoader.getDepartmentedProvider(RecordSettingKeys.DOCUMENT_FONDS).getOn(department).isEmpty();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        converterRegisterer
                .registerForIntegerId(Fond.class, fondLoader())
                .registerForStringId(FondType.class, FondType.CODEBOOK);
    }
}
