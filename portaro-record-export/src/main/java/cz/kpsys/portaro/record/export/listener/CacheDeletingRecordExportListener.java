package cz.kpsys.portaro.record.export.listener;

import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.ip.IpAddress;
import cz.kpsys.portaro.record.export.RecordExport;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CacheDeletingRecordExportListener implements RecordExportListener {

    @NonNull CacheDeletableById cacheDeletableByRecordId;

    @Override
    public void recordsExported(@NonNull List<UUID> chunk, @NonNull RecordExport recordExport, @NonNull IpAddress initiatorIp) {
        for (UUID recordId : chunk) {
            cacheDeletableByRecordId.deleteFromCacheById(recordId);
        }
    }
}
