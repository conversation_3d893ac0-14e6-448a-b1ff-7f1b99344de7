package cz.kpsys.portaro.record.export.marc;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.marcxml.model.StrictDatafieldMarcDto;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;

import java.util.List;

public interface DatafieldsMarcGenerator<SUBJECT> {

    @NonNull
    List<StrictDatafieldMarcDto> generate(@NonNull Record record, @NonNull SUBJECT subject, @NonNull Department ctx);

}
