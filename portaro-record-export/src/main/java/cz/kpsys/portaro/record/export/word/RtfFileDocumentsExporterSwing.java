package cz.kpsys.portaro.record.export.word;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.io.FileStreamConsumer;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.export.Exporter;
import cz.kpsys.portaro.export.ToStringExporter;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.TypeDescriptor;

import javax.swing.text.BadLocationException;
import javax.swing.text.html.HTMLEditorKit;
import javax.swing.text.rtf.RTFEditorKit;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.util.List;
import java.util.Locale;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RtfFileDocumentsExporterSwing implements Exporter<List<Record>> {

    @NonNull String filename;
    @NonNull ToStringExporter<List<Record>> toStringExporter;

    @Override
    public void export(@NonNull List<Record> documents, @NonNull UserAuthentication currentAuth, Department ctx, Locale locale, FileStreamConsumer streamConsumer) {
        try {
            String html = toStringExporter.exportToString(documents, currentAuth, ctx, locale);

            //read from html
            HTMLEditorKit htmlEditorKit = new HTMLEditorKit();
            javax.swing.text.Document doc = htmlEditorKit.createDefaultDocument();
            Reader htmlReader = new StringReader(html);
            htmlEditorKit.read(htmlReader, doc, 0);

            //write to rtf
            RTFEditorKit rtfEditorKit = new RTFEditorKit();
            ByteArrayOutputStream rtfOutputStream = new ByteArrayOutputStream();
            rtfEditorKit.write(rtfOutputStream, doc, 0, doc.getLength());
            byte[] docData = rtfOutputStream.toByteArray();

            //stream
            FileStreamConsumer.streamBytes(streamConsumer, filename, docData);

        } catch (IOException | BadLocationException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public TypeDescriptor getType() {
        return TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Record.class));
    }

}