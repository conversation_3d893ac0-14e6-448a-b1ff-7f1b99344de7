package cz.kpsys.portaro.record.grid;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.LabeledReference;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.grid.Fieldable;
import cz.kpsys.portaro.grid.GridField;
import cz.kpsys.portaro.record.detail.FieldId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public record CompoundGridField(

        @NonNull
        FieldId fieldId,

        @NonNull
        Text text,

        @NonNull
        Map<String, List<GridField>> fields,

        @Nullable
        LabeledReference<UUID> recordReference,

        @Nullable
        LabeledReference<UUID> origin

) implements GridField, Fieldable {

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, CompoundGridField.class, CompoundGridField::fieldId);
    }

    @Override
    public int hashCode() {
        return fieldId().hashCode();
    }
}
