package cz.kpsys.portaro.record.grid;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualFunction;
import cz.kpsys.portaro.commons.convert.ConversionException;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.contextual.ContextualVisibleDepartmentsLoader;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.grid.*;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.frontend.ErroredFieldResponse;
import cz.kpsys.portaro.record.detail.value.AcceptableValueFieldValue;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.fond.SimpleFondResponse;
import cz.kpsys.portaro.record.holding.RecordHolding;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.security.api.PermissionResultResponse;
import cz.kpsys.portaro.security.api.PermissionResultToResponseConverter;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Stream;

import static java.util.function.Predicate.isEqual;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.*;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class RecordToRecordRowsConverter implements ViewableItemsTypedConverter<Record, RecordRow> {

    @NonNull Function<@NonNull PermissionResult, @NonNull PermissionResultResponse> permissionResultToResponseConverter = new PermissionResultToResponseConverter();
    @NonNull RecordStatusResolver recordStatusResolver;
    @NonNull SecurityManager securityManager;
    @NonNull ContextualVisibleDepartmentsLoader assignableDepartmentsLoader;
    @NonNull AuthenticatedContextualFunction<Record, Department, List<RecordHolding>> visibleRecordHoldingsResolver;


    @NonNull
    @Override
    public List<RecordRow> convert(@NonNull List<Record> records, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exportTypes, @NonNull Map<String, Object> additionalModel) {
        return ListUtil.convert(records, record -> convertSingle(record, currentAuth, ctx, locale, exportTypes, additionalModel));
    }

    @NonNull
    @Override
    public RecordRow convertSingle(@NonNull Record record, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exportTypes, @NonNull Map<String, Object> additionalModel) {
        FieldContainer detail = Objects.requireNonNull(record.getDetail(), "Cannot convert record to table record, because record detail is null");

        try {
            SimpleFondResponse fondResponse = SimpleFondResponse.mapFromFond(record.getFond());
            Map<String, List<GridField>> cells = collectAndGroup(detail.streamFields());
            PermissionResult editFastPermission = securityManager.resolve(RecordSecurityActions.RECORD_EDIT_FAST, currentAuth, ctx, record); // Zjistovani jeho konkretnich prav je pomale (nacita se po jednom editLevel z record operation), takze tady budeme resit jen fast variantu
            PermissionResult deleteRecordPermission = securityManager.resolve(RecordSecurityActions.RECORD_HOLDING_DELETE_OF_RECORD, currentAuth, ctx, record);
            List<RecordStatusTransition> nextPhases = recordStatusResolver.nextStates(record, ctx, currentAuth);
            List<RecordStatusTransitionResponse> recordStatusTransitions = ListUtil.convertStrict(nextPhases, transition -> mapStatusTransition(transition, ctx));
            var assignableDepartments = assignableDepartmentsLoader.getAll(currentAuth, ctx);
            return new RecordRow(
                    record.getId(),
                    record.getName(),
                    fondResponse,
                    !record.isActive(),
                    mapStatus(record.getStatus(), ctx),
                    permissionResultToResponseConverter.apply(editFastPermission),
                    permissionResultToResponseConverter.apply(deleteRecordPermission),
                    recordStatusTransitions,
                    cells,
                    visibleRecordHoldingsResolver.getOn(record, currentAuth, ctx),
                    ListUtil.convert(assignableDepartments, Department::asDepartmentResponse)
            );

        } catch (Exception e) {
            throw new ConversionException("Exception while mapping record detail (%s) to grid cells: %s".formatted(record.getId(), e.getMessage()), e);
        }
    }

    private @NonNull Function<Field<?>, GridField> mapFieldToGridField() {
        return field -> {
            if (field.getError() != null) {
                Text text = field.hasValueHolder() ? field.getExistingValueHolder().text() : Texts.ofEmpty();
                return new ErrorGridField(field.getFieldId(), text, ErroredFieldResponse.map(field.getError()), recordRef(field), originRef(field));
            }

            if (field.getType().isGroup()) {
                Map<String, List<GridField>> subcells = collectAndGroup(field.streamFields());
                return new CompoundGridField(field.getFieldId(), field.getText(), subcells, recordRef(field), originRef(field));
            }

            // prazdna pole (bez hodnoty, bez recordu, bez podpoli, bez chyby) zkusime podporovat
            //Assert.state(fieldValueHolder != null, () -> "Cannot convert record to table cell labeled value, because field does not have any subfield and valueHolder is null (field %s)".formatted(field.getFieldId()));
            if (!field.hasValueHolder()) {
                return new EmptyGridField(field.getFieldId(), Texts.ofEmpty(), recordRef(field), originRef(field));
            }

            ScalarFieldValue<?> existingValueHolder = field.getExistingValueHolder();

            if (existingValueHolder instanceof AcceptableValueFieldValue<?> acceptableValueHolder) {
                return new OptionGridField<>(field.getFieldId(), acceptableValueHolder.value().getId(), acceptableValueHolder.text(), recordRef(field), originRef(field));
            }

            return new ScalarGridField<>(field.getFieldId(), existingValueHolder.value(), existingValueHolder.text(), recordRef(field), originRef(field));
        };
    }

    private @Nullable LabeledRecordRef recordRef(Field<?> field) {
        if (field.hasRecordLink()) {
            Text text = field.hasValueHolder() ? field.getExistingValueHolder().text() : Texts.ofEmpty();
            return LabeledRecordRef.ofRecordLink(field.getExistingRecordLink(), text);
        }
        return null;
    }

    private @Nullable LabeledRecordRef originRef(Field<?> field) {
        var thisRecordId = field.getRecordFieldId().recordIdFondPair();
        return field.getMaxOneOrigin()
                .filter(not(isEqual(thisRecordId)))
                .map(link -> {
                    Text text = field.hasValueHolder() ? field.getExistingValueHolder().text() : Texts.ofEmpty();
                    return LabeledRecordRef.ofRecordLink(link, text);
                })
                .orElse(null);
    }

    private @NonNull <F extends Field<?>> Map<String, List<GridField>> collectAndGroup(@NonNull Stream<F> fieldStream) {
        return fieldStream.collect(groupingBy(Field::getTypeId, mapping(mapFieldToGridField(), toList())));
    }

    private @NonNull RecordStatusTransitionResponse mapStatusTransition(RecordStatusTransition transition, @NonNull Department ctx) {
        return new RecordStatusTransitionResponse(
                mapStatus(transition.targetStatus(), ctx),
                permissionResultToResponseConverter.apply(transition.permission())
        );
    }

    private @NonNull RecordStatusResponse mapStatus(@NonNull RecordStatus status, @NonNull Department ctx) {
        return RecordStatusResponse.map(status, recordStatusResolver, ctx);
    }
}
