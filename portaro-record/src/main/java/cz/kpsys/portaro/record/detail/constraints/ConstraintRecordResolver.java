package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;

import java.util.Set;

public sealed interface ConstraintRecordResolver permits JunctionRecordsLink, SourceRecordLinkRecordResolver, ThisFieldRecordResolver {

    Set<RecordIdFondPair> resolve(@NonNull RecordIdFondPair sourceRecord, Department ctx, LinkConstraintsResolver linkConstraintsResolver);
}
