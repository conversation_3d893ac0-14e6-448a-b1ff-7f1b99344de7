package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;

public class Constraints {

    public static RecordHasGivenLinkedRecord.Builder mustHaveIn(@NonNull FieldTypeId fieldTypeIdWhereLinkedRecordShouldBe) {
        return new RecordHasGivenLinkedRecord.Builder(fieldTypeIdWhereLinkedRecordShouldBe);
    }

    public static SourceRecordLinkRecordResolver sourceRecordLinkIn(@NonNull FieldTypeId linkFieldTypeId) {
        return SourceRecordLinkRecordResolver.ofLinkIn(linkFieldTypeId);
    }

    public static SourceRecordLinkRecordResolver sourceRecordOriginIn(@NonNull FieldTypeId linkFieldTypeId) {
        return SourceRecordLinkRecordResolver.ofOriginIn(linkFieldTypeId);
    }

    public static JunctionRecordsLink.Builder linkIn(@NonNull FieldTypeId junctionRecordsRightSideLinkFieldTypeId) {
        return new JunctionRecordsLink.Builder(junctionRecordsRightSideLinkFieldTypeId);
    }

}
