package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/// matcher, ktery pracuje s vazebnim recordem (podobne jako v DB v M-N tabulce) tak, ze vraci pravou stranu (dany link) vazebniho recordu, splnujici levou stranu
public record JunctionRecordsLink(

        @NonNull Fond junctionRecordsRootFond,
        @NonNull RecordHasGivenLinkedRecord junctionRecordConstraintResolver,
        @NonNull FieldTypeId junctionRecordsRightSideLinkFieldTypeId

) implements ConstraintRecordResolver {

    @Override
    public Set<RecordIdFondPair> resolve(@NonNull RecordIdFondPair sourceRecord, Department ctx, LinkConstraintsResolver linkConstraintsResolver) {
        HavingAnyOfRecordLink junctionRecordConstraint = junctionRecordConstraintResolver.resolve(sourceRecord, ctx, linkConstraintsResolver);
        List<Record> junctionRecords = linkConstraintsResolver.searchRecordsWithFieldLinkingToGivenRecord(ctx, junctionRecordsRootFond, junctionRecordConstraint);

        return junctionRecords.stream()
                .map(source -> getRecordLinkFromField(source, junctionRecordsRightSideLinkFieldTypeId).orElseThrow(() -> new IllegalStateException("Junction record " + source + " does not have link in " + junctionRecordsRightSideLinkFieldTypeId)))
                .collect(Collectors.toUnmodifiableSet());
    }

    private static @NonNull Optional<RecordIdFondPair> getRecordLinkFromField(Record detailedRecord, FieldTypeId linkFieldTypeId) {
        return detailedRecord.getDetail().getFirstFieldRecursive(By.typeId(linkFieldTypeId))
                .flatMap(Field::getRecordLink);
    }

    @Override
    public String toString() {
        return "{@" + junctionRecordsRootFond.getId() + " & " + junctionRecordConstraintResolver + "}" + junctionRecordsRightSideLinkFieldTypeId + " :link";
    }

    public record Builder(
            @NonNull FieldTypeId junctionRecordsRightSideLinkFieldTypeId
    ) {

        public JunctionRecordsLink ofRecordsWhich(@NonNull Fond junctionRecordsRootFond, @NonNull RecordHasGivenLinkedRecord junctionRecordConstraintResolver) {
            return new JunctionRecordsLink(junctionRecordsRootFond, junctionRecordConstraintResolver, junctionRecordsRightSideLinkFieldTypeId);
        }
    }
}
