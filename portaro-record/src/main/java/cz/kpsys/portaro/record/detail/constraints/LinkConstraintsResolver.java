package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.spec.RecordIdFieldTypeId;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.search.restriction.FieldTypeSearchField;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.In;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LinkConstraintsResolver {

    @NonNull ByIdLoadable<Record, UUID> nonDetailedRecordLoader;
    @NonNull IdAndIdsLoadable<Record, UUID> recordLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchSqlLoader;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader;

    public LinkedRecordConstraints resolve(@NonNull RecordIdFieldTypeId recordIdFieldTypeId, Department ctx) {
        Record record = nonDetailedRecordLoader.getById(recordIdFieldTypeId.recordIdFondPair().id().id());
        Fond fond = record.getFond();
        EditableFieldType<?> fondedFieldType = fieldTypesByFondLoader.findByFondAndId(fond, recordIdFieldTypeId.fieldTypeId(), FieldTypesByFondLoader.WhenMissing.THROW);

        LinkedRecordConstraints constraints = LinkedRecordConstraints.empty();

        Optional<Fond> linkedFond = fondedFieldType.getLinkRootFond();
        if (linkedFond.isPresent()) {
            constraints = constraints.withAddedConstraint(new HavingFond(linkedFond.get()));
        }

        for (RecordConstraintResolver constraintDef : fondedFieldType.getLinkConstraints()) {
            constraints = constraints.withAddedConstraint(constraintDef.resolve(recordIdFieldTypeId.recordIdFondPair(), ctx, this));
        }

        return constraints;
    }

    public @NonNull Record loadDetailedRecord(RecordIdFondPair recordId) {
        return recordLoader.getById(recordId.id().id());
    }

    public @NonNull List<Record> searchRecordsWithFieldLinkingToGivenRecord(Department ctx, Fond searchedRecordsRootFond, HavingAnyOfRecordLink searchedRecordsConstraint) {
        return detailedRecordSearchSqlLoader.getContent(RangePaging.forAll(), p -> {
            p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
            p.set(CoreSearchParams.FACETS_ENABLED, false);
            p.set(CoreSearchParams.INCLUDE_DRAFT, false);
            p.set(CoreSearchParams.INCLUDE_DELETED, false);
            p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
            p.set(RecordConstants.SearchParams.ROOT_FOND, List.of(searchedRecordsRootFond));
            p.set(CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.SUBTREE));
            p.set(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION, new Conjunction<>(
                    new Term<>(FieldTypeSearchField.loadOfLink(searchedRecordsConstraint.linkFieldTypeId(), fieldTypeSearchFieldLoader), new In(ListUtil.convertStrict(searchedRecordsConstraint.acceptableRecords(), source -> source.id().id())))
            ));
        });
    }

}
