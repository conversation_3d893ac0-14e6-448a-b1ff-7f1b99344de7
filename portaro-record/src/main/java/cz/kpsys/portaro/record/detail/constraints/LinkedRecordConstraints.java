package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.NonNull;
import lombok.With;

import java.util.ArrayList;
import java.util.List;

@With
public record LinkedRecordConstraints(
        @NonNull List<RecordConstraint> constraints
) {

    public static LinkedRecordConstraints empty() {
        return new LinkedRecordConstraints(new ArrayList<>());
    }

    public LinkedRecordConstraints withAddedConstraint(@NonNull RecordConstraint constraint) {
        return withConstraints(ListUtil.createNewListAppending(constraints, constraint));
    }
}
