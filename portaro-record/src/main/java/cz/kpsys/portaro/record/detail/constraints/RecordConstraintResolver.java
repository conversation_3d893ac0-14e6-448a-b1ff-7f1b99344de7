package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;

public sealed interface RecordConstraintResolver permits RecordHasGivenLinkedRecord, SimpleParameterConstraintResolver {

    RecordConstraint resolve(@NonNull RecordIdFondPair sourceRecord, Department ctx, LinkConstraintsResolver linkConstraintsResolver);

}
