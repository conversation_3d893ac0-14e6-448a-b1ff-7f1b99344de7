package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.util.Set;

public record RecordHasGivenLinkedRecord(

        @NonNull FieldTypeId fieldTypeIdWhereLinkedRecordShouldBe,
        @NonNull ConstraintRecordResolver linkedRecordResolver

) implements RecordConstraintResolver {

    @Override
    public HavingAnyOfRecordLink resolve(@NonNull RecordIdFondPair sourceRecord, Department ctx, LinkConstraintsResolver linkConstraintsResolver) {
        Set<RecordIdFondPair> acceptableLinkedRecords = linkedRecordResolver.resolve(sourceRecord, ctx, linkConstraintsResolver);
        assertNotEmpty(sourceRecord, acceptableLinkedRecords);
        return new HavingAnyOfRecordLink(fieldTypeIdWhereLinkedRecordShouldBe, acceptableLinkedRecords);
    }

    private void assertNotEmpty(@NonNull RecordIdFondPair sourceRecord, Set<RecordIdFondPair> acceptableLinkedRecords) {
        Assert.notEmpty(acceptableLinkedRecords, () -> "Record " + sourceRecord + " does not have linked record of " + linkedRecordResolver);
    }

    @Override
    public String toString() {
        return "record:" + fieldTypeIdWhereLinkedRecordShouldBe + ":link must be " + linkedRecordResolver;
    }

    public record Builder(
            @NonNull FieldTypeId fieldTypeIdWhereLinkedRecordShouldBe
    ) {

        public RecordHasGivenLinkedRecord linkWithAnyOf(@NonNull ConstraintRecordResolver linkedRecordResolver) {
            return new RecordHasGivenLinkedRecord(fieldTypeIdWhereLinkedRecordShouldBe, linkedRecordResolver);
        }
    }

}
