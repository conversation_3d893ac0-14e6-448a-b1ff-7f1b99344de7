package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Disjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.SearchMatcher;
import lombok.NonNull;

public record SimpleParameterConstraintResolver(

        @NonNull Conjunction<SearchField> restriction

) implements RecordConstraintResolver {

    public static <E> SimpleParameterConstraintResolver create(SearchField param, SearchMatcher matcher) {
        Conjunction<SearchField> conjunction = new Conjunction<>();
        conjunction.add(new Term<>(param, matcher));
        return create(conjunction);
    }

    public static SimpleParameterConstraintResolver create(Conjunction<SearchField> restriction) {
        return new SimpleParameterConstraintResolver(restriction);
    }

    public static SimpleParameterConstraintResolver create(Disjunction<SearchField> restriction) {
        Conjunction<SearchField> conjunction = new Conjunction<>();
        conjunction.add(restriction);
        return new SimpleParameterConstraintResolver(conjunction);
    }

    @Override
    public String toString() {
        return restriction.toString();
    }

    @Override
    public RecordConstraint resolve(@NonNull RecordIdFondPair sourceRecord, Department ctx, LinkConstraintsResolver linkConstraintsResolver) {
        return new RestrictionConstraint(restriction());
    }
}
