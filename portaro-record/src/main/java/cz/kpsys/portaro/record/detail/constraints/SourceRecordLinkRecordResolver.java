package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

public record SourceRecordLinkRecordResolver(

        @NonNull FieldTypeId linkFieldTypeId,
        @NonNull Function<Field<? extends ScalarFieldValue<?>>, RecordIdFondPair> recordFromFieldExtractor

) implements ConstraintRecordResolver {

    public static final Function<Field<? extends ScalarFieldValue<?>>, RecordIdFondPair> RECORD_LINK_EXTRACTOR = field -> field.getRecordLink().orElseThrow(() -> new IllegalStateException("Record " + field.getRecordIdFondPair() + " does not have link in " + field));
    public static final Function<Field<? extends ScalarFieldValue<?>>, RecordIdFondPair> RECORD_ORIGIN_EXTRACTOR = field -> field.getMaxOneOrigin().orElseThrow(() -> new IllegalStateException("Record " + field.getRecordIdFondPair() + " does not have origin in " + field));

    public static SourceRecordLinkRecordResolver ofLinkIn(@NonNull FieldTypeId linkFieldTypeId) {
        return new SourceRecordLinkRecordResolver(linkFieldTypeId, RECORD_LINK_EXTRACTOR);
    }

    public static SourceRecordLinkRecordResolver ofOriginIn(@NonNull FieldTypeId linkFieldTypeId) {
        return new SourceRecordLinkRecordResolver(linkFieldTypeId, RECORD_ORIGIN_EXTRACTOR);
    }

    @Override
    public Set<RecordIdFondPair> resolve(@NonNull RecordIdFondPair sourceRecord, Department ctx, LinkConstraintsResolver linkConstraintsResolver) {
        Record detailedSourceRecord = linkConstraintsResolver.loadDetailedRecord(sourceRecord);
        return getRecordLinksFromFieldType(detailedSourceRecord, linkFieldTypeId);
    }

    /// obecne by mel byt jen jeden link
    private @NonNull Set<RecordIdFondPair> getRecordLinksFromFieldType(Record detailedRecord, FieldTypeId linkFieldTypeId) {
        Set<RecordIdFondPair> links = detailedRecord.getDetail().deepStreamFields()
                .filter(By.typeId(linkFieldTypeId))
                .map(recordFromFieldExtractor)
                .collect(Collectors.toUnmodifiableSet());
        Assert.notEmpty(links, () -> "Record %s does not have any %s fields".formatted(detailedRecord, linkFieldTypeId));
        return links;
    }

}
