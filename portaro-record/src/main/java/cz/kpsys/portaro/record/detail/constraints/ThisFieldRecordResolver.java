package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;

import java.util.Set;

public record ThisFieldRecordResolver() implements ConstraintRecordResolver {

    public static ThisFieldRecordResolver create() {
        return new ThisFieldRecordResolver();
    }

    @Override
    public Set<RecordIdFondPair> resolve(@NonNull RecordIdFondPair sourceRecord, Department ctx, LinkConstraintsResolver linkConstraintsResolver) {
        return Set.of(sourceRecord);
    }

    @Override
    public String toString() {
        return "{thisField}:record";
    }
}
