package cz.kpsys.portaro.record.detail.data;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import static cz.kpsys.portaro.databasestructure.RecordDb.FIELD_TYPE.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class FieldTypeEntity implements Identified<String> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    String id;

    @Column(name = NAZEV)
    String name;

    @Column(name = PIC)
    String pic;

    @Column(name = FORMAT)
    @Nullable
    @NullableNotBlank
    String format;

    @Column(name = EXPORT_ID)
    @Nullable
    @NullableNotBlank
    String exportId;

    @Column(name = NENIOPAK)
    Boolean notRepeatable;

    @Column(name = AUTTYP)
    @NonNull
    Integer authorityType;

    @Column(name = FONDEXT)
    Integer linkedRecordFond;

    @Column(name = PODPEXT)
    String linkedRecordEntryFieldSubfieldCode;

    @Column(name = PRENASET)
    Integer transferTypeId;

    @Column(name = FK_FRAZESKUP)
    String phraseGroupId;

    @Column(name = GENERATION)
    String generation;

}
