package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.BooleanFieldValue;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.util.Objects;
import java.util.function.Function;

import static cz.kpsys.portaro.CoreConstants.Datatype.BOOLEAN;

public record Equal<T extends ScalarFieldValue<?>>(
        @NonNull
        Formula<T> first,

        @NonNull
        Formula<T> second
) implements BinaryFunction<BooleanFieldValue> {
    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<BooleanFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return BOOLEAN;
    }

    @Override
    public @NonNull FormulaEvaluation<BooleanFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        var firstEval = evaluator.resolveFormulaValue(sourceNode, first);
        var secondEval = evaluator.resolveFormulaValue(sourceNode, second);

        FormulaEvaluations<T> operandEvals = FormulaEvaluations.extract(firstEval, secondEval);
        if (operandEvals.hasFail()) {
            return operandEvals.toMostCriticalMultiFailEval().castedFailed();
        }

        var first = firstEval.existingSuccess();
        var second = secondEval.existingSuccess();

        return compute(first, second);
    }

    public @NonNull FormulaEvaluation<BooleanFieldValue> compute(@NonNull T first, @NonNull T second) {
        var mergedOrigins = ListUtil.unionSet(first.origins(), second.origins());
        Boolean equal = Objects.equals(first.value(), second.value());
        return FormulaEvaluation.success(BooleanFieldValue.of(equal, mergedOrigins));
    }
}
