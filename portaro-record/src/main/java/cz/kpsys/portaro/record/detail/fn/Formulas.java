package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.fn.bool.And;
import cz.kpsys.portaro.record.detail.fn.bool.Not;
import cz.kpsys.portaro.record.detail.fn.bool.Or;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAccessor;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class Formulas {

    public static <RES extends FieldValue<?>> Ref<RES> ref(@NonNull LookupDefinition pointer) {
        return new Ref<>(pointer);
    }

    public static <RES extends FieldValue<?>> Ref<RES> ref(String linkFieldTypeId, String linkedFieldTypeId) {
        return ref(LookupDefinition.ofSpecificFieldLink(FieldTypeId.parse(linkFieldTypeId), FieldTypeId.parse(linkedFieldTypeId)));
    }

    public static <RES extends FieldValue<?>> Ref<RES> localRef(@NonNull String targetFieldTypeId) {
        return new Ref<>(LookupDefinition.ofSelfRecord(FieldTypeId.parse(targetFieldTypeId)));
    }

    public static <RES extends FieldValue<?>> BackRef<RES> backRef(@NonNull FieldTypeId linkingRecordsLinkAndValueFieldTypeId) {
        return new BackRef<>(new LinkingRecordsLookupDefinition(linkingRecordsLinkAndValueFieldTypeId, linkingRecordsLinkAndValueFieldTypeId, null));
    }

    public static <RES extends FieldValue<?>> BackRef<RES> backRef(@NonNull FieldTypeId linkingRecordsLinkFieldTypeId, @NonNull FieldTypeId linkingRecordsValueFieldTypeId) {
        return new BackRef<>(new LinkingRecordsLookupDefinition(linkingRecordsLinkFieldTypeId, linkingRecordsValueFieldTypeId, null));
    }

    public static <RES extends FieldValue<?>> BackRef<RES> backRef(@NonNull FieldTypeId linkingRecordsLinkFieldTypeId, @NonNull FieldTypeId linkingRecordsValueFieldTypeId, @NonNull Collection<Fond> linkingRecordsFondIds) {
        return new BackRef<>(new LinkingRecordsLookupDefinition(linkingRecordsLinkFieldTypeId, linkingRecordsValueFieldTypeId, linkingRecordsFondIds.stream().map(BasicIdentified::getId).collect(Collectors.toUnmodifiableSet())));
    }

    public static Constant<NumberFieldValue> constZero() {
        return Constant.ofZero();
    }

    public static Constant<NumberFieldValue> constOne() {
        return Constant.ofOne();
    }

    public static Constant<NumberFieldValue> constInteger(@NonNull BigDecimal value) {
        return Constant.ofInteger(value);
    }

    public static Constant<NumberFieldValue> constLong(@NonNull Provider<Long> value) {
        return Constant.ofLong(value);
    }

    public static Constant<StringFieldValue> constString(@NonNull Provider<String> value) {
        return Constant.ofString(value);
    }

    public static Constant<AcceptableValueFieldValue<?>> constAcceptable(@NonNull Provider<String> value) {
        return Constant.ofAcceptable(value);
    }

    public static Constant<AcceptableValueFieldValue<?>> constAcceptable(@NonNull String value) {
        return Constant.ofAcceptable(() -> value);
    }

    public static Constant<NumberFieldValue> constDecimal2(@NonNull BigDecimal value) {
        return Constant.ofDecimal2(value);
    }

    public static Constant<BooleanFieldValue> constFalse() {
        return Constant.ofBoolean(false);
    }

    public static Constant<BooleanFieldValue> constTrue() {
        return Constant.ofBoolean(true);
    }

    public static Constant<LocalDateFieldValue> constLocalDate(@NonNull Provider<LocalDate> value) {
        return Constant.ofLocalDate(value);
    }

    public static <RES extends ScalarFieldValue<?>> Equal<RES> equal(Formula<RES> first, Formula<RES> second) {
        return new Equal<>(first, second);
    }

    public static And and(Formula<BooleanFieldValue> first, Formula<BooleanFieldValue> second) {
        return new And(first, second);
    }

    public static Or or(Formula<BooleanFieldValue> first, Formula<BooleanFieldValue> second) {
        return new Or(first, second);
    }

    public static Not not(Formula<BooleanFieldValue> operand) {
        return new Not(operand);
    }

    public static Round round0(@NonNull Formula<NumberFieldValue> operand) {
        return Round.create(operand, CoreConstants.Datatype.NUMBER);
    }

    public static Round round2(@NonNull Formula<NumberFieldValue> operand) {
        return Round.create(operand, CoreConstants.Datatype.NUMBER_DECIMAL_2);
    }

    public static <RES extends FieldValue<?>> Cache<RES> cache(@NonNull Formula<RES> formula) {
        return new Cache<>(formula);
    }

    @SafeVarargs
    public static <RES extends FieldValue<?>> Coalesce<RES> coalesce(@NonNull Formula<RES> primary, @NonNull Formula<RES> secondary, @NonNull Formula<RES>... others) {
        return Coalesce.createLazy(primary, secondary, others);
    }

    @SafeVarargs
    public static <RES extends FieldValue<?>> Coalesce<RES> eagerCoalesce(@NonNull Formula<RES> primary, @NonNull Formula<RES> secondary, @NonNull Formula<RES>... others) {
        return Coalesce.createEager(primary, secondary, others);
    }

    public static <RES extends FieldValue<?>> Conditional<RES> conditional(@NonNull Formula<BooleanFieldValue> condition, @NonNull Formula<RES> onTrue, @NonNull Formula<RES> onFalse) {
        return Conditional.create(condition, onTrue, onFalse);
    }

    @SafeVarargs
    public static <RES extends ScalarFieldValue<?>> Match<RES> match(@NonNull Case<RES>... cases) {
        return new Match<>(List.of(cases));
    }

    public static <RES extends ScalarFieldValue<?>> WhenCase<RES> whenCase(@NonNull Formula<BooleanFieldValue> condition, @NonNull Formula<RES> onTrue) {
        return new WhenCase<>(condition, onTrue);
    }

    public static <RES extends ScalarFieldValue<?>> DefaultCase<RES> defaultCase(@NonNull Formula<RES> defaultValue) {
        return new DefaultCase<>(defaultValue);
    }

    @SafeVarargs
    public static Multiplication multiply(@NonNull @NotEmpty Formula<NumberFieldValue>... operands) {
        return new Multiplication(List.of(operands));
    }

    public static Division divide4(@NonNull Formula<NumberFieldValue> dividend, @NonNull Formula<NumberFieldValue> divider) {
        return Division.ofDecimal4(dividend, divider);
    }

    public static Coalesce<NumberFieldValue> orZero(@NonNull Formula<NumberFieldValue> primary) {
        return coalesce(primary, constZero());
    }

    public static Coalesce<BooleanFieldValue> orFalse(@NonNull Formula<BooleanFieldValue> primary) {
        return coalesce(primary, constFalse());
    }

    @SafeVarargs
    public static Sum sum(@NonNull @NotEmpty Formula<NumberFieldValue>... operands) {
        return new Sum(List.of(operands));
    }

    @SafeVarargs
    public static Max max(@NonNull @NotEmpty Formula<NumberFieldValue>... operands) {
        return new Max(List.of(operands));
    }

    public static Replace replace(@NonNull Formula<StringFieldValue> input, @NonNull String replacementFormat, @NonNull Formula<StringFieldValue> replacement) {
        return new Replace(input, replacementFormat, replacement);
    }

    @SafeVarargs
    public static <T extends ScalarFieldValue<?>> Format<T> format(@NonNull String format, @NonNull Formula<T>... target) {
        return new Format<>(format, List.of(target));
    }

    public static <T extends ScalarFieldValue<? extends TemporalAccessor>> DateFormat<T> dateFormat(@NonNull String format, @NonNull Formula<T> operand) {
        return new DateFormat<>(format, operand);
    }

    public static NumericIdOf idOf(@NonNull Formula<AcceptableValueFieldValue<Identified<Integer>>> target) {
        return new NumericIdOf(target);
    }

    public static VectorSize vectorSize(@NonNull Formula<VectorFieldValue<?, ?>> operand) {
        return new VectorSize(operand);
    }
}
