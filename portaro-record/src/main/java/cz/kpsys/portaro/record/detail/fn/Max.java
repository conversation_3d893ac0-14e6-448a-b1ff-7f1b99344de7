package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.datatype.DatatypeUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.function.Function;

public record Max(

        @NonNull
        @NotEmpty
        List<Formula<NumberFieldValue>> operands

) implements NumericNaryOperator {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<NumberFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        List<ScalarDatatype> operandDatatypes = ListUtil.convertStrict(operands, operandResultDatatypeResolver::apply);
        return DatatypeUtil.getMostPreciseDatatype(operandDatatypes);
    }

    @Override
    public @NonNull FormulaEvaluation<NumberFieldValue> compute(@NonNull @NotEmpty List<NumberFieldValue> operandVector) {
        if (operandVector.isEmpty()) {
            throw new IllegalArgumentException("Cannot compute max of empty list");
        }

        BigDecimal maxValue = null;
        var origins = new HashSet<RecordIdFondPair>();

        for (ScalarFieldValue<BigDecimal> operandValue : operandVector) {
            BigDecimal currentValue = operandValue.value();
            if (maxValue == null || currentValue.compareTo(maxValue) > 0) {
                maxValue = currentValue;
                origins.clear();
                origins.addAll(operandValue.origins());
            }
        }

        return FormulaEvaluation.success(NumberFieldValue.of(maxValue, origins));
    }

    @Override
    public String toString() {
        return "Max(%s)".formatted(StringUtil.listToString(operands, ", "));
    }
}
