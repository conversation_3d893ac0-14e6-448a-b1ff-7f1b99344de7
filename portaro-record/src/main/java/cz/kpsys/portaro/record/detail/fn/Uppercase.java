package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.util.function.Function;

import static cz.kpsys.portaro.CoreConstants.Datatype.TEXT;

public record Uppercase(
        @NonNull
        Formula<StringFieldValue> operand

) implements UnaryFunction<StringFieldValue> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<StringFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return TEXT;
    }

    @Override
    public @NonNull FormulaEvaluation<StringFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        var operandEval = evaluator.resolveFormulaValue(sourceNode, operand);

        if (operandEval.isFailure()) {
            return operandEval;
        }

        var operand = operandEval.existingSuccess();

        return FormulaEvaluation.success(StringFieldValue.of(operand.value().toUpperCase(), operand.origins()));
    }
}
