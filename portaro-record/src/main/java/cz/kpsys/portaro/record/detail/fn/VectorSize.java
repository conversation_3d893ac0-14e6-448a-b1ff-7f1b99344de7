package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.detail.value.VectorFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.math.BigDecimal;
import java.util.Set;
import java.util.function.Function;

import static cz.kpsys.portaro.CoreConstants.Datatype.NUMBER;

public record VectorSize(
        @NonNull Formula<VectorFieldValue<?, ?>> operand
) implements UnaryFunction<NumberFieldValue> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<NumberFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return NUMBER;
    }

    @Override
    public @NonNull FormulaEvaluation<NumberFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        var resolvedOperandValue = evaluator.resolveFormulaValue(sourceNode, operand).asVector();

        if (resolvedOperandValue.isFailure()) {
            return resolvedOperandValue.castedFailed();
        }

        return FormulaEvaluation.success(NumberFieldValue.of(BigDecimal.valueOf(resolvedOperandValue.existingSuccess().values().size()), Set.of()));
    }
}
