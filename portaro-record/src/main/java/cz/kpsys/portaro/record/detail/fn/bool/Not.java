package cz.kpsys.portaro.record.detail.fn.bool;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.fn.FormulaEvaluation;
import cz.kpsys.portaro.record.detail.fn.FormulaEvaluator;
import cz.kpsys.portaro.record.detail.fn.UnaryFunction;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.BooleanFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.util.function.Function;

import static cz.kpsys.portaro.CoreConstants.Datatype.BOOLEAN;

public record Not(
        @NonNull
        Formula<BooleanFieldValue> operand

) implements UnaryFunction<BooleanFieldValue> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<BooleanFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return BOOLEAN;
    }

    @Override
    public @NonNull FormulaEvaluation<BooleanFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        var operandEval = evaluator.resolveFormulaValue(sourceNode, operand);

        if (operandEval.isFailure()) {
            return operandEval;
        }

        var operand = operandEval.existingSuccess();

        return FormulaEvaluation.success(BooleanFieldValue.of(!operand.value(), operand.origins()));
    }
}
