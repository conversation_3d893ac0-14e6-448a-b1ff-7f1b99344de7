package cz.kpsys.portaro.record.detail.fn.bool;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.fn.*;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.BooleanFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.util.function.Function;

import static cz.kpsys.portaro.CoreConstants.Datatype.BOOLEAN;

public record Or(
        @NonNull
        Formula<BooleanFieldValue> first,

        @NonNull
        Formula<BooleanFieldValue> second
) implements BinaryFunction<BooleanFieldValue> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<BooleanFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return BOOLEAN;
    }

    @Override
    public @NonNull FormulaEvaluation<BooleanFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        var firstEval = evaluator.resolveFormulaValue(sourceNode, first);
        var secondEval = evaluator.resolveFormulaValue(sourceNode, second);

        FormulaEvaluations<BooleanFieldValue> operandEvals = FormulaEvaluations.extract(firstEval, secondEval);
        if (operandEvals.hasFail()) {
            return operandEvals.toMostCriticalMultiFailEval().castedFailed();
        }

        var first = firstEval.existingSuccess();
        var second = secondEval.existingSuccess();

        return compute(first, second);
    }

    public @NonNull FormulaEvaluation<BooleanFieldValue> compute(@NonNull BooleanFieldValue first, @NonNull BooleanFieldValue second) {
        var mergedOrigins = ListUtil.unionSet(first.origins(), second.origins());
        Boolean and = first.value() || second.value();
        return FormulaEvaluation.success(BooleanFieldValue.of(and, mergedOrigins));
    }
}
