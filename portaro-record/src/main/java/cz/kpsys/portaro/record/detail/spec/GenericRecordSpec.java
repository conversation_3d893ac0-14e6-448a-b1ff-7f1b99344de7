package cz.kpsys.portaro.record.detail.spec;

import lombok.NonNull;
import lombok.With;

import java.util.Set;

@With
public record GenericRecordSpec(

        @NonNull
        RecordMatcher recordMatcher,

        @NonNull
        FieldsSpec fieldsSpec

) implements FieldedRecordSpec {

    @Override
    public @NonNull Set<FieldSpec> existingFields() {
        return fieldsSpec.existingSpecs();
    }

    @Override
    public boolean isEmpty() {
        return fieldsSpec.isEmpty();
    }

    @Override
    public boolean equals(Object o) {
        return this == o || o instanceof FieldedRecordSpec that && fieldsSpec.equals(that.fieldsSpec()) && recordMatcher.equals(that.recordMatcher());
    }

    @Override
    public int hashCode() {
        int result = recordMatcher.hashCode();
        result = 31 * result + fieldsSpec.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return recordMatcher + ":" + fieldsSpec;
    }

}
