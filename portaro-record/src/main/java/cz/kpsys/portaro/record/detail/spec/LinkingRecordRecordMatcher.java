package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public record LinkingRecordRecordMatcher(

        @NonNull
        FieldTypeId linkingRecordsLinkFieldTypeId,

        @NonNull
        RecordIdentifier linkingRecordsLink,

        @Nullable
        Set<Integer> linkingRecordsFondIds

) implements RecordMatcher {

    public static LinkingRecordRecordMatcher of(@NonNull FieldTypeId linkingRecordsLinkFieldTypeId, @NonNull RecordIdentifier linkingRecordsLink, @Nullable Set<Integer> linkingRecordsFondIds) {
        return new LinkingRecordRecordMatcher(linkingRecordsLinkFieldTypeId, linkingRecordsLink, linkingRecordsFondIds);
    }

    @Deprecated
    @Override
    public @Nullable RecordIdFondPair recordIdFondPair() {
        return null;
    }

    @Override
    public boolean matches(@NonNull Field<?> field) {
        if (!field.getType().getFieldTypeId().equals(linkingRecordsLinkFieldTypeId)) {
            return false;
        }
        if (!field.hasRecordLink()) {
            return false;
        }
        if (!field.getExistingRecordLink().id().equals(linkingRecordsLink)) {
            return false;
        }
        if (linkingRecordsFondIds != null && !linkingRecordsFondIds.contains(field.getRecordIdFondPair().fond().getId())) {
            return false;
        }
        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LinkingRecordRecordMatcher that)) {
            return false;
        }
        return Objects.equals(linkingRecordsFondIds(), that.linkingRecordsFondIds()) &&
               linkingRecordsLink().equals(that.linkingRecordsLink()) &&
               linkingRecordsLinkFieldTypeId().equals(that.linkingRecordsLinkFieldTypeId());
    }

    @Override
    public int hashCode() {
        int result = linkingRecordsLinkFieldTypeId().hashCode();
        result = 31 * result + linkingRecordsLink().hashCode();
        result = 31 * result + Objects.hashCode(linkingRecordsFondIds());
        return result;
    }

    @Override
    public String toString() {
        String fondCondition = ListUtil.notNullStream(linkingRecordsFondIds).map(Object::toString).collect(Collectors.joining(",", "@", " "));
        return "{" + fondCondition + linkingRecordsLinkFieldTypeId + ":link = " + linkingRecordsLink.abbr() + "}";
    }

}
