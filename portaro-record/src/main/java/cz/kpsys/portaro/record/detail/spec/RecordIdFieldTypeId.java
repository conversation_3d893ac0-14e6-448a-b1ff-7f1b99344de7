package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.UUID;

public record RecordIdFieldTypeId(

        @NonNull
        RecordIdFondPair recordIdFondPair,

        @NonNull
        FieldTypeId fieldTypeId

) {

    public static final String RECORD_ID_FIELD_TYPE_ID_DELIM = ":";

    public static RecordIdFieldTypeId of(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldTypeId fieldTypeId) {
        return new RecordIdFieldTypeId(recordIdFondPair, fieldTypeId);
    }

    public String value() {
        return recordIdFondPair.id().id() + RECORD_ID_FIELD_TYPE_ID_DELIM + fieldTypeId.value();
    }

    @Override
    public String toString() {
        return recordIdFondPair.id().abbrDelim() + fieldTypeId;
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class RecordIdFieldTypeIdToStringConverter implements Converter<RecordIdFieldTypeId, String> {

        @Override
        public String convert(RecordIdFieldTypeId source) {
            return source.recordIdFondPair.id() + RECORD_ID_FIELD_TYPE_ID_DELIM + source.fieldTypeId.value();
        }
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class StringToRecordIdFieldTypeIdConverter implements Converter<String, RecordIdFieldTypeId> {

        @NonNull ByIdLoadable<RecordIdFondPair, UUID> recordFondPairsLoader;

        @Override
        public RecordIdFieldTypeId convert(String string) {
            if (!string.contains(RECORD_ID_FIELD_TYPE_ID_DELIM)) {
                throw new IllegalArgumentException("Invalid format. Expected {recordId}:{fieldTypeId}, got: " + string);
            }
            String[] parts = string.split(RECORD_ID_FIELD_TYPE_ID_DELIM, 2);
            if (parts.length != 2) {
                throw new IllegalArgumentException("Invalid format. Expected {recordId}:{fieldTypeId}, got: " + string);
            }
            UUID recordId = StringToUuidConverter.fromString(parts[0]);
            FieldTypeId fieldTypeId = FieldTypeId.parse(parts[1]);
            RecordIdFondPair recordIdFondPair = recordFondPairsLoader.getById(recordId);
            return RecordIdFieldTypeId.of(recordIdFondPair, fieldTypeId);
        }
    }

}
