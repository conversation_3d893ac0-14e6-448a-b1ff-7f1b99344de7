package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.isbn.Isbn;
import cz.kpsys.portaro.record.load.EntityFieldCodeMapper;
import cz.kpsys.portaro.record.search.restriction.FieldTypeSearchField;
import cz.kpsys.portaro.search.AbstractSingleColumnSpringDbSearchLoader;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Junction;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.*;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.Brackets;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.commons.util.ListUtil.getListOfIds;
import static cz.kpsys.portaro.databasestructure.RecordDb.*;
import static cz.kpsys.portaro.record.RecordKeyType.*;
import static cz.kpsys.portaro.search.CoreSearchParams.DEPARTMENT;

/**
 * Wide-usable SQL-based search loader for generating index or search authority-related documents
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbDocumentIdSearchLoader extends AbstractSingleColumnSpringDbSearchLoader<MapBackedParams, UUID, RangePaging> {

    @NonNull DepartmentAccessor departmentAccessor;

    public SpringDbDocumentIdSearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                          @NonNull QueryFactory queryFactory,
                                          @NonNull DepartmentAccessor departmentAccessor) {
        super(jdbcTemplate, queryFactory, RECORD.TABLE, RECORD.ID, new SelectedColumnRowMapper<>(UUID.class, RECORD.ID));
        this.departmentAccessor = departmentAccessor;
        setDistinct(true);
    }

    @Override
    protected void select(@NonNull SelectQuery sq, @NonNull MapBackedParams mapBackedParams, @Nullable SortingItem customSorting) {
        sq.selectDistinct(
                TC(RECORD.TABLE, RECORD.ID),
                TC(RECORD.TABLE, RECORD.SORTING_KEY) // sorting key is in order-by, so in PG must be in select query
        );
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.from(RECORD.TABLE);

        boolean joinWithHoldings = false;

        if (p.hasNotNull(RecordConstants.SearchParams.RECORD_RELATED_RECORD)) {
            RecordIdFondPair recordHeader = p.get(RecordConstants.SearchParams.RECORD_RELATED_RECORD);
            sq.joinOrExists(queryFactory,
                    RECORD.TABLE,
                    RECORD.ID,
                    KAT1_7.KAT1_7,
                    KAT1_7.TARGET_RECORD_ID,
                    "record_related_record",
                    (where, alias) -> where.and().eq(TC(alias, KAT1_7.TARGET_RECORD_ID), recordHeader.id().id()));
        }

        if (p.hasNotNull(CoreSearchParams.NAME)) {
            sq.joinOrExists(queryFactory, RECORD.TABLE, RECORD.ID, RECORD_KEY.TABLE, RECORD_KEY.RECORD_ID, "name", (where, alias) -> where
                    .and().eq(TC(alias, RECORD_KEY.NAME), ORIG_PRIMARY_NAME.getId())
                    .and().eq(TC(alias, RECORD_KEY.VAL), p.get(CoreSearchParams.NAME)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.AUTHOR)) {
            sq.joinOrExists(queryFactory, RECORD.TABLE, RECORD.ID, RECORD_KEY.TABLE, RECORD_KEY.RECORD_ID, "author", (where, alias) -> where
                    .and().eq(TC(alias, RECORD_KEY.NAME), ORIG_AUTHOR_NAME.getId())
                    .and().eq(TC(alias, RECORD_KEY.VAL), p.get(RecordConstants.SearchParams.AUTHOR)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.ISBN)) {
            Isbn isxn = new Isbn(p.get(RecordConstants.SearchParams.ISBN));
            sq.joinOrExists(queryFactory, RECORD.TABLE, RECORD.ID, RECORD_KEY.TABLE, RECORD_KEY.RECORD_ID, "isbn", (where, alias) -> where
                    .and().eq(TC(alias, RECORD_KEY.NAME), CORE_ISBN.getId())
                    .and().eq(TC(alias, RECORD_KEY.VAL), isxn.getCoreValue()));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.PUBLISHER)) {
            sq.joinOrExists(queryFactory, RECORD.TABLE, RECORD.ID, RECORD_KEY.TABLE, RECORD_KEY.RECORD_ID, "publisher", (where, alias) -> where
                    .and().eq(TC(alias, RECORD_KEY.NAME), ORIG_PUBLISHER_NAME.getId())
                    .and().eq(TC(alias, RECORD_KEY.VAL), p.get(RecordConstants.SearchParams.PUBLISHER)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.PUBLICATION_YEAR)) {
            sq.joinOrExists(queryFactory, RECORD.TABLE, RECORD.ID, RECORD_KEY.TABLE, RECORD_KEY.RECORD_ID, "publication_start", (where, alias) -> where
                    .and().eq(TC(alias, RECORD_KEY.NAME), PUBLICATION_START_YEAR.getId())
                    .and().gtEq(TC(alias, RECORD_KEY.VAL), p.get(RecordConstants.SearchParams.PUBLICATION_YEAR)));

            sq.joinOrExists(queryFactory, RECORD.TABLE, RECORD.ID, RECORD_KEY.TABLE, RECORD_KEY.RECORD_ID, "publication_end", (where, alias) -> where
                    .and().eq(TC(alias, RECORD_KEY.NAME), PUBLICATION_END_YEAR.getId())
                    .and().ltEq(TC(alias, RECORD_KEY.VAL), p.get(RecordConstants.SearchParams.PUBLICATION_YEAR)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.PREFIX)) {
            if (p.get(RecordConstants.SearchParams.PREFIX).length() > Record.RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH) {
                return false;
            }
            sq.joins().add(AS(RECORD_KEY.TABLE, "prefix"), new Brackets(sq).eqRaw(TC(RECORD.TABLE, RECORD.ID), TC("prefix", RECORD_KEY.RECORD_ID))
                    .and().eq(TC("prefix", RECORD_KEY.NAME), FLAT_PRIMARY_NAME.getId())
                    .and().like(TC("prefix", RECORD_KEY.VAL), StringUtil.flatString(p.get(RecordConstants.SearchParams.PREFIX)).toUpperCase(), false, true, Record.RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH).toString());
        }

        if (p.hasNotNull(RecordConstants.SearchParams.DIACRITICAL_PREFIX)) {
            if (p.get(RecordConstants.SearchParams.DIACRITICAL_PREFIX).length() > Record.RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH) {
                return false;
            }
            sq.joins().add(AS(RECORD_KEY.TABLE, "diaPrefix"), new Brackets(sq).eqRaw(TC(RECORD.TABLE, RECORD.ID), TC("diaPrefix", RECORD_KEY.RECORD_ID))
                    .and().eq(TC("diaPrefix", RECORD_KEY.NAME), ORIG_PRIMARY_NAME.getId())
                    .and().like(upper(TC("diaPrefix", RECORD_KEY.VAL)), p.get(RecordConstants.SearchParams.DIACRITICAL_PREFIX).toUpperCase(), false, true, Record.RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH).toString());
        }

        if (p.hasNotNull(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION)) {
            Junction<FieldTypeSearchField> fieldValueConjunction = p.get(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION);
            if (fieldValueConjunction.isEmpty()) {
                return false;
            }
            Assert.isInstanceOf(Conjunction.class, fieldValueConjunction, () -> "Only Conjunction restriction type is supported, but %s specified".formatted(fieldValueConjunction.getClass().getSimpleName()));
            for (Restriction<? extends FieldTypeSearchField> restriction : fieldValueConjunction.getItems()) {
                Assert.isInstanceOf(Term.class, restriction, () -> "Only Term restriction type is supported, but %s specified".formatted(restriction.getClass().getSimpleName()));
                Term<FieldTypeSearchField> term = (Term<FieldTypeSearchField>) restriction;
                FieldTypeSearchField searchField = term.field();
                FieldTypeId fieldTypeId = searchField.fieldType().getFieldTypeId();
                SearchMatcher matcher = term.matcher();

                sq.joinOrExists(queryFactory, RECORD.TABLE, RECORD.ID, RECORD_FIELD.TABLE, RECORD_FIELD.RECORD_ID, "fieldValue_" + fieldTypeId.dbAliasCompatibleValue(), (where, alias) -> {
                    where.and()
                            .eq(TC(alias, RECORD_FIELD.FIELD_CODE), fieldTypeId.existingParent().getCode())
                            .and()
                            .eq(TC(alias, RECORD_FIELD.SUBFIELD_CODE), EntityFieldCodeMapper.codeToDbCode(fieldTypeId.getCode()));

                    switch (searchField.subject()) {
                        case LINK -> {
                            switch (matcher) {
                                case Eq eq -> {
                                    Assert.isInstanceOf(UUID.class, eq.value(), () -> "Only UUID type is supported when by-link search is requested, but %s specified".formatted(eq.value().getClass().getSimpleName()));
                                    where.and().eq(TC(alias, RECORD_FIELD.TARGET_RECORD_ID), eq.value());
                                }
                                case In(Collection<?> collection) -> {
                                    List<UUID> recordIds = ListUtil.castItems(collection, UUID.class).toList();
                                    where.and().in(TC(alias, RECORD_FIELD.TARGET_RECORD_ID), recordIds);
                                }
                                default -> throw new IllegalArgumentException("Unsupported matcher type for by-link search: " + matcher.getClass().getSimpleName());
                            }
                        }
                        case ORIGIN -> {
                            switch (matcher) {
                                case Eq eq -> {
                                    Assert.isInstanceOf(UUID.class, eq.value(), () -> "Only UUID type is supported when by-origin search is requested, but %s specified".formatted(eq.value().getClass().getSimpleName()));
                                    where.and().eq(TC(alias, RECORD_FIELD.ORIGIN_RECORD_ID), eq.value());
                                }
                                case In(Collection<?> collection) -> {
                                    List<UUID> recordIds = ListUtil.castItems(collection, UUID.class).toList();
                                    where.and().in(TC(alias, RECORD_FIELD.ORIGIN_RECORD_ID), recordIds);
                                }
                                default -> throw new IllegalArgumentException("Unsupported matcher type for by-origin search: " + matcher.getClass().getSimpleName());
                            }
                        }
                        case VALUE -> {
                            switch (matcher) {
                                case Eq eq -> {
                                    switch (eq.value()) {
                                        case LocalDate localDate -> where.and().eq(TC(alias, RECORD_FIELD.DATE_VALUE), localDate);
                                        case Instant instant -> where.and().eq(TC(alias, RECORD_FIELD.DATETIME_VALUE), instant);
                                        case String string -> where.and().eq(TC(alias, RECORD_FIELD.TEXT_VALUE), string);
                                        case Boolean bool -> where.and().eq(TC(alias, RECORD_FIELD.BOOLEAN_VALUE), bool);
                                        case BigDecimal bigDecimal -> where.and().eq(TC(alias, RECORD_FIELD.NUMERIC_VALUE), bigDecimal);
                                        default -> throw new IllegalArgumentException("Only LocalDate, Instant, String, Boolean and BigDecimal type is supported when by-value search is requested, but %s specified".formatted(eq.value().getClass().getSimpleName()));
                                    }
                                }
                                case Lt lt -> {
                                    switch (lt.value()) {
                                        case Instant _,
                                             LocalDate _ -> where.and().lt(TC(alias, RECORD_FIELD.DATE_VALUE), lt.value());
                                        default -> throw new IllegalArgumentException("Only Instant or LocalDate type is supported when by-value search is requested, but %s specified".formatted(lt.value().getClass().getSimpleName()));
                                    }
                                }
                                case Between<?> between -> {
                                    Assert.state(between.existingFrom() instanceof Instant || between.existingFrom() instanceof LocalDate, () -> "Only Instant or LocalDate types are supported when by-value search is requested, but %s specified".formatted(between.existingFrom().getClass().getSimpleName()));
                                    String valueColumn = TC(alias, RECORD_FIELD.DATE_VALUE);
                                    where.and().gtEq(valueColumn, between.existingFrom());
                                    if (between.toIsExclusive()) {
                                        where.and().lt(valueColumn, between.existingExclusiveTo());
                                    } else {
                                        where.and().ltEq(valueColumn, between.existingInclusiveTo());
                                    }
                                }
                                case In(Collection<?> collection) -> {
                                    Class<?> itemType = ListUtil.inspectCollectionType(collection);

                                    if (itemType == null) {
                                        throw new IllegalArgumentException("In matcher must have at least one item");
                                    }

                                    if (LocalDate.class.equals(itemType)) { // TODO: instant or date? What column?
                                        where.and().in(TC(alias, RECORD_FIELD.DATE_VALUE), collection);
                                    } else if (Instant.class.equals(itemType)) {
                                        where.and().in(TC(alias, RECORD_FIELD.DATETIME_VALUE), collection);
                                    } else if (String.class.equals(itemType)) {
                                        where.and().in(TC(alias, RECORD_FIELD.TEXT_VALUE), collection);
                                    } else if (Boolean.class.equals(itemType)) {
                                        where.and().in(TC(alias, RECORD_FIELD.BOOLEAN_VALUE), collection);
                                    } else if (BigDecimal.class.equals(itemType)) {
                                        where.and().in(TC(alias, RECORD_FIELD.NUMERIC_VALUE), collection);
                                    } else {
                                        throw new IllegalArgumentException("Only LocalDate, Instant, String, Boolean and BigDecimal type is supported when by-value search is requested, but %s specified".formatted(itemType.getSimpleName()));
                                    }
                                }
                                case IsSuperset(var timespec) -> where.and().rangeContains(TC(alias, RECORD_FIELD.DATETIMERANGE_VALUE), timespec);
                                case Overlaps(var timespec) -> where.and().rangeOverlaps(TC(alias, RECORD_FIELD.DATETIMERANGE_VALUE), timespec);
                                default -> throw new IllegalArgumentException("Unsupported matcher type: " + matcher.getClass().getSimpleName());
                            }
                        }
                        default -> throw new IllegalArgumentException(searchField.subject() + " subject is not supported for databased record search");
                    }

                });
            }
        }

        if (p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD)) {
            sq.where().and().notIn(TC(RECORD.TABLE, RECORD.ID), p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD));
        }

        if (p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS)) {
            sq.where().and().notIn(TC(RECORD.TABLE, RECORD.RECORD_STATUS_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.FOND)) {
            if (!p.hasLength(RecordConstants.SearchParams.FOND)) {
                return false;
            }
            sq.where().and().in(TC(RECORD.TABLE, RECORD.FOND_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.FOND)));
        }

        if (p.isEmptyOrFalse(CoreSearchParams.INCLUDE_DRAFT)) {
            sq.where().and().isNotNull(withoutIndexString(TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID)));
        }

        if (p.hasNotNull(DEPARTMENT)) {
            if (!p.hasLength(DEPARTMENT)) {
                return false;
            }
            if (!departmentAccessor.coversAllDepartments(p.get(DEPARTMENT))) {
                joinWithHoldings = true;
                sq.where().and().in(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.DEPARTMENT_ID), getListOfIds(p.get(DEPARTMENT)));
            }
        }

        if (p.isEmptyOrFalse(RecordConstants.SearchParams.INCLUDE_EXCLUDED)) {
            joinWithHoldings = true;
            sq.where().and().isNull(withoutIndexString(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.DISCARDION_EVENT_ID)));
        }

        if (p.isEmptyOrFalse(CoreSearchParams.INCLUDE_DELETED)) {
            sq.where().and().isNull(withoutIndexString(TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID)));
            joinWithHoldings = true;
            sq.where().and().isNull(withoutIndexString(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.DELETION_EVENT_ID)));
        }

        if (joinWithHoldings) {
            sq.joins().add(RECORD_HOLDING.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.RECORD_ID)));
        }

        return true;
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        return Sorting.ofAsc(TC(RECORD.TABLE, RECORD.SORTING_KEY));
    }
}
