package cz.kpsys.portaro.record.edit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.constraints.RecordConstraintResolver;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.format.ValueFormat;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.value.FieldPayload;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import cz.kpsys.portaro.record.detail.value.FieldValueConverter;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.jspecify.annotations.Nullable;

import java.util.*;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EditableFieldType<V extends FieldValue<?>> implements FieldType<V> {

    public static final Comparator<EditableFieldType<?>> EDITATION_ORDER_COMPARATOR = Comparator.comparing(EditableFieldType::getEditationOrder);

    @NonNull
    FieldType<?> delegate;

    @JsonIgnore
    @Getter
    @NonNull
    Fond effectiveFond;

    @NonNull
    FieldSettings editSetting;

    @NonNull
    Function<FieldType<?>, EditableFieldType<?>> toEditableSubfieldTypeConverter;

    @Nullable
    @NonFinal
    List<EditableFieldType<?>> editableSubfieldTypes = null;

    public EditableFieldType(@NonNull FieldType<V> delegate,
                             @NonNull Fond effectiveFond,
                             @NonNull FieldSettings editSetting,
                             @NonNull Function<FieldType<?>, EditableFieldType<?>> toEditableSubfieldTypeConverter) {
        this.delegate = delegate;
        this.effectiveFond = effectiveFond;
        this.editSetting = editSetting;
        this.toEditableSubfieldTypeConverter = toEditableSubfieldTypeConverter;
    }

    public static <V extends FieldValue<?>> EditableFieldType<V> ofAlwaysVisibleSingle(@NonNull FieldType<V> target, @NonNull Fond effectiveFond) {
        return new EditableFieldType<>(
                target,
                effectiveFond,
                FieldSettings.ofAlwaysVisible(FieldSettings.FIRST_ORDER),
                SubfieldTypeToEditableConverter.createEmpty(effectiveFond)
        );
    }

    public static <V extends FieldValue<?>> EditableFieldType<V> ofHiddenSingle(@NonNull FieldType<V> target, @NonNull Fond effectiveFond) {
        return new EditableFieldType<>(
                target,
                effectiveFond,
                FieldSettings.ofHidden(FieldSettings.FIRST_ORDER),
                SubfieldTypeToEditableConverter.createEmpty(effectiveFond)
        );
    }

    public static <V extends FieldValue<?>> @NonNull EditableFieldType<V> ofUnknown(@NonNull FieldType<V> topfieldType, @NonNull Fond fond) {
        return new EditableFieldType<V>(topfieldType, fond, FieldSettings.unknown(), SubfieldTypeToEditableConverter.createEmpty(fond));
    }

    public String getId() {
        return delegate.getId();
    }

    public Text getText() {
        return delegate.getText();
    }

    public String getName() {
        return delegate.getName();
    }

    public @NonNull String getCode() {
        return delegate.getCode();
    }

    public @NonNull FieldTypeId getFieldTypeId() {
        return delegate.getFieldTypeId();
    }

    public boolean isRepeatable() {
        return delegate.isRepeatable();
    }

    public boolean isAutonomous() {
        return delegate.isAutonomous();
    }

    @JsonIgnore
    @Override
    public Optional<ScalarDatatype> getDatatype() {
        return delegate.getDatatype();
    }

    @JsonIgnore
    @Override
    public Optional<ValueFormat> getValueFormat() {
        return delegate.getValueFormat();
    }

    @JsonIgnore
    @Override
    public @NonNull Optional<Fond> getLinkRootFond() {
        return delegate.getLinkRootFond();
    }

    @JsonIgnore
    public boolean isLinkFieldType() {
        return getLinkRootFond().isPresent();
    }

    @JsonIgnore
    public ScalarDatatype getDatatypeOrThrow() {
        return delegate.getDatatypeOrThrow();
    }

    @JsonIgnore
    public FieldExportSetting getExportSetting() {
        return delegate.getExportSetting();
    }

    @JsonIgnore
    public TransferType getTransferType() {
        return delegate.getTransferType();
    }

    @JsonIgnore
    public boolean isVirtualGroup() {
        return delegate.isVirtualGroup();
    }

    @JsonIgnore
    public boolean isGroup() {
        return delegate.isGroup();
    }

    @JsonIgnore
    public boolean hasAutonomousSubfieldTypes() {
        return delegate.hasAutonomousSubfieldTypes();
    }

    public FieldType<?> addSubfieldType(FieldType<?> subfieldType) {
        return delegate.addSubfieldType(subfieldType);
    }

    @JsonIgnore
    public FieldValueConverter getValueConverter() {
        return delegate.getValueConverter();
    }

    @JsonIgnore
    public @NonNull FieldSource getFieldSource() {
        return delegate.getFieldSource();
    }

    @JsonIgnore
    public @NonNull Set<LookupDefinition> getLookups() {
        return delegate.getLookups();
    }

    @JsonIgnore
    public @NonNull Optional<Formula<?>> getFormula() {
        return editSetting.formula().or(delegate::getFormula);
    }

    @JsonIgnore
    public @NonNull Optional<Codebook<? extends LabeledIdentified<?>, ?>> getCodebook() {
        return delegate.getCodebook();
    }

    @JsonIgnore
    @Override
    public @NonNull FdefGeneration getGeneration() {
        return delegate.getGeneration();
    }

    @JsonIgnore
    @Override
    public @NonNull FieldStorageBehaviour getFieldStorageBehaviour() {
        return delegate.getFieldStorageBehaviour();
    }

    @JsonIgnore
    public @NonNull FieldDisplayType getDisplayType() {
        return editSetting.displayType();
    }

    @JsonIgnore
    public @NonNull FieldRequirementType getRequirementType() {
        return editSetting.requirementType();
    }

    @JsonIgnore
    public boolean isRequired() {
        return FieldRequirementType.REQUIRED.equals(getRequirementType());
    }

    @JsonIgnore
    public @NonNull Integer getEditationOrder() {
        return editSetting.order();
    }

    @JsonIgnore
    public @NonNull Set<RecordConstraintResolver> getLinkConstraints() {
        return editSetting.linkConstraints();
    }

    @Deprecated
    @Override
    public @NonNull EditableFieldType<?> getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull FieldTypeId fieldTypeId) {
        return getSubfieldTypeFor(fieldTypeId);
    }

    @Override
    public @NonNull EditableFieldType<?> getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull String subfieldCode) {
        FieldType<?> target = delegate.getSubfieldTypeOrParentVirtualGroupTypeFor(subfieldCode);
        return toEditableSubfieldTypeConverter.apply(target);
    }

    @Override
    public @NonNull EditableFieldType<?> getSubfieldTypeFor(@NonNull FieldTypeId fieldTypeId) {
        FieldType<?> target = delegate.getSubfieldTypeFor(fieldTypeId);
        return toEditableSubfieldTypeConverter.apply(target);
    }

    @Override
    public @NonNull List<EditableFieldType<?>> getSubfieldTypes() {
        if (editableSubfieldTypes == null) {
            editableSubfieldTypes = delegate.getSubfieldTypes().stream()
                    .map(toEditableSubfieldTypeConverter)
                    .sorted(EDITATION_ORDER_COMPARATOR)
                    .toList();
        }
        return editableSubfieldTypes;
    }

    public <VH extends ScalarFieldValue<?>> Field<VH> createEmptyField(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldId fieldId, @NonNull UUID id) {
        return Field.empty(id, recordIdFondPair, RecordFieldId.of(recordIdFondPair, fieldId), this);
    }

    public <VH extends ScalarFieldValue<?>> Field<VH> createField(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldId fieldId, @NonNull UUID id, @NonNull FieldPayload<VH> payload) {
        Field<VH> field = createEmptyField(recordIdFondPair, fieldId, id);
        field.setPayload(payload);
        return field;
    }

    public <VH extends ScalarFieldValue<?>> Field<VH> createEmptyFieldByParentId(@NonNull RecordIdFondPair recordIdFondPair, @Nullable FieldId parentFieldId, @NonNull UUID id) {
        FieldId fieldId = parentFieldId != null
                ? parentFieldId.sub(getCode(), FieldId.FIRST_FIELD_REPETITION)
                : FieldId.top(getCode(), FieldId.FIRST_FIELD_REPETITION);
        return createEmptyField(recordIdFondPair, fieldId, id);
    }

    public <VH extends ScalarFieldValue<?>> Field<VH> createFieldByParentId(@NonNull RecordIdFondPair recordIdFondPair, @Nullable FieldId parentFieldId, @NonNull UUID id, @NonNull FieldPayload<VH> payload) {
        Field<VH> field = createEmptyFieldByParentId(recordIdFondPair, parentFieldId, id);
        field.setPayload(payload);
        return field;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof FieldType<?> that)) {
            return false;
        }
        return delegate.equals(that);
    }

    @Override
    public int hashCode() {
        return delegate.hashCode();
    }

    @Override
    public String toString() {
        return delegate.toString();
    }

    @Override
    public @NullableNotBlank String pic() {
        return delegate.pic();
    }

}
