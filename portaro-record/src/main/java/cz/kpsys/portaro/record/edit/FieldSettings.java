package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.object.NonNullOrderedRecord;
import cz.kpsys.portaro.record.detail.constraints.RecordConstraintResolver;
import cz.kpsys.portaro.record.detail.fn.Formula;
import lombok.NonNull;
import lombok.With;

import java.util.Optional;
import java.util.Set;

import static cz.kpsys.portaro.record.edit.FieldDisplayType.*;
import static cz.kpsys.portaro.record.edit.FieldRequirementType.INHERIT;

public record FieldSettings(

        @NonNull
        OriginTracker<FieldDisplayType, FieldSettingId> trackedDisplayType,

        @With
        @NonNull
        OriginTracker<FieldRequirementType, FieldSettingId> trackedRequirementType,

        @NonNull
        OriginTracker<Integer, FieldSettingId> trackedOrder,

        @NonNull
        OriginTracker<Set<RecordConstraintResolver>, FieldSettingId> trackedLinkConstraints,

        @NonNull
        OriginTracker<Optional<Formula<?>>, FieldSettingId> trackedFormula

) implements NonNullOrderedRecord {

    public static final Integer FIRST_ORDER = 1;
    public static final Integer IND_1_ORDER = 10001;
    public static final Integer IND_2_ORDER = 10002;
    public static final Set<RecordConstraintResolver> NO_CONSTRAINTS = Set.of();

    public FieldDisplayType displayType() {
        return trackedDisplayType.value();
    }

    public FieldRequirementType requirementType() {
        return trackedRequirementType.value();
    }

    public @NonNull Integer order() {
        return trackedOrder.value();
    }

    public Set<RecordConstraintResolver> linkConstraints() {
        return trackedLinkConstraints.value();
    }

    public Optional<Formula<?>> formula() {
        return trackedFormula.value();
    }


    public FieldSettings withRequirementType(FieldRequirementType fieldRequirementType) {
        return new FieldSettings(
                trackedDisplayType(),
                new OriginTracker<>(fieldRequirementType, trackedRequirementType.origin()),
                trackedOrder(),
                trackedLinkConstraints(),
                trackedFormula()
        );
    }

    public static FieldSettings unknown() {
        return new FieldSettings(OriginTracker.init(DISABLED), OriginTracker.init(INHERIT), OriginTracker.init(LATEST), OriginTracker.init(NO_CONSTRAINTS), OriginTracker.init(Optional.empty()));
    }

    public static @NonNull FieldSettings ofAlwaysVisible(int order) {
        return new FieldSettings(OriginTracker.init(ALWAYS), OriginTracker.init(INHERIT), OriginTracker.init(order), OriginTracker.init(NO_CONSTRAINTS), OriginTracker.init(Optional.empty()));
    }

    public static @NonNull FieldSettings ofWhenFilled(int order) {
        return new FieldSettings(OriginTracker.init(WHEN_FILLED), OriginTracker.init(INHERIT), OriginTracker.init(order), OriginTracker.init(NO_CONSTRAINTS), OriginTracker.init(Optional.empty()));
    }

    public static @NonNull FieldSettings ofDisabled(int order) {
        return new FieldSettings(OriginTracker.init(DISABLED), OriginTracker.init(INHERIT), OriginTracker.init(order), OriginTracker.init(NO_CONSTRAINTS), OriginTracker.init(Optional.empty()));
    }

    public static @NonNull FieldSettings ofHidden(int order) {
        return new FieldSettings(OriginTracker.init(HIDDEN), OriginTracker.init(INHERIT), OriginTracker.init(order), OriginTracker.init(NO_CONSTRAINTS), OriginTracker.init(Optional.empty()));
    }

}
