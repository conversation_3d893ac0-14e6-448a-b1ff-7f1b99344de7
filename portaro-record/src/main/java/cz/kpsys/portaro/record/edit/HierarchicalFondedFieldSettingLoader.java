package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.concurrent.ReentrantLockLocker;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.InheritanceLoader;
import cz.kpsys.portaro.record.RecordWellKnownFields;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.PrefabCodebookDelegatingFieldTypeIdByConfigPathLoader;
import cz.kpsys.portaro.record.detail.RecordSequenceValueProvider;
import cz.kpsys.portaro.record.detail.constraints.*;
import cz.kpsys.portaro.record.detail.fn.Constant;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.fn.Formulas;
import cz.kpsys.portaro.record.detail.fn.Ref;
import cz.kpsys.portaro.record.detail.value.BooleanFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.setting.FondedValues;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static cz.kpsys.portaro.record.detail.FieldTypes.*;
import static cz.kpsys.portaro.record.detail.fn.Formulas.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class HierarchicalFondedFieldSettingLoader implements FondedFieldSettingLoader, CacheCleaner {

    @NonNull SpringDbFondedFieldTypeEntityByFondLoader editableFieldTypeDtoByFondLoader;
    @NonNull PrefabCodebookDelegatingFieldTypeIdByConfigPathLoader fieldTypeIdByConfigPathLoader;
    @NonNull InheritanceLoader<Fond> enabledFondInheritanceLoader;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull Provider<@NonNull String> recordCodeLabelingFormatProvider;
    @NonNull Provider<@NonNull Optional<String>> libraryCodeLabelingProvider;
    @NonNull ContextualProvider<Department, FondedValues<@NonNull Boolean>> generate001FondedValuesProvider;
    @NonNull ContextualProvider<Department, FondedValues<@NonNull Boolean>> generate003FondedValuesProvider;
    @NonNull Provider<Department> rootDepartmentProvider;
    @NonNull ContextualProvider<@NonNull String, @NonNull Long> recordSequenceProvider;
    @NonNull Provider<@NonNull String> rootSerialCodeProvider;


    @NonFinal boolean cacheValid;
    @NonNull ReentrantLockLocker locker = new ReentrantLockLocker();
    @NonNull Map<FieldSettingId, FieldSettings> effectifeFondedCache = new ConcurrentHashMap<>();

    public void loadCaches() {
        locker.lock(() -> {
            if (cacheValid) {
                return;
            }

            List<FondedFieldTypeEntity> entities = editableFieldTypeDtoByFondLoader.loadAll();
            List<Fond> hierarchicallySortedFonds = enabledFondInheritanceLoader.getAllHierarchicallySorted();
            for (Fond effectiveFond : hierarchicallySortedFonds) {
                List<FondedFieldTypeEntity> typesForFond = entities.stream().filter(entity -> entity.styleId().equals(effectiveFond.getId())).toList();
                for (FondedFieldTypeEntity type : typesForFond) {
                    FieldTypeId realTypeId = getRealTypeIdFromConfigPath(type);
                    if (realTypeId == null) {
                        continue;
                    }
                    Fond entityFond = fondLoader.getById(type.styleId());
                    createSettingsForThisAndAllChildren(effectiveFond, entityFond, type, realTypeId);
                }
            }

            // Set required fields to their parents (of the same fond) recursively
            for (Map.Entry<FieldSettingId, FieldSettings> identifiedSetting : effectifeFondedCache.entrySet()) {
                if (identifiedSetting.getValue().requirementType() != FieldRequirementType.REQUIRED) {
                    continue;
                }
                FieldSettingId settingId = identifiedSetting.getKey();
                FieldTypeId requiredFieldTypeId = settingId.fieldTypeId();
                while ((requiredFieldTypeId = requiredFieldTypeId.getParent()) != null) {
                    effectifeFondedCache.computeIfPresent(new FieldSettingId(settingId.fond(), requiredFieldTypeId), (_, setting) -> setting.withRequirementType(FieldRequirementType.REQUIRED));
                }
            }

            cacheValid = true;
        });
    }

    private void createSettingsForThisAndAllChildren(Fond effectiveFond, Fond entityFond, FondedFieldTypeEntity entity, FieldTypeId realTypeId) {
        List<Fond> topToBottomFields = enabledFondInheritanceLoader.getThisAndChildren(effectiveFond);
        for (Fond nodeFond : topToBottomFields) {
            FieldSettingId originalFieldSettingId = new FieldSettingId(entityFond, realTypeId);
            FieldSettings fieldSettings = new FieldSettings(
                    new OriginTracker<>(entity.fieldDisplayType(), originalFieldSettingId),
                    new OriginTracker<>(getRequirementType(entity, realTypeId, nodeFond), originalFieldSettingId),
                    new OriginTracker<>(entity.order(), originalFieldSettingId),
                    new OriginTracker<>(getLinkConstraintDefs(realTypeId, entity), originalFieldSettingId),
                    new OriginTracker<>(getFondedFormula(realTypeId, nodeFond).map(Formulas::cache), originalFieldSettingId)
            );
            effectifeFondedCache.merge(new FieldSettingId(nodeFond, realTypeId), fieldSettings, this::createBestCombination);
        }
    }

    @Override
    public void clearCache() {
        locker.lock(() -> {
            cacheValid = false;
            effectifeFondedCache.clear();
        });
    }

    @Override
    public Map<Fond, FieldSettings> getEffectiveFondedByFieldTypeId(@NonNull FieldTypeId fieldTypeId) {
        loadCaches();

        Map<Fond, FieldSettings> settingsAccumulator = new HashMap<>();

        for (Map.Entry<FieldSettingId, FieldSettings> identifiedSetting : effectifeFondedCache.entrySet()) {
            if (identifiedSetting.getKey().fieldTypeId().equals(fieldTypeId)) {
                settingsAccumulator.put(identifiedSetting.getKey().fond(), identifiedSetting.getValue());
            }
        }

        return settingsAccumulator;
    }

    @Override
    public Map<FieldTypeId, FieldSettings> getFieldTypedByEffectiveFond(@NonNull Fond fond) {
        loadCaches();

        Map<FieldTypeId, FieldSettings> settingsAccumulator = new HashMap<>();

        for (Map.Entry<FieldSettingId, FieldSettings> identifiedSetting : effectifeFondedCache.entrySet()) {
            if (identifiedSetting.getKey().fond().equals(fond)) {
                settingsAccumulator.put(identifiedSetting.getKey().fieldTypeId(), identifiedSetting.getValue());
            }
        }

        return settingsAccumulator;
    }

    private @Nullable FieldTypeId getRealTypeIdFromConfigPath(@NonNull FondedFieldTypeEntity entity) {
        Optional<FieldTypeId> fieldTypeId = fieldTypeIdByConfigPathLoader.findByConfigPath(entity.configPath());
        if (fieldTypeId.isEmpty()) {
            log.warn("Field type defined in styly not found in fdef[aut] by config path {}", entity.configPath());
            return null;
        }
        return fieldTypeId.get();
    }

    private @NonNull FieldRequirementType getRequirementType(FondedFieldTypeEntity entity, FieldTypeId realTypeId, Fond nodeFond) {
        return switch (entity.fieldRequirementType()) {
            case REQUIRED -> FieldRequirementType.REQUIRED;
            case INHERIT -> {
                Optional<FieldTypeId> entryNativeSubfieldOpt = recordEntryFieldTypeIdResolver.getEntryNativeSubfieldOpt(nodeFond);
                if (entryNativeSubfieldOpt.isPresent() && realTypeId.equals(entryNativeSubfieldOpt.get())) {
                    yield FieldRequirementType.REQUIRED;
                }
                yield FieldRequirementType.INHERIT;
            }
        };
    }

    private Set<RecordConstraintDef> getLinkConstraintDefs(FieldTypeId realTypeId, FondedFieldTypeEntity entity) {
        Set<RecordConstraintDef> linkConstraints = FieldSettings.NO_CONSTRAINTS;

        // PRO test.kpsys.cz
        if (false && realTypeId.value().equals("d2025.main.a")) {
            linkConstraints = Set.of(LinkedRecordAsRelatedRecordConstraintDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d1001.main")));
        }
        if (realTypeId.value().equals("d2054.main.a")) {
            linkConstraints = Set.of(
                    LinkingRecordsAsRelatedRecordsConstraintDef.throwWhenMissing(
                            fondLoader.getById(87), // personWorkCatalogLinkFond (Vazby činností osob)
                            FieldTypeId.parse("d8720.main"),
                            ThisFieldLinkDef.create(LinkedRecordAsRelatedRecordConstraintDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d2050.main"))),
                            FieldTypeId.parse("d8710.main")
                    )
            );
        }
        if (realTypeId.value().equals("d2040.main.a")) {
            linkConstraints = Set.of(
                    LinkingRecordsAsRelatedRecordsConstraintDef.throwWhenMissing(
                            fondLoader.getById(87), // personWorkCatalogLinkFond (Vazby činností osob)
                            FieldTypeId.parse("d8720.main"),
                            ThisFieldLinkDef.create(LinkedRecordAsRelatedRecordConstraintDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d2050.main"))),
                            FieldTypeId.parse("d8710.main")
                    )
            );
        }
        if (realTypeId.value().equals("d2064.main.a")) {
            linkConstraints = Set.of(
                    LinkingRecordsAsRelatedRecordsConstraintDef.throwWhenMissing(
                            fondLoader.getById(86), // propertyWorkCatalogLinkFond (Vazby činností strojů)
                            FieldTypeId.parse("d8721.a"),
                            ThisFieldLinkDef.create(LinkedRecordAsRelatedRecordConstraintDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d2060.a"))),
                            FieldTypeId.parse("d8711.main")
                    )
            );
        }
        if (realTypeId.value().equals("d2045.main.a")) {
            linkConstraints = Set.of(
                    LinkingRecordsAsRelatedRecordsConstraintDef.throwWhenMissing(
                            fondLoader.getById(86), // propertyWorkCatalogLinkFond (Vazby činností strojů)
                            FieldTypeId.parse("d8721.a"),
                            ThisFieldLinkDef.create(LinkedRecordAsRelatedRecordConstraintDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d2060.a"))),
                            FieldTypeId.parse("d8711.main")
                    )
            );
        }
        if (realTypeId.value().equals("d1608.main.a")) {
            linkConstraints = Set.of(LinkedRecordAsRelatedRecordConstraintDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d1609.a")));
        }

        /// TODO Chceme filtrovat posle RID linkovaneho zaznamu, tzn realne ulozeno v 1609, ale mame přelinkovano v 1607
//        if (realTypeId.value().equals("d1610.main.a") || realTypeId.value().equals("d1620.main.a")) {
//            linkConstraints = Set.of(LinkedRecordAsRelatedRecordConstraintDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d1607.a")));
//        }

//        if (realTypeId.value().equals("d1610.main.a") || realTypeId.value().equals("d1620.main.a")) {
//            linkConstraints = Set.of(
//                    LinkingRecordsAsRelatedRecordsConstraintDef.throwWhenMissing(
//                            fondLoader.getById(79),
//                            FieldTypeId.parse("d1609.a"),
//                            ThisFieldLinkDef.create(LinkedRecordAsRelatedRecordConstraintDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d1002.main"))),
//                            FieldTypeId.parse("d1607.a")
//                    )
//            );
//        }

//        if (realTypeId.value().equals("d2051.a")) {
//            // mzdové položky
//            linkConstraints = Set.of(
//                    LinkedRecordAsRelatedRecordConstraintDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d2050.main")),
//                    SimpleParameterConstraintDef.create(new Conjunction<>(
//                            // Toto musime bohuzel delat kvuli lucenu, ale chceme v podstate d1838_o <= datum AND (d1838_o >= datum OR d1838_o is null). Kvuli lucenu to ale musime spreadnout na konjunkci disjunkci
//                            new Disjunction<>(
//                                    new Term<>(FieldTypedSearchFieldParsing.ofValue(FieldTypeId.parse("d1838.n")).toSearchField(), new LtEq(LocalDate.now())),
//                                    new Term<>(FieldTypedSearchFieldParsing.ofValue(FieldTypeId.parse("d1838.o")).toSearchField(), new GtEq(LocalDate.now()))
//                            ),
//                            new Disjunction<>(
//                                    new Term<>(FieldTypedSearchFieldParsing.ofValue(FieldTypeId.parse("d1838.n")).toSearchField(), new LtEq(LocalDate.now())),
//                                    new Term<>(FieldTypedSearchFieldParsing.ofValue(FieldTypeId.parse("d1838.o")).toSearchField(), new IsNull())
//                            )
//                    ))
//            );
//        }
        return linkConstraints;
    }

    private FieldSettings createBestCombination(FieldSettings existingFieldSettings, FieldSettings newFieldSettings) {
        OriginTracker<FieldDisplayType, FieldSettingId> newDisplayType = combineDisplayType(existingFieldSettings, newFieldSettings);
        OriginTracker<FieldRequirementType, FieldSettingId> newRequirementType = combineRequirementType(existingFieldSettings, newFieldSettings);
        OriginTracker<Integer, FieldSettingId> newOrder = combineOrder(existingFieldSettings, newFieldSettings);
        OriginTracker<Set<RecordConstraintDef>, FieldSettingId> newConstraints = combineLinkConstraints(existingFieldSettings, newFieldSettings);
        OriginTracker<Optional<Formula<?>>, FieldSettingId> newFormula = newFieldSettings.trackedFormula(); // most fond specific formula should be selected.
        return new FieldSettings(newDisplayType, newRequirementType, newOrder, newConstraints, newFormula);
    }

    private @NonNull Optional<Formula<?>> getFondedFormula(FieldTypeId typeId, Fond nodeFond) {

        FondedValues<@NonNull Boolean> generate001FondedValues = this.generate001FondedValuesProvider.getOn(rootDepartmentProvider.get());
        boolean generationOf001Enabled = generate001FondedValues.findFor(nodeFond.getId()).orElse(false);
        if (generationOf001Enabled && typeId.equals(RecordWellKnownFields.DocumentControlNumber.TYPE_ID)) {
            return Optional.of(replace(replace(replace(Constant.ofString(recordCodeLabelingFormatProvider), "%UUID%", localRef(RECORD_ID_FIELD_CODE)), "%NN%", localRef(DOCUMENT_KINDED_ID_FIELD_CODE)),
                    "%FF%",
                    format("%02.0f", idOf(localRef(FOND_FIELD_CODE)))));
        }


        if (generationOf001Enabled && typeId.equals(RecordWellKnownFields.AuthorityControlNumber.TYPE_ID)) {
            return Optional.of(replace(replace(replace(Constant.ofString(recordCodeLabelingFormatProvider), "%UUID%", localRef(RECORD_ID_FIELD_CODE)), "%NN%", localRef(AUTHORITY_KINDED_ID_FIELD_CODE)),
                    "%FF%",
                    format("%02.0f", idOf(localRef(FOND_FIELD_CODE)))));
        }


        FondedValues<@NonNull Boolean> generate003FondedValues = this.generate003FondedValuesProvider.getOn(rootDepartmentProvider.get());
        boolean generationOf003Enabled = generate003FondedValues.findFor(nodeFond.getId()).orElse(false);
        if (generationOf003Enabled && typeId.equals(RecordWellKnownFields.DocumentControlNumberIdentifier.TYPE_ID) && libraryCodeLabelingProvider.get().isPresent()) {
            return libraryCodeLabelingProvider.get().map(Constant::ofString);
        }

        if (generationOf003Enabled && typeId.equals(RecordWellKnownFields.AuthorityControlNumberIdentifier.TYPE_ID) && libraryCodeLabelingProvider.get().isPresent()) {
            return libraryCodeLabelingProvider.get().map(Constant::ofString);
        }

        if (rootSerialCodeProvider.get().equals("100007000310") || rootSerialCodeProvider.get().equals("100007001019")) { //PROD + TEST

            // TODO je potřeba vyřešit to že kdyby se tady dalo místo fond 4 (Zakázky smluvní) nebo 5 (Zakázky vykazované) dalo 1 (Zakázky) tak aby to fungovalo
            if ((nodeFond.getId().equals(4) || nodeFond.getId().equals(5)) && typeId.value().equals("d245.z")) {
                String sequenceName = "zakazky_2025";

                return Optional.of(format("%s%.1sZ%s%s",
                        format("%s", dateFormat("yy", constLocalDate(LocalDate::now))),
                        localRef("d1106.t"),
                        ref("d1012.a", "d1221.a"),
                        format("%05.0f", constLong(RecordSequenceValueProvider.of(recordSequenceProvider, sequenceName)))));
            }

            if ((nodeFond.getId().equals(21) || nodeFond.getId().equals(22)) && typeId.value().equals("d2071.c")) { // Vykazana cena
                /// Stroj
                var mnozstviStrojNaklad = round2(orZero(multiply(
                        ref("d2064.main", "d2221.c"),
                        localRef("d2070.o") // pocet jednotek vykazanych stroje
                )));

                ///  Stroje Víkend
                var mnozstviStrojNakladVikend = round2(orZero(multiply(
                        ref("d2064.main", "d2221.e"),
                        localRef("d2070.o") // pocet hodin vykazanych stroje
                )));

                ///  Stroje Svátek
                var mnozstviStrojNakladSvatek = round2(orZero(multiply(
                        ref("d2064.main", "d2221.f"),
                        localRef("d2070.o") // pocet hodin vykazanych stroje
                )));

                /// Pracovní den
                var mnozstviOsobaNaklad = round2(orZero(multiply(
                        ref("d2054.main", "d2221.c"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNaklad = round2(orZero(multiply(
                        ref("d2054.main", "d2221.d"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Víkend
                var mnozstviOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2054.main", "d2221.e"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2054.main", "d2221.e"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek
                var mnozstviOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2054.main", "d2221.f"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2054.main", "d2221.f"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                return Optional.of(match(
                        whenCase(ref("d2030.d", "d2116.o"), sum(mnozstviOsobaNakladSvatek, prescasOsobaNakladSvatek, mnozstviStrojNakladSvatek)),
                        whenCase(ref("d2030.d", "d2115.o"), sum(mnozstviOsobaNakladVikend, prescasOsobaNakladVikend, mnozstviStrojNakladVikend)),
                        defaultCase(sum(mnozstviOsobaNaklad, prescasOsobaNaklad, mnozstviStrojNaklad))
                ));
            }

            /// VYPOCET nakladu a jejich vykazane nakladove ceny
            if ((nodeFond.getId().equals(21) || nodeFond.getId().equals(22) || nodeFond.getId().equals(24) || nodeFond.getId().equals(39)) && typeId.value().equals("d2081.c")) { // Vykazana cena
                Ref<BooleanFieldValue> jeSvatek = ref("d2030.d", "d2116.o");
                Ref<BooleanFieldValue> jeVikend = ref("d2030.d", "d2115.o");

                /// Stroj
                var mnozstviStrojProdej = round2(orZero(multiply(
                        ref("d2045.main", "d2221.c"),
                        localRef("d2080.o") // pocet jednotek uctovanych stroje
                )));

                ///  Stroje Víkend
                var mnozstviStrojProdejVikend = round2(orZero(multiply(
                        ref("d2045.main", "d2221.e"),
                        localRef("d2080.o") // pocet hodin vykazanych stroje
                )));

                ///  Stroje Svátek
                var mnozstviStrojProdejSvatek = round2(orZero(multiply(
                        ref("d2045.main", "d2221.f"),
                        localRef("d2080.o") // pocet hodin vykazanych stroje
                )));

                /// Pracovní den
                var mnozstviOsobaProdej = round2(orZero(multiply(
                        ref("d2040.main", "d2221.c"),
                        localRef("d2080.m") // pocet hodin uctovanych
                )));
                var prescasOsobaProdej = round2(orZero(multiply(
                        ref("d2040.main", "d2221.d"),
                        localRef("d2080.n") // pocet hodin uctovanych prescasovych
                )));

                /// Víkend
                var mnozstviOsobaProdejVikend = round2(orZero(multiply(
                        ref("d2040.main", "d2221.e"),
                        localRef("d2080.m") // pocet hodin uctovanych
                )));
                var prescasOsobaProdejVikend = round2(orZero(multiply(
                        ref("d2040.main", "d2221.e"),
                        localRef("d2080.n") // pocet hodin uctovanych prescasovych
                )));

                /// Svátek
                var mnozstviOsobaProdejSvatek = round2(orZero(multiply(
                        ref("d2040.main", "d2221.f"),
                        localRef("d2080.m") // pocet hodin uctovanych
                )));
                var prescasOsobaProdejSvatek = round2(orZero(multiply(
                        ref("d2040.main", "d2221.f"),
                        localRef("d2080.n") // pocet hodin uctovanych prescasovych
                )));

                return Optional.of(match(
                        whenCase(jeSvatek, sum(mnozstviOsobaProdejSvatek, prescasOsobaProdejSvatek, mnozstviStrojProdejSvatek)),
                        whenCase(jeVikend, sum(mnozstviOsobaProdejVikend, prescasOsobaProdejVikend, mnozstviStrojProdejVikend)),
                        defaultCase(sum(mnozstviOsobaProdej, prescasOsobaProdej, mnozstviStrojProdej))
                ));
            }

            /// Výkaz stroje s obsluhou
            if (nodeFond.getId().equals(20) && typeId.value().equals("d2071.c")) { // Vykazana cena

                Ref<BooleanFieldValue> jeSvatek = ref("d2030.d", "d2116.o");
                Ref<BooleanFieldValue> jeVikend = ref("d2030.d", "d2115.o");

                /// Stroj
                var mnozstviStrojNaklad = round2(orZero(multiply(
                        ref("d2064.main", "d2221.c"),
                        localRef("d2070.o") // pocet jednotek vykazanych stroje
                )));

                ///  Stroje Víkend
                var mnozstviStrojNakladVikend = round2(orZero(multiply(
                        ref("d2064.main", "d2221.e"),
                        localRef("d2070.o") // pocet hodin vykazanych
                )));

                ///  Stroje Svátek
                var mnozstviStrojNakladSvatek = round2(orZero(multiply(
                        ref("d2064.main", "d2221.f"),
                        localRef("d2070.o") // pocet hodin vykazanych
                )));

                /// Pracovní den
                var mnozstviOsobaNaklad = round2(orZero(multiply(
                        ref("d2054.main", "d2221.c"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNaklad = round2(orZero(multiply(
                        ref("d2054.main", "d2221.d"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Víkend
                var mnozstviOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2054.main", "d2221.e"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2054.main", "d2221.e"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek
                var mnozstviOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2054.main", "d2221.f"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2054.main", "d2221.f"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));


                ///  Stroje s obsluhou Extra a Extra
                /// Pracovní den - Extra a Extra
                var mnozstviPracovnikNakladExtra = round2(orZero(multiply(
                        max(ref("d2054.main", "d2221.c"), ref("d2064.main", "d2221.c")),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasPracovnikNakladExtra = round2(orZero(multiply(
                        max(ref("d2054.main", "d2221.d"),  ref("d2064.main", "d2221.d")),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Víkend - Extra a Extra
                var mnozstviPracovnikNakladVikendExtra = round2(orZero(multiply(
                        max(ref("d2054.main", "d2221.e"), ref("d2064.main", "d2221.e")),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasPracovnikNakladVikendExtra = round2(orZero(multiply(
                        max(ref("d2054.main", "d2221.e"), ref("d2064.main", "d2221.e")),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek - Extra a Extra
                var mnozstviPracovnikNakladSvatekExtra = round2(orZero(multiply(
                        max(ref("d2054.main", "d2221.f"), ref("d2064.main", "d2221.f")),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasPracovnikNakladSvatekExtra = round2(orZero(multiply(
                        max(ref("d2054.main", "d2221.f"), ref("d2064.main", "d2221.f")),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                var sameUnits = equal(localRef("d2054.main.j"), localRef("d2064.main.j"));

                var workerIsExtraActivity = equal(idOf(ref("d2054.main", "d2205.t")), constLong(() -> 34L));
                var machineIsExtraActivity = equal(idOf(ref("d2064.main", "d2205.t")), constLong(() -> 34L));

                var noneAreExtraActivity = and(
                        not(workerIsExtraActivity),
                        not(machineIsExtraActivity)
                );

                var onlyWorkerIsExtraActivity = and(workerIsExtraActivity, not(machineIsExtraActivity));
                var onlyMachineIsExtraActivity = and(not(workerIsExtraActivity), machineIsExtraActivity);

                var bothAreExtraActivity = and(
                        workerIsExtraActivity,
                        machineIsExtraActivity
                );

                // NP*tP + NS*tS
                var defaultCalculation = match(
                        whenCase(jeSvatek, sum(mnozstviOsobaNakladSvatek, prescasOsobaNakladSvatek, mnozstviStrojNakladSvatek)),
                        whenCase(jeVikend, sum(mnozstviOsobaNakladVikend, prescasOsobaNakladVikend, mnozstviStrojNakladVikend)),
                        defaultCase(sum(mnozstviOsobaNaklad, prescasOsobaNaklad, mnozstviStrojNaklad))
                );

                // NS*tS
                // Pokud je pouze ceník u stroje extra
                var onlyMachineIsExtraActivityCalculation = match(
                        whenCase(jeSvatek, sum(mnozstviOsobaNakladSvatek)),
                        whenCase(jeVikend, sum(mnozstviStrojNakladVikend)),
                        defaultCase(sum(mnozstviStrojNaklad))
                );

                // >NC*tP
                // Pokud jsou oba ceníky extra bere se ten dražší a množství se počítá pouze z pracovníka
                var bothAreExtraActivityCalculation =  match(
                        whenCase(jeSvatek, sum(mnozstviPracovnikNakladSvatekExtra, prescasPracovnikNakladSvatekExtra)),
                        whenCase(jeVikend, sum(mnozstviPracovnikNakladVikendExtra, prescasPracovnikNakladVikendExtra)),
                        defaultCase(sum(mnozstviPracovnikNakladExtra, prescasPracovnikNakladExtra))
                );

                /*
                V případě že se jednotky schodují
                +-------------------+---------------+---------------------+
                | Pracovník         | Stroj         | tP = tS              |
                +===================+===============+=====================+
                | Běžná cena        | Běžná cena    | NP*tP + NS*tS        |
                +-------------------+---------------+---------------------+
                | Extra cena        | Extra cena    | >NC*tS               |
                +-------------------+---------------+---------------------+
                | Běžná cena        | Extra cena    | NS*tS                |
                +-------------------+---------------+---------------------+
                | Extra cena        | Běžná cena    | NP*tP + NS*tS        |
                +-------------------+---------------+---------------------+
                */
                var extraActivityMatch = match(
                        whenCase(noneAreExtraActivity, defaultCalculation),
                        whenCase(bothAreExtraActivity, bothAreExtraActivityCalculation),
                        whenCase(onlyMachineIsExtraActivity, onlyMachineIsExtraActivityCalculation),
                        whenCase(onlyWorkerIsExtraActivity, defaultCalculation),
                        defaultCase(defaultCalculation)
                );

                return Optional.of(conditional(sameUnits, extraActivityMatch, defaultCalculation));
            }

            if (nodeFond.getId().equals(20) && typeId.value().equals("d2081.c")) { // Vykazana cena

                Ref<BooleanFieldValue> jeSvatek = ref("d2030.d", "d2116.o");
                Ref<BooleanFieldValue> jeVikend = ref("d2030.d", "d2115.o");

                /// Stroj
                var mnozstviStrojNaklad = round2(orZero(multiply(
                        ref("d2045.main", "d2221.c"),
                        localRef("d2080.o") // pocet jednotek vykazanych stroje
                )));

                ///  Stroje Víkend
                var mnozstviStrojNakladVikend = round2(orZero(multiply(
                        ref("d2045.main", "d2221.e"),
                        localRef("d2080.o") // pocet hodin vykazanych
                )));

                ///  Stroje Svátek
                var mnozstviStrojNakladSvatek = round2(orZero(multiply(
                        ref("d2045.main", "d2221.f"),
                        localRef("d2080.o") // pocet hodin vykazanych
                )));

                /// Pracovní den
                var mnozstviOsobaNaklad = round2(orZero(multiply(
                        ref("d2040.main", "d2221.c"),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNaklad = round2(orZero(multiply(
                        ref("d2040.main", "d2221.d"),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                /// Víkend
                var mnozstviOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2040.main", "d2221.e"),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2040.main", "d2221.e"),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek
                var mnozstviOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2040.main", "d2221.f"),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2040.main", "d2221.f"),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));


                ///  Stroje s obsluhou Extra a Extra
                /// Pracovní den - Extra a Extra
                var mnozstviPracovnikNakladExtra = round2(orZero(multiply(
                        max(ref("d2040.main", "d2221.c"), ref("d2045.main", "d2221.c")),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasPracovnikNakladExtra = round2(orZero(multiply(
                        max(ref("d2040.main", "d2221.d"),  ref("d2045.main", "d2221.d")),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                /// Víkend - Extra a Extra
                var mnozstviPracovnikNakladVikendExtra = round2(orZero(multiply(
                        max(ref("d2040.main", "d2221.e"), ref("d2045.main", "d2221.e")),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasPracovnikNakladVikendExtra = round2(orZero(multiply(
                        max(ref("d2040.main", "d2221.e"), ref("d2045.main", "d2221.e")),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek - Extra a Extra
                var mnozstviPracovnikNakladSvatekExtra = round2(orZero(multiply(
                        max(ref("d2040.main", "d2221.f"), ref("d2045.main", "d2221.f")),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasPracovnikNakladSvatekExtra = round2(orZero(multiply(
                        max(ref("d2040.main", "d2221.f"), ref("d2045.main", "d2221.f")),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                var sameUnits = equal(localRef("d2040.main.j"), localRef("d2045.main.j"));

                var workerIsExtraActivity = equal(idOf(ref("d2040.main", "d2205.t")), constLong(() -> 34L));
                var machineIsExtraActivity = equal(idOf(ref("d2045.main", "d2205.t")), constLong(() -> 34L));

                var noneAreExtraActivity = and(
                        not(workerIsExtraActivity),
                        not(machineIsExtraActivity)
                );

                var onlyWorkerIsExtraActivity = and(workerIsExtraActivity, not(machineIsExtraActivity));
                var onlyMachineIsExtraActivity = and(not(workerIsExtraActivity), machineIsExtraActivity);

                var bothAreExtraActivity = and(
                        workerIsExtraActivity,
                        machineIsExtraActivity
                );

                // NP*tP + NS*tS
                var defaultCalculation = match(
                        whenCase(jeSvatek, sum(mnozstviOsobaNakladSvatek, prescasOsobaNakladSvatek, mnozstviStrojNakladSvatek)),
                        whenCase(jeVikend, sum(mnozstviOsobaNakladVikend, prescasOsobaNakladVikend, mnozstviStrojNakladVikend)),
                        defaultCase(sum(mnozstviOsobaNaklad, prescasOsobaNaklad, mnozstviStrojNaklad))
                );

                // NS*tS
                // Pokud je pouze ceník u stroje extra
                var onlyMachineIsExtraActivityCalculation = match(
                        whenCase(jeSvatek, sum(mnozstviOsobaNakladSvatek)),
                        whenCase(jeVikend, sum(mnozstviStrojNakladVikend)),
                        defaultCase(sum(mnozstviStrojNaklad))
                );

                // >NC*tP
                // Pokud jsou oba ceníky extra bere se ten dražší a množství se počítá pouze z pracovníka
                var bothAreExtraActivityCalculation =  match(
                        whenCase(jeSvatek, sum(mnozstviPracovnikNakladSvatekExtra, prescasPracovnikNakladSvatekExtra)),
                        whenCase(jeVikend, sum(mnozstviPracovnikNakladVikendExtra, prescasPracovnikNakladVikendExtra)),
                        defaultCase(sum(mnozstviPracovnikNakladExtra, prescasPracovnikNakladExtra))
                );

                /*
                V případě že se jednotky schodují
                +-------------------+---------------+---------------------+
                | Pracovník         | Stroj         | tP = tS              |
                +===================+===============+=====================+
                | Běžná cena        | Běžná cena    | NP*tP + NS*tS        |
                +-------------------+---------------+---------------------+
                | Extra cena        | Extra cena    | >NC*tS               |
                +-------------------+---------------+---------------------+
                | Běžná cena        | Extra cena    | NS*tS                |
                +-------------------+---------------+---------------------+
                | Extra cena        | Běžná cena    | NP*tP + NS*tS        |
                +-------------------+---------------+---------------------+
                */
                var extraActivityMatch = match(
                        whenCase(noneAreExtraActivity, defaultCalculation),
                        whenCase(bothAreExtraActivity, bothAreExtraActivityCalculation),
                        whenCase(onlyMachineIsExtraActivity, onlyMachineIsExtraActivityCalculation),
                        whenCase(onlyWorkerIsExtraActivity, defaultCalculation),
                        defaultCase(defaultCalculation)
                );

                return Optional.of(conditional(sameUnits, extraActivityMatch, defaultCalculation));
            }
        }

        return Optional.empty();
    }

    private static @NonNull OriginTracker<FieldDisplayType, FieldSettingId> combineDisplayType(FieldSettings existingFieldSettings, FieldSettings newFieldSettings) {
        FieldSettings picked = pickBetterByDisplayType(existingFieldSettings, newFieldSettings);
        return new OriginTracker<>(picked.trackedDisplayType().value(), picked.trackedDisplayType().origin());
    }

    private static FieldSettings pickBetterByDisplayType(FieldSettings existing, FieldSettings other) {
        List<FieldDisplayType> order = FieldDisplayType.ORDERED_BY_BEST_FOR_EDIT;

        int existingIndex = order.indexOf(existing.trackedDisplayType().value());
        int otherIndex = order.indexOf(other.trackedDisplayType().value());

        if (otherIndex < existingIndex) {
            return other;
        }
        return existing;
    }

    private static OriginTracker<FieldRequirementType, FieldSettingId> combineRequirementType(
            FieldSettings existingFieldSettings,
            FieldSettings newFieldSettings
    ) {
        FieldRequirementType left = existingFieldSettings.requirementType();
        FieldRequirementType right = newFieldSettings.requirementType();

        FieldRequirementType merged = FieldRequirementType.merge(left, right);
        FieldSettingId origin = (merged.equals(right) && !merged.equals(left))
                ? newFieldSettings.trackedRequirementType().origin()
                : existingFieldSettings.trackedRequirementType().origin();

        return new OriginTracker<>(merged, origin);
    }

    private static OriginTracker<Integer, FieldSettingId> combineOrder(
            FieldSettings existingFieldSettings,
            FieldSettings newFieldSettings
    ) {
        if (existingFieldSettings.order() <= newFieldSettings.order()) {
            return new OriginTracker<>(
                    existingFieldSettings.order(),
                    existingFieldSettings.trackedOrder().origin()
            );
        } else {
            return new OriginTracker<>(
                    newFieldSettings.order(),
                    newFieldSettings.trackedOrder().origin()
            );
        }
    }

    private static OriginTracker<Set<RecordConstraintDef>, FieldSettingId> combineLinkConstraints(
            FieldSettings existingFieldSettings,
            FieldSettings newFieldSettings
    ) {
        Set<RecordConstraintDef> union = ListUtil.unionSet(
                existingFieldSettings.linkConstraints(),
                newFieldSettings.linkConstraints()
        );

        if (union.equals(existingFieldSettings.linkConstraints())) {
            return new OriginTracker<>(union, existingFieldSettings.trackedLinkConstraints().origin());
        } else {
            return new OriginTracker<>(union, newFieldSettings.trackedLinkConstraints().origin());
        }
    }
}
