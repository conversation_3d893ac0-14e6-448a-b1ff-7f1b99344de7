package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.object.Ordered;
import cz.kpsys.portaro.commons.util.ComparatorForExplicitIdSorting;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.FieldTypes;
import cz.kpsys.portaro.record.detail.constraints.RecordConstraintResolver;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SubfieldTypeToEditableConverter implements Function<FieldType<?>, EditableFieldType<?>> {

    @NonNull Fond fond;
    @NonNull Map<FieldTypeId, FieldSettings> typeIdToEditableSubfieldSettingMap;

    public static @NonNull SubfieldTypeToEditableConverter createEmpty(@NonNull Fond fond) {
        return new SubfieldTypeToEditableConverter(fond, Map.of());
    }

    @Override
    public EditableFieldType<?> apply(FieldType<?> subfieldType) {
        FieldSettings setting = subfieldType.isVirtualGroup()
                ? getEditableSubfieldSettingForComposite(subfieldType)
                : getEditableSubfieldSettingForSimple(subfieldType);
        return new EditableFieldType<>(subfieldType, fond, setting, this);
    }

    private FieldSettings getEditableSubfieldSettingForSimple(FieldType<?> subfieldType) {
        FieldSettings definedSettings = typeIdToEditableSubfieldSettingMap.get(subfieldType.getFieldTypeId());
        if (definedSettings != null) {
            return definedSettings;
        }
        if (subfieldType.getCode().equals(FieldTypes.IND_1_FIELD_CODE)) {
            return FieldSettings.ofAlwaysVisible(FieldSettings.IND_1_ORDER);
        }
        if (subfieldType.getCode().equals(FieldTypes.IND_2_FIELD_CODE)) {
            return FieldSettings.ofAlwaysVisible(FieldSettings.IND_2_ORDER);
        }
        return FieldSettings.unknown();
    }

    private FieldSettings getEditableSubfieldSettingForComposite(FieldType<?> subfieldType) {
        FieldDisplayType fieldDisplayType = collectSubfieldTypeFields(subfieldType, FieldSettings::displayType)
                .min(ComparatorForExplicitIdSorting.forIdentity(FieldDisplayType.ORDERED_BY_BEST_FOR_EDIT))
                .orElse(FieldDisplayType.DISABLED);

        FieldRequirementType fieldRequirementType = collectSubfieldTypeFields(subfieldType, FieldSettings::requirementType)
                .reduce(FieldRequirementType::merge)
                .orElse(FieldRequirementType.INHERIT);

        Integer order = collectSubfieldTypeFields(subfieldType, FieldSettings::order)
                .min(Comparator.naturalOrder())
                .orElse(Ordered.LATEST);

        Set<RecordConstraintResolver> linkConstraints = collectSubfieldTypeFields(subfieldType, FieldSettings::linkConstraints)
                .findFirst()
                .orElse(FieldSettings.NO_CONSTRAINTS);

        Optional<Formula<?>> formula = collectSubfieldTypeFields(subfieldType, FieldSettings::formula)
                .findFirst()
                .orElse(Optional.empty());

        // TODO není to ideální protože tady ztratníme informaci o originu odkud se ta hodnota vzala.
        return new FieldSettings(OriginTracker.init(fieldDisplayType), OriginTracker.init(fieldRequirementType), OriginTracker.init(order), OriginTracker.init(linkConstraints), OriginTracker.init(formula));
    }


    private <E> Stream<E> collectSubfieldTypeFields(FieldType<?> subfieldType, Function<FieldSettings, E> mapper) {
        return subfieldType.getSubfieldTypes().stream()
                .map(FieldType::getFieldTypeId)
                .map(typeIdToEditableSubfieldSettingMap::get)
                .filter(Objects::nonNull)
                .map(mapper);
    }
}
