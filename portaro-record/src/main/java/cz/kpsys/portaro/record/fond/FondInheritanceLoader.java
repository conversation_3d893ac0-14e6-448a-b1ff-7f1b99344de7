package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.hierarchy.InheritanceLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FondInheritanceLoader implements InheritanceLoader<Fond>, InclusionLoader<Fond> {

    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull TransactionTemplate readonlyTransactionTemplate;
    @NonNull FondRelationsMapProvider fondRelationsMapProvider;

    public @NonNull Fond getById(@NonNull Integer id) {
        return fondLoader.getById(id);
    }

    @Override
    public List<Fond> getAll() {
        return readonlyTransactionTemplate.execute(_ -> fondLoader.getAll());
    }

    @Override
    public List<Fond> getAllHierarchicallySorted() {
        return FondHierarchicalSorter.sortHierarchically(getAll());
    }

    @Override
    public List<Fond> getThisAndChildren(Fond refNode) {
        return readonlyTransactionTemplate.execute(_ ->
                getThisAndOtherByRelationsMap(fondRelationsMapProvider.relationsMapWhereKeyParentFondId(), refNode));
    }

    @Override
    public List<Fond> getThisAndAncestors(Fond refNode) {
        return readonlyTransactionTemplate.execute(_ ->
                getThisAndOtherByRelationsMap(fondRelationsMapProvider.relationsMapWhereKeyFondId(), refNode));
    }

    @Override
    public List<Fond> getThisAndIncludedAndChildren(Fond refNode) {
        return readonlyTransactionTemplate.execute(_ ->
                getThisAndOtherByRelationsMap(fondRelationsMapProvider.relationsMapWithInclusionsWhereParentOrFondIdKey(), refNode));
    }

    @Override
    public List<Fond> getThisAndIncludersAndAncestors(Fond refNode) {
        return readonlyTransactionTemplate.execute(_ ->
                getThisAndOtherByRelationsMap(fondRelationsMapProvider.relationsMapWithInclusionsWhereIncludedFondIdKey(), refNode));
    }

    private @NonNull List<Fond> getThisAndOtherByRelationsMap(@NonNull Provider<Map<Integer, Set<Integer>>> relationsMapParentFondIdAsKeyProvider, Fond refNode) {
        Map<Integer, Set<Integer>> relationsMap = relationsMapParentFondIdAsKeyProvider.get();
        return Stream.concat(
                        Stream.of(refNode),
                        Stream.of(refNode).flatMap(fond -> getAllByRelationsMap(fond, relationsMap).stream())
                )
                .distinct()
                .toList();
    }

    private Set<Fond> getAllByRelationsMap(Fond fond, Map<Integer, Set<Integer>> relationsMap) {
        Set<Fond> all = new HashSet<>();
        loadAllByRelationsMap(all, fond, relationsMap);
        return all;
    }

    private void loadAllByRelationsMap(Set<Fond> accumulator, Fond fond, Map<Integer, Set<Integer>> relationsMap) {
        if (relationsMap.containsKey(fond.getId())) {
            relationsMap.get(fond.getId()).forEach(child -> fondLoader.findById(child).ifPresent(childFond -> {
                accumulator.add(childFond);
                loadAllByRelationsMap(accumulator, childFond, relationsMap);
            }));
        }
    }
}
