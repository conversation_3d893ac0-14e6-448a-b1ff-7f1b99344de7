package cz.kpsys.portaro.record.holding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.department.Department;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import org.springframework.lang.Nullable;

import java.util.UUID;

@Value
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class RecordHolding implements Identified<UUID> {

    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @NonNull
    UUID recordId;

    @NonNull
    Department department;

    @NonNull
    UUID creationEventId;

    @Nullable
    UUID discardionEventId;

    @Nullable
    UUID deletionEventId;

    public boolean isOfSameDepartment(RecordHolding other) {
        return other.getDepartment().equals(department);
    }

    @JsonIgnore
    public boolean isDiscarded() {
        return discardionEventId != null;
    }

    @JsonIgnore
    public boolean isDeleted() {
        return deletionEventId != null;
    }

    @JsonIgnore
    public boolean isDiscardedOrDeleted() {
        return isDiscarded() || isDeleted();
    }

}
