package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import cz.kpsys.portaro.user.role.editor.EditLevel;
import cz.kpsys.portaro.user.role.editor.EditorAccount;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordOperationSearchDelegatingRecordEditLevelProvider implements RecordEditLevelProvider {

    private static final List<Integer> USED_OPERATION_TYPE_IDS = List.of(
            RecordOperationType.ID_CREATION,
            RecordOperationType.ID_EDITATION,
            RecordOperationType.ID_DOCUMENT_DISCARDION,
            RecordOperationType.ID_STATUS_CHANGE_TO_FINISHED_CATALOGING,
            RecordOperationType.ID_BULK_EDITATION,
            RecordOperationType.ID_MERGING,
            RecordOperationType.ID_IMPORT,
            RecordOperationType.ID_DELETION,
            RecordOperationType.ID_AUTHORITY_CREATION_OF_DOCUMENT,
            RecordOperationType.ID_RESTORATION,
            RecordOperationType.ID_PUBLICATION_BY_WEB,
            RecordOperationType.ID_UNBLOCK_EDITATION,
            RecordOperationType.ID_AUTHORITY_SENT_TO_NATIONAL_AUTHORITIES,
            RecordOperationType.ID_CREATION_BY_CI,
            RecordOperationType.ID_EDITATION_BY_CI
    );

    @NonNull ParameterizedSearchLoader<MapBackedParams, RecordOperation> recordOperationSearchLoader;
    @NonNull ByIdLoadable<RecordOperationType, Integer> recordOperationTypeLoader;
    @NonNull UserByBasicUserLoader userLoader;


    @Override
    public EditLevel getRecordEditLevel(Record record) {
        return getRecordEditLevelOpt(record).orElse(EditLevel.WORST);
    }

    private Optional<EditLevel> getRecordEditLevelOpt(Record record) {
        List<RecordOperation> content = recordOperationSearchLoader.getContent(RangePaging.forSingle(), SortingItem.ofSimpleDesc(RecordConstants.SearchParams.OPERATION_DATE.getName()), StaticParamsModifier.of(
            RecordConstants.SearchParams.OPERATION_TYPE, ListUtil.convert(USED_OPERATION_TYPE_IDS, recordOperationTypeLoader::getById),
            RecordConstants.SearchParams.RECORD, List.of(record.getId())
        ));

        if (content.isEmpty()) {
            return Optional.empty();
        }

        User user = userLoader.getUser(content.getFirst().getUser());

        return user.getEditorAccounts().stream()
                .map(EditorAccount::getEditLevel)
                .min(Comparator.comparing(EditLevel::getValue));
    }
}
