package cz.kpsys.portaro.record.search;

import cz.kpsys.portaro.appserver.Appserver;
import cz.kpsys.portaro.appserver.XmlAppserverRequest;
import cz.kpsys.portaro.appserver.XmlFormatter;
import cz.kpsys.portaro.appserver.mapping.AppserverErrorChain;
import cz.kpsys.portaro.appserver.mapping.AppserverErrorHandler;
import cz.kpsys.portaro.appserver.mapping.AppserverResponseHandler;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.appserver.oxm.AppserverTag;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.convert.StringToStringListConverter;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.BasicSearchField;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.lucene.FacetKeyIdentified;
import cz.kpsys.portaro.search.lucene.IncorrectLuceneQueryException;
import cz.kpsys.portaro.search.lucene.InvalidSearchPageResponseException;
import cz.kpsys.portaro.search.lucene.QueryNotExistsException;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Between;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.In;
import cz.kpsys.portaro.search.restriction.matcher.SearchMatcher;
import cz.kpsys.portaro.search.restriction.serialize.lucene.LocalDateToFacetValueConverter;
import cz.kpsys.portaro.search.sorting.SortFieldsBySearchFieldLoader;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jspecify.annotations.Nullable;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class AppserverSearchEngine {

    public static final String PATH = Appserver.APIPATH_HLEDANI;

    private static final String IDS = "IDS";
    private static final String TOTAL = "TOTAL";
    private static final String FACETS = "FACETS";
    private static final String FACET = "FACET";
    private static final String FACET_ID = "id";
    private static final String KEYWORD = "KEYWORD";
    private static final String FACET_DATATYPE = "datatype";
    private static final String KEY_VALUE = "value";
    private static final String KEY_COUNT = "count";
    private static final String KEY_SELECTED = "selected";
    private static final String KEY_LIMIT = "limit";
    private static final String PARAM_DEPARTMENTS = "departments";

    @NonNull MappingAppserverService mappingAppserver;
    @NonNull ContextualProvider<Department, List<String>> desiredFacetsProvider;
    @NonNull ContextualProvider<Department, @NonNull Integer> maxNumberOfFacetKeysProvider;
    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull SortFieldsBySearchFieldLoader<String> luceneSortFieldLoader;
    @NonNull LocalDateToFacetValueConverter facetLocalDateToStringConverter = new LocalDateToFacetValueConverter();


    public AppserverSearchResponse search(String generatedLuceneQuery, MapBackedParams p, Range range, SortingItem sorting, Department ctx) {
        if (StringUtil.isNullOrEmpty(generatedLuceneQuery)) {
            throw new QueryNotExistsException();
        }

        AppserverTag elem = createElem(generatedLuceneQuery,
                sorting,
                ObjectUtil.isTrue(p.get(CoreSearchParams.FACETS_ENABLED)),
                p.get(CoreSearchParams.FACET_RESTRICTION),
                range,
                desiredFacetsProvider.getOn(ctx),
                maxNumberOfFacetKeysProvider.getOn(ctx),
                ctx
        );
        AppserverSearchResponse result = mappingAppserver.call(
                XmlAppserverRequest.byPost(PATH, elem),
                new ResponseHandler(),
                new ErrorHandler<>(generatedLuceneQuery));

        InvalidSearchPageResponseException.assertValidResponseElementsCount(range, result.content().size(), result.totalElements(), String.valueOf(elem), result.rawAppserverResponse());

        log.info("Found total {} items after search by:\n{}\nGenerated lucene query:\n{}", result.totalElements(), p, generatedLuceneQuery);

        return result;
    }


    private AppserverTag createElem(String generatedLuceneQuery, @Nullable SortingItem sorting, boolean facetsEnabled, @Nullable Restriction<BasicSearchField> facetRestriction, Range range, List<String> desiredFacets, Integer maxNumberOfFacetKeys, Department ctx) {
        AppserverTag elem = AppserverTag.group("hled")
                .withParam("diacritic", "false")
                .withParam("prefix", "true")

                .addChild("QUERY", generatedLuceneQuery)
                .addChild(AppserverTag.group("RANGE")
                        .withParam("from", range.getInclusiveFrom())
                        .withParam("to", range.getInclusiveTo()));

        if (sorting != null && !sorting.isDefault()) {
            String singleLuceneSortFieldName = luceneSortFieldLoader.convertToSearchFieldName(sorting);
            elem.addChild(AppserverTag.group("SORT")
                    .withParam("field", singleLuceneSortFieldName)
                    .withParam("order", sorting.asc() ? "asc" : "desc"));
        }


        //rezy
        if (facetsEnabled && ListUtil.hasLength(desiredFacets)) {
            AppserverTag facetsElem = AppserverTag.group(FACETS).withParam(PARAM_DEPARTMENTS, StringUtil.listToStringOfIds(departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.SUBTREE), ","));
            elem.addChild(facetsElem);

            for (String desiredFacet : desiredFacets) {
                AppserverTag facetElem = AppserverTag.group(FACET)
                        .withParam(FACET_ID, desiredFacet)
                        .withParam(KEY_LIMIT, maxNumberOfFacetKeys);
                facetsElem.addChild(facetElem);

                //pro selectovane klice vypiseme vsechny ty, ktere jsou daneho facetu
                facetRestriction = ObjectUtil.firstNotNull(facetRestriction, new Conjunction<>());
                Assert.isInstanceOf(Conjunction.class, facetRestriction);
                ((List<Term<SearchField>>) ((Conjunction) facetRestriction).getItems()).stream()
                        .filter(selectedFacetKeyTerm -> selectedFacetKeyTerm.field().getId().equals(desiredFacet))
                        .map(this::termToMatcher)
                        .flatMap(in -> in.value().stream())
                        .forEach(selectedFacetKey -> facetElem.addChild(AppserverTag.group(KEYWORD)
                                .withParam(KEY_VALUE, selectedFacetKey)
                                .withParam(KEY_SELECTED, "true")));
            }
        }

        return elem;
    }

    // FIXME: Remove after AppServer starts supporting between condition
    private In termToMatcher(Term<SearchField> term) {
        SearchMatcher matcher = term.matcher();

        if (matcher instanceof Between<?> betweenMatcher && "REZS_ROK".equals(term.field().getId())) {
            Between<Integer> intBetweenMatcher = (Between<Integer>) betweenMatcher;
            List<FacetKeyIdentified> values = IntStream
                    .rangeClosed(intBetweenMatcher.existingFrom(), intBetweenMatcher.existingInclusiveTo())
                    .boxed()
                    .map(Integer::toUnsignedString)
                    .map(FacetKeyIdentified::new)
                    .collect(Collectors.toList());
            return new In(values);
        }

        if (matcher instanceof Eq eqMatcher) {
            var value = mapFacetValueToAppserverFormat(eqMatcher.value());
            return In.ofItems(value);
        }

        return (In) matcher;
    }

    @Nullable
    private Object mapFacetValueToAppserverFormat(Object matcherValue) {
        return switch (matcherValue) {
            case LocalDate localDate -> facetLocalDateToStringConverter.convert(localDate);
            default -> matcherValue;
        };
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class ResponseHandler implements AppserverResponseHandler<AppserverSearchResponse> {

        @NonNull Converter<String, @NonNull List<String>> resultIdsConverter = new StringToStringListConverter(";").throwOnBlankItems();

        @Override
        public AppserverSearchResponse mapResponse(Document d) {
            //rozparsovani vysledku
            Element hledElement = d.getRootElement();

            Integer totalElements = Integer.parseInt(hledElement.getChildTextTrim(TOTAL));

            //nacteme idcka zaznamu
            String idsString = hledElement.getChildTextTrim(IDS);
            List<String> content = resultIdsConverter.convert(idsString);

            List<AppserverSearchResponse.FacetDto> facets = List.of();

            //nacteme rezy
            Element facetsElement = hledElement.getChild(FACETS);
            if (facetsElement != null) {
                facets = ListUtil.convert(facetsElement.getChildren(FACET), this::mapFacet);
            }

            Optional<String> prettyResponseXml = new XmlFormatter().tryConvertToPrettyString(d);

            return new AppserverSearchResponse(totalElements, content, facets, prettyResponseXml.orElseGet(d::toString));
        }


        private AppserverSearchResponse.FacetDto mapFacet(Element facetElement) {
            String facetId = facetElement.getAttributeValue(FACET_ID);
            String facetDatatype = facetElement.getAttributeValue(FACET_DATATYPE);
            List<AppserverSearchResponse.FacetKeyDto> keys = ListUtil.convert(facetElement.getChildren(KEYWORD), this::mapFacetKey);
            return new AppserverSearchResponse.FacetDto(facetId, facetDatatype, keys);
        }


        private AppserverSearchResponse.FacetKeyDto mapFacetKey(Element keywordElement) {
            String keyValue = keywordElement.getAttributeValue(KEY_VALUE);
            Integer keyCount = Optional.ofNullable(keywordElement.getAttributeValue(KEY_COUNT)).map(Integer::parseInt).orElse(null);
            return new AppserverSearchResponse.FacetKeyDto(keyValue, keyCount);
        }

    }




    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class ErrorHandler<E> implements AppserverErrorHandler<E> {

        @NonNull String finalLuceneQuery;

        @Override
        public E handleError(int errorNumber, String errorMessage, Document xml, AppserverErrorChain<E> chain) {
            if (errorMessage.contains("ParseException")) {
                throw new IncorrectLuceneQueryException(finalLuceneQuery);
            }
            return chain.next(errorNumber, errorMessage, xml);
        }

    }
}
