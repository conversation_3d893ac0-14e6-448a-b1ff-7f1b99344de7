package cz.kpsys.portaro.record.search;

import cz.kpsys.portaro.commons.convert.ConversionException;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.BasicMapSearchParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.field.StaticSearchFields;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.In;
import cz.kpsys.portaro.search.restriction.matcher.SearchMatcher;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SubkindFieldTermToFondListConverter implements Converter<Term<SearchField>, Restriction<? extends SearchField>> {

    @NonNull AllValuesProvider<Fond> documentFondsProvider;
    @NonNull AllValuesProvider<Fond> authorityFondsProvider;

    @Override
    public Restriction<? extends SearchField> convert(@NonNull Term<SearchField> source) {
        SearchField field = source.field();
        SearchMatcher matcher = source.matcher();

        if (!field.equals(StaticSearchFields.SUBKIND)) {
            throw new ConversionException("Field must be " + StaticSearchFields.SUBKIND);
        }

        if (matcher instanceof Eq) {
            return eqKindToInFondsTerm((Eq) matcher);
        }
        if (matcher instanceof In) {
            return inKindToInFondsTerm((In) matcher);
        }
        throw new IllegalStateException(String.format("Illegal matcher %s for search field %s", matcher, field));
    }


    private Term<SearchField> inKindToInFondsTerm(In matcher) {
        Object value = matcher.value();
        Assert.isInstanceOf(List.class, value, () -> String.format("Value type %s must be list for kind field", value));
        List<Fond> fonds = ((List<String>) value).stream()
                .map(this::kindToFonds)
                .flatMap(Collection::stream)
                .toList();
        return new Term<>(RecordConstants.SearchFields.FOND, new In(fonds));
    }


    private Term<SearchField> eqKindToInFondsTerm(Eq matcher) {
        Object value = matcher.value();
        Assert.isInstanceOf(String.class, value, () -> String.format("Value type %s must be of String for kind field", value));
        List<Fond> fonds = kindToFonds((String) value);
        return new Term<>(RecordConstants.SearchFields.FOND, new In(fonds));
    }


    private List<Fond> kindToFonds(String kind) {
        return switch (kind) {
            case BasicMapSearchParams.SUBKIND_DOCUMENT -> documentFondsProvider.getAll();
            case BasicMapSearchParams.SUBKIND_AUTHORITY -> authorityFondsProvider.getAll();
            default -> throw new UnsupportedOperationException(String.format("Subkind %s is not supported for converting to fond list", kind));
        };
    }

}
