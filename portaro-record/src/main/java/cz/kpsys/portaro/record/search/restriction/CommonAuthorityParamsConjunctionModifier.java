package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.BasicMapSearchParams;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Not;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.In;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

import static cz.kpsys.portaro.record.RecordConstants.SearchFields.RECORD_STATUS;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CommonAuthorityParamsConjunctionModifier implements RestrictionModifier<MapBackedParams> {

    @NonNull AllValuesProvider<Fond> enabledFondsProvider;

    @Override
    public Conjunction<SearchField> modify(Conjunction<SearchField> authorityConjunction, MapBackedParams p, Department ctx) {

        List<Fond> authorityFonds = new ArrayList<>(Fond.filterAuthorityFonds(enabledFondsProvider.getAll()));

        if (!p.hasNotNull(CoreSearchParams.FINAL_RAW_QUERY)) {

            //pokud jsme specifikovali search types a neobsahuji autority, vyprazdnime povolene fondy
            if (p.hasNotNull(CoreSearchParams.KIND) && !p.get(CoreSearchParams.KIND).contains(BasicMapSearchParams.KIND_RECORD)) {
                authorityFonds.clear();
            }

            if (p.hasNotNull(RecordConstants.SearchParams.FOND)) {
                List<Fond> authorityFondsFromFonds = Fond.filterAuthorityFonds(p.get(RecordConstants.SearchParams.FOND));
                authorityFonds = ListUtil.intersection(authorityFonds, authorityFondsFromFonds); //po tomto muze zbyt vysledny list prazdny
            }

            authorityConjunction.add(new Term<>(RecordConstants.SearchFields.FOND, new In(authorityFonds)));

            //draft
            authorityConjunction.addIf(!p.hasNotNull(CoreSearchParams.INCLUDE_DRAFT) || !p.get(CoreSearchParams.INCLUDE_DRAFT), () -> new Not<>(new Term<>(RECORD_STATUS, new Eq(RecordStatus.DRAFT))));

            //excluded - this is based on used index
            //authorityConjunction.addIf(!p.has(RecordConstants.SearchParams.INCLUDE_EXCLUDED) || !p.get(RecordConstants.SearchParams.INCLUDE_EXCLUDED), () -> new Not<>(new Term<>(RECORD_STATUS, new Eq(RecordStatus.EXCLUDED))));

            //deleted
            authorityConjunction.addIf(!p.hasNotNull(CoreSearchParams.INCLUDE_DELETED) || !p.get(CoreSearchParams.INCLUDE_DELETED), () -> new Not<>(new Term<>(RECORD_STATUS, new Eq(RecordStatus.DELETED))));
        }

        return authorityConjunction;
    }

}
