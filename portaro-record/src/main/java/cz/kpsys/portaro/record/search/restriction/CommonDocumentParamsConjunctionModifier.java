package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondType;
import cz.kpsys.portaro.search.BasicMapSearchParams;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.field.StaticSearchFields;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.In;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CommonDocumentParamsConjunctionModifier implements RestrictionModifier<MapBackedParams> {

    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull Codebook<Fond, Integer> fondLoader;

    @Override
    public Conjunction<SearchField> modify(Conjunction<SearchField> documentConjunction, MapBackedParams p, Department ctx) {

        List<Fond> documentFonds = new ArrayList<>(Fond.filterDocumentFonds(enabledFondsProvider.getAll()));

        if (!p.hasNotNull(CoreSearchParams.FINAL_RAW_QUERY)) {

            //pokud jsme specifikovali search types a neobsahuji dokument, vyprazdnime povolene fondy
            if (p.hasNotNull(CoreSearchParams.KIND) && !p.get(CoreSearchParams.KIND).contains(BasicMapSearchParams.KIND_RECORD)) {
                documentFonds.clear();
            }

            if (p.hasNotNull(RecordConstants.SearchParams.FOND)) {
                List<Fond> documentFondsFromFonds = Fond.filterDocumentFonds(p.get(RecordConstants.SearchParams.FOND));
                documentFonds = ListUtil.intersection(documentFonds, documentFondsFromFonds); //po tomto muze zbyt vysledny list prazdny
            }

            if (p.hasNotNull(RecordConstants.SearchParams.FOND_TYPE)) {
                FondType fondType = p.get(RecordConstants.SearchParams.FOND_TYPE);
                List<Fond> documentFondsFromFonds =  Fond.filterDocumentFonds(ListUtil.filter(fondLoader.getAll(), fondType::matches));
                documentFonds = ListUtil.intersection(documentFonds, documentFondsFromFonds); //po tomto muze zbyt vysledny list prazdny
            }

            documentConjunction.add(new Term<>(RecordConstants.SearchFields.FOND, new In(documentFonds)));

            //departments
            List<Department> departments = departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.ALL);
            if (p.hasNotNull(CoreSearchParams.DEPARTMENT)) {
                departments = ListUtil.intersection(departments, p.get(CoreSearchParams.DEPARTMENT));
            }

            boolean shouldIncludeActive = p.get(CoreSearchParams.INCLUDE_ACTIVE); // normal record state
            boolean shouldIncludeDrafts = p.get(CoreSearchParams.INCLUDE_DRAFT); // has no activation event
            boolean shouldIncludeExcluded = p.get(RecordConstants.SearchParams.INCLUDE_EXCLUDED); // has discardion event
            boolean shouldIncludeDeleted = p.get(CoreSearchParams.INCLUDE_DELETED); // has deletion event

            if (shouldIncludeDrafts && !shouldIncludeActive && !shouldIncludeExcluded && !shouldIncludeDeleted) {
                documentConjunction.add(new Term<>(StaticSearchFields.DRAFT_DEPARTMENT, new In(departments))); // draft-only search
            } else if (shouldIncludeExcluded || shouldIncludeDrafts) {
                documentConjunction.add(new Term<>(StaticSearchFields.UNRESTRICTED_DEPARTMENT, new In(departments)));
            } else {
                documentConjunction.add(new Term<>(StaticSearchFields.DEPARTMENT, new In(departments)));
            }
        }

        return documentConjunction;
    }

}
