package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.detail.constraints.*;
import cz.kpsys.portaro.record.detail.spec.RecordIdFieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.field.StaticSearchFields;
import cz.kpsys.portaro.search.restriction.*;
import cz.kpsys.portaro.search.restriction.matcher.*;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.List;
import java.util.function.Function;

import static cz.kpsys.portaro.record.RecordConstants.SearchFields.RECORD_STATUS;
import static cz.kpsys.portaro.record.RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS;
import static cz.kpsys.portaro.record.search.restriction.FieldTypedSearchFieldParsing.DYNAMIC_FIELD_PREFIX;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CommonRecordSearchParamsRestrictionModifier implements RestrictionModifier<MapBackedParams> {

    @NonNull LinkConstraintsResolver linkConstraintsResolver;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader;

    @Override
    public Conjunction<SearchField> modify(Conjunction<SearchField> conjunction, MapBackedParams p, Department ctx) {
        Assert.state(!p.hasNotNull(CoreSearchParams.SUBKIND), () -> CoreSearchParams.SUBKIND + " parameter must be already expanded to fonds before conjunction modifier");
        Assert.state(!p.hasNotNull(RecordConstants.SearchParams.ROOT_FOND), () -> RecordConstants.SearchParams.ROOT_FOND + " parameter must be already expanded to fonds before conjunction modifier");

        if (!p.hasNotNull(CoreSearchParams.FINAL_RAW_QUERY)) {

            //document status
            conjunction.addIf(p.hasLength(FORBIDDEN_RECORD_STATUS), () -> new Not<>(new Term<>(RECORD_STATUS, new In(p.get(FORBIDDEN_RECORD_STATUS)))));

            //related record (napr. hledani dokumentu podle autority)
            conjunction.addIfHas(p, RecordConstants.SearchParams.RECORD_RELATED_RECORD, val -> new Term<>(RecordConstants.SearchFields.RECORD_RELATED_RECORD, new Eq(val)));

            //forbidden record ids
            conjunction.addIf(p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD), () -> new Not<>(new Term<>(RecordConstants.SearchFields.RECORD_ID, new In(p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD)))));
        }

        conjunction.addIfHas(p, RecordConstants.SearchParams.RECORD, val -> new Term<>(RecordConstants.SearchFields.RECORD_ID, new In(val)));

        conjunction.addIfHas(p, CoreSearchParams.NAME, val -> new Term<>(StaticSearchFields.NAME, new EqWords(val)));

        conjunction.addIfHas(p, RecordConstants.SearchParams.PREFIX, val -> new Term<>(StaticSearchFields.NAME, new StartsWithWords(val)));

        if (p.hasAnyPrefix(DYNAMIC_FIELD_PREFIX)) {
            buildFieldRestrictions(p, conjunction);
        }

        if (p.hasNotNull(RecordConstants.SearchParams.CONSTRAINTS_RECORD_FIELD_TYPE)) {
            RecordIdFieldTypeId constraintsRecordFieldType = p.get(RecordConstants.SearchParams.CONSTRAINTS_RECORD_FIELD_TYPE);
            LinkedRecordConstraints constraints = linkConstraintsResolver.resolve(constraintsRecordFieldType, ctx);

            for (RecordConstraint constraint : constraints.constraints()) {
                Restriction<SearchField> term = switch (constraint) {
                    case HavingFond c -> {
                        List<Fond> loadableFonds = enabledLoadableFondsExpander.apply(c.fond());
                        yield new Term<>(RecordConstants.SearchFields.FOND, new In(loadableFonds));
                    }
                    case HavingAnyOfRecordLink c -> new Term<>(FieldTypeSearchField.loadOfLink(c.linkFieldTypeId(), fieldTypeSearchFieldLoader), new In(c.acceptableRecords()));
                    case RestrictionConstraint c -> c.restriction();
                    case ForbidConstraint _ -> FalseRestriction.create();
                    case AllowConstraint _ -> TrueRestriction.create();
                    default -> throw new IllegalArgumentException("Unknown constraint type: " + constraint.getClass().getName());
                };
                if (term instanceof TrueRestriction<?>) {
                    continue;
                }
                conjunction.add(term);
            }
        }

        return conjunction;
    }

    private <PARAMS extends MapBackedParams> void buildFieldRestrictions(PARAMS p, Conjunction<SearchField> conjunction) {
        List<MapBackedParams.Entry<Object>> dynamicSearchParams = p.getByPrefix(DYNAMIC_FIELD_PREFIX);
        for (MapBackedParams.Entry<Object> dynamicSearchParam : dynamicSearchParams) {
            FieldTypeSearchField searchField = fieldTypeSearchFieldLoader.getById(dynamicSearchParam.param().getId());
            var matcher = resolveDynamicParamMatcher(dynamicSearchParam);
            conjunction.addTerm(searchField, matcher);
        }
    }

    private @NonNull SearchMatcher resolveDynamicParamMatcher(MapBackedParams.Entry<Object> dynamicSearchParam) {
        if (dynamicSearchParam.value() == null) {
            return new IsNull();
        }
        if (dynamicSearchParam.value() instanceof String stringValue) {
            return new EqWords(stringValue);
        }
        return new Eq(dynamicSearchParam.value());
    }

}
