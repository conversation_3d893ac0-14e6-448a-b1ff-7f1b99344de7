package cz.kpsys.portaro.record.search.restriction;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.search.field.SearchField;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

public record FieldTypeSearchField(

        @NonNull
        String id,

        @NonNull
        @NotEmpty
        Text text,

        @JsonIgnore
        @NonNull
        FieldType<?> fieldType,

        @JsonIgnore
        @NonNull
        FieldTypeSearchField.Subject subject,

        @JsonIgnore
        @NonNull
        ScalarDatatype indexedValueDatatype

) implements SearchField {

    public enum Subject {
        VALUE,
        LINK,
        ORIGIN,
        SORT
    }

    public static FieldTypeSearchField ofValue(@NonNull FieldType<?> fieldType) {
        String searchFieldName = FieldTypedSearchFieldParsing.ofValue(fieldType.getFieldTypeId()).toSearchFieldName();
        return new FieldTypeSearchField(searchFieldName, MultiText.ofTexts("Value of {}", fieldType.getText()), fieldType, Subject.VALUE, fieldType.getDatatypeOrThrow());
    }

    public static FieldTypeSearchField ofLink(@NonNull FieldType<?> fieldType) {
        String searchFieldName = FieldTypedSearchFieldParsing.ofLink(fieldType.getFieldTypeId()).toSearchFieldName();
        return new FieldTypeSearchField(searchFieldName, MultiText.ofTexts("Link of {}", fieldType.getText()), fieldType, Subject.LINK, CoreConstants.Datatype.UUID);
    }

    public static FieldTypeSearchField ofOrigin(@NonNull FieldType<?> fieldType) {
        String searchFieldName = FieldTypedSearchFieldParsing.ofOrigin(fieldType.getFieldTypeId()).toSearchFieldName();
        return new FieldTypeSearchField(searchFieldName, MultiText.ofTexts("Origin of {}", fieldType.getText()), fieldType, Subject.ORIGIN, CoreConstants.Datatype.UUID);
    }

    public static FieldTypeSearchField loadOfValue(@NonNull FieldTypeId fieldTypeId, @NonNull ByIdLoadable<FieldTypeSearchField, String> loader) {
        return loader.getById(FieldTypedSearchFieldParsing.ofValue(fieldTypeId).toSearchFieldName());
    }

    public static FieldTypeSearchField loadOfOrigin(@NonNull FieldTypeId fieldTypeId, @NonNull ByIdLoadable<FieldTypeSearchField, String> loader) {
        return loader.getById(FieldTypedSearchFieldParsing.ofOrigin(fieldTypeId).toSearchFieldName());
    }

    public static FieldTypeSearchField loadOfLink(@NonNull FieldTypeId fieldTypeId, @NonNull ByIdLoadable<FieldTypeSearchField, String> loader) {
        return loader.getById(FieldTypedSearchFieldParsing.ofLink(fieldTypeId).toSearchFieldName());
    }

    @Override
    public @NonNull String sourceDescription() {
        return "record field type";
    }

    @Override
    public boolean equals(Object o) {
        return o instanceof SearchField that && id().equals(that.id());
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }

    @Override
    public String toString() {
        return "FieldTypeSearchField " + id();
    }

}
