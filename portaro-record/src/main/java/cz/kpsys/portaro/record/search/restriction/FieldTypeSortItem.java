package cz.kpsys.portaro.record.search.restriction;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.NonNull;
import lombok.With;

import java.util.function.Function;

public record FieldTypeSortItem(

        @JsonIgnore
        @With
        @NonNull
        String field,

        @JsonIgnore
        @With
        boolean asc,

        @NonNull
        Text text,

        @JsonIgnore
        @NonNull
        FieldType<?> fieldType

) implements SortingItem {

    public static FieldTypeSortItem of(@NonNull FieldType<?> fieldType, boolean asc) {
        String sortFieldName = FieldTypedSearchFieldParsing.ofSort(fieldType.getFieldTypeId()).toSearchFieldName();
        Text text = asc ? fieldType.getText() : MultiText.ofTexts("{} sest.", fieldType.getText());
        return new FieldTypeSortItem(sortFieldName, asc, text, fieldType);
    }

    public static FieldTypeSortItem ofAsc(@NonNull FieldType<?> fieldType) {
        return of(fieldType, true);
    }

    public static FieldTypeSortItem ofDesc(@NonNull FieldType<?> fieldType) {
        return of(fieldType, false);
    }

    @Override
    public FieldTypeSortItem reverse() {
        return withAsc(!asc);
    }

    @Override
    public FieldTypeSortItem mapField(Function<String, String> fieldNameConverter) {
        return withField(fieldNameConverter.apply(field()));
    }

    @Override
    public boolean equals(Object o) {
        return o instanceof SortingItem that && asc == that.asc() && field.equals(that.field());
    }

    @Override
    public int hashCode() {
        int result = field.hashCode();
        result = 31 * result + Boolean.hashCode(asc);
        return result;
    }

    @Override
    public @NonNull String toString() {
        return "LuceneSortItem " + id();
    }
}