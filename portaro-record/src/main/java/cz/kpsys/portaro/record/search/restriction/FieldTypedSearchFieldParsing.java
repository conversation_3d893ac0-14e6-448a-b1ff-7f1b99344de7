package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;

import static cz.kpsys.portaro.record.detail.FieldTypeId.DELIMITER;

public record FieldTypedSearchFieldParsing(

        @NonNull
        FieldTypeId fieldTypeId,

        @NonNull
        FieldTypeSearchField.Subject subject

) {

    public static final String DYNAMIC_FIELD_PREFIX = "field_";
    private static final String DYNAMIC_FIELD_DELIMITER = "_";
    private static final String DYNAMIC_FIELD_VALUE_SUFFIX = "_val";
    private static final String DYNAMIC_FIELD_LINK_SUFFIX = "_link";
    private static final String DYNAMIC_FIELD_ORIGIN_SUFFIX = "_origin";
    private static final String DYNAMIC_FIELD_SORT_SUFFIX = "_sort";

    public static FieldTypedSearchFieldParsing ofValue(@NonNull FieldTypeId fieldTypeId) {
        return new FieldTypedSearchFieldParsing(fieldTypeId, FieldTypeSearchField.Subject.VALUE);
    }

    public static FieldTypedSearchFieldParsing ofLink(@NonNull FieldTypeId fieldTypeId) {
        return new FieldTypedSearchFieldParsing(fieldTypeId, FieldTypeSearchField.Subject.LINK);
    }

    public static FieldTypedSearchFieldParsing ofOrigin(@NonNull FieldTypeId fieldTypeId) {
        return new FieldTypedSearchFieldParsing(fieldTypeId, FieldTypeSearchField.Subject.ORIGIN);
    }

    public static FieldTypedSearchFieldParsing ofSort(@NonNull FieldTypeId fieldTypeId) {
        return new FieldTypedSearchFieldParsing(fieldTypeId, FieldTypeSearchField.Subject.SORT);
    }

    public static boolean hasDynamicFieldPrefix(@NonNull String searchFieldName) {
        return searchFieldName.startsWith(DYNAMIC_FIELD_PREFIX);
    }

    public String toSearchFieldName() {
        String suffix = switch (subject) {
            case VALUE -> DYNAMIC_FIELD_VALUE_SUFFIX;
            case LINK -> DYNAMIC_FIELD_LINK_SUFFIX;
            case ORIGIN -> DYNAMIC_FIELD_ORIGIN_SUFFIX;
            case SORT -> DYNAMIC_FIELD_SORT_SUFFIX;
        };
        return DYNAMIC_FIELD_PREFIX +
               fieldTypeId.value().replace(DELIMITER, DYNAMIC_FIELD_DELIMITER) +
               suffix;
    }

}
