package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.EqWords;
import cz.kpsys.portaro.search.restriction.matcher.StartsWithWords;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldedQAddingConjunctionModifier implements RestrictionModifier<MapBackedParams> {

    @NonNull ByIdLoadable<? extends SearchField, String> searchFieldLoader;

    @Override
    public Conjunction<SearchField> modify(Conjunction<SearchField> rootConjunction, MapBackedParams p, Department ctx) {
        boolean rightHandExtension = ObjectUtil.isTrue(p.get(CoreSearchParams.RIGHT_HAND_EXTENSION));
        String q = p.get(CoreSearchParams.Q);

        if (q != null && p.hasNotNull(CoreSearchParams.FIELD)) {
            List<? extends Term<? extends SearchField>> collect = p.get(CoreSearchParams.FIELD).stream()
                    .map(fieldName -> createTerm(fieldName, q, rightHandExtension))
                    .toList();
            rootConjunction.add(new Conjunction<>(collect));
        }

        return rootConjunction;
    }

    private Term<? extends SearchField> createTerm(String fieldName, String q, boolean rightHandExtension) {
        SearchField searchField = searchFieldLoader.getById(fieldName);
        return new Term<>(searchField, rightHandExtension ? new StartsWithWords(q) : new EqWords(q));
    }

}
