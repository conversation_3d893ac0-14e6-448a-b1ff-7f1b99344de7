package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.function.TriConsumer;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.MoreThanOneItemFoundException;
import cz.kpsys.portaro.commons.property.Property;
import cz.kpsys.portaro.commons.property.SimpleProperty;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.search.MapBackedParams;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PrefixedFieldSetter implements TriConsumer<String, MapBackedParams, List<@Nullable String>> {

    @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader;
    @NonNull DatatypableStringConverter datatypableStringConverter;

    @Override
    public void accept(String prefixedSuffixedSearchFieldName, MapBackedParams params, List<@Nullable String> queryValues) {
        FieldTypeSearchField searchField = fieldTypeSearchFieldLoader.getById(prefixedSuffixedSearchFieldName);
        FieldType<?> fieldType = searchField.fieldType();

        Property<Object> property = SimpleProperty.ofSameIdAndName(prefixedSuffixedSearchFieldName, fieldType.getText());
        Object convertedValue = convertSingleValueOrThrow(queryValues, searchField);
        params.set(property, convertedValue);
    }

    private @Nullable Object convertSingleValueOrThrow(List<@Nullable String> queryValues, FieldTypeSearchField searchField) {
        MoreThanOneItemFoundException.check(queryValues.size(), String.class, "single scalar query value");

        String singleValue = queryValues.getFirst();
        return datatypableStringConverter.convertFromString(singleValue, searchField.indexedValueDatatype());
    }
}