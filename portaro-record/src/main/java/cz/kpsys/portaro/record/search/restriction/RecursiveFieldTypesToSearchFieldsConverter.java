package cz.kpsys.portaro.record.search.restriction;

import com.google.common.collect.Streams;
import cz.kpsys.portaro.record.detail.FieldType;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.stream.Stream;

public class RecursiveFieldTypesToSearchFieldsConverter implements Converter<List<? extends FieldType<?>>, List<FieldTypeSearchField>> {

    @Override
    public List<FieldTypeSearchField> convert(@NonNull List<? extends FieldType<?>> source) {
        return source.stream()
                .flatMap(RecursiveFieldTypesToSearchFieldsConverter::streamSearchFields)
                .toList();
    }

    private static @NonNull Stream<FieldTypeSearchField> streamSearchFields(FieldType<?> fieldType) {
        return Streams.concat(
                getValueSearchField(fieldType),
                getLinkSearchField(fieldType),
                getOriginSearch<PERSON>ield(fieldType),
                getNestedSearchFields(fieldType)
        );
    }

    private static @NonNull Stream<FieldTypeSearchField> getValueSearchField(FieldType<?> fieldType) {
        if (fieldType.isAutonomous() && fieldType.getDatatype().isPresent()) {
            return Stream.of(FieldTypeSearchField.ofValue(fieldType));
        }
        return Stream.empty();
    }

    private static @NonNull Stream<FieldTypeSearchField> getLinkSearchField(FieldType<?> fieldType) {
        if (fieldType.isAutonomous() && fieldType.getLinkRootFond().isPresent()) {
            return Stream.of(FieldTypeSearchField.ofLink(fieldType));
        }
        return Stream.empty();
    }

    private static @NonNull Stream<FieldTypeSearchField> getOriginSearchField(FieldType<?> fieldType) {
        if (fieldType.isAutonomous() && fieldType.getDatatype().isPresent()) {
            return Stream.of(FieldTypeSearchField.ofOrigin(fieldType));
        }
        return Stream.empty();
    }

    private static @NonNull Stream<FieldTypeSearchField> getNestedSearchFields(FieldType<?> fieldType) {
        return fieldType.getSubfieldTypes().stream()
                .flatMap(RecursiveFieldTypesToSearchFieldsConverter::streamSearchFields);
    }
}
