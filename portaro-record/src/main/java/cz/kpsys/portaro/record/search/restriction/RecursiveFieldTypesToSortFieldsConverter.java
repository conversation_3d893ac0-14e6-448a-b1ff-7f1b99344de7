package cz.kpsys.portaro.record.search.restriction;

import com.google.common.collect.Streams;
import cz.kpsys.portaro.record.detail.FieldType;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.stream.Stream;

public class RecursiveFieldTypesToSortFieldsConverter implements Converter<List<? extends FieldType<?>>, List<FieldTypeSortItem>> {

    @Override
    public List<FieldTypeSortItem> convert(@NonNull List<? extends FieldType<?>> source) {
        return source.stream()
                .flatMap(RecursiveFieldTypesToSortFieldsConverter::streamSortFields)
                .toList();
    }

    private static @NonNull Stream<FieldTypeSortItem> streamSortFields(FieldType<?> fieldType) {
        return Streams.concat(
                getSortFields(fieldType),
                getNestedSortFields(fieldType)
        );
    }

    private static @NonNull Stream<FieldTypeSortItem> getSortFields(FieldType<?> fieldType) {
        if (!fieldType.isAutonomous()) {
            return Stream.empty();
        }
        if (fieldType.getDatatype().isEmpty()) {
            return Stream.empty();
        }
        if (fieldType.getCodebook().isPresent()) { // ciselniky nesortujeme
            return Stream.empty();
        }
        return Stream.of(
                FieldTypeSortItem.ofAsc(fieldType),
                FieldTypeSortItem.ofDesc(fieldType)
        );
    }

    private static @NonNull Stream<FieldTypeSortItem> getNestedSortFields(FieldType<?> fieldType) {
        return fieldType.getSubfieldTypes().stream()
                .flatMap(RecursiveFieldTypesToSortFieldsConverter::streamSortFields);
    }
}
