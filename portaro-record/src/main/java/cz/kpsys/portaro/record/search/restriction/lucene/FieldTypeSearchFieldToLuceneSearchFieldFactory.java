package cz.kpsys.portaro.record.search.restriction.lucene;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.record.search.restriction.FieldTypeSearchField;
import cz.kpsys.portaro.record.search.restriction.FieldTypedSearchFieldParsing;
import cz.kpsys.portaro.search.restriction.serialize.lucene.DynamicFieldToLuceneSearchFieldFactory;
import cz.kpsys.portaro.search.restriction.serialize.lucene.LuceneConstants;
import cz.kpsys.portaro.search.restriction.serialize.lucene.LuceneQueryField;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import static cz.kpsys.portaro.record.detail.FieldTypeId.DELIMITER;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldTypeSearchFieldToLuceneSearchFieldFactory implements DynamicFieldToLuceneSearchFieldFactory {

    @NonNull ByIdLoadable<FieldTypeSearchField, String> fieldTypeSearchFieldLoader;

    @Override
    public boolean supports(@NonNull String searchFieldName) {
        return FieldTypedSearchFieldParsing.hasDynamicFieldPrefix(searchFieldName);
    }

    @Override
    public LuceneQueryField getLuceneSearchFieldByExpandedSearchField(@NonNull String prefixedSuffixedSearchFieldName) {
        FieldTypeSearchField searchField = fieldTypeSearchFieldLoader.getById(prefixedSuffixedSearchFieldName);
        String luceneSearchFieldName = toLuceneSearchFieldName(searchField);
        return LuceneQueryField.create(luceneSearchFieldName, searchField.indexedValueDatatype());
    }

    public static @NonNull String toLuceneSearchFieldName(FieldTypeSearchField searchField) {
        String suffix = switch (searchField.subject()) {
            case VALUE -> LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_VALUE_SUFFIX;
            case LINK -> LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_LINK_SUFFIX;
            case ORIGIN -> LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_ORIGIN_SUFFIX;
            default -> throw new IllegalStateException("Unsupported subject " + searchField.subject() + " for search field: " + searchField);
        };
        return LuceneConstants.FIELD_SEARCH_PREFIX +
               searchField.fieldType().getFieldTypeId().value().replace(DELIMITER, LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_FIELDTYPE_DELIMITER) +
               suffix;
    }
}
