package cz.kpsys.portaro.record.search.restriction.lucene;

import cz.kpsys.portaro.record.search.restriction.FieldTypeSortItem;
import cz.kpsys.portaro.search.restriction.serialize.lucene.DynamicFieldToLuceneSortFieldFactory;
import cz.kpsys.portaro.search.restriction.serialize.lucene.LuceneConstants;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import static cz.kpsys.portaro.record.detail.FieldTypeId.DELIMITER;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldTypeSearchFieldToLuceneSortFieldFactory implements DynamicFieldToLuceneSortFieldFactory {

    @Override
    public boolean supports(@NonNull SortingItem sortingItem) {
        return sortingItem instanceof FieldTypeSortItem;
    }

    @Override
    public String getLuceneSearchFieldByExpandedSearchField(@NonNull SortingItem sortingItem) {
        return toLuceneSearchFieldName((FieldTypeSortItem) sortingItem);
    }

    private static @NonNull String toLuceneSearchFieldName(FieldTypeSortItem sortingItem) {
        return LuceneConstants.FIELD_SEARCH_PREFIX +
               sortingItem.fieldType().getFieldTypeId().value().replace(DELIMITER, LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_FIELDTYPE_DELIMITER) +
               LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_SORT_SUFFIX;
    }
}
