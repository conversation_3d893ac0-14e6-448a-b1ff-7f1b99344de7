package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@Tag("ci")
@Tag("unit")
class FieldsSpecTest {

    @Test
    void fieldTypeShouldCoverFieldId() {
        FieldsSpec fieldTypeSpec = FieldsSpec.ofFieldType(FieldTypeId.parse("d830"));
        FieldsSpec fieldIdSpec = FieldsSpec.ofField(FieldId.parse("d830#0"));
        assertTrue(fieldTypeSpec.covers(fieldIdSpec));
    }

    @Test
    void shouldDoNotningWhenAddingFieldIdIntoCoveringFieldType() {
        FieldsSpec unioned = FieldsSpec.ofFieldType(FieldTypeId.parse("d830"))
                .add(FieldSpec.ofField(FieldId.parse("d830#0")));
        assertEquals(1, unioned.existingSpecs().size());
        assertEquals(FieldsSpec.ofFieldType(FieldTypeId.parse("d830")), unioned);
    }

    @Test
    void shouldReplaceItemWhenAddingFieldIdIntoCoveredFieldType() {
        FieldsSpec unioned = FieldsSpec.ofField(FieldId.parse("d830#0"))
                .add(FieldSpec.ofFieldType(FieldTypeId.parse("d830")));
        assertEquals(1, unioned.existingSpecs().size());
        assertEquals(FieldsSpec.ofFieldType(FieldTypeId.parse("d830")), unioned);
    }

    @Test
    void shouldAppendItemWhenAddingFieldIdIntoElseFieldType() {
        FieldsSpec unioned = FieldsSpec.ofField(FieldId.parse("d830#0"))
                .add(FieldSpec.ofFieldType(FieldTypeId.parse("d831")));
        assertEquals(2, unioned.existingSpecs().size());
    }

    @Test
    void shouldNotCoverFieldIdOfAnotherType() {
        FieldsSpec spec = FieldsSpec.ofFieldType(FieldTypeId.parse("d2025"));
        FieldsSpec tested = FieldsSpec.ofField(FieldId.parse("d2040#0.main#0"));
        assertFalse(spec.covers(tested));
    }
}