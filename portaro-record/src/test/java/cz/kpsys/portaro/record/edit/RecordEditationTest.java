package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.InMemoryRepository;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.RecordSaveCommand;
import cz.kpsys.portaro.record.RecordSaver;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.RecordStatusResolver;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.dflt.DefaultFieldValueCommandResolver;
import cz.kpsys.portaro.record.detail.value.StringValueCommand;
import cz.kpsys.portaro.record.edit.fieldshierarchy.FieldValueCommandResolver;
import cz.kpsys.portaro.record.edit.view.UserInteractionRecordEditation;
import cz.kpsys.portaro.record.edit.view.UserInteractionRecordEditationFactory;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.holding.RecordHoldingUpserter;
import cz.kpsys.portaro.record.load.RecursiveRecordFieldsLoader;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.security.StaticSecurityManager;
import cz.kpsys.portaro.user.Person;
import lombok.NonNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cz.kpsys.portaro.record.detail.TestingFieldTypeFactory.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

@Tag("ci")
@Tag("unit")
public class RecordEditationTest {

    public static final Fond PERIO_FOND = Fond.testingPerio();
    public static final Fond MONOGRAPHY_FOND = Fond.testingMonography();
    public static final CurrentAuth currentAuth = createAuth();
    private RecordEditationFactory recordEditationFactory;
    private UserInteractionRecordEditationFactory userInteractionRecordEditationFactory;
    private RecordFieldEditor recordFieldEditor;


    @BeforeEach
    public void setUp() {
        InMemoryRepository<FieldType<?>, String> fieldTypeLoaderByStringId = InMemoryRepository.ofIdentified();
        ByIdLoadable<FieldType<?>, FieldTypeId> fieldTypeLoader = fieldTypeId -> fieldTypeLoaderByStringId.getById(fieldTypeId.value());

        recordFieldEditor = new RecordFieldEditor(
                fieldTypeLoader,
                RecordSaveCommand::record,
                new RecordHoldingUpserter.Testing(),
                DefaultFieldValueCommandResolver.noneReturning(),
                new FieldDependencyResolver(new StaticRecordEntryFieldTypeIdResolver()),
                new StaticRecordEntryFieldTypeIdResolver(),
                Mockito.mock(ParameterizedSearchLoader.class), // TODO: no tests are testing use-case of replacing scalar values with linked record values, so we just provide mocked classes to satisfy constructor
                Mockito.mock(HierarchyLoader.class),
                Mockito.mock(RecordEditationFactory.class),
                InMemoryRepository.ofIdentified()
        );

        fieldTypeLoaderByStringId.save(controlfieldType(FieldTypeId.top("d8")));

        final FieldType<?> type456 = datafieldType(FieldTypeId.top("d456"));
        FieldType<?> type456a = standardSubfieldType(FieldTypeId.top("d456").sub("a"));
        fieldTypeLoaderByStringId.save(type456a);
        type456.addSubfieldType(type456a);
        FieldType<?> type456b = standardSubfieldType(FieldTypeId.top("d456").sub("b"));
        fieldTypeLoaderByStringId.save(type456b);
        type456.addSubfieldType(type456b);
        FieldType<?> type456c = standardSubfieldType(FieldTypeId.top("d456").sub("c"));
        fieldTypeLoaderByStringId.save(type456c);
        type456.addSubfieldType(type456c);
        fieldTypeLoaderByStringId.save(type456);

        final FieldType<?> type789 = datafieldType(FieldTypeId.top("d789"));
        FieldType<?> type789a = standardSubfieldType(FieldTypeId.top("d789").sub("a"));
        fieldTypeLoaderByStringId.save(type789a);
        type789.addSubfieldType(type789a);
        FieldType<?> type789b = standardSubfieldType(FieldTypeId.top("d789").sub("b"));
        fieldTypeLoaderByStringId.save(type789b);
        type789.addSubfieldType(type789b);
        fieldTypeLoaderByStringId.save(type789);

        FieldTypesByFondLoader editableFieldTypesByFondLoader = new FieldTypesByFondLoader() {
            @Override
            public List<EditableFieldType<?>> getTopfieldTypesByFond(@NonNull Fond fond) {
                if (fond.equals(MONOGRAPHY_FOND)) {
                    //cba, vstupni prvek je c
                    //ba
                    Map<FieldTypeId, FieldSettings> configPathToEditableSubfieldSettingMap = Map.of(
                            FieldTypeId.subfield(type456.getFieldTypeId(), "c"), FieldSettings.ofAlwaysVisible(1),
                            FieldTypeId.subfield(type456.getFieldTypeId(), "b"), FieldSettings.ofWhenFilled(2),
                            FieldTypeId.subfield(type456.getFieldTypeId(), "a"), FieldSettings.ofAlwaysVisible(3),
                            FieldTypeId.subfield(type789.getFieldTypeId(), "b"), FieldSettings.ofAlwaysVisible(1),
                            FieldTypeId.subfield(type789.getFieldTypeId(), "a"), FieldSettings.ofAlwaysVisible(2)
                    );
                    return List.of(
                            new EditableFieldType<>(type456, fond, FieldSettings.ofAlwaysVisible(1), new SubfieldTypeToEditableConverter(fond, configPathToEditableSubfieldSettingMap)),
                            new EditableFieldType<>(type789, fond, FieldSettings.ofAlwaysVisible(2), new SubfieldTypeToEditableConverter(fond, configPathToEditableSubfieldSettingMap))
                    );

                }
                if (fond.equals(PERIO_FOND)) {
                    //bac, vstupni prvek je a
                    //ab
                    Map<FieldTypeId, FieldSettings> configPathToEditableSubfieldSettingMap = Map.of(
                            FieldTypeId.subfield(type456.getFieldTypeId(), "b"), FieldSettings.ofAlwaysVisible(1),
                            FieldTypeId.subfield(type456.getFieldTypeId(), "a"), FieldSettings.ofAlwaysVisible(2),
                            FieldTypeId.subfield(type456.getFieldTypeId(), "c"), FieldSettings.ofAlwaysVisible(3),
                            FieldTypeId.subfield(type789.getFieldTypeId(), "a"), FieldSettings.ofAlwaysVisible(1),
                            FieldTypeId.subfield(type789.getFieldTypeId(), "b"), FieldSettings.ofDisabled(2)
                    );
                    return List.of(
                            new EditableFieldType<>(type456, fond, FieldSettings.ofAlwaysVisible(1), new SubfieldTypeToEditableConverter(fond, configPathToEditableSubfieldSettingMap)),
                            new EditableFieldType<>(type789, fond, FieldSettings.ofAlwaysVisible(2), new SubfieldTypeToEditableConverter(fond, configPathToEditableSubfieldSettingMap))
                    );
                }
                throw new IllegalStateException();
            }

            @Override
            public <RET extends EditableFieldType<?>> RET findTopfieldTypeByFondAndId(@NonNull Fond fond, @NonNull FieldTypeId topfieldId, @NonNull WhenMissing<RET> whenMissing) {
                Assert.isTrue(topfieldId.getLevel() == FieldTypeId.LEVEL_TOPFIELD, () -> "FieldTypeId " + topfieldId + " is not topfield!");

                EditableFieldType<?> actual = ListUtil.getByIdOrNull(getTopfieldTypesByFond(fond), topfieldId.toString());
                if (actual != null) {
                    return (RET) actual;
                }
                return whenMissing.onMissing(fond, topfieldId);
            }
        };

        RecordFieldTypesLoader recordEditableFieldTypesLoader = new ByFondLoaderDelegatingRecordFieldTypesLoader(editableFieldTypesByFondLoader);
        StaticRecordEntryFieldTypeIdResolver entryFieldTypeIdResolver = new StaticRecordEntryFieldTypeIdResolver()
                .with(MONOGRAPHY_FOND, type456c.getFieldTypeId())
                .with(PERIO_FOND, type456a.getFieldTypeId());
        ContextualProvider<Department, @NonNull RecordStatus> publishingDocumentRecordStatusProvider = ContextIgnoringContextualProvider.of(RecordStatus.FINISHED_CATALOGING);
        recordEditationFactory = new RecordEditationFactory(
                recordEditableFieldTypesLoader,
                new RecordValidator(entryFieldTypeIdResolver, editableFieldTypesByFondLoader),
                new RecordSaver.Testing(),
                editableFieldTypesByFondLoader,
                publishingDocumentRecordStatusProvider,
                new RecordHoldingUpserter.Testing(),
                Mockito.mock(FieldValueCommandResolver.class),
                fieldTypeLoader,
                recordFieldEditor,
                new RecursiveRecordFieldsLoader(
                        (_) -> List.of(),
                        new AllPassingTestingFieldTypesByFondLoader(new TestingFieldTypeLoader()),
                        new StaticRecordEntryFieldTypeIdResolver()
                ),
                DefaultFieldValueCommandResolver.noneReturning(),
                ContextIgnoringContextualFunction.ofNonNullReturningFunction(_ -> false),
                new RecordStatusResolver(publishingDocumentRecordStatusProvider, _ -> Optional.of(RecordStatus.BACKWARD_CATALOGING), StaticSecurityManager.allPermitting())
        );

        userInteractionRecordEditationFactory = new UserInteractionRecordEditationFactory(recordFieldEditor);
    }


    @Test
    public void testCreateNewDocument() {
        RecordEditation defaultEditation = recordEditationFactory
                .on(Department.testing(1))
                .ofNew(MONOGRAPHY_FOND)
                .build(currentAuth);

        UserInteractionRecordEditation editation = userInteractionRecordEditationFactory.on(Department.testing(1), defaultEditation, currentAuth)
                .withEmptyFieldsAutoCompling(true)
                .withRootFond(Fond.testingPerson())
                .build();


        //zkontrolujeme editable field types
        assertEquals(2, editation.getSubfieldTypes().size());
        assertEquals(List.of("c", "b", "a"), subfieldTypeCodes(editation, 0));
        assertEquals(List.of("b", "a"), subfieldTypeCodes(editation, 1));

        //zkontrolujeme fieldy v zaznamu
        assertFalse(editation.getRecord().getFields().isEmpty());
        assertEquals("d456", editation.getRecord().getFields().getFirst().getCode());
        assertEquals(List.of("c", "a"), subfieldCodes(editation, 0)); //pouze 2 pole - styl zobrazeni podpole b je "neprazdne"
    }


    /**
     * Test, zda se smazou pole, která jsou prázdná, ve starém fondu být mají a v novém fondu být nemají.
     */
    @Test
    public void testChangeFondWillNotRemainEmptyFieldsFromLastFond() {
        RecordEditation defaultEditation = recordEditationFactory
                .on(Department.testing(1))
                .ofNew(MONOGRAPHY_FOND)
                .build(currentAuth);

        UserInteractionRecordEditation editation = userInteractionRecordEditationFactory.on(Department.testing(1), defaultEditation, currentAuth)
                .withEmptyFieldsAutoCompling(true)
                .withRootFond(Fond.testingPerson())
                .build();

        assertEquals("d456", editation.getFields().getFirst().getCode());
        assertEquals(List.of("c", "a"), subfieldCodes(editation, 0));
        assertEquals(List.of("b", "a"), subfieldCodes(editation, 1));

        editation.changeFond(PERIO_FOND); //zmenou na perio vynutime napr. u 456 pridani podpole "b" a prerovnani poradi

        assertEquals("d456", editation.getFields().getFirst().getCode());
        assertEquals(List.of("b", "a", "c"), subfieldCodes(editation, 0));
        assertEquals(List.of("a"), subfieldCodes(editation, 1));
    }


    @Test
    public void testSetFieldValueOfPreCreatedField() {
        RecordEditation defaultEditation = recordEditationFactory
                .on(Department.testing(1))
                .ofNew(MONOGRAPHY_FOND)
                .build(currentAuth);

        UserInteractionRecordEditation editation = userInteractionRecordEditationFactory.on(Department.testing(1), defaultEditation, currentAuth)
                .withEmptyFieldsAutoCompling(true)
                .withRootFond(Fond.testingPerson())
                .build();

        FieldTypeId f456a = FieldTypeId.parse("d456.a");
        FieldEditationCommand command = FieldEditationCommand.of(editation.getRecord(), f456a.toFieldIdWithAllFirstIndices(), new StringValueCommand("karel", Department.testing(1), currentAuth));
        recordFieldEditor.editField(editation, command);

        assertEquals("karel", editation.getRecord().getDetail().getFirstField(By.code("d456"), By.code("a")).orElseThrow().getRaw());
    }


    @Test
    public void testSetFieldValueOfNotPreCreatedField() {
        RecordEditation defaultEditation = recordEditationFactory
                .on(Department.testing(1))
                .ofNew(MONOGRAPHY_FOND)
                .build(currentAuth);

        UserInteractionRecordEditation editation = userInteractionRecordEditationFactory.on(Department.testing(1), defaultEditation, currentAuth)
                .withEmptyFieldsAutoCompling(false)
                .withRootFond(Fond.testingPerson())
                .build();

        FieldTypeId f456a = FieldTypeId.parse("d456.a");
        FieldEditationCommand command = FieldEditationCommand.of(editation.getRecord(), f456a.toFieldIdWithAllFirstIndices(), new StringValueCommand("karel", Department.testing(1), currentAuth))
                .createMissingHierarchy();
        recordFieldEditor.editField(editation, command);

        assertEquals("karel", editation.getRecord().getDetail().getFirstField(By.code("d456"), By.code("a")).orElseThrow().getRaw());
    }


    @Test
    public void testMoveSecondSubfieldUp() {
        RecordEditation defaultEditation = recordEditationFactory
                .on(Department.testing(1))
                .ofNew(PERIO_FOND)
                .build(currentAuth);

        UserInteractionRecordEditation editation = userInteractionRecordEditationFactory.on(Department.testing(1), defaultEditation, currentAuth)
                .withEmptyFieldsAutoCompling(true)
                .withRootFond(Fond.testingPerson())
                .build();

        //momentalne je poradi bac (pro fond 3)
        new RecordFieldMover().moveField(editation.getRecord().getDetail(), FieldMovementCommand.up(FieldId.parse("d456#0.a#0")));
        //nyni musi byt poradi abc

        assertEquals(List.of("a", "b", "c"), subfieldCodes(editation, 0));
    }


    @Test
    public void testCannotMoveFirstSubfieldUp() {
        RecordEditation defaultEditation = recordEditationFactory
                .on(Department.testing(1))
                .ofNew(PERIO_FOND)
                .build(currentAuth);

        UserInteractionRecordEditation editation = userInteractionRecordEditationFactory.on(Department.testing(1), defaultEditation, currentAuth)
                .withEmptyFieldsAutoCompling(true)
                .withRootFond(Fond.testingPerson())
                .build();

        //momentalne je poradi bac (pro fond 3)
        new RecordFieldMover().moveField(editation.getRecord().getDetail(), FieldMovementCommand.up(FieldId.parse("d456#0.b#0")));
        //nyni musi byt poradi nezmenene, tedy bac

        //nyni musi byt prvni a to co bylo prvni ted musi byt druhe
        assertEquals(List.of("b", "a", "c"), subfieldCodes(editation, 0));
    }


    private List<String> subfieldTypeCodes(RecordEditation editation, int fieldTypeIndex) {
        return editation.getSubfieldTypes().get(fieldTypeIndex)
                .getSubfieldTypes()
                .stream()
                .map(FieldType::getCode)
                .toList();
    }


    private List<String> subfieldCodes(RecordEditation editation, int ofFieldIndex) {
        return editation.getFields().get(ofFieldIndex)
                .streamFields()
                .map(Field::getCode)
                .toList();
    }

    private static CurrentAuth createAuth() {
        return CurrentAuth.createWithAbsoluteAuthenticity(Person.testing(123));
    }

}
