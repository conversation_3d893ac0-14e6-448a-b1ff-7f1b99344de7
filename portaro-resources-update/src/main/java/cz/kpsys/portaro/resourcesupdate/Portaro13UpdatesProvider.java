package cz.kpsys.portaro.resourcesupdate;

import cz.kpsys.portaro.commons.file.FileUtils;
import cz.kpsys.portaro.commons.io.FileZipperImpl;
import cz.kpsys.portaro.commons.object.ProxiedList;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.databaseproperties.DatabaseConnectionSettings;
import cz.kpsys.portaro.resourcesupdate.db.*;
import cz.kpsys.portaro.resourcesupdate.db.firebird.FirebirdSequenceIdTriggerGenerator;
import cz.kpsys.portaro.resourcesupdate.db.firebird.FirebirdUniqueKeyGenerator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cz.kpsys.portaro.commons.file.FileUtils.path;
import static org.apache.commons.io.FileUtils.listFiles;

// @formatter:off
@SuppressWarnings({
        "checkstyle:EmptyLineSeparator",
        "checkstyle:WhitespaceAround",
        "checkstyle:MemberName",
        "checkstyle:WhitespaceAfter",
        "SpellCheckingInspection"
})
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class Portaro13UpdatesProvider implements UpdatesProvider {

    @NonNull JdbcTemplate jdbcTemplate;
    @Nullable String customFolderPath;
    @NonNull String tomcatFolderPath;
    @NonNull UpdateFactory f;
    @NonNull DbFileUpdateFactory ff;
    @NonFinal List<VersionedUpdateProxy> updates;

    public Portaro13UpdatesProvider(@NonNull UpdateFactory updateFactory,
                                    @NonNull JdbcTemplate jdbcTemplate,
                                    @Nullable String customFolderPath,
                                    @NonNull DbFileUpdateFactory ff) {
        this.ff = ff;
        this.tomcatFolderPath = System.getProperty("catalina.base");
        this.f = updateFactory;
        this.jdbcTemplate = jdbcTemplate;
        this.customFolderPath = customFolderPath;
    }

    @Override
    public Iterable<VersionedUpdateProxy> getUpdates() {
        updates = new ArrayList<>(6000);
        fill1();
        fill2();
        return updates;
    }


    private void add(int version, Runnable update) {
        updates.add(new VersionedUpdateProxy(version, update));
    }


    private void fill1() {
        add(1, f.insertMessage("commons.zpet", "Zpět", "Back"));
        add(1, f.insertMessage("commons.vlastnost", "Vlastnost", "Property"));
        add(1, f.insertMessage("commons.hodnota", "Hodnota", "Value"));
        add(1, f.insertMessage("editace.editaceExemplare", "Editace exempláře", "Exemplar editation"));
        add(1, f.insertMessage("editace.editaceExemplareSCarovymKodem", "Editace exempláře s čárovým kódem {0}", "Exemplar with bar code {0} editation"));
        add(1, f.insertMessage("oblibene.oblibene", "Oblíbené", "Favourites"));
        add(1, f.insertMessage("oblibene.1ZaznamVOblibenych", "1 záznam v oblíbených", "1 item in favourites"));
        add(1, f.insertMessage("oblibene.xZaznamyVOblibenych", "{0} záznamy v oblíbených", "{0} items in favourites"));
        add(1, f.insertMessage("oblibene.xZaznamuVOblibenych", "{0} záznamů v oblíbených", "{0} items in favourites"));
        add(1, f.insertMessage("oblibene.vOblibenychNejsouZadneZaznamy", "Žádné záznamy", "No items"));
        add(1, f.insertMessage("oblibene.vyprazdnit", "Smazat vše", "Remove all"));
        add(1, f.insertMessage("oblibene.zobrazitVse", "Zobrazit vše", "Show all"));
        add(1, f.insertMessage("oblibene.pridatVseVyhledane", "Přidat vyhledané", "Add all searched"));
        add(1, f.insertMessage("oblibene.zOblibenych", "Z oblíbených", "From favourites"));
        add(1, f.insertMessage("oblibene.doOblibenych", "Do oblíbených", "Into favourites"));
        add(2, f.insertMessage("message.exception.keyCannotBeEmpty", "Klíč nesmí být prázdný!", "key cannot be empty!"));
        add(4, f.insertMessage("message.appServer.nelzeProlongovatPrekrocenTerminVraceni", "Nelze prodloužit, překročen termín vrácení", "Cannot renew, return time exceeded"));
        add(5, f.insertMessage("detail.nahratObalku", "Nahrát obálku", "Upload cover"));
        add(6, f.insertMessage("login.prihlaseniLdapem", "Přihlášení pomocí centrálního systému", "Login with central system"));
        add(7, f.insertMessage("header.POZNAMKA", "Poznámka", "Note"));
        add(7, f.insertMessage("header.STATUS", "Status", "Status"));
        add(7, f.insertMessage("message.appServer.nelzeVypujcitBudovaJeOffline", "Nelze vypůjčit dokument který je umístěn na off-line budově!", "Cannot lent document on offline building"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitPrekrocenMaxPocetVypCtenare", "Překročen maximální povolený počet výpůjček čtenáři, opravdu půjčit další?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitPrekrocenMaxPocetVypProTutoKategorii", "Překročen maximální povolený počet výpůjček pro danou kategorii výpůjčky, opravdu půjčit další?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitDokumentVypujcenVMinulostiNaposledyX", "Čtenář měl již dokumet vypůjčen v minulosti (naposledy {0}), opravdu vypůjčit znovu?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitCtenarMaTitulAktualneVypujcen", "Čtenář má titul aktálně vypůjčen, opravdu vypůjčit znovu?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitCtenarMaUpominky", "Čtenář má upomínané výpůjčky, opravdu půjčit další?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitCtenarMaDluhy", "Čtenář má dluhy, opravdu provést výpůjčku?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitZvlastniInfoKLokaci", "Pozor - zvláštní informace k lokaci dokumentu: {0} ...Opravdu vypůjčit?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitZvlastniInfoKeStatusu", "Pozor - zvláštní informace ke statusu dokumentu: {0} ...Opravdu vypůjčit?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitZvlastniInfoKeKategorii", "Pozor - zvláštní informace ke kategorii dokumentu: {0} ...Opravdu vypůjčit?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitStatusJeZpetnaKatalogizace", "Dokument byl zpracován zpětnou katalogizací, nejprve by měl být zpracován, chcete jej půjčit?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitCtenarMaPropadlouRegistraci", "Čtenář nemá platnou registraci, opravdu půjčit exemplář?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitDokumentMaPrilohu", "Dokument má přílohu! Opravdu vypůjčit?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitNaExemplarJeNeodeslanaRezervace", "Na exemplář je neodeslaná rezervace! Opravdu vypůjčit?"));
        add(7, f.insertMessage("message.appServer.dotazOpravduVypujcitDokumentJeZJineBudovy", "POZOR! Dokument je z jiné budovy! Opravdu vypůjčit?"));
        add(8, f.insertMessage("commons.ANO", "ANO", "YES"));
        add(8, f.insertMessage("commons.NE", "NE", "NO"));
        add(9, f.insertMessage("message.appServer.dotazOpravduVratitDokumentJeZJineBudovy", "POZOR! Dokument je z jiné budovy! Opravdu vrátit?"));
        add(9, f.insertMessage("message.appServer.dotazOpravduVratitDokumentJeZJineLokace", "POZOR! Dokument je z jiné lokace! Opravdu vrátit?"));
        add(9, f.insertMessage("message.appServer.dotazOpravduVratitDokumentMaPrilohu", "Dokument má přílohu! Opravdu vrátit?"));
        add(9, f.insertMessage("message.appServer.dotazOpravduVratitCtenarMaUpominky", "Čtenář má upomínané výpůjčky, opravdu vrátit?"));
        add(9, f.insertMessage("message.appServer.dotazOpravduVratitZvlastniInfoKLokaci", "Pozor - zvláštní informace k lokaci dokumentu: {0} ...Opravdu vrátit?"));
        add(9, f.insertMessage("message.appServer.dotazOpravduVratitZvlastniInfoKeStatusu", "Pozor - zvláštní informace ke statusu dokumentu: {0} ...Opravdu vrátit?"));
        add(9, f.insertMessage("message.appServer.dotazOpravduVratitZvlastniInfoKeKategorii", "Pozor - zvláštní informace ke kategorii dokumentu: {0} ...Opravdu vrátit?"));
        add(9, f.insertMessage("message.appServer.dotazOpravduVratitStatusJeZpetnaKatalogizace", "Dokument byl zpracován zpětnou katalogizací, nejprve by měl být zpracován, chcete jej vrátit?"));
        add(9, f.insertMessage("message.appServer.dotazOpravduVratitNaExemplarJeNeodeslanaRezervace", "Na exemplář je neodeslaná rezervace! Opravdu vrátit?"));
        add(10, f.insertMessage("message.appServer.dotazOpravduVypujcitDokumentJeZJineLokace", "POZOR! Dokument je z jiné lokace! Opravdu vypůjčit?"));
        add(11, f.insertMessage("commons.CustomSoubory", "Editace designu", "Design editation"));
        add(12, f.updateMessage("message.validation.someFieldsNotFilled", "Vyplňte všechna povinná pole", "Fill all mandatory fields"));
        add(13, f.insertMessage("message.exception.proTentoFondNeniNastavenFiltrPoli", "Pro tento fond není nastaven seznam zobrazovaných polí. Prosíme, nastavte ho v ini", "List of showing fields is not set for this fond. Set it in ini, please."));
        add(14, f.sql("INSERT INTO INI_SEKCE (poradi, id_sekce, suffix_num, limit_od, limit_do) values (30, 'EVERBIS', 0, null, null)"));
        add(14, f.insertIniKey("EVERBIS", "EditovatelnaPolePodleFondu", 20, "Seznam polí, která se budou zobrazovat v editačním formuláři podle jednotlivých fondů", null, "1(245^abnpch; 240^a; 250^a; 242^abnp; 100^a7; 700^a; 110^a; 111^a; 260^abc; 300^abe; 440^avx; 500^a; 501^a; 504^a; 505^a; 530^a; 546^a; 520^a; 525^a; 20^az; 650^a;) 3(245^ahbnpc; 242^ab; 247^a; 240^a; 246^a; 100^a; 700^a4; 110^a; 110^a; 260^abc; 500^a; 653^a; 41^a; 44^a; 520^a; 22^a; 506^a; 525^a; 530^au; 515^a; 547^a; 580^a; 550^a; 538^a; 250^a; 300^a; 310^a; 362^a; 650^a; 655^a; 710^a; 711^a; 780^atx; 785^ax;)"));
        add(15, f.insertMessage("ctenari.hledatNaVsechBudovach", "Zobrazit čtenáře na všech budovách", "Show readers on all buildings"));
        add(16, f.insertMessage("statistiky.statistikyPouzivaniZKlienta", "Statistiky využívání modulu Z-klient", "Statistics of Z-client module usage"));
        add(16, f.insertMessage("commons.Mesic", "Měsíc", "Month"));
        add(16, f.insertMessage("statistiky.zServerConnectionString", "Server:DB", "Server:DB"));
        add(16, f.insertMessage("statistiky.pocetHledani", "Počet hledání", "Number of searches"));
        add(16, f.insertMessage("statistiky.pocetUspesnychHledani", "Počet úspěšných hledání", "Number of succesful searches"));
        add(16, f.insertMessage("statistiky.pocetPouzitychZaznamu", "Počet použitých záznamů", "Number of used records"));
        add(17, f.insertMessage("editace.editaceCtenare", "Editace čtenáře", "Reader editation"));
        add(18, f.insertMessage("editace.zobrazitKontoCtenare", "Zobrazit konto čtenáře", "Show reader accout"));
        add(19, f.sql("UPDATE INI_KEYS SET DEFVAL='Username-Password', POPIS = 'Způsob klasického přhlašování čtenářů (tzn. z databáze). Může jich být i více, pak bude systém dané způsoby zkoušet postupně dokud se nepřihlásí.' WHERE ID_KLIC = 'ZPUSOB_PRIHLASENI_CTENARU'"));
        add(20, f.sql("UPDATE INI_KEYS SET FK_VALHOD = NULL WHERE ID_KLIC = 'ZPUSOB_PRIHLASENI_CTENARU'"));
        add(21, f.updateMessageKey("header.PRIR_CISLO", "exemplar.prirCislo"));
        add(22, f.updateMessageKey("header.SIGNATURA", "exemplar.signatura"));
        add(23, f.updateMessageKey("header.BAR_COD", "exemplar.carovyKod"));
        add(24, f.updateMessageKey("header.LOKACE", "exemplar.lokace"));
        add(25, f.updateMessageKey("header.KATEGORIE", "exemplar.kategorieVypujcky"));
        add(26, f.updateMessageKey("header.DOSTUPNOST", "exemplar.dostupnost"));
        add(27, f.updateMessageKey("header.BUDOVA", "exemplar.budova"));
        add(28, f.updateMessageKey("header.TEM_SKUP", "exemplar.tematickaSkupina"));
        add(29, f.updateMessageKey("header.STATUS", "exemplar.status"));
        add(30, f.updateMessageKey("header.POZNAMKA", "exemplar.poznamka"));
        add(31, f.updateMessageKey("header.CENA", "exemplar.cena"));
        add(32, f.updateMessageKey("header.UZIVATEL", "exemplar.majitel"));
        add(33, f.updateMessageKey("header.ROK_PRIR", "exemplar.rokPrirustku"));
        add(34, f.updateMessageKey("header.ZPUSOB_NABYTI", "exemplar.zpusobNabyti"));
        add(35, f.updateMessageKey("header.INDIVIDUALNI_HODNOTA", "exemplar.individualniHodnota"));
        add(36, f.updateMessageKey("header.DATUM", "exemplar.datum"));
        add(37, f.updateMessageKey("header.UMISTENI", "exemplar.umisteni"));
        add(38, f.updateMessageKey("header.CISLO", "exemplar.cislo"));
        add(39, f.updateMessageKey("header.CLANKY", "exemplar.clanky"));
        add(40, f.updateMessageKey("header.ROCNIK", "exemplar.rocnik"));
        add(41, f.updateMessageKey("header.ROK", "exemplar.rok"));
        add(42, f.updateMessageKey("header.ROZMEZI_CISEL", "exemplar.rozmeziCisel"));
        add(43, f.insertMessage("ctenar.cisloLegitimace", "Číslo legitimace", "Legitimation number"));
        add(43, f.insertMessage("ctenar.carovyKod", "Čárový kód", "Bar code"));
        add(43, f.insertMessage("ctenar.jmeno", "Jméno", "First name"));
        add(43, f.insertMessage("ctenar.prijmeni", "Příjmení", "Surname"));
        add(43, f.insertMessage("ctenar.email", "Email", "Email"));
        add(43, f.insertMessage("ctenar.titul", "Titul", "Degree"));
        add(43, f.insertMessage("ctenar.datumRegistrace", "Datum registrace", "Registration date"));
        add(43, f.insertMessage("ctenar.datumVyprseniRegistrace", "Datum vypršení registrace", "Registration expiration date"));
        add(43, f.insertMessage("ctenar.datumNarozeni", "Datum narození", "Birth date"));
        add(43, f.insertMessage("ctenar.kategorieCtenare", "Kategorie čtenáře", "Reader category"));
        add(43, f.insertMessage("ctenar.typBlokace", "Typ blokace", "Blocking type"));
        add(43, f.insertMessage("ctenar.trvaleMesto", "Trvalé bydliště - město", "Permanent address - city"));
        add(43, f.insertMessage("ctenar.trvalaUlice", "Trvalé bydliště - ulice", "Permanent address - street"));
        add(43, f.insertMessage("ctenar.trvalePSC", "Trvalé bydliště - PSČ", "Permanent address - zip code"));
        add(43, f.insertMessage("ctenar.prechodneMesto", "Přechodné bydliště -", "Temporary address - city"));
        add(43, f.insertMessage("ctenar.prechodnaUlice", "Přechodné bydliště -", "Temporary address - street"));
        add(43, f.insertMessage("ctenar.prechodnePsc", "Přechodné bydliště -", "Temporary address - zip code"));
        add(43, f.insertMessage("ctenar.zapnutaPrechodnaAdresaProTisk", "Přechodná adresa pro tisk", "Temporary address for printing"));
        add(43, f.insertMessage("ctenar.zamestnani", "Zaměstnání", "Employment"));
        add(43, f.insertMessage("ctenar.adresaZamestnani", "Adresa zaměstnání", "Employment adress"));
        add(43, f.insertMessage("ctenar.vzdelani", "Vzdělání", "Education"));
        add(43, f.insertMessage("ctenar.trida", "Třída", "Class"));
        add(43, f.insertMessage("ctenar.cisloObcanskehoPrukazu", "Číslo OP", "Identity card number"));
        add(43, f.insertMessage("ctenar.poznamka", "Poznámka", "Note"));
        add(43, f.insertMessage("ctenar.smsCislo", "SMS číslo", "SMS number"));
        add(43, f.insertMessage("ctenar.telefon", "Telefonní číslo", "Telephone number"));
        add(43, f.insertMessage("ctenar.pujcovna", "Půjčovna", "Rent"));
        add(43, f.insertMessage("ctenar.bakalari", "Bakaláři", "Bachelors"));
        add(43, f.insertMessage("ctenar.vzkaz", "Vzkaz", "Message"));
        add(43, f.insertMessage("ctenar.blokovanInternet", "Blokován internet", "Internet blocking"));
        add(43, f.insertMessage("ctenar.typTiskuRezervaci", "Typ tisku rezervací", "Holds print type"));
        add(43, f.insertMessage("ctenar.typTiskuUpominek", "Typ tisku upomínek", "Overdue notices print type"));
        add(43, f.insertMessage("ctenar.guId", "GU ID", "GU ID"));
        add(43, f.insertMessage("ctenar.netId", "NET ID", "NET ID"));
        add(43, f.insertMessage("ctenar.posilanyPredupominky", "Posílání předupomínek", "Pre-overdue notices sending"));
        add(43, f.insertMessage("ctenar.povolenSelfCheck", "Povolen self-check", "Self-check enabled"));
        add(43, f.insertMessage("ctenar.rfidUserId", "RFID", "RFID"));
        add(44, f.updateMessage("ctenar.prechodneMesto", "Přechodné bydliště - město", "Permanent address - city"));
        add(44, f.updateMessage("ctenar.prechodnaUlice", "Přechodné bydliště - ulice", "Permanent address - street"));
        add(44, f.updateMessage("ctenar.prechodnePsc", "Přechodné bydliště - PSČ", "Permanent address - zip code"));
        add(45, f.insertMessage("ctenar.neblokovanStdCtenar", "Standardní neblokovaný čtenář", "Standard non-blocked reader"));
        add(45, f.insertMessage("ctenar.smazanyCtenar", "Smazaný čtenář", "Deleted reader"));
        add(45, f.insertMessage("ctenar.knihovna", "Knihovna", "Library"));
        add(46, f.insertMessage("ctenar.blokovanStdCtenar", "Standardní blokovaný čtenář", "Standard blocked reader"));
        add(47, f.insertMessage("editace.VytvoritNovehoCtenare", "Vytvořit nového čtenáře", "Create new reader"));
        add(48, f.insertMessage("ctenar.tiskPostou", "Poštou - fyzický tisk na tiskárnu", "By post - physical print on printer"));
        add(48, f.insertMessage("ctenar.tiskEmailem", "E-mailem - jako příloha", "By email - as attachment"));
        add(48, f.insertMessage("ctenar.tiskSMSZpravou", "SMS zprávou", "By SMS"));
        add(49, f.insertMessage("editace.ctenarSmazan", "Čtenář byl úspěšně smazán.", "Reader was successfully deleted."));
        add(49, f.insertMessage("message.appServer.nelzeSmazatCtenareMaDluhy", "Nelze smazat čtenáře, má dluhy!", "Cannot delete reader, he has debts!"));
        add(50, f.insertMessage("message.appServer.nelzeSmazatCtenareMaAktivniVypujcky", "Nelze smazat čtenáře, má aktivní výpůjčky!", "Cannot delete reader, he has active loans!"));
        add(50, f.insertMessage("message.appServer.dotazOpravduSmazatCtenareMaDluhNaRegPopl", "Opravdu smazat čtenáře? Má dluh na registračním poplatku.", "Really delete reader? He has doubt on registration charge."));
        add(50, f.insertMessage("message.appServer.dotazOpravduSmazatCtenareMaPripraveneRezervace", "Opravdu smazat čtenáře? Má připravené rezervace", "Really delete reader? He has prepared reservations."));
        add(50, f.insertMessage("message.appServer.dotazOpravduSmazatCtenareMaRezervace", "Opravdu smazat čtenáře? Má rezervace.", "Really delete reader? He has reservations."));
        add(51, f.insertMessage("ctenar.budovy", "Čtenářovy budovy", "Buildings"));
        add(52, f.sql("GRANT SELECT,INSERT,UPDATE,DELETE ON DEF_CTEN_CISLEG TO OPAC"));
        add(53, f.insertMessage("editace.vygenerovatCisloLegitimace", "Vygenerovat číslo legitimace", "Generate legitimation number."));
        add(54, f.insertMessage("login.jsemZamestnanecKnihovny", "Jsem zaměstnanec knihovny", "I am a library employee"));
        add(55, f.updateMessage("detail.zadejteMailNaKterySeMaZaznamPoslat", "Zadejte email, na který se má obsah poslat", "Fill e-mail adress for content delivering"));
        add(55, f.updateMessage("detail.mailSeZaznamemBylUspesneOdeslan", "Email byl úspěšně odeslán.", "Email was successfully sent."));
        add(56, f.insertMessage("mail.informaceODokumentuX", "Informace o dokumentu {0}", "Information about document {0}"));
        add(56, f.insertMessage("mail.nazevXPodtitulYAutorZLinkR", "Název dokumentu: {0}\nPodtitul: {1}\nAutor: {2}\nOdkaz na záznam: {3}", "Document name: {0}\nSubtitle: {1}\nAuthor: {2}\nRecord address: {3}"));
        add(56, f.insertMessage("mail.oblibeneMailSubject", "Záznamy v seznamu oblíbených dokumentů", "Records in favourite documents list"));
        add(57, f.insertIniKey("OPAC", "PerzistentniOblibene", 300, "Zda se mají oblíbené dokument ukládat do databáze, či mají být jen v rámci session", 1, "ANO"));
        add(59, f.insertIniKey("OPAC", "ZobrazovanaPoleDokumentu", 320, "Seznam polí a podpolí podle čísel fondů, která se budou zobrazovat v detailu záznamu. Má smysl, pouze pokud je vypnuto zobrazování všech polí", null, null));
        add(60, f.disableIniTriggers());
        add(60, f.sql("DELETE FROM INI_FILE WHERE FK_SEKCE = 'EVERBIS'"));
        add(61, f.sql("DELETE FROM INI_KEYS WHERE FK_SEKCE = 'EVERBIS'"));
        add(62, f.sql("GRANT SELECT ON INI_KEYS TO OPAC"));
        add(63, f.insertMessage("login.ldap.usernameLabel", "Uživatelské jméno", "Username"));
        add(64, f.insertMessage("login.ldap.passwordLabel", "Heslo", "Password"));
        add(65, f.insertMessage("login.ldap.loginButton", "Přihlásit se", "Login"));
        add(66, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (25, 1, 'off', 'Objednávání vypnuto')"));
        add(67, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (25, 2, 'classic', 'Běžný způsob objednávání')"));
        add(68, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (25, 3, 'mail', 'Objednávání odesláním emailu')"));
        add(69, f.insertIniKey("OPAC", "ZpusobObjednavani", 350, "Způsob, jakým se bude na webu objednávat. Aby se tento klíč bral v úvahu, musí být zapnuto VYPUC.POVOLIT_OBJEDNAVKY.", 25, "classic"));
        add(70, f.insertMessage("mail.ReceiverEmail", "Komu", "To"));
        add(70, f.insertMessage("mail.Subject", "Předmět", "Subject"));
        add(70, f.insertMessage("mail.SenderEmail", "Od", "From"));
        add(70, f.insertMessage("mail.SenderName", "Vaše jméno", "Your name"));
        add(70, f.insertMessage("mail.Body", "Zpráva", "Message"));
        add(70, f.insertMessage("mail.EnterAddressForSendEmail", "Zadejte adresu, na kterou se má email poslat", "Enter the address where to send email."));
        add(70, f.insertMessage("mail.MailWasSuccessfullySent", "Email byl úspěšně odeslán.", "Email was sent successfully."));
        add(70, f.insertMessage("mail.Send", "Odeslat email", "Send email"));
        add(71, f.sql("DELETE FROM LOKALIZACE WHERE ID_LOKALIZACE = 'detail.zadejteMailNaKterySeMaZaznamPoslat'"));
        add(71, f.sql("DELETE FROM LOKALIZACE WHERE ID_LOKALIZACE = 'detail.mailSeZaznamemBylUspesneOdeslan'"));
        add(72, f.insertIniKey("OPAC", "EmailPrijemceObjednavek", 360, "Emailová adresa, na kterou se budou posílat objednávky. Aby se tento klíč bral v úvahu, musí být klíč OPAC.ZpusobObjednavani nastaven na mail.", null, null));
        add(73, f.insertIniKey("OPAC", "URL", 5, "URL adresa Portara, nutno zadat vcetne http:// a bez lomitka na konci", null, null));
        add(74, f.insertMessage("mail.OdeslatEmailSObednavkou", "Odeslat email s objednávkou", "Send email with order"));
        add(75, f.insertMessage("hledani.nenalezenaZadnaVypujcka", "Nenalezena žádná výpůjčka", "No loan found"));
        add(76, f.sql("UPDATE INI_KEYS SET POPIS = 'Seznam statusů exemplářů, které budou zobrazovány' WHERE ID_KLIC = 'STATUSY_ZOBRAZOVANYCH_EXEMPLARU'"));
        add(77, f.insertMessage("mail.PoznamkaFormuEmailSObjednavkou", " ", " "));
        add(78, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (26, 1, 'Off', 'Vypnuté komentáře')"));
        add(79, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (26, 2, 'LoggedUsers', 'Komentovat mohou pouze přihlášení uživatelé')"));
        add(90, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (26, 3, 'Everybody', 'Komentovat mohou všichni (i nepřihlášení uživatelé)')"));
        add(91, f.insertIniKey("OPAC", "StylKomentaru", 251, "Způsob, jakým budou na webu umožněny komentáře.", 26, "Everybody"));
        add(92, f.disableIniTriggers());
        add(92, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'zapnutyAnonymniKomentare'"));
        add(93, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'zapnutyAnonymniKomentare'"));
        add(94, f.insertMessage("exemplar.typCisla.StandardniPrirustek", "Standardní přírůstek", "Standard exemplar"));
        add(94, f.insertMessage("exemplar.typCisla.Issue", "Číslo časopisu", "Issue"));
        add(94, f.insertMessage("exemplar.typCisla.Vazba", "Vazba", "Binding"));
        add(95, f.updateMessageKey("exemplar.datum", "exemplar.datumPorizeni"));
        add(96, f.insertMessage("exemplar.cisloFaktury", "Číslo faktury", "Invoice number"));
        add(96, f.insertMessage("exemplar.ubytek", "Úbytek", "Decrease"));
        add(96, f.insertMessage("exemplar.polozkaFaktury", "Položka faktury", "Invoice item"));
        add(96, f.insertMessage("exemplar.prilohy", "Přílohy", "Attachments"));
        add(96, f.insertMessage("exemplar.poradi", "Pořadí", "Order"));
        add(96, f.insertMessage("exemplar.dodavatel", "Dodavatel", "Supplier"));
        add(96, f.insertMessage("exemplar.typCisla", "Typ čísla", "Issue type"));
        add(96, f.insertMessage("exemplar.pocetKusu", "Počet kusů", "Number of pieces"));
        add(96, f.insertMessage("exemplar.muzejniRocnik", "Ročník muzejního exempláře", "Museum exemplar volume"));
        add(96, f.insertMessage("exemplar.rokMuzejnihoRocniku", "Rok muzejního ročníku", "Year of museum volume"));
        add(96, f.insertMessage("exemplar.rozmeziCiselMuzejnihoRocniku", "Rozmezí čísel muzejného ročníku", "Issue range of museum volume"));
        add(96, f.insertMessage("exemplar.casPosledniZmeny", "Čas poslední změny", "Last change time"));
        add(96, f.insertMessage("exemplar.ucet", "Účet", "Account"));
        add(96, f.insertMessage("exemplar.zpusobZalozeni", "Způsob založení", "Way of foundation"));
        add(97, f.grantSelect("DEF_UCTY", "opac"));
        add(98, f.grantSelect("DEF_ZPUS_ZAL_EXEMP", "opac"));
        add(99, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY", "var(fond)|<br/>|250^^;^^|260^ ^;^^|300^<br/>^;^a^|700^<br/>{detail.ostatniAutori}: ^;^a^|44^<br/>^;^^"));
        add(100, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-HLEDANI_KOMPAKTNI", "var(fond)|260^^^^"));
        add(101, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-HLEDANI_PLNE", "var(fond)|<br/>|260^^^^|910^<br/>{detail.signatura}: ^;^b^"));
        add(102, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY", "$fond|$nl|250^^;^^|260^ ^;^^|300^<br/>^;^a^|700^<br/>{detail.ostatniAutori}: ^;^a^|44^<br/>^;^^"));
        add(103, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-HLEDANI_KOMPAKTNI", "$fond|260^^^^"));
        add(104, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-HLEDANI_PLNE", "$fond|$nl|260^^^^|910^<br/>{detail.signatura}: ^;^b^"));
        add(105, f.insertMessage("detail.exemplare", "Exempláře", "Exemplars"));
        add(106, f.insertMessage("editace.novyDokument", "Nový dokument", "New document"));
        add(106, f.insertMessage("editace.novaAutorita", "Nová autorita", "New authority"));
        add(107, f.deleteMessage("editace.novyZaznam"));
        add(108, f.insertMessage("editace.Katalogizace", "Katalogizace", "Cataloging"));
        add(109, f.grantSelect("DEF_AKCE", "opac"));
        add(110, f.grantSelect("AKCE_PRAVA", "opac"));
        add(111, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (25, 4, 'mailAlways', 'Objednávání odesláním emailu i když není dokument volný nebo nemá exempláře')"));
        add(112, f.insertMessage("editace.Vygenerovat", "Vygenerovat", "Generate"));
        add(113, f.deleteMessage("editace.vygenerovatPrirCislo"));
        add(114, f.deleteMessage("editace.vygenerovatCisloLegitimace"));
        add(115, f.grantSelect("DEF_PUJC_CISLEG", "opac"));
        add(116, f.insertMessage("login.loginPage.usernameLabel", "Uživatelské jméno", "Heslo"));
        add(117, f.insertMessage("login.loginPage.passwordLabel", "Heslo", "Password"));
        add(118, f.insertMessage("hledani.title.zserverX", "Hledání v Z-serveru", "Search in Z-server"));
        add(119, f.insertMessage("hledani.title.zserver", "Hledání v Z-serveru {0}", "Search in Z-server {0}"));
        add(120, f.insertMessage("message.appServer.nelzeDohledatNevyrizenyPozadavekNaRezervaci", "Nelze dohledat nevyřízený požadavek na rezervaci dle zadání", "Cannot find that reservation request"));
        add(121, f.insertMessage("message.appServer.ctenarBlokovanNeboSmazan", "Čtenář blokován, nebo smazán", "Reader is blocked or deleted"));
        add(122, f.insertMessage("message.appServer.nelzeRezervovatVolne", "Nelze rezervovat volné", "Cannot reserve free"));
        add(123, f.insertMessage("message.appServer.ctenarMaTitulJizVypujcenNeboRezervovan", "Čtenář má aktuálně titul vypůjčen nebo rezervován", "Reader has already loaned or reservated book"));
        add(124, f.insertMessage("util.EditaceRezu", "Nastavení řezů", "Facets settings"));
        add(125, f.insertMessage("commons.Poradi", "Pořadí", "Order"));
        add(126, f.insertMessage("util.TypDefinice", "Typ definice", "Definition type"));
        add(127, f.insertMessage("util.TypDefinice.List", "Seznam polí", "Field list"));
        add(128, f.insertMessage("util.TypDefinice.Script", "Skript (jen pro zkušené uživatele)", "Script (for advanced users only)"));
        add(129, f.insertMessage("util.Definice", "Definice", "Definition"));
        add(130, f.insertMessage("util.ExemplarovyTyp", "Exemplářový řez", "Exemplar facet"));
        add(131, f.insertMessage("commons.Zapnuto", "Zapnuto", "Enabled"));
        add(132, f.insertMessage("commons.Novy", "Nový", "New"));
        add(133, f.insertMessage("exemplar.holder", "Vypůjčitel", "Holder"));
        add(134, f.insertIniKey("OPAC", "VlastnostProMapy", 380, "Exemplářová vlastnost (sloupec), podle které se budou načítat mapy knihoven.", null, "LOKACE"));
        add(135, f.updateIniPopis("OPAC", "ZAZNAM-SLOUPCE_EXEMPLARE", "Sloupce v seznamu exemplářů. Povolene hodnoty: PRIR_CISLO, BAR_COD, SIGNATURA, LOKACE, KATEGORIE, DOSTUPNOST, BUDOVA, TEM_SKUP, STATUS, POZNAMKA, CENA, UZIVATEL, ROK_PRIR, ZPUSOB_NABYTI, ZPUSOB_ZALOZENI, INDIVIDUALNI_HODNOTA, DATUM, UMISTENI, HOLDER"));
        add(136, f.insertMessage("statistiky.AUTORITNI", "Autoritní", "Authority"));
        add(137, f.insertMessage("statistiky.OBLIBENE", "Oblíbené", "Favourites"));
        add(138, f.insertIniKey("OPAC", "DEFINICE_ODSTAVCE-HLEDANI_VICE", 39, "Definice odstavce prvku výsledku hledání při kliknutí na tlačítko více.", null, null));
        add(139, f.insertIniKey("OPAC", "ZpusobRezervovani", 355, "Způsob, jakým se bude na webu rezervovat.", 27, "classic"));
        add(140, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (27, 1, 'off', 'Rezervování vypnuto')"));
        add(141, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (27, 2, 'classic', 'Běžný způsob rezervování')"));
        add(142, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (27, 3, 'mail', 'Rezervace odesláním emailu')"));
        add(143, f.insertMessage("mail.SendEmail", "Odeslat email", "Send email"));
        add(144, f.insertMessage("mail.TargetAddress", "Cílová adresa", "Target address"));
        add(145, f.insertMessage("commons.Format", "Formát", "Format"));
        add(146, f.insertMessage("commons.optional", "Nepovinné", "Optional"));
        add(147, f.insertMessage("detail.Souvisejici", "Související", "Related"));
        add(148, f.insertMessage("templates.MailDocument", "Standardní", "Standard"));
        add(149, f.insertMessage("commons.Options", "Možnosti", "Options"));
        add(149, f.insertMessage("util.CacheRefreshedForXMs", "Cache refreshovana za {0} ms.", "Cache refreshed for {0} ms."));
        add(150, f.insertMessage("commons.VybratVse", "Vybrat vše", "Select all"));
        add(151, f.insertMessage("detail.DokumentJeVeZpracovani", "Dokument je momentálně ve zpracování", "Document is currently in processing"));
        add(152, f.insertMessage("document.status.2", "Zpětná katalogizace", "Reverse cataloging"));
        add(153, f.insertMessage("document.status.3", "Standardní záznam", "Standard record"));
        add(154, f.insertMessage("document.status.4", "Ukončená katalogizace", "Finished cataloging"));
        add(155, f.insertMessage("document.status.5", "Odesláno do CASLIN", "Sent to CASLIN"));
        add(156, f.insertMessage("document.status.6", "Akceptováno systémem CASLIN", "Accepted by CASLIN system"));
        add(157, f.insertMessage("util.VerzePortara", "Verze Portaro", "Portaro version"));
        add(158, f.insertMessage("util.VerzeAplikacnihoServeru", "Verze aplikačního serveru", "Application server version"));
        add(159, f.insertMessage("templates.MailFavourites", "Standardní", "Standard"));
        add(160, f.insertMessage("commons.OdznacitVse", "Odznačit vše", "Unselect all"));
        add(161, f.updateMessage("konto.vypujckaBylaProdlouzenaOXDniJednaSeOYProdlouzeni", "Výpůjčka byla prodloužena o {0} dní (do {2,date,dd.MM.yyyy}). <br/> Jedná se o {1}. prodloužení", "Loan was renewed for {0} days (until {2,date,dd.MM.yyyy}).<br/> It is {1}. renewal."));
        add(162, f.insertMessage("message.appServer.nelzeProlongovatNeposunulUzBySeTerminVraceni", "Již nelze dále prodlužovat", "Cannot by further renewed"));
        add(163, f.insertMessage("message.appServer.tutoTemSkupNelzeObjednavat", "V této tem.skup. nelze objednávat", "Topic group does not allow ordering"));
        add(164, f.insertMessage("message.appServer.dokumentSTimtoZpNabNelzeObjednavat", "Dokument s tímto zp.nab. nelze objednávat", "Acquisition way does not allow ordering"));
        add(165, f.insertMessage("message.appServer.vTetoLokaciNelzeObjednavat", "V této lokaci nelze objednávat", "Location does not allow ordering"));
        add(166, f.updateMessageKey("message.appServer.tutoTemSkupNelzeObjednavat", "message.appServer.VolnyAleTutoTemSkupNelzeObjednavat"));
        add(167, f.updateMessageKey("message.appServer.dokumentSTimtoZpNabNelzeObjednavat", "message.appServer.VolnyAleTentoZpNabNelzeObjednavat"));
        add(168, f.updateMessageKey("message.appServer.vTetoLokaciNelzeObjednavat", "message.appServer.VolnyAleVTetoLokaciNelzeObjednavat"));
        add(169, f.updateMessage("message.appServer.VolnyAleTutoTemSkupNelzeObjednavat", "Volný, ale tato tem.skup. neumožňuje objednávky", "Available, but this topic group does not allow ordering"));
        add(170, f.updateMessage("message.appServer.VolnyAleTentoZpNabNelzeObjednavat", "Volný, ale způsob nabytí neumožňuje objednávky", "Available, but this acquisition way does not allow ordering"));
        add(171, f.updateMessage("message.appServer.VolnyAleVTetoLokaciNelzeObjednavat", "Volný, ale v této lokaci nelze objednávat", "Available, but this location does not allow ordering"));
        add(172, f.updateMessage("message.appServer.statusExemplareNeumoznujeVypujcky", "Status ex. neumožňuje výpůjčky", "Item status does not allow loans"));
        add(173, f.insertMessage("ctenar.HesloByloUspesneZmeneno", "Heslo bylo úspěšně změněno", "Password successfully changed"));
        add(174, f.insertMessage("message.appServer.nelzeProlongovatPasivniMVS", "Nelze prodloužit ex. z cizí knihovny", "Cannot renew ex. from foreign library"));
        add(175, f.insertMessage("message.appServer.VolnyAleStatusNeumoznujeObjednavat", "Volný, ale status neumožňuje objednávat", "Available, but this status does not allow ordering"));
        add(176, f.insertMessage("mail.ObjednavamVybraneDokumenty", "Objednávám vybrané dokumenty", "Order of searched documents"));
        add(177, f.dbDependentSql(new String[] {"ALTER TABLE OPAC_PIN DROP CONSTRAINT PK_OPAC_PIN", "ALTER TABLE OPAC_PIN ALTER COLUMN USERNAME type STRING_30", "ALTER TABLE OPAC_PIN ADD CONSTRAINT PK_OPAC_PIN PRIMARY KEY (USERNAME)"}, new String[]{})); //zvetseni sloupce pro username
        add(178, f.dbDependentSql(new String[] {"ALTER TABLE OPAC_PIN ALTER COLUMN PASSWORD TYPE STRING_30"}, new String[]{})); //zvetseni sloupce pro password
        add(179, f.insertIniKey("OPAC", "BuiltInTemplates", 380, "Zda mají být zobrazovány výchozí (zabudované) šablony (např. u emailů apod.)", 1, "ANO"));
        add(180, f.insertMessage("commons.Budovy", "Budovy", "Buildings"));
        add(181, f.updateMessage("commons.odeslatMvsZadanku", "MVS", "MVS"));
        add(182, f.insertMessage("vysledky.NacistDalsi", "Načíst další", "Load next"));
        add(183, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'KONTO-MAXIMUM_NACITANYCH_VRACENYCH_VYPUJCEK'"));
        add(184, f.insertMessage("ctenar.TransactionsBlocked", "Blokovány transakce", "Transactions blocked"));
        add(185, f.insertMessage("message.appServer.dotazOpravduVratitCtenarMaBlokovanyTransakce", "Čtenář má blokované transakce! Opravdu vrátit?"));
        add(186, f.insertIniKey("EVERBIS", "PreeditSearchEnabled", 20, "Zda se má před vytvořením nového záznamu zobrazit hledání záznamu v DB a v Z-serveru.", 1, "ANO"));
        add(187, f.insertIniKey("OPAC", "IndividualBuildingCatalogsEnabled", 400, "Zda používat zvláštní katalogy pro jednotlivé budovy", 1, "NE"));
        add(188, f.insertMessage("login.loginPage.enterValidationCode", "Vložit validační kód", "Enter validation code"));
        add(189, f.insertMessage("message.validation.ValidationCodeIsNotValid", "Validační kód není platný", "Validation code is not valid"));
        add(190, f.insertMessage("login.loginPage.validationCodeLabel", "Validační kód", "Validation code"));
        add(191, f.insertIniKey("VYPUC", "SMTP_AUTH", 174, "Zda v SMTP spojení používat autentizaci. Pokud ano, musí být nastavené SMTP jméno a heslo.", 1, "ANO"));
        add(192, f.insertIniKey("OPAC", "CtenaremEditovatelneVlastnostiCtenare", 420, "Vlastnosti čtenáře, které si sám čtenář může editovat. Možné hodnoty jsou: JMENO, PRIJMENI, TITUL, TRV_MI, TRV_UL, TRV_PSC, PRE_MI, PRE_UL, PRE_PSC, ZAMESTNANI, ADR_ZAM, VZDELANI, TRIDA, CISOP, EMAIL, SMSCISLO, TELEFON", null, ""));
        add(193, f.updateIniPopis("OPAC", "CtenaremEditovatelneVlastnostiCtenare", "Vlastnosti čtenáře, které si sám čtenář může editovat. Možné hodnoty jsou: JMENO, PRIJMENI, USERNAME, TITUL, TRV_MI, TRV_UL, TRV_PSC, PRE_MI, PRE_UL, PRE_PSC, ZAMESTNANI, ADR_ZAM, VZDELANI, TRIDA, CISOP, EMAIL, SMSCISLO, TELEFON"));
        add(194, f.insertMessage("ctenar.username", "Uživatelské jméno", "Username"));
        add(195, f.insertMessage("login.resetPassword.subject", "Změna uživatelského hesla do katalogu knihovny", "Change password to library catalog"));
        add(196, f.insertMessage("commons.NotFound", "Nenalezeno", "Not found"));
        add(197, f.insertMessage("mail.EnterYourEmailAddress", "Zadejte vaši emailovou adresu", "Enter your email address"));
        add(198, f.insertMessage("login.resetPassword.EnterNewPassword", "Zadejte nové heslo", "Enter new password"));
        add(199, f.insertMessage("login.resetPassword.SaveAndLogin", "Uložit a rovnou přihlásit", "Save and login"));
        add(200, f.insertMessage("login.resetPassword.ForgottenPassword", "Zapomenuté heslo", "Forgotten password"));
        add(201, f.deleteMessage("message.appServer.statusExemplareNeumoznujeVypujcky"));
        add(202, f.insertMessage("message.appServer.statusExemplareNeumoznujeVypujcky", "Status ex. neumožňuje výpůjčky", "Item status does not allow loans"));
        add(203, f.insertMessage("login.resetPassword.MoreThanOneReaderWithThisEmailException", "Tento email používá více čtenářů", "More readers use this email"));
        add(204, f.sql("INSERT INTO INI_SEKCE (poradi, id_sekce, suffix_num, limit_od, limit_do) values (33, 'OPAC_SEARCH', 0, null, null)"));
        add(205, f.insertIniKey("OPAC_SEARCH", "DefaultneRozbaleneRezy", 100, "Seznam řezů, které mají být ihned po vyhledání rozbaleny (tzn. jejich klíče budou ihned viditelné)", null, "REZS_FOND; REZS_UNINAZEV; REZS_AUTOR; REZS_ROK; REZS_PREDMET; REZS_DESKRIPTOR"));
        add(206, f.disableIniTriggers());
        add(206, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'DefaultneRozbaleneRezy' WHERE FK_KLIC = 'HLEDANI-DEFAULTNE_ROZBALENE_REZY'"));
        add(207, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-DEFAULTNE_ROZBALENE_REZY'"));
        add(208, f.insertIniKey("OPAC_SEARCH", "TypyHledani", 30, "Seznam hledání, která budou v katalogu zobrazována", null, "JEDNODUCHE; ROZSIRENE"));
        add(209, f.disableIniTriggers());
        add(209, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'TypyHledani' WHERE FK_KLIC = 'HLEDANI-SEZNAM_ZOBRAZOVANYCH_HLEDANI'"));
        add(210, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-SEZNAM_ZOBRAZOVANYCH_HLEDANI'"));
        add(211, f.insertIniKey("OPAC_SEARCH", "RozsireneDefaultDiakritika", 200, "Zda má být v rozšířeném hledání defaultně zapnuto hledání s diakritikou", 1, "ANO"));
        add(212, f.disableIniTriggers());
        add(212, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'RozsireneDefaultDiakritika' WHERE FK_KLIC = 'HLEDANI-DEFAULT_DIAKRITIKA_V_KOMPLEXNIM'"));
        add(213, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-DEFAULT_DIAKRITIKA_V_KOMPLEXNIM'"));
        add(214, f.insertIniKey("OPAC_SEARCH", "RozsireneDefaultSklonovani", 210, "Zda má být v rozšířeném hledání defaultně zapnuto skloňování slov", 1, "ANO"));
        add(215, f.disableIniTriggers());
        add(215, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'RozsireneDefaultSklonovani' WHERE FK_KLIC = 'HLEDANI-DEFAULT_SKLONOVANI_V_KOMPLEXNIM'"));
        add(216, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-DEFAULT_SKLONOVANI_V_KOMPLEXNIM'"));
        add(217, f.insertIniKey("OPAC_SEARCH", "DefaultKompaktniZobrazeni", 60, "Zapnout kompaktní styl jako výchozí zobrazení seznamu vyhledaných dokumentů", 1, "NE"));
        add(218, f.disableIniTriggers());
        add(218, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'DefaultKompaktniZobrazeni' WHERE FK_KLIC = 'HLEDANI-DEFAULTNE_KOMPAKTNI_ZOBRAZENI_ZAP'"));
        add(219, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-DEFAULTNE_KOMPAKTNI_ZOBRAZENI_ZAP'"));
        add(220, f.insertIniKey("OPAC_SEARCH", "VelikostStrankyDokumentuKompakt", 45, "Počet dokumentů na jedné stránce v kompaktním zobrazení hledání dokumentů", null, "20"));
        add(221, f.disableIniTriggers());
        add(221, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'VelikostStrankyDokumentuKompakt' WHERE FK_KLIC = 'HLEDANI-VELIKOST_STRANKY_DOK_KOMPAKTNI_ZOBRAZENI'"));
        add(222, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-VELIKOST_STRANKY_DOK_KOMPAKTNI_ZOBRAZENI'"));
        add(223, f.insertIniKey("OPAC_SEARCH", "VelikostStrankyDokumentuPlne", 48, "Počet dokumentů na jedné stránce v plném zobrazení hledání dokumentů", null, "10"));
        add(224, f.disableIniTriggers());
        add(224, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'VelikostStrankyDokumentuPlne' WHERE FK_KLIC = 'HLEDANI-VELIKOST_STRANKY_DOK_PLNE_ZOBRAZENI'"));
        add(225, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-VELIKOST_STRANKY_DOK_PLNE_ZOBRAZENI'"));
        add(226, f.insertIniKey("OPAC_SEARCH", "VelikostStrankyAutorit", 51, "Počet autorit na jedné stránce při vyhledávání autorit", null, "40"));
        add(227, f.disableIniTriggers());
        add(227, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'VelikostStrankyAutorit' WHERE FK_KLIC = 'HLEDANI-VELIKOST_STRANKY_AUT'"));
        add(228, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-VELIKOST_STRANKY_AUT'"));
        add(229, f.insertIniKey("OPAC_SEARCH", "Rezy", 80, "Seznam zapnutých řezů", null, "REZS_FOND; REZS_UNINAZEV; REZS_AUTOR; REZS_ROK; REZS_PREDMET; REZS_DESKRIPTOR; REZS_BUDOVA; REZS_LOKACE; REZS_VYPKAT; REZS_JAZYK"));
        add(230, f.disableIniTriggers());
        add(230, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'Rezy' WHERE FK_KLIC = 'HLEDANI-REZY'"));
        add(231, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-REZY'"));
        add(232, f.insertIniKey("OPAC_SEARCH", "MaxKlicuRezu", 85, "Maximální počet načítaných klíčů řezů", null, "25"));
        add(233, f.disableIniTriggers());
        add(233, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'MaxKlicuRezu' WHERE FK_KLIC = 'HLEDANI-MAXIMUM_KLICU_REZU'"));
        add(234, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-MAXIMUM_KLICU_REZU'"));
        add(235, f.insertIniKey("OPAC_SEARCH", "MaxIhnedZobrazenychKlicuRezu", 88, "Maximální počet klíčů, které se ihned zobrazí v hlavní nabídce řezů, ostatní se zobrazí až po rozkliknutí", null, "5"));
        add(236, f.disableIniTriggers());
        add(236, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'MaxIhnedZobrazenychKlicuRezu' WHERE FK_KLIC = 'HLEDANI-MAXIMUM_ZOBRAZENYCH_KLICU_REZU'"));
        add(237, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-MAXIMUM_ZOBRAZENYCH_KLICU_REZU'"));
        add(238, f.insertIniKey("OPAC_SEARCH", "VzorGlobalniHledaniPokus1", 24, "Vzor pro první pokus globálního hledání", null, "P245:?^5 OR P245:?*^4 OR P650:?*^2 OR P653:?*^2 OR PALL:?* OR PFULLTEXT:?*"));
        add(239, f.disableIniTriggers());
        add(239, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'VzorGlobalniHledaniPokus1' WHERE FK_KLIC = 'vzorGlobalniHledaniPokus1'"));
        add(240, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'vzorGlobalniHledaniPokus1'"));
        add(241, f.insertIniKey("OPAC_SEARCH", "VzorGlobalniHledaniPokus2", 26, "Vzor pro druhý pokus globálního hledání", null, "P245:?~^5 OR P245:?~^4 OR P650:?~^2 OR P653:?~^2 OR PALL:?~ OR PFULLTEXT:?~"));
        add(242, f.disableIniTriggers());
        add(242, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'VzorGlobalniHledaniPokus2' WHERE FK_KLIC = 'vzorGlobalniHledaniPokus2'"));
        add(243, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'vzorGlobalniHledaniPokus2'"));
        add(244, f.insertIniKey("OPAC_SEARCH", "VzorGlobalniHledaniPokus3", 28, "Vzor pro třetí pokus globálního hledání", null, null));
        add(245, f.disableIniTriggers());
        add(245, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'VzorGlobalniHledaniPokus3' WHERE FK_KLIC = 'vzorGlobalniHledaniPokus3'"));
        add(246, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'vzorGlobalniHledaniPokus3'"));
        add(247, f.insertIniKey("OPAC_SEARCH", "TextDefDokumentPlne", 10, "Definice odstavce prvku výsledku hledání v plnohodnotném zobrazení", null, "#fond()\n<br/>\n#sf(260)\n#sf(910 ''b'' ''Signatura: '')"));
        add(248, f.disableIniTriggers());
        add(248, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'TextDefDokumentPlne' WHERE FK_KLIC = 'DEFINICE_ODSTAVCE-HLEDANI_PLNE'"));
        add(249, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'DEFINICE_ODSTAVCE-HLEDANI_PLNE'"));
        add(250, f.insertIniKey("OPAC_SEARCH", "TextDefDokumentKompakt", 14, "Definice odstavce prvku výsledku hledání v kompaktním zobrazení", null, "#fond()\n<br/>\n#sf(260)\n#sf(910 ''b'' ''Signatura: '')"));
        add(251, f.disableIniTriggers());
        add(251, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'TextDefDokumentKompakt' WHERE FK_KLIC = 'DEFINICE_ODSTAVCE-HLEDANI_KOMPAKTNI'"));
        add(252, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'DEFINICE_ODSTAVCE-HLEDANI_KOMPAKTNI'"));
        add(253, f.insertIniKey("OPAC_SEARCH", "TextDefDokumentVice", 18, "Definice odstavce prvku výsledku hledání při kliknutí na tlačítko více.", null, null));
        add(254, f.disableIniTriggers());
        add(254, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'TextDefDokumentVice' WHERE FK_KLIC = 'DEFINICE_ODSTAVCE-HLEDANI_VICE'"));
        add(255, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'DEFINICE_ODSTAVCE-HLEDANI_VICE'"));
        add(256, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'HLEDANI-DEFINOVANA_NABIDKA_FONDU_ZAP'"));
        add(258, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-DEFINOVANA_NABIDKA_FONDU_ZAP'"));
        add(257, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'HLEDANI-DEFINOVANA_NABIDKA_AUT_FONDU_ZAP'"));
        add(259, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-DEFINOVANA_NABIDKA_AUT_FONDU_ZAP'"));
        add(260, f.insertIniKey("OPAC", "FondyDokumentu", 123, "Seznam fondů dokumentů zobrazovaných v katalogu", null, "1; 2; 3; 4; 21"));
        add(261, f.disableIniTriggers());
        add(261, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC', FK_KLIC = 'FondyDokumentu' WHERE FK_KLIC = 'HLEDANI-DEFINOVANA_NABIDKA_FONDU'"));
        add(262, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-DEFINOVANA_NABIDKA_FONDU'"));
        add(263, f.insertIniKey("OPAC", "FondyAutorit", 127, "Seznam autoritních fondů zobrazovaných v katalogu", null, "31; 41; 49; 51; 52"));
        add(264, f.disableIniTriggers());
        add(264, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC', FK_KLIC = 'FondyAutorit' WHERE FK_KLIC = 'HLEDANI-DEFINOVANA_NABIDKA_AUT_FONDU'"));
        add(265, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'HLEDANI-DEFINOVANA_NABIDKA_AUT_FONDU'"));
        add(266, f.deleteMessage("message.validation.ctenarWithThisNameAlreadyExists"));
        add(267, f.insertMessage("message.validation.ThisValueIsAlreadyUsed", "Tato hodnota je již používána", "This value is already used"));
        add(269, f.insertIniKey("OPAC", "ExemplarColumns", 300, "Sloupce v seznamu exemplářů podle daných fondů. Pokud daný fond v seznamu není, použije se defaultní nastavení.", null, "3(PRIR_CISLO; SIGNATURA; ROCNIK; ROK; ROZMEZI_CISEL; LOKACE; KATEGORIE; DOSTUPNOST)"));
        add(270, f.dbDependentSql(new String[] {"CREATE OR ALTER VIEW VIEW_OPAC_NEWS(FK_ZAZ, FK_DOKFOND, DATUM, FK_BUDOVA, STATUS4, FK_STATUS) AS SELECT KAT1_5.FK_ZAZ, KAT1_4.FK_DOKFOND, MAX(KAT1_5.datum) DATUM, KAT1_5.FK_BUDOVA, KAT1_4.STATUS4, KAT1_5.FK_STATUS FROM KAT1_5 JOIN KAT1_4 ON KAT1_4.ID_ZAZ = KAT1_5.FK_ZAZ WHERE (KAT1_5.TYP_CISLA = 0) and (datum >= cast('TODAY' as date) - 500) GROUP BY KAT1_5.FK_ZAZ, KAT1_5.DATUM, KAT1_4.FK_DOKFOND, KAT1_4.STATUS4, KAT1_5.FK_STATUS, KAT1_5.FK_BUDOVA ORDER BY DATUM DESC, KAT1_5.FK_ZAZ", "GRANT SELECT ON VIEW_OPAC_NEWS TO OPAC"}, new String[]{}));
        add(271, new ExceptionCatchingUpdate(f.dbDependentSql(new String[] {"ALTER TABLE OPAC_LOG_SESSIONS ADD FK_BUDOVA SMALL_NULL"}, new String[]{})));
        add(272, f.dbDependentSql(new String[] {"CREATE OR ALTER VIEW VIEW_OPAC_NEWS (FK_ZAZ, FK_DOKFOND, DATUM, FK_BUDOVA, STATUS4, FK_STATUS) AS SELECT KAT1_5.FK_ZAZ, KAT1_4.FK_DOKFOND, MAX(KAT1_5.datum) DATUM, KAT1_5.FK_BUDOVA, KAT1_4.STATUS4, KAT1_5.FK_STATUS FROM KAT1_5 JOIN KAT1_4 ON KAT1_4.ID_ZAZ = KAT1_5.FK_ZAZ WHERE (KAT1_5.TYP_CISLA = 0) and STATUS4 NOT IN (9,99) and (datum >= cast('TODAY' as date) - 500) GROUP BY KAT1_5.FK_ZAZ, KAT1_4.FK_DOKFOND, KAT1_4.STATUS4, KAT1_5.FK_STATUS, KAT1_5.FK_BUDOVA", "GRANT SELECT ON VIEW_OPAC_NEWS TO OPAC"}, new String[]{}));
        add(273, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (29, 1, 'relevance', 'Dle relevance')"));
        add(274, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (29, 2, 'nazev', 'Dle názvu')"));
        add(275, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (29, 3, 'autor', 'Dle autora')"));
        add(276, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (29, 4, 'datum', 'Dle data vydání')"));
        add(277, f.insertIniKey("OPAC_SEARCH", "DefaultRazeni", 75, "Výchozí způsob řazení vyhledaných záznamů", 29, "relevance"));
        add(278, f.insertMessage("loan.XOnWaitingList", "{0}. v pořadí", "{0}. on waiting list"));
        add(279, f.deleteMessage("konto.vPoradi"));
        add(280, f.updateMessageKey("konto.odeslanaRezervace", "loan.SentReservation"));
        add(281, f.updateMessageKey("konto.neodeslanaRezervace", "loan.UnsentReservation"));
        add(282, f.insertMessage("konto.NotApprovedYet", "Zatím neschváleno", "Not approved yet"));
        add(283, f.updateMessageKey("konto.NotApprovedYet", "loan.NotApprovedYet"));
        add(284, f.updateMessageKey("konto.jizNelzeZrusit", "loan.CannotBeCancelled"));
        add(285, f.updateMessage("loan.CannotBeCancelled", "Nelze zrušit", "Cannot cancel"));
        add(286, f.updateMessageKey("konto.nevyrizeno", "loan.UnprocessedOrder"));
        add(287, f.updateMessageKey("konto.vyrizeno", "loan.ProcessedOrder"));
        add(288, f.updateMessageKey("konto.zrusenaRezervace", "loan.CancelledReservation"));
        add(289, f.updateMessageKey("konto.vracenaVypujcka", "loan.ReturnedLoan"));
        add(290, f.insertIniKey("OPAC", "ForgottenCredentialsEnabled", 229, "Zda má být zapnuta moznost obnovení zapomenutého hesla čtenářů", 1, "ANO"));
        add(291, f.insertIniKey("OPAC_SEARCH", "ShowOnlyUsedAuthorities", 300, "Zda vyhledávat jen autority, které jsou někde použité", 1, "NE"));
        add(292, f.insertIniKey("EVERBIS", "EditovatelneVlastnostiExemplare", 200, "Vlastnosti exempláře, které lze přes web editovat. Mozne hodnoty jsou SIGNATURA, BAR_COD, KATEGORIE, TEM_SKUP, ZPUSOB_NABYTI, ZPUSOB_ZALOZENI, BUDOVA, LOKACE, STATUS, CENA, UZIVATEL, ROK_PRIR, POZNAMKA, UCET, CISLO_FAKTURY, TYP_CISLA, POCET_KUSU.", null, "SIGNATURA; BAR_COD; KATEGORIE; TEM_SKUP; ZPUSOB_NABYTI; ZPUSOB_ZALOZENI; BUDOVA; LOKACE; STATUS; CENA; UZIVATEL; ROK_PRIR; POZNAMKA; UCET; CISLO_FAKTURY; TYP_CISLA; POCET_KUSU"));
        add(293, f.updateIniDefaultValue("EVERBIS", "EditovatelneVlastnostiExemplare", "PRIR_CISLO; SIGNATURA; BAR_COD; KATEGORIE; TEM_SKUP; ZPUSOB_NABYTI; ZPUSOB_ZALOZENI; BUDOVA; LOKACE; STATUS; CENA; UZIVATEL; ROK_PRIR; POZNAMKA; UCET; CISLO_FAKTURY; TYP_CISLA; POCET_KUSU; PRILOHY"));
        add(294, f.updateIniPopis("EVERBIS", "EditovatelneVlastnostiExemplare", "Vlastnosti exempláře, které lze přes web editovat. Mozne hodnoty jsou PRIR_CISLO, SIGNATURA, BAR_COD, KATEGORIE, TEM_SKUP, ZPUSOB_NABYTI, ZPUSOB_ZALOZENI, BUDOVA, LOKACE, STATUS, CENA, UZIVATEL, ROK_PRIR, POZNAMKA, UCET, CISLO_FAKTURY, TYP_CISLA, POCET_KUSU, PRILOHY."));
        add(295, f.insertMessage("loan.VyzadatPresMvs", "Vyžádat přes MVS", "Request via MVS"));
        add(296, f.insertMessage("loans.RenewAllAtOnce", "Prodloužit vše", "Renew all"));
        add(297, f.insertMessage("loans.NotRenewed", "Neprodlouženo", "Not renewed"));
        add(298, f.grantSelect("VIEW_TISK_PRIRUSTKY", "opac"));
        add(299, f.insertMessage("statistiky.PrirustkovySeznam", "Přírůstkový seznam", "Exemplar list"));
        add(300, f.insertIniKey("OPAC", "ExemplarTabsByBuildings", 280, "Zda mají být exempláře zobrazeny v záložkách podle budov nebo všechny v jedné tabulce (ano = rozdělení podle budov)", 1, "ANO"));
        add(302, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE CTENARI ADD OPENID STRING_40"},
                new String[]{})));
        add(303, f.insertIniKey("OPAC", "CitationDoctypes", 281, "Seznam doctypů citací (typ dokumentu posílaný v každém požadavku na citace.com) podle fondů. Formát zápisu je fond(doctype) fond(doctype)...", null, "1(book) 2(contribution) 3(journal) 5(article) 8(ebook) 10(thesis) 11(article)"));
        add(304, f.insertMessage("ctenar.RegistrationExpired", "Prošlá registrace", "Expired registratinon"));
        add(307, f.insertIniKey("EVERBIS", "ShowOnlyOwnBuildingLoans", 400, "Zda mají být výpůjčky, rezervace, objednávky filtrovány podle budovy přihlášeného knihovníka", 1, "NE"));
        add(308, f.insertIniKey("OPAC", "RegistrationType", 227, "Typ registrovani ctenaru. off - vypnuto, credentials - pokud jsou ctenari zaevidovani, jen nemaji uzivatelske jmeno a heslo, all - kompletni registrace", 30, "credentials"));
        add(309, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (30, 1, 'off', 'Vypnuto')"));
        add(310, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (30, 2, 'credentials', 'Registrace uživatelského jména a hesla')"));
        add(311, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (30, 3, 'all', 'Kompletní registrace čtenáře')"));
        add(312, f.insertMessage("ctenar.password", "Heslo", "Password"));
        add(313, f.insertMessage("commons.ConfirmPassword", "Zadejte heslo znovu", "Enter password again"));
        add(314, f.insertIniKey("OPAC", "Loans", 355, "Nastavení půjčování v celém katalogu. ANO = zapnuto", 1, "ANO"));
        add(315, f.insertIniKey("OPAC", "Money", 160, "Nastavení zobrazení dluhů a kreditů čtenářů. ANO = zapnuto", 1, "ANO"));
        add(316, f.insertMessage("message.warning.IpAddressNotPermitted", "Soubor je dostupný pouze z vybraných počítačů", "File is available from selected computers only."));
        add(317, f.dbDependentSql(
                new String[] {"CREATE OR ALTER VIEW VIEW_OPAC_NEWS (FK_ZAZ, FK_DOKFOND, DATUM, FK_BUDOVA, STATUS4, FK_STATUS) AS SELECT KAT1_5.FK_ZAZ, KAT1_4.FK_DOKFOND, MAX(KAT1_5.datum) DATUM, KAT1_5.FK_BUDOVA, KAT1_4.STATUS4, KAT1_5.FK_STATUS FROM KAT1_5 JOIN KAT1_4 ON KAT1_4.ID_ZAZ = KAT1_5.FK_ZAZ WHERE (KAT1_5.TYP_CISLA = 0) and (datum >= cast('TODAY' as date) - 500) GROUP BY KAT1_5.FK_ZAZ, KAT1_4.FK_DOKFOND, KAT1_4.STATUS4, KAT1_5.FK_STATUS, KAT1_5.FK_BUDOVA"},
                new String[]{}));
        add(318, f.insertIniKey("OPAC", "MvsActive", 175, "Zda jsou zapnuty aktivní MVS", 1, "NE"));
        add(319, f.insertIniKey("OPAC", "MvsPassive", 178, "Zda jsou zapnuty pasivní MVS", 1, "NE"));
        add(320, f.disableIniTriggers());
        add(320, f.sql("UPDATE INI_FILE SET FK_KLIC = 'MvsPassive' WHERE FK_KLIC = 'KONTO-ZOBRAZOVAT_MVS'"));
        add(321, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'KONTO-ZOBRAZOVAT_MVS'"));
        add(322, f.insertIniKey("OPAC_SEARCH", "DefaultMaxAgeOfNewsSearch", 400, "Výchozí maximální stáří novinek (ve dnech) v hledání nejnovějších", null, "30"));
        add(323, f.updateMessage("detail.dokumentJeVolnyNaTetoBudove", "Kniha je k vyzvednutí na budově", "Document is to pick up on building"));
        add(324, f.insertMessage("detail.DokumentJeKZarezervovaniNaBudove", "Kniha je k zarezervování na budově", "Document is to reserve on building"));
        add(325, f.updateMessage("detail.nejdriveZvolteZdaChceteKnihuObjednatNeboRezervovat", "Nejdříve kliknutím na objednávku nebo rezervaci zvolte, zda chcete knihu objednat nebo rezervovat", "First by clicking on order or reservation choose, if you want to make a book request or placing on hold"));
        add(326, f.updateMessage("detail.zajemDo", "Chci čekat maximálně do", "I want to wait up to"));
        add(327, f.updateMessageKey("detail.zajemDo", "loans.IWantWaitUpTo"));
        add(328, f.deleteMessage("detail.denDoKterehoJsteOchotenNaKnihuCekat"));
        add(329, f.insertMessage("konto.RegistrationAgreement", "Přihláška čtenáře", "Reader registration"));
        add(330, f.sql("UPDATE INI_KEYS SET FK_SEKCE = 'EMAIL' WHERE ID_KLIC = 'SMTP_AUTH'"));
        add(331, f.insertMessage("konto.ReceiptOfPayment", "Potvrzení o zaplacení", "Receipt of payment"));
        add(332, f.insertMessage("commons.DateFrom", "Datum od", "Date from"));
        add(333, f.insertMessage("commons.DateTo", "Datum do", "Date to"));
        add(334, f.insertMessage("print.PrintSetting", "Nastavení tisku", "Print setting"));
        add(335, f.insertMessage("konto.Payments", "Platby", "Payments"));
        add(336, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (31, 1, 'Off', 'Vypnuté citace')"));
        add(337, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (31, 2, 'Citace.com', 'Citace ze serveru citace.com')"));
        add(338, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (31, 3, 'Template', 'Citace generované šablonou')"));
        add(339, f.insertIniKey("OPAC", "CitationService", 286, "Nastavení služby pro generování citací.", 31, "Off"));
        add(340, f.updateIniPopis("OPAC", "ZAZNAM-SLOUPCE_EXEMPLARE", "Sloupce v seznamu exemplářů. Povolene hodnoty: PRIR_CISLO, BAR_COD, SIGNATURA, LOKACE, KATEGORIE, DOSTUPNOST, BUDOVA, TEM_SKUP, STATUS, POZNAMKA, CENA, UZIVATEL, ROK_PRIR, ZPUSOB_NABYTI, ZPUSOB_ZALOZENI, INDIVIDUALNI_HODNOTA, DATUM, UMISTENI, HOLDER"));
        add(341, f.insertMessage("loans.RequestAll", "Vyžádat vše", "Request all"));
        add(342, f.insertIniKey("OPAC", "SearchElsewhere", 261, "Nastavení položek Hledat jinde", null,
                """
                        [\s
                          {
                            "name":"Google Books",\s
                            "template":"http://books.google.com/books?vid=ISBN{isbn}"
                          },\s
                          {
                            "name":"WorldCat",\s
                            "template":"http://www.worldcat.org/isbn/{isbn}"
                          },\s
                          {
                            "name":"Souborný katalog ČR",\s
                            "template":"http://sigma.nkp.cz:4505/F/?func=find-c&amp;ccl_term=isn={isbn}&amp;local_base=SKC"
                          },\s
                          {
                            "name":"Jednotná informační brána",\s
                            "template":"http://www.jib.cz/V/?func=meta-1-check&amp;mode=advanced&amp;find_op_0=AND&amp;find_code_2=ISBN&amp;find_request_2={isbn}&amp;ckbox=CNL03210"
                          },\s
                          {
                            "name":"SFX Rozcestník",\s
                            "template":"http://sfx.jib.cz/sfxlcl3?url_ver=Z39.88-2004&amp;rft_val_fmt=info:ofi/fmt:kev:mtx:book&amp;rft.isbn={isbn}"
                          },\s
                          {
                            "name":"Amazon",\s
                            "template":"http://www.amazon.com/gp/search?index=books&amp;linkCode=qs&amp;keywords={name}"
                          }\s
                        ]"""
        ));
        add(343, f.updateMessageKey("exemplar.signatura", "exemplar.callNumber"));
        add(344, f.updateMessageKey("exemplar.prirCislo", "exemplar.accessNumber"));
        add(345, f.updateMessageKey("exemplar.dostupnost", "exemplar.availability"));
        add(346, f.updateMessageKey("exemplar.carovyKod", "exemplar.barCode"));
        add(347, f.updateMessageKey("ctenar.carovyKod", "ctenar.barCode"));
        add(349, f.insertIniKey("OPAC", "Renewal", 373, "Zda je možné z portara prodlužovat výpůjčky", 1, "ANO"));
        add(350, f.insertMessage("exemplar.type", "Typ", "Type"));
        add(351, f.updateMessageKey("exemplar.kategorieVypujcky", "exemplar.loanCategory"));
        add(352, f.dbDependentSql(
                new String[] {"ALTER TABLE opac_log_sessions ADD USER_AGENT STRING_1000"},
                new String[]{}));
        add(353, f.insertMessage("loans.ExemplarJeKVyzvednutiNaBudove", "Exemplář je volny, můžete si ho vyzvednout na budově", "Exemplar is to pick up on building"));
        add(354, f.updateMessageKey("detail.dokumentJeVolnyNaBudove", "loans.DokumentJeKVyzvednutiNaBudove"));
        add(355, f.updateMessage("loans.dokumentJeKVyzvednutiNaBudove", "Kniha je volná, můžete si ji vyzvednout na budově", "Document is to pick up on building"));
        add(356, f.updateMessageKey("detail.dokumentJeVolnyNaNasledujicichBudovach", "loans.DokumentJeKVyzvednutiNaBudovach"));
        add(357, f.updateMessage("loans.dokumentJeKVyzvednutiNaBudovach", "Kniha je volná, můžete si ji vyzvednout na budovách", "Document is to pick up on buildings"));
        add(358, f.updateMessageKey("detail.dokumentJeVolnyNaTetoBudove", "loans.DokumentJeKObjednaniNaBudove"));
        add(359, f.updateMessage("loans.DokumentJeKObjednaniNaBudove", "Kniha je volná, můžete si ji zamluvit na budově", "Document is to book on building"));
        add(360, f.updateMessageKey("detail.zvolteBudovuNaKterouSiChceteProKnihuDojit", "loans.DokumentJeKObjednaniNaBudovach"));
        add(361, f.updateMessageKey("detail.DokumentJeKZarezervovaniNaBudove", "loans.DokumentJeKZarezervovaniNaBudove"));
        add(362, f.updateMessageKey("detail.zvolteBudovyNaKterychChceteRezervovatKnihu", "loans.DokumentJeKZarezervovaniNaBudovach"));
        add(363, f.updateMessageKey("detail.neboNaJinychBudovachRezervovat", "loans.NeboNaJinychBudovachRezervovat"));
        add(364, f.updateMessageKey("detail.nejdriveZvolteZdaChceteKnihuObjednatNeboRezervovat", "loans.NejdriveZvolteZdaChceteKnihuObjednatNeboRezervovat"));
        add(365, f.insertMessage("loans.ExemplarJeKObjednaniNaBudove", "Exemplář je volný, můžete si ho zamluvit na budově", "Exemplar is to book on building"));
        add(366, f.insertIniKey("EVERBIS", "EditovatelneVlastnostiCisla", 220, "Vlastnosti cisla, které lze přes web editovat. Mozne hodnoty jsou vsechny vlastnosti exemplare + CISLO, EV_CISLO", null, "PRIR_CISLO; SIGNATURA; BAR_COD; CISLO; EV_CISLO; KATEGORIE; TEM_SKUP; ZPUSOB_NABYTI; ZPUSOB_ZALOZENI; BUDOVA; LOKACE; STATUS; CENA; PRILOHY; POZNAMKA;"));
        add(367, f.updateMessageKey("exemplar.cislo", "exemplar.issueName"));
        add(368, f.insertMessage("exemplar.evidenceNumber", "Evidenční číslo"));
        add(369, f.insertIniKey("EVERBIS", "ZServers", 50, "Seznam Z-serverů (ZSERVER01; ZSERVER02; ...), které má eVerbis používat", null, "ZSERVER01"));
        add(370, f.updateIniDefaultValue("OPAC", "SearchElsewhere",
                """
                        [\s
                          {
                            "name":"Google Books",\s
                            "template":"http://books.google.com/books?vid=ISBN{isbn}"
                          },\s
                          {
                            "name":"WorldCat",\s
                            "template":"http://www.worldcat.org/isbn/{isbn}"
                          },\s
                          {
                            "name":"Souborný katalog ČR",\s
                            "template":"http://aleph.nkp.cz/F/?func=find-c&amp;ccl_term=isn={isbn}&amp;local_base=SKC"
                          },\s
                          {
                            "name":"Jednotná informační brána",\s
                            "template":"http://www.jib.cz/V/?func=meta-1-check&amp;mode=advanced&amp;find_op_0=AND&amp;find_code_2=ISBN&amp;find_request_2={isbn}&amp;ckbox=CNL03210"
                          },\s
                          {
                            "name":"SFX Rozcestník",\s
                            "template":"http://sfx.jib.cz/sfxlcl3?url_ver=Z39.88-2004&amp;rft_val_fmt=info:ofi/fmt:kev:mtx:book&amp;rft.isbn={isbn}"
                          },\s
                          {
                            "name":"Amazon",\s
                            "template":"http://www.amazon.com/gp/search?index=books&amp;linkCode=qs&amp;keywords={name}"
                          }\s
                        ]"""
        ));
        add(371, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (29, 5, '-nazev', 'Dle názvu sestupně')"));
        add(372, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (29, 6, '-autor', 'Dle autora sestupně')"));
        add(373, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (29, 7, '-datum', 'Dle data vydání sestupně')"));
        add(374, f.insertIniKey("OPAC", "BuiltInDefaultCovers", 80, "Seznam typů výchozích obálek (tzn. těch, které se zobrazí, pokud neexistuje naskenovaná obálka) a čísel fondů, ve kterých se mají zobrazit. Formát zápisu je fond(obalka) fond(obalka)...Tedy napřiklad: 1(book) 3(periodical). Možné hodnoty obálek jsou book, periodical, electronic, article, audio. U fondů, pro které není obálka nadefinována se použije výchozí, tedy book.", null, "1(book) 3(periodical) 5(article) 8(audio) 11(article)"));
        add(375, f.insertMessage("registrace.registraceKnihovny", "Registrace knihovny", "Library registration"));
        add(376, f.insertMessage("ctenar.sigla", "Sigla", "Library sign"));
        add(377, f.insertMessage("ctenar.libraryFullName", "Celý název knihovny", "Full name of library"));
        add(378, f.insertMessage("ctenar.libraryShortName", "Krátký název (Zkratka) knihovny", "Short name of library"));
        add(379, f.updateMessageKey("ctenar.typBlokace", "ctenar.blockingType"));
        add(380, f.insertMessage("registrace.ProRegistraciKnihovnyPouzijteLink", "Pro registraci knihovny použijte odkaz", "For library registration use link"));
        add(381, f.insertMessage("statistiky.Souhrny", "Souhrny", "Summaries"));
        add(382, f.insertMessage("statistiky.OverallStats", "Deník veřejné knihovny", "Overall stats"));
        add(383, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_NADPISU",
                "#sf(910 ''b'' ''{detail.signatura}: '')\n" +
                        "#sf(80 ''a'' '' MDT: '')"
        ));
        add(384, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY",
                """
                        #fond() <br/>
                        #sf(250 '''' '''' ''<br/>'')
                        #sf(260 '''' '''' ''<br/>'' )
                        #sf(300 '''' '''' ''<br/>'')
                        #sf(700 '''' ''{detail.ostatniAutori}: '' ''<br/>'')\s
                        #sf(44)"""
        ));
        add(385, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-POD_OBALKOU",
                """
                        #sf(650 ''a'' '''' ''<br/>'')
                        #sf(653 ''a'' '''' ''<br/>'')
                        #sf(520 '''' ''<br/>'' ''<br/>'')
                        #sf(500 '''' ''<br/>'' ''<br/>'')"""
        ));
        add(386, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-AUTORITA",
                """
                        #sf(670 '''' '''' ''<br/>''  ''<br/>'' )
                        #sf(678 '''' '''' ''<br/>''  ''<br/>'')\s
                        #sf(856)"""
        ));
        add(387, f.insertMessage("message.exception.coverLoad", "Nepodařilo se načíst obálku", "Failed to load cover"));
        add(388, f.insertMessage("message.exception.coverLoad.noIsbn", "Nepodařilo se načíst obálku - záznam nemá ISBN", "Failed to load cover - record does not contain ISBN"));
        add(389, f.insertMessage("message.exception.coverLoad.noCover", "V žádné službě se se obálka nenachází", "There is not any cover in any service"));
        add(390, f.insertMessage("message.exception.coverLoad.connectionProblem", "Nepodařilo se načíst obálku - problém s připojením", "Failed to load cover - connection problem"));
        add(391, f.insertMessage("message.exception.coverLoad.temporaryBlocked", "Nepodařilo se načíst obálku - služba je dočasně zablokovaná", "Failed to load cover - service is temporary blocked"));
        add(392, f.insertMessage("commons.ToInclusive", "Do (včetně)", "To (inclusive)"));
        add(393, f.insertMessage("commons.From", "Od", "From"));
        add(394, f.insertMessage("commons.To", "Do", "Do"));
        add(395, f.insertMessage("commons.from", "od", "from"));
        add(396, f.insertMessage("commons.to", "do", "do"));
        add(397, f.disableIniTriggers());
        add(397, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'UMOZNOVAT_REGISTRACE'"));
        add(398, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'UMOZNOVAT_REGISTRACE'"));
        add(399, f.insertMessage("message.appServer.dotazOpravduVypujcitRezervaceJeNaJinyExemplar", "Ctenář má již rezervaci na jiný exemplář tohoto titulu! Opravdu vypůjčit?"));
        add(400, f.insertMessage("message.appServer.dotazOpravduVypujcitNaTitulJeNevyrizenaObjednavka", "Půjčujete titul z nevyřízené objednávky! Opravdu vypůjčit?"));
        add(401, f.deleteMessage("message.appServer.dotazOpravduVratitCtenarMaBlokovanyTransakce"));
        add(401, f.insertMessage("message.appServer.dotazOpravduVypujcitCtenarMaBlokovanyTransakce", "Čtenář má blokované transakce! Opravdu vypůjčit?"));
        add(402, f.insertMessage("message.appServer.dotazOpravduVratitMVS", "Jedná se o MVS, opravdu vrátit?"));
        add(403, f.insertMessage("message.appServer.nelzeVratitNevypujceno", "Nelze vrátit - nevypůjčeno!"));
        add(404, f.insertMessage("message.appServer.nelzeVratitCiziVypujcka", "Nelze vrátit cizí výpůjčku!"));
        add(405, f.insertMessage("message.appServer.nelzeVratitNaCiziBudove", "Nelze vrátit na jiné budově!"));
        add(406, f.insertMessage("message.appServer.nelzeVratitNaCiziPujcovne", "Nelze vrátit na jiné půjčovně!"));
        add(407, f.insertMessage("message.appServer.nelzeVratitRFID", "Nelze vrátit přes SelfCheck!"));
        add(408, f.deleteMessage("exemplar.rocnik"));
        add(409, f.deleteMessage("exemplar.rok"));
        add(410, f.deleteMessage("exemplar.rozmeziCisel"));
        add(411, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (31, 4, 'CustomTemplate', 'Citace generované vlastní šablonou')"));
        add(412, f.insertMessage("commons.Identifikator", "Identifikátor", "Identifier"));
        add(413, f.deleteMessage("konto.vypujckaBylaProdlouzenaOXDniJednaSeOYProdlouzeni"));
        add(414, f.insertMessage("loan.VypujckaCisloXBylaProdlouzenaOYDniJednaSeOZProdlouzeni", "Výpůjčka číslo {0} byla prodloužena o {1} dní (do {2,date,dd.MM.yyyy}). <br/> Jedná se o {3}. prodloužení", "Loan number {0} was renewed for {1} days (until {2,date,dd.MM.yyyy}).<br/> It is {3}. renewal."));
        add(415, f.updateMessageKey("ctenar.pujcovna", "ctenar.lendPlace"));
        add(416, f.updateMessage("exemplar.muzejniRocnik", "Ročník", "Volume"));
        add(417, f.updateMessage("exemplar.rokMuzejnihoRocniku", "Rok", "Year"));
        add(418, f.updateMessage("exemplar.rozmeziCiselMuzejnihoRocniku", "Rozmezí čísel", "Issue range"));
        add(419, f.updateMessageKey("exemplar.muzejniRocnik", "exemplar.museumVolume"));
        add(420, f.updateMessageKey("exemplar.rokMuzejnihoRocniku", "exemplar.museumVolumeYear"));
        add(421, f.updateMessageKey("exemplar.rozmeziCiselMuzejnihoRocniku", "exemplar.museumVolumeIssueRange"));
        add(422, f.updateMessageKey("exemplar.museumVolume", "exemplar.bundledVolumeNumber"));
        add(423, f.updateMessageKey("exemplar.museumVolumeYear", "exemplar.bundledVolumeYear"));
        add(424, f.updateMessageKey("exemplar.museumVolumeIssueRange", "exemplar.bundledVolumeIssueRange"));
        add(425, f.updateMessageKey("exemplar.evidenceNumber", "exemplar.issueEvidenceNumber"));
        add(426, f.insertMessage("loan.MoznostiVypujceni", "Možnosti vypůjčení", "Lend options"));
        add(427, f.grantSelect("INI_KEYS", "opac"));
        add(428, new ExceptionCatchingUpdate(f.insertMessage("templates.PrintFavourites", "Jednoduchý výpis", "Simple")));
        add(429, f.insertMessage("loan.StatusNepovolujePujceni", "Status nepovoluje půjčení", "Cannot lend because of status"));
        add(430, f.insertMessage("loan.KombinaceCtenKatAVypKatNepovolujePujceni", "Kombinace kategorií čtenáře a výpůjčky nepovoluje půjčení", "Cannot lend because of combination of reader and loan category"));
        add(431, f.insertMessage("loan.NelzeVypujcitExemplarJeJizVypujcenNeboRezervovan", "Nelze vypůjčit, exemplář je již jednou vypůjčen/rezervován", "Cannot lend, exemplar is already lent or reserved"));
        add(432, f.insertMessage("loan.VypujcitDalsi", "Vypůjčit další", "Lend next"));
        add(433, f.insertIniKey("OPAC", "ExemplarDataInRecord", 270, "Zda se mají do záznamu (do polí) načítat i exemplářové údaje. To může být užitečné při zobrazování těchto údajů v šablonách (velocity).", 1, "NE"));
        add(434, f.insertMessage("commons.FondyDokumentu", "Fondy dokumentu", "Document fonds"));
        add(435, f.insertMessage("commons.FondyAutorit", "Fondy autorit", "Authority fonds"));
        add(436, f.insertMessage("commons.LendPlaces", "Půjčovny", "Lend places"));
        add(437, f.insertMessage("commons.Locations", "Lokace", "Locations"));
        add(438, f.updateMessageKey("ctenar.lendPlace", "ctenar.rental"));
        add(439, f.updateMessageKey("commons.LendPlaces", "commons.Rentals"));
        add(440, f.updateIniPopis("OPAC", "CtenaremEditovatelneVlastnostiCtenare", "Vlastnosti čtenáře, které si sám čtenář může editovat. Možné hodnoty jsou: JMENO, PRIJMENI, USERNAME, TITUL, TRV_MI, TRV_UL, TRV_PSC, PRE_MI, PRE_UL, PRE_PSC, ZAMESTNANI, ADR_ZAM, VZDELANI, TRIDA, CISOP, EMAIL, SMSCISLO, TELEFON, TYP_TISK_REZE, TYP_TISK_UPOM"));
        add(441, f.deleteMessage("util.pridatKlic"));
        add(442, f.deleteMessage("commons.Options"));
        add(443, f.deleteMessage("util.CacheRefreshedForXMs"));
        add(444, f.insertMessage("loan.VratitDalsi", "Vrátit další", "Return next"));
        add(445, f.insertMessage("detail.PublishedIn", "Publikováno v", "Published in"));
        add(446, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY",
                """
                        #fond()
                        <br/>
                        #sf(773 '''' ''{detail.PublishedIn} '' ''<br/>'')
                        #sf(250 '''' '''' ''<br/>'')
                        #sf(260 '''' '''' ''<br/>'' )
                        #sf(300 '''' '''' ''<br/>'')
                        #sf(700 '''' ''{detail.ostatniAutori}: '' ''<br/>'')\s
                        #sf(44)"""
        ));
        add(447, f.updateIniDefaultValue("OPAC_SEARCH", "TextDefDokumentPlne",
                """
                        #fond()
                        <br/>
                        #sf(773 '''' ''{detail.PublishedIn} '' ''<br/>'')
                        #sf(260)
                        #sf(910 ''b'' ''{detail.signatura}: '')"""
        ));
        add(448, f.insertMessage("ctenar.ProdlouzitRegistraci", "Prodloužit registraci", "Extend registration"));
        add(449, f.deleteMessage("rejstrik.rejstrikNazvuAutorit"));
        add(450, f.insertMessage("renewal.nelzeProlongovatExemplarJeVCirkulaci", "Nelze prodloužit, exemplář bude přesunut", "Cannot renew, exemplar will be moved"));
        add(451, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY",
                """
                        #fond()
                        <br/>
                        #sf(773 '''' ''{detail.PublishedIn} '' ''<br/>'')
                        #sf(250 '''' '''' ''<br/>'')
                        #sf(260 ''ab'' '''' '' '' '''' '': '')
                        #sf(260 ''cdefg'')
                        #if($record.query(''260'').any)<br/>#end
                        #sf(300 '''' '''' ''<br/>'')
                        #sf(700 ''a'' ''{detail.ostatniAutori}: '' ''<br/>'')\s
                        #sf(44)"""
        ));
        add(453, f.grantSelect("DEF_GROUP", "opac"));
        add(454, f.deleteMessage("commons.mobilniZobrazeni"));
        add(455, f.deleteMessage("commons.klasickeZobrazeni"));
        add(456, f.updateIniDefaultValue("OPAC", "SeznamLokalizaci", "cs; en; de"));
        add(457, f.insertMessage("commons.Prazdny", Map.of("text_cze", "Prázdný", "text_eng", "Empty", "text_ger", "Leer")));
        add(458, f.insertIniKey("EVERBIS", "SloupcePrirustkovehoSeznamu", 600, "Seznam sloupců z pohledu VIEW_TISK_PRIRUSTKY, které mají být zobrazeny v přírůstkovém seznamu. Názvy sloupců korespondují se sloupci v pohledu a ctí nastavené pořadí.", null, "PRIR_CISLO; SIGNATURA; NAZEV_LOKACE; NAZEV_VYPKAT; FK_TEMSKUP; NAZEV_TEMSKUP; NAZEV; AUTOR; ROKVYD; NAKLADATEL; BAR_COD; DATUM; POZNAMKA; NAZEV_STATUS; NAZEV_DOKFOND; NAZEV_BUDOVA"));
        add(459, f.insertIniKey("OPAC", "RenewalOfDelayed", 378, "Zda čtenáři mohou prodlužovat výpůjčky s prošlou výpůjční lhůtou, pokud na ně ještě nebyla vygenerovaná upomínka.", 1, "ANO"));
        add(460, f.dbDependentSql(
                new String[] {"ALTER TABLE opac_log_sessions ADD FK_UZIV SMALL_NULL"},
                new String[]{}));
        String[] stringsBotsUserAgents = {
                "bot.htm",
                "googlebot",
                "surveybot",
                "yahoo.com",
                "seznambot",
                "fulltext.sblog.cz",
                "baiduspider",
                "apache-httpclient",
                "check_http",
                "yandexbot",
                "java/",
                "ezooms",
                "exabot",
                "ahrefsbot",
                "yandex",
                "crawler",
                "python-openid",
                "python-urllib",
                "cliqz.com",
                "microsoft url control",
                "ia_archiver",
                "libwww-perl",
                "feedreader",
                "pycurl",
                "openstat",
                "xpymep.exe",
                "bot.php",
                "mj12bot",
                "wotbox",
                "pagesinventory",
                "ahrefsbot",
                "compspy.com",
                "windows-rss-platform",
                "bubing",
                "zmeu",
                "seznam screenshot-generator",
                "facebook.com",
                "masscan",
                "niki-bot",
                "www.checkprivacy",
                "thefuckie",
                "nutch",
                "www.feedly.com",
                "addthis.com",
                "proxy gear pro",
                "nikto",
                "dotbot",
                "mj12bot",
                "prtg network monitor",
                "spbot",
                "gigablastopensource",
                "metaspider",
                "publiclibraryarchive",
                "visionutils",
                "feedfetcher-google",
                "/usr/bin/"
        };
        for (String ua : stringsBotsUserAgents) {
            add(461, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%"+ua+"%'")));
        }
        add(461, f.deleteMessage("konto.vypujckaBylaZrusena"));
        add(462, f.insertMessage("loan.ProdlouzenoDoXYteProdlouzeni", "Prodlouženo do {0,date,d.M.yyyy} ({1}.prodloužení)", "Renewed to {0,date,d.M.yyyy} ({1}.renewal)"));
        add(463, f.deleteMessage("konto.pocetVypujcek"));
        add(464, f.deleteMessage("hledani.nalezenExemplarX"));
        add(465, f.deleteMessage("hledani.nalezenaVypujckaCtenareX"));
        add(466, f.deleteMessage("hledani.nenalezenaZadnaVypujcka"));
        add(467, f.deleteMessage("konto.dluhy"));
        add(468, f.deleteMessage("konto.kredity"));
        add(469, f.insertMessage("konto.StavKonta", Map.of("text_cze", "Stav konta", "text_eng", "Account balance", "text_ger", "Kontostatus")));
        add(470, f.insertIniKey("OPAC", "BudovyPasuNovinek", 220, "Seznam budov, ze kterých se maji načítat záznamy do pásu s novinkami. Prázdný = všechny budovy", null, null));
        add(471, f.updateMessageKey("exemplar.callNumber", "exemplar.signature"));
        add(472, f.grantSelect("DEF_SIGN_RADY", "opac"));
        add(473, f.insertMessage("availability.ExemplarNemaNastavenouBudovu", "Exemplář nemá nastavenou budovu", "Exemplar has not building"));
        add(474, f.insertMessage("availability.ExemplarNeniMeziPlatnymiBudovamiCtenare", "Exemplář není mezi platnými budovami čtenáře", "Exemplar is not in reader's buildings list"));
        add(475, f.insertMessage("availability.ExemplarJeVyrazen", "Exemplář je vyřazen", "Exemplar is deleted"));
        add(476, f.insertIniKey("OPAC", "RootAuthority", 500, "ID autority nejvyšší úrovně, ze které se bude větvit autoritní thesaurus", null, null));
        add(477, f.updateMessageKey("exemplar.zpusobZalozeni", "exemplar.creationWay"));
        add(478, f.insertMessage("availability.ExemplarJeDislokovany", "Exemplář je dislokovaný", "Exemplar is dislocated"));
        add(479, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '**************'")));
        add(480, f.grantSelect("VIEW_TISK_UBYTKY", "opac"));
        add(481, f.insertMessage("statistiky.UbytkovySeznam", "Úbytkový seznam", "Decreases list"));
        add(482, f.insertIniKey("EVERBIS", "SloupceUbytkovehoSeznamu", 650, "Seznam sloupců z pohledu VIEW_TISK_UBYTKY, které mají být zobrazeny v úbytkovém seznamu. Názvy sloupců korespondují se sloupci v pohledu a ctí nastavené pořadí.", null, "UBYT_PRIRC; SIGNATURA; LOKACE; VYPKAT; FK_TEMSKUP; TEMSKUP; NAZEV; AUTOR; ROKVYD; NAKLADATEL; DATUM_UBYT; DUVOD_UBY;"));
        add(483, f.insertMessage("commons.LoanCategories", "Kategorie výpůjček", "Loan categories"));
        add(484, f.updateMessageKey("ctenar.poznamka", "ctenar.note"));
        add(485, f.updateMessage("detail.nejstePrihlasenProNeanonymniKomentarSePrihaste", "Nejste přihlášen, pokud nechcete, aby byl komentář anonymní, přihlaste se", "You are not logged-in. Log-in unless you want to create anonymous comment."));
        add(486, f.updateMessage("detail.proPridaniKomentareSePrihlaste", "Pro přidání komentáře se nejdříve přihlaste", "Login first to add a comment"));
        add(487, f.updateMessage("detail.proRezervaciSePrihlaste", "Pro rezervaci se přihlaste", "Log in for placing on hold"));
        add(488, f.updateMessage("hledaneDotazy.proZobrazeniUlozenychSePrihlaste", "Pro zobrazení uložených dotazů se přihlaste", "Log in for show saved queries"));
        add(489, f.updateMessage("message.warning.notLoggedIn", "Nejdříve se přihlaste", "Login first"));
        add(490, f.updateMessage("message.warning.proZjisteniDostupnostiSePrihlaste", "Pro zjištění dostupnosti se přihlaste", "Login for check availability"));
        add(491, f.dbDependentSql(new String[] {"ALTER TABLE OPAC_LOG_SESSIONS ADD CAS_PRIHLASENI DATETIME"}, new String[]{}));
        add(492, f.disableIniTriggers());
        add(492, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'ZAZNAM-CITACE_ZAP'"));
        add(493, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'ZAZNAM-CITACE_ZAP'"));
        add(494, f.updateIniDefaultValue("EVERBIS", "SloupceUbytkovehoSeznamu", "UBYT_CISLO; UBYT_PRIRC; SIGNATURA; LOKACE; VYPKAT; FK_TEMSKUP; TEMSKUP; NAZEV; AUTOR; ROKVYD; NAKLADATEL; DATUM_UBYT; DUVOD_UBY;"));
        add(495, MultipleUpdate.of(
                f.updateMessage("message.rezy.REZS_FOND", "Fond dokumentu", "Type of document"),
                f.updateMessageTranslation("message.rezy.REZS_FOND", "text_ger", "Typ des Dokumenten")
        ));
        add(496, MultipleUpdate.of(
                f.updateMessage("availability.ExemplarJeDislokovany", "Exemplář je dislokovaný", "Copy is dislocated"),
                f.updateMessageTranslation("availability.ExemplarJeDislokovany", "text_ger", "Disloziertes Exemplar")
        ));
        add(497, f.insertMessage("statistiky.MistniSeznam", "Místní seznam", "Local list"));
        add(498, f.updateIniDefaultValue("OPAC_SEARCH", "VzorGlobalniHledaniPokus1", "P245:\"?\"^6 OR P245:?^5 OR P245:?*^4 OR P650:?*^2 OR P653:?*^2 OR PALL:?* OR PFULLTEXT:?*"));
        add(499, f.updateIniDefaultValue("OPAC_SEARCH", "VzorGlobalniHledaniPokus1", "whole(P245:\"?\"^6) OR and(P245:?^5 OR P245:?*^4 OR P650:?*^2 OR P653:?*^2 OR PALL:?* OR PFULLTEXT:?*)"));
        add(500, f.deleteMessage("message.warning.IpAddressNotPermitted"));
        add(501, f.insertMessage("extres.ProStazeniJeVyzadovanoPrihlaseni", "Pro stažení souboru je vyžadováno přihlášení", "File download requires to be logged in"));
        add(502, f.insertIniKey("OPAC", "CredentialsRegistration", 233, "Typ registrace přihlašovacích údajů (tzn. uživatelského jména a hesla do katalogu, čtenář již musí být v systému zaevidován)", 33, "username+password"));
        add(503, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (33, 1, 'off', 'Vypnuto')"));
        add(504, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (33, 2, 'username', 'Registrace pouze uživatelského jména')"));
        add(505, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (33, 3, 'password', 'Registrace pouze hesla')"));
        add(506, f.sql("INSERT INTO INI_VALHOD (ID_VALHOD, PORADI, OBSAH, POPIS) values (33, 4, 'username+password', 'Registrace uživatelského jména a hesla')"));
        add(507, f.insertIniKey("OPAC", "FullRegistration", 234, "Plná registrace (tzn. zaevidování čtenáře a jeho údajů do systému)", 1, "NE"));
        add(508, f.disableIniTriggers());
        add(508, () -> {
            try {
                String value = jdbcTemplate.queryForObject("SELECT HODNOTA FROM INI_FILE WHERE FK_SEKCE = 'OPAC' AND FK_KLIC = 'RegistrationType'", String.class);
                if (value.equals("off")) {
                    jdbcTemplate.update("INSERT INTO INI_FILE (FK_SEKCE, FK_PUJC, FK_KLIC, CITAC, HODNOTA, POZNAMKA) VALUES ('OPAC', null, 'CredentialsRegistration', null, 'off', null)");
                    jdbcTemplate.update("INSERT INTO INI_FILE (FK_SEKCE, FK_PUJC, FK_KLIC, CITAC, HODNOTA, POZNAMKA) VALUES ('OPAC', null, 'FullRegistration', null, 'NE', null)");
                } else if (value.equals("all")) {
                    jdbcTemplate.update("INSERT INTO INI_FILE (FK_SEKCE, FK_PUJC, FK_KLIC, CITAC, HODNOTA, POZNAMKA) VALUES ('OPAC', null, 'CredentialsRegistration', null, 'off', null)");
                    jdbcTemplate.update("INSERT INTO INI_FILE (FK_SEKCE, FK_PUJC, FK_KLIC, CITAC, HODNOTA, POZNAMKA) VALUES ('OPAC', null, 'FullRegistration', null, 'ANO', null)");
                }
            } catch (EmptyResultDataAccessException e) {
                // dont worry
            }
        });
        add(509, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'RegistrationType'"));
        add(510, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'RegistrationType'"));
        add(511, f.sql("DELETE FROM INI_VALHOD WHERE ID_VALHOD = 30"));
        add(512, f.updateMessageKey("message.appServer.nelzeSmazatCtenareMaDluhy", "ctenar.NelzeSmazatCtenareMaDluhy"));
        add(513, f.updateMessageKey("message.appServer.nelzeSmazatCtenareMaAktivniVypujcky", "ctenar.NelzeSmazatCtenareMaAktivniVypujcky"));
        add(514, f.updateMessageKey("message.appServer.dotazOpravduSmazatCtenareMaDluhNaRegPopl", "ctenar.OpravduSmazatCtenareMaDluhNaRegPopl"));
        add(515, f.updateMessageKey("message.appServer.dotazOpravduSmazatCtenareMaPripraveneRezervace", "ctenar.OpravduSmazatCtenareMaPripraveneRezervace"));
        add(516, f.updateMessageKey("message.appServer.dotazOpravduSmazatCtenareMaRezervace", "ctenar.OpravduSmazatCtenareMaRezervace"));
        add(517, f.updateMessageKey("message.appServer.dotazOpravduVratitZvlastniInfoKLokaci", "loan.OpravduVratitZvlastniInfoKLokaci"));
        add(518, f.updateMessageKey("message.appServer.dotazOpravduVratitZvlastniInfoKeStatusu", "loan.OpravduVratitZvlastniInfoKeStatusu"));
        add(519, f.updateMessageKey("message.appServer.dotazOpravduVratitZvlastniInfoKeKategorii", "loan.OpravduVratitZvlastniInfoKeKategorii"));
        add(520, f.updateMessageKey("message.appServer.dotazOpravduVratitStatusJeZpetnaKatalogizace", "loan.OpravduVratitStatusJeZpetnaKatalogizace"));
        add(521, f.updateMessageKey("message.appServer.dotazOpravduVratitNaExemplarJeNeodeslanaRezervace", "loan.OpravduVratitNaExemplarJeNeodeslanaRezervace"));
        add(522, f.updateMessageKey("message.appServer.dotazOpravduVratitMVS", "loan.OpravduVratitMVS"));
        add(523, f.updateMessageKey("message.appServer.dotazOpravduVratitCtenarMaUpominky", "loan.OpravduVratitCtenarMaUpominky"));
        add(524, f.updateMessageKey("message.appServer.dotazOpravduVratitDokumentMaPrilohu", "loan.OpravduVratitDokumentMaPrilohu"));
        add(525, f.updateMessageKey("message.appServer.dotazOpravduVratitDokumentJeZJineLokace", "loan.OpravduVratitDokumentJeZJineLokace"));
        add(526, f.updateMessageKey("message.appServer.dotazOpravduVratitDokumentJeZJineBudovy", "loan.OpravduVratitDokumentJeZJineBudovy"));
        add(527, f.updateMessageKey("message.appServer.nelzeVratitNevypujceno", "loan.NelzeVratitNevypujceno"));
        add(528, f.updateMessageKey("message.appServer.nelzeVratitCiziVypujcka", "loan.NelzeVratitCiziVypujcka"));
        add(529, f.updateMessageKey("message.appServer.nelzeVratitNaCiziBudove", "loan.NelzeVratitNaCiziBudove"));
        add(530, f.updateMessageKey("message.appServer.nelzeVratitNaCiziPujcovne", "loan.NelzeVratitNaCiziPujcovne"));
        add(531, f.updateMessageKey("message.appServer.nelzeVratitRFID", "loan.NelzeVratitRFID"));
        add(532, f.updateMessageKey("message.appServer.dotazOpravduVypujcitDokumentJeZJineBudovy", "loan.OpravduVypujcitDokumentJeZJineBudovy"));
        add(533, f.updateMessageKey("message.appServer.dotazOpravduVypujcitDokumentJeZJineLokace", "loan.OpravduVypujcitDokumentJeZJineLokace"));
        add(534, f.updateMessageKey("message.appServer.dotazOpravduVypujcitPrekrocenMaxPocetVypCtenare", "loan.OpravduVypujcitPrekrocenMaxPocetVypCtenare"));
        add(535, f.updateMessageKey("message.appServer.dotazOpravduVypujcitPrekrocenMaxPocetVypProTutoKategorii", "loan.OpravduVypujcitPrekrocenMaxPocetVypProTutoKategorii"));
        add(536, f.updateMessageKey("message.appServer.dotazOpravduVypujcitDokumentMaPrilohu", "loan.OpravduVypujcitDokumentMaPrilohu"));
        add(537, f.updateMessageKey("message.appServer.dotazOpravduVypujcitDokumentVypujcenVMinulostiNaposledyX", "loan.OpravduVypujcitDokumentVypujcenVMinulostiNaposledyX"));
        add(538, f.updateMessageKey("message.appServer.dotazOpravduVypujcitCtenarMaTitulAktualneVypujcen", "loan.OpravduVypujcitCtenarMaTitulAktualneVypujcen"));
        add(539, f.updateMessageKey("message.appServer.dotazOpravduVypujcitCtenarMaUpominky", "loan.OpravduVypujcitCtenarMaUpominky"));
        add(541, f.updateMessageKey("message.appServer.dotazOpravduVypujcitCtenarMaDluhy", "loan.OpravduVypujcitCtenarMaDluhy"));
        add(542, f.updateMessageKey("message.appServer.dotazOpravduVypujcitZvlastniInfoKLokaci", "loan.OpravduVypujcitZvlastniInfoKLokaci"));
        add(543, f.updateMessageKey("message.appServer.dotazOpravduVypujcitZvlastniInfoKeStatusu", "loan.OpravduVypujcitZvlastniInfoKeStatusu"));
        add(544, f.updateMessageKey("message.appServer.dotazOpravduVypujcitZvlastniInfoKeKategorii", "loan.OpravduVypujcitZvlastniInfoKeKategorii"));
        add(545, f.updateMessageKey("message.appServer.dotazOpravduVypujcitStatusJeZpetnaKatalogizace", "loan.OpravduVypujcitStatusJeZpetnaKatalogizace"));
        add(546, f.updateMessageKey("message.appServer.dotazOpravduVypujcitCtenarMaPropadlouRegistraci", "loan.OpravduVypujcitCtenarMaPropadlouRegistraci"));
        add(547, f.updateMessageKey("message.appServer.dotazOpravduVypujcitNaExemplarJeNeodeslanaRezervace", "loan.OpravduVypujcitNaExemplarJeNeodeslanaRezervace"));
        add(548, f.updateMessageKey("message.appServer.dotazOpravduVypujcitRezervaceJeNaJinyExemplar", "loan.OpravduVypujcitRezervaceJeNaJinyExemplar"));
        add(549, f.updateMessageKey("message.appServer.dotazOpravduVratitCtenarMaBlokovanyTransakce", "loan.OpravduVratitCtenarMaBlokovanyTransakce"));
        add(550, f.updateMessageKey("message.appServer.dotazOpravduVypujcitNaTitulJeNevyrizenaObjednavka", "loan.OpravduVypujcitNaTitulJeNevyrizenaObjednavka"));
        add(551, f.updateMessageKey("message.appServer.nelzeVypujcitBudovaJeOffline", "loan.NelzeVypujcitBudovaJeOffline"));
        add(552, f.insertIniKey("OPAC", "VlastnostiCtenareVPlneRegistraci", 450, "Vlastnosti čtenáře zobrazované v plné registraci. Možné hodnoty jsou: JMENO, PRIJMENI, USERNAME, TITUL, TRV_MI, TRV_UL, TRV_PSC, PRE_MI, PRE_UL, PRE_PSC, ZAMESTNANI, ADR_ZAM, VZDELANI, TRIDA, CISOP, EMAIL, SMSCISLO, TELEFON, TYP_TISK_REZE, TYP_TISK_UPOM", null, "TITUL; JMENO; PRIJMENI; EMAIL; USERNAME; PASSWORD; TELEFON;"));
        add(553, f.insertMessage("reader.CannotExtendRegistrationWhichIsNotExpired", "Nelze prodloužit registraci, čtenář ji má stále platnou.", "Cannot extend registration, registration is still valid"));
        add(554, f.dbDependentSql(
                new String[] {"ALTER TABLE OPAC_LOG_SESSIONS ALTER COLUMN IP_ADRESA TYPE STRING_50"},
                new String[]{})); //zvetseni sloupce pro IP adresu, aby se do ni vesla IPv6
        add(555, f.grantSelect("UBYTKY", "opac"));
        add(556, f.grantSelect("DEF_ZPNAHR", "opac"));
        add(557, f.insertMessage("exemplar.ExeplarVyrazen", "Exemplář byl vyřazen", "Item discarded"));
        add(558, f.deleteMessage("registrace.ProRegistraciKnihovnyPouzijteLink"));
        add(559, f.deleteMessage("commons.exemplarSmazan"));
        add(560, f.insertMessage("exemplar.ExemplarSmazan", "Exemplář byl smazán", "Item deleted"));
        add(561, f.insertMessage("exemplar.ubytkoveCislo", "Úbytkové číslo", "Discard number"));
        add(562, f.insertMessage("exemplar.duvodUbytku", "Důvod úbytku", "Reason"));
        add(563, f.insertMessage("exemplar.zpusobNahrazeniUbytku", "Způsob nahrazení", "Replacement way"));
        add(564, f.insertMessage("exemplar.cenaUbytku", "Cena úbytku", "Cost"));
        add(565, f.insertMessage("exemplar.cisloPoradyUbytku", "Číslo porady", "Consultation number"));
        add(566, f.insertMessage("volume.periodicita.denik", "Deník", "Daily"));
        add(567, f.insertMessage("volume.periodicita.tydenik", "Týdeník", "Weekly"));
        add(568, f.insertMessage("volume.periodicita.mesicnik", "Měsíčník", "Monthly"));
        add(569, f.insertMessage("volume.periodicita.rocnik", "Ročník", "Annual"));
        add(570, f.insertMessage("volume.NovyRocnik", "Nový ročník", "New volume"));
        add(571, f.insertMessage("volume.volumeNumber", "Označení", "Volume number"));
        add(572, f.insertMessage("volume.volumeYear", "Rok", "Year"));
        add(573, f.insertMessage("volume.pocetKusu", "Počet kusů", "Issue quantity"));
        add(574, f.insertMessage("volume.poznamka", "Poznámka", "Note"));
        add(575, f.insertMessage("volume.periodicita", "Periodicita", "Periodicity"));
        add(576, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%dotbot%'")));
        add(577, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%mj12bot%'")));
        add(578, f.disableIniTriggers());
        add(578, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'ZAZNAM-SLOUPCE_SVAZKY'"));
        add(579, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'ZAZNAM-SLOUPCE_SVAZKY'"));
        add(580, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {
                        "ALTER TABLE INI_KEYS ADD STRUKTURA STRING_10 DEFAULT 'SCALAR' NOT NULL",
                        "COMMENT ON COLUMN INI_KEYS.STRUKTURA IS 'Struktura hodnoty klíče, možné hodnoty jsou SCALAR a LIST'"
                },
                new String[]{})));
        add(581, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {
                        "ALTER TABLE INI_KEYS ADD DATOVY_TYP STRING_20",
                        "COMMENT ON COLUMN INI_KEYS.DATOVY_TYP IS 'Datový typ hodnoty (nebo hodnot, pokud se jedna o seznam), možné hodnoty jsou NULL - vyhozi, pokud je nastaven, tak podle FK_VALHOD, jinak text, TEXT - libovolný text, NUMBER - číslo, VALHOD - hodnota z FK_VALHOD'"
                },
                new String[]{})));
        add(582, f.updateIniType("OPAC", "RootAuthority", "SCALAR", "NUMBER"));
        add(583, f.updateIniType("OPAC", "NOVINKY-POUZE_STATUS", "LIST", "NUMBER"));
        add(584, f.updateIniType("OPAC", "ZAKAZANE_DOKUMENTY", "LIST", "NUMBER"));
        add(585, f.updateIniType("OPAC", "STATUSY_ZOBRAZOVANYCH_EXEMPLARU", "LIST", "NUMBER"));
        add(586, f.updateIniType("OPAC", "FondyDokumentu", "LIST", "NUMBER"));
        add(587, f.updateIniType("OPAC", "FondyAutorit", "LIST", "NUMBER"));
        add(588, f.updateIniType("OPAC", "KONTO-VYCHOZI_POCET_DNI_ZAJMU", "SCALAR", "NUMBER"));
        add(589, f.updateIniType("OPAC", "NOVINKY-POCET_NACITANYCH", "SCALAR", "NUMBER"));
        add(590, f.updateIniType("OPAC", "NOVINKY-POCET_ZOBRAZOVANYCH", "SCALAR", "NUMBER"));
        add(591, f.updateIniType("OPAC", "OBECNE-ZAKAZANE_STATUSY_DOKUMENTU", "LIST", "NUMBER"));
        add(592, f.updateIniType("OPAC", "ZAZNAM-MAXIMUM_PODOBNYCH", "SCALAR", "NUMBER"));
        add(593, f.updateIniKeysValhod("OPAC", "CtenaremEditovatelneVlastnostiCtenare", 34));
        add(594, f.insertIniValhod(34, 1, "JMENO", "Křestní jméno"));
        add(595, f.insertIniValhod(34, 2, "PRIJMENI", "Příjmení"));
        add(596, f.insertIniValhod(34, 3, "USERNAME", "Uživatelské jméno"));
        add(597, f.insertIniValhod(34, 5, "TITUL", "Titul"));
        add(598, f.insertIniValhod(34, 6, "TRV_MI", "Město"));
        add(599, f.insertIniValhod(34, 7, "TRV_UL", "Ulice"));
        add(600, f.insertIniValhod(34, 8, "TRV_PSC", "PSČ"));
        add(601, f.insertIniValhod(34, 9, "PRE_MI", "Přechodné město"));
        add(602, f.insertIniValhod(34, 10, "PRE_UL", "Přechodná ulice"));
        add(603, f.insertIniValhod(34, 11, "PRE_PSC", "Přechodné PSČ"));
        add(604, f.insertIniValhod(34, 12, "ZAMESTNANI", "Zaměstnání"));
        add(605, f.insertIniValhod(34, 13, "ADR_ZAM", "Adresa zam."));
        add(606, f.insertIniValhod(34, 14, "VZDELANI", "Vzdělání"));
        add(607, f.insertIniValhod(34, 15, "TRIDA", "Třída"));
        add(608, f.insertIniValhod(34, 16, "CISOP", "Číslo OP"));
        add(609, f.insertIniValhod(34, 17, "EMAIL", "Email"));
        add(610, f.insertIniValhod(34, 18, "SMSCISLO", "Číslo pro SMS"));
        add(611, f.insertIniValhod(34, 19, "TELEFON", "Telefonní číslo"));
        add(612, f.insertIniValhod(34, 20, "TYP_TISK_REZE", "Typ tisku rezervací"));
        add(613, f.insertIniValhod(34, 21, "TYP_TISK_UPOM", "Typ tisku upomínek"));
        add(614, f.updateIniType("OPAC", "CtenaremEditovatelneVlastnostiCtenare", "LIST", "VALHOD"));
        add(615, f.insertIniValhod(34, 4, "PASSWORD", "Heslo"));
        add(616, f.updateIniType("OPAC", "VlastnostiCtenareVPlneRegistraci", "LIST", "VALHOD"));
        add(618, f.updateIniKeysValhod("OPAC", "VlastnostiCtenareVPlneRegistraci", 34));
        add(619, f.insertIniValhod(35, 1, "PRIR_CISLO", "Přírůstkové číslo"));
        add(620, f.insertIniValhod(35, 2, "BAR_COD", "Čárový kód"));
        add(621, f.insertIniValhod(35, 3, "SIGNATURA", "Signatura"));
        add(622, f.insertIniValhod(35, 4, "LOKACE", "Lokace"));
        add(623, f.insertIniValhod(35, 5, "KATEGORIE", "Kategorie výpůjčky"));
        add(624, f.insertIniValhod(35, 6, "DOSTUPNOST", "Dostupnost"));
        add(625, f.insertIniValhod(35, 7, "BUDOVA", "Budova"));
        add(626, f.insertIniValhod(35, 8, "TEM_SKUP", "Tematická skupina"));
        add(627, f.insertIniValhod(35, 9, "STATUS", "Status ex."));
        add(628, f.insertIniValhod(35, 10, "POZNAMKA", "Poznámka"));
        add(629, f.insertIniValhod(35, 11, "CENA", "Cena"));
        add(630, f.insertIniValhod(35, 12, "UZIVATEL", "Uživatel"));
        add(631, f.insertIniValhod(35, 13, "ROK_PRIR", "Rok přírůstku"));
        add(632, f.insertIniValhod(35, 14, "ZPUSOB_NABYTI", "Způsob nabytí"));
        add(633, f.insertIniValhod(35, 15, "ZPUSOB_ZALOZENI", "Způsob založení"));
        add(634, f.insertIniValhod(35, 16, "INDIVIDUALNI_HODNOTA", "Individuální hodnota (např. stavěcí znak apod.)"));
        add(635, f.insertIniValhod(35, 17, "DATUM", "Datum"));
        add(636, f.insertIniValhod(35, 18, "UMISTENI", "Umístění v knihovně"));
        add(637, f.insertIniValhod(35, 19, "HOLDER", "Aktuální vypůjčitel"));
        add(638, f.updateIniKeysValhod("OPAC", "ZAZNAM-SLOUPCE_EXEMPLARE", 35));
        add(639, f.updateIniType("OPAC", "ZAZNAM-SLOUPCE_EXEMPLARE", "LIST", "VALHOD"));
        add(640, f.insertIniValhod(36, 1, "PRIR_CISLO", "Přírůstkové číslo"));
        add(641, f.insertIniValhod(36, 2, "BAR_COD", "Čárový kód"));
        add(642, f.insertIniValhod(36, 3, "SIGNATURA", "Signatura"));
        add(643, f.insertIniValhod(36, 4, "LOKACE", "Lokace"));
        add(644, f.insertIniValhod(36, 5, "KATEGORIE", "Kategorie výpůjčky"));
        add(645, f.insertIniValhod(36, 6, "DOSTUPNOST", "Dostupnost"));
        add(646, f.insertIniValhod(36, 7, "BUDOVA", "Budova"));
        add(647, f.insertIniValhod(36, 8, "TEM_SKUP", "Tematická skupina"));
        add(648, f.insertIniValhod(36, 9, "STATUS", "Status ex."));
        add(649, f.insertIniValhod(36, 10, "POZNAMKA", "Poznámka"));
        add(650, f.insertIniValhod(36, 11, "CENA", "Cena"));
        add(651, f.insertIniValhod(36, 12, "UZIVATEL", "Uživatel"));
        add(652, f.insertIniValhod(36, 13, "ROK_PRIR", "Rok přírůstku"));
        add(653, f.insertIniValhod(36, 14, "ZPUSOB_NABYTI", "Způsob nabytí"));
        add(654, f.insertIniValhod(36, 15, "ZPUSOB_ZALOZENI", "Způsob založení"));
        add(655, f.insertIniValhod(36, 16, "INDIVIDUALNI_HODNOTA", "Individuální hodnota (např. stavěcí znak apod.)"));
        add(656, f.insertIniValhod(36, 17, "DATUM", "Datum"));
        add(657, f.insertIniValhod(36, 18, "UMISTENI", "Umístění v knihovně"));
        add(658, f.insertIniValhod(36, 19, "HOLDER", "Aktuální vypůjčitel"));
        add(659, f.insertIniValhod(36, 20, "CISLO", "Číslo"));
        add(660, f.insertIniValhod(36, 21, "CLANKY", "Články"));
        add(661, f.updateIniKeysValhod("OPAC", "ZAZNAM-SLOUPCE_CISLA", 36));
        add(662, f.updateIniType("OPAC", "ZAZNAM-SLOUPCE_CISLA", "LIST", "VALHOD"));
        add(663, f.updateIniDefaultValue("OPAC", "ZAZNAM-SLOUPCE_CISLA", "CISLO; BAR_COD; SIGNATURA; BUDOVA; LOKACE; KATEGORIE; DOSTUPNOST;"));
        add(664, f.insertMessage("exemplar.datumPorizeni", "Dat.pořízení", "Date"));
        add(665, f.insertMessage("konto.SoucetPokutZaAktualniVypujcky", "Součet pokut za aktuální výpůjčky", "Sum of penalties for current loans"));
        add(666, f.deleteMessage("message.warning.nepovolenaAkce"));
        add(667, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%prtg network monitor%'")));
        add(668, f.insertMessage("loan.OpravduVyzadatJizVMinulostiPujceno", "Knihu jste již měl vypůjčenou, opravdu vyžádat?", "You have allready lent this book in past, really request?"));
        add(669, f.deleteMessage("detail.odeslatPozadavek"));
        add(670, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%spbot%'")));
        add(671, f.insertMessage("loan.VypKatNepovolujePujceni", "Kategorie výpůjčky nepovoluje půjčení", "Cannot lend because of loan category"));
        add(672, f.insertMessage("loan.NelzeVypujcitNaCiziBudove", "Nelze vypůjčit na cizí budově", "Cannot lend on else building"));
        add(673, f.updateMessageKey("message.appServer.nenalezenaVypujcka", "loan.VypujckaNenalezena"));
        add(674, f.updateMessageKey("message.appServer.nelzeMazatPripraveneRezervace", "loan.NelzeRusitPripraveneRezervace"));
        add(675, f.updateMessageKey("message.appServer.nelzeMazatMVS", "loan.NelzeRusitMvs"));
        add(676, f.updateMessageKey("message.warning.ctenarBlokovan", "loan.NelzeProdlouzitCtenarBlokovan"));
        add(677, f.updateMessageKey("message.appServer.mateProslouDobuRegistrace", "loan.NelzeProdlouzitMateProslouRegistraci"));
        add(678, f.updateMessageKey("message.appServer.nelzeProlongovatRezervaci", "loan.NelzeProdlouzitRezervaci"));
        add(679, f.updateMessageKey("message.appServer.neplatnyIdEx", "loan.NeplatneIdExemplare"));
        add(680, f.updateMessageKey("message.appServer.idExNeexistuje", "loan.ExemplarSTimtoIdNeexistuje"));
        add(681, f.updateMessageKey("message.appServer.vypujckaJeUpominana", "loan.NelzeProdlouzitVypujckaJeUpominana"));
        add(682, f.updateMessageKey("message.appServer.nelzeProlongovatMVS", "loan.NelzeProdlouzitMvs"));
        add(683, f.updateMessageKey("message.appServer.jizDnesProlongovano", "loan.NelzeProdlouzitDnesJizProdlouzeno"));
        add(684, f.updateMessageKey("message.appServer.nelzeVicekratProlongovat", "loan.NelzeVicekratProdlouzit"));
        add(685, f.updateMessageKey("message.appServer.naTitulPodanaRezervace", "loan.NelzeProdlouzitTitulJeZarezervovan"));
        add(686, f.updateMessageKey("message.appServer.jizNedavnoProlongovano", "loan.NelzeProdlouzitJizNedavnoProdlouzeno"));
        add(687, f.updateMessageKey("message.appServer.nelzeProlongovatPrekrocenTerminVraceni", "loan.NelzeProdlouzitPrekrocenTerminVraceni"));
        add(688, f.insertMessage("loan.OpravduProdlouzitTitulJeZarezervovan", "Titul je zarezervován, opravdu prodloužít?", "Document is reserved, really renew?"));
        add(689, f.updateMessageKey("message.appServer.nelzeProlongovatPasivniMVS", "loan.NelzeProdlouzitPasivniMvs"));
        add(690, f.updateMessageKey("renewal.nelzeProlongovatExemplarJeVCirkulaci", "loan.NelzeProdlouzitExemplarVCirkulaci"));
        add(691, f.updateMessageKey("message.appServer.zaznamSTimtoIdNeexistuje", "loan.ZaznamSTimtoIdNeexistuje"));
        add(692, f.updateMessageKey("message.appServer.ctenarMaBlokovanyTransakce", "loan.CtenarMaBlokovanyTransakce"));
        add(693, f.updateMessageKey("message.appServer.ctenarJeVyrazenNeboSmazan", "loan.CtenarJeVyrazenNeboSmazan"));
        add(694, f.updateMessageKey("message.appServer.ctenarSTimtoIdNeexistuje", "loan.CtenarSTimtoIdNeexistuje"));
        add(695, f.insertMessage("loan.OpravduVratitBudeVygenerovanaPokuta", "Opravdu vrátit? Bude vygenerována pokuta."));
        add(696, f.insertMessage("loan.PoznamkaKVraceniX", "Poznámka: {0}"));
        add(697, f.updateMessage("detail.svazaneRocniky", "Svázané ročníky", "Bound volumes"));
        add(698, f.updateMessageKey("detail.volnyNaBudove", "loan.VolnyNaBudove"));
        add(699, f.updateMessageKey("detail.volnyNaBudovach", "loan.VolnyNaBudovach"));
        add(700, f.updateMessageKey("detail.naOstatnichBudovach", "loan.NaOstatnichBudovach"));
        add(701, f.updateMessageKey("detail.dokumentNeniZarezervovan", "loan.DokumentNeniZarezervovan"));
        add(702, f.updateMessageKey("detail.dokumentJeXxZarezervovan", "loan.DokumentJeXxZarezervovan"));
        add(703, f.updateMessageKey("commons.XKVypujceniAYPrezencneZZ", "loan.XKVypujceniAYPrezencneZZ"));
        add(704, f.updateMessageKey("commons.dokumentNemaExemplare", "loan.DokumentNemaExemplare"));
        add(705, f.updateMessageKey("loans.NotRenewed", "loan.NotRenewed"));
        add(705, f.updateMessageKey("loans.NejdriveZvolteZdaChceteKnihuObjednatNeboRezervovat", "loan.NejdriveZvolteZdaChceteKnihuObjednatNeboRezervovat"));
        add(706, f.updateMessageKey("loans.ExemplarJeKVyzvednutiNaBudove", "loan.ExemplarJeKVyzvednutiNaBudove"));
        add(707, f.updateMessageKey("loans.DokumentJeKVyzvednutiNaBudove", "loan.DokumentJeKVyzvednutiNaBudove"));
        add(708, f.updateMessageKey("loans.DokumentJeKVyzvednutiNaBudovach", "loan.DokumentJeKVyzvednutiNaBudovach"));
        add(709, f.updateMessageKey("loans.ExemplarJeKObjednaniNaBudove", "loan.ExemplarJeKObjednaniNaBudove"));
        add(710, f.updateMessageKey("loans.DokumentJeKObjednaniNaBudove", "loan.DokumentJeKObjednaniNaBudove"));
        add(711, f.updateMessageKey("loans.DokumentJeKObjednaniNaBudovach", "loan.DokumentJeKObjednaniNaBudovach"));
        add(712, f.updateMessageKey("loans.IWantWaitUpTo", "loan.IWantWaitUpTo"));
        add(713, f.updateMessageKey("loans.DokumentJeKZarezervovaniNaBudove", "loan.DokumentJeKZarezervovaniNaBudove"));
        add(714, f.updateMessageKey("loans.DokumentJeKZarezervovaniNaBudovach", "loan.DokumentJeKZarezervovaniNaBudovach"));
        add(715, f.updateMessageKey("loans.RenewAllAtOnce", "loan.RenewAllAtOnce"));
        add(716, f.updateMessageKey("loans.RequestAll", "loan.RequestAll"));
        add(717, f.insertMessage("commons.Tezaurus", "Tezaurus", "Thesaurus"));
        add(718, f.updateMessageKey("detail.dokumentNemaExemplare", "detail.ZadneExemplareKDispozici"));
        add(719, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"CREATE OR ALTER VIEW VIEW_OPAC_NEWS (FK_ZAZ, FK_DOKFOND, DATUM, FK_BUDOVA, STATUS4, FK_STATUS) AS SELECT KAT1_5.FK_ZAZ, KAT1_4.FK_DOKFOND, MAX(KAT1_5.datum) DATUM, KAT1_5.FK_BUDOVA, KAT1_4.STATUS4, KAT1_5.FK_STATUS FROM KAT1_5 JOIN KAT1_4 ON KAT1_4.ID_ZAZ = KAT1_5.FK_ZAZ WHERE (datum >= cast('TODAY' as date) - 500) GROUP BY KAT1_5.FK_ZAZ, KAT1_4.FK_DOKFOND, KAT1_4.STATUS4, KAT1_5.FK_STATUS, KAT1_5.FK_BUDOVA"},
                new String[]{})));
        add(720, f.updateMessageKey("exemplar.prilohy", "exemplar.attachments"));
        add(721, f.updateMessageKey("exemplar.poradi", "exemplar.order"));
        add(722, f.updateMessageKey("exemplar.dodavatel", "exemplar.supplier"));
        add(723, f.updateMessageKey("exemplar.polozkaFaktury", "exemplar.invoiceItem"));
        add(724, f.updateMessageKey("exemplar.cisloFaktury", "exemplar.invoiceNumber"));
        add(725, f.updateMessageKey("exemplar.individualniHodnota", "exemplar.customValue"));
        add(726, f.updateMessageKey("exemplar.poznamka", "exemplar.note"));
        add(727, f.updateMessageKey("exemplar.lokace", "exemplar.location"));
        add(728, f.updateMessageKey("exemplar.tematickaSkupina", "exemplar.thematicGroup"));
        add(729, f.updateMessageByKeyAndCzech("detail.ZadneExemplareKDispozici", "Dokument nemá exempláře", "Žádné exempláře k dispozici", "No exemplars"));
        add(730, f.disableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(730, new ExceptionCatchingUpdate(f.sql("UPDATE def_hledradek SET VYCETPOLI = 'P41' where UPPER(NAZEV) = 'JAZYK' and VYCETPOLI = 'REZS_JAZYK' and FK_HLED in (103,102)")));
        add(731, f.grantSelect("DEF_STAVZ", "opac"));
        add(732, f.insertMessage("registrace.WebovyPristup", "Webový přístup", "Web access"));
        add(733, f.insertMessage("registrace.ZvolteProRegistraciWebovehoPristupu", "Zvolte, pokud jste již v knihovně evidován/a, ale nemáte přístupové údaje do webového katalogu.", "Choose, when you allready have reader account in library, but you have not credentials to this catalog."));
        add(734, f.insertMessage("registrace.Predregistrace", "Předregistrace", "Pre-registration"));
        add(735, f.insertMessage("registrace.ZvolteProNovouRegistraci", "Zvolte, pokud v knihovně nejste ještě zaevidován/a a chcete se nově zaregistrovat.", "Choose, when you are not registered in library yet."));
        add(736, f.insertMessage("registrace.MvsKnihovna", "MVS knihovna"));
        add(737, f.insertMessage("registrace.ZvolteProRegistraciMvsKnihovny", "Zvolte, pokud v jste jiná knihovna a chcete využívat MVS této knihovny.", "Choose, when you are other library and want to use ILL services of this library."));
        add(738, f.insertIniValhod(35, 20, "ROZSAH", "Rozsah vazby"));
        add(739, f.insertMessage("exemplar.bindingIssueRange", "Rozsah", "Range"));
        add(740, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_NADPISU",
                """
                        <div class="panel-body">
                          #sf(910 ''b'' ''{detail.signatura}: '')
                          #sf(80 ''a'' '' MDT: '')
                        </div>"""));
        add(741, f.disableIniTriggers());
        add(741, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'RozsireneDefaultDiakritika'"));
        add(742, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'RozsireneDefaultDiakritika'"));
        add(743, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'RozsireneDefaultSklonovani'"));
        add(744, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'RozsireneDefaultSklonovani'"));
        add(745, f.deleteMessage("hledani.hledatSDiakritikou"));
        add(746, f.deleteMessage("hledani.hledatPodobna"));
        add(747, f.deleteMessage("hledani.prohledatTituly"));
        add(748, f.deleteMessage("hledani.title.hledaniNovin"));
        add(749, f.deleteMessage("hledani.hledatVPrilozenychTextech"));
        add(750, f.deleteMessage("commons.obdobi"));
        add(751, new ExceptionCatchingUpdate(f.sql("DROP TABLE OPAC_COVERS")));
        add(752, new ExceptionCatchingUpdate(f.sql("DROP TABLE OPAC_DISKUSE")));
        add(752, new ExceptionCatchingUpdate(f.sql("DROP TABLE OPAC_SDI")));
        add(753, new ExceptionCatchingUpdate(f.sql("DROP SEQUENCE SEQ_ID_OPAC_SDI")));
        add(754, new ExceptionCatchingUpdate(f.sql("DROP SEQUENCE SEQ_ID_OPAC_TAG_2")));
        add(755, f.dbDependentSql(
                new String[] {
                        "CREATE TABLE OPAC_SDI (\n" +
                                "   ID_OPAC_SDI GENERATORY NOT NULL PRIMARY KEY,\n" +
                                "   FK_CTEN INT_NONULL,\n" +
                                "   FK_UZIV SMALL_NULL,\n" +
                                "   NAZEV STRING_50 NOT NULL,\n" +
                                "   QUERY UTF_4000 NOT NULL,\n" +
                                "   PERIODICITA SMALL_NOTNULL,\n" +
                                "   JE_POVOL BOOLEAN DEFAULT 1,\n" +
                                "   DAT_VYTVORENI TYPEDATE NOT NULL,\n" +
                                "   DAT_UKONCENI TYPEDATE\n" +
                                ")",
                        "CREATE SEQUENCE SEQ_ID_OPAC_SDI",
                        "CREATE TRIGGER TRG_OPAC_SDI_BI0 for OPAC_SDI\n" +
                                "active before insert position 0\n" +
                                "as\n" +
                                "begin\n" +
                                "   new.ID_OPAC_SDI = next value for SEQ_ID_OPAC_SDI;\n" +
                                "end"
                },
                new String[]{}));
        add(756, new ExceptionCatchingUpdate(f.sql("DROP SEQUENCE SEQ_ID_OPAC_SEARCHED_WORDS")));
        add(756, f.grantSelect("OPAC_SDI", "opac"));
        add(757, f.updateMessageKey("commons.ulozit", "commons.Ulozit"));
        add(758, f.insertMessage("sdi.Periodicity", "Periodicita", "Periodicity"));
        add(759, f.insertMessage("commons.DatumVytvoreni", "Datum vytvoření", "Create date"));
        add(760, f.insertMessage("commons.DatumUkonceni", "Datum ukončení", "Termination date"));
        add(761, f.insertMessage("sdi.DatumPoslednihoOdeslani", "Datum posledního odeslání", "Last send date"));
        add(762, f.insertMessage("sdi.periodicity.Daily", "Denně", "Daily"));
        add(763, f.insertMessage("sdi.periodicity.Weekly", "Týdně", "Weekly"));
        add(764, f.insertMessage("sdi.periodicity.Monthly", "Měsíčně", "Monthly"));
        add(765, f.insertMessage("sdi.NastaveniOdesilani", "Nastavení odesílání nových výsledků", "New results sending settings"));
        add(766, f.insertMessage("sdi.PosilatNovinkyZTohotoHledani", "Posílat novinky z tohoto hledání", "Periodically send news of this search"));
        add(767, f.insertMessage("commons.Ulozeno", "Uloženo", "Saved"));
        add(768, f.dbDependentSql(
                new String[] {
                        "CREATE TABLE OPAC_SDI_SENDINGS (\n" +
                                "   FK_OPAC_SDI GENERATORY NOT NULL,\n" +
                                "   CAS DATETIME NOT NULL,\n" +
                                "   POC_ZAZN INT_NONULL,\n" +
                                "   CHYBA STRING_512\n" +
                                ")"},
                new String[]{}));
        add(769, f.grantSelect("OPAC_SDI_SENDINGS", "opac"));
        add(770, f.insertMessage("sdi.SdiRequests", "Zasílání novinek z vyhledávání", "Sending news in searches"));
        add(771, f.insertMessage("sdi.ZobrazitVyhledaneZaznamy", "Zobrazit vyhledávání", "Show search"));
        add(772, f.insertMessage("commons.Vypnuto", "Vypnuto", "Off"));
        add(772, f.insertMessage("commons.Vypnout", "Vypnout", "Turn off"));
        add(773, f.insertMessage("commons.Zapnout", "Zapnout", "Turn on"));
        add(774, f.grantSelect("VYKONY_DOK", "opac"));
        add(775, f.deleteMessage("sdi.DatumPoslednihoOdeslani"));
        add(776, f.insertMessage("sdi.ZasilaniXNaY", "Zasílání <strong>{0}</strong> na {1}", "Sending <strong>{0}</strong> to {1}"));
        add(777, f.insertMessage("commons.Vytvoreno", "Vytvořeno", "Created"));
        add(778, f.insertMessage("commons.do", "do", "to"));
        add(779, f.insertMessage("commons.Vyhledano", "Vyhledáno", "Searched"));
        add(780, f.insertMessage("sdi.NalezenoXNovinek", "nalezeno {0} novinek.", "found {0} news."));
        add(781, f.insertMessage("commons.ZadnePolozky", "Žádné položky", "No items"));
        add(782, f.insertMessage("extZdroje.CtenarNemaNastavenyEmail", "Nelze vypůjčit, nemáte nastavený email", "Cannot lend, you have not filled an email"));
        add(783, f.grantSelect("DEF_UZIV", "opac"));
        add(784, new ExceptionCatchingUpdate(f.sql("DROP TABLE OPAC_DEZIDERATA")));
        add(785, f.dbDependentSql(
                new String[] {
                        "CREATE TABLE OPAC_SEARCH_KEYS (\n" +
                                "   NAZEV STRING_30 NOT NULL,\n" +
                                "   KLIC STRING_30 NOT NULL,\n" +
                                "   TYP_HODN STRING_30 NOT NULL\n" +
                                ")"},
                new String[]{}));
        add(786, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('FOND_DOKUMENTU', 'FOND', 'FOND_DOKUMENTU')"));
        add(787, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('FOND_AUTORIT', 'FOND', 'FOND_AUTORIT')"));
        add(788, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('LOKACE', 'REZS_LOKACE', 'LOKACE')"));
        add(789, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('BUDOVA', 'REZS_BUDOVA', 'BUDOVA')"));
        add(790, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('SIGNATURA', 'PSIGNATURA', 'SIGNATURA')"));
        add(791, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('PRIRCISLO', 'PPRIRCISLO', 'PRIRCISLO')"));
        add(792, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('ROK_VYDANI', 'REZS_ROK', 'ROZSAH_ROK')"));
        add(793, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('P20', 'P20', 'ISBN')"));
        add(794, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('P22', 'P22', 'ISSN')"));
        add(795, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('P41', 'P41', 'VAL63K')"));
        add(796, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('P44', 'P44', 'VAL44A')"));
        add(797, f.sql("INSERT INTO OPAC_SEARCH_KEYS (NAZEV, KLIC, TYP_HODN) values ('P799', 'P799', 'VAL799A')"));
        add(798, f.disableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(798, f.sql("UPDATE def_hledradek SET VYCETPOLI = 'SIGNATURA' where VYCETPOLI = 'PSIGNATURA' and FK_HLED in (103,102)"));
        add(798, f.sql("UPDATE def_hledradek SET VYCETPOLI = 'PRIRCISLO' where VYCETPOLI = 'PPRIRCISLO' and FK_HLED in (103,102)"));
        add(799, f.grantSelect("OPAC_SEARCH_KEYS", "opac"));
        add(800, f.disableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(800, f.sql("UPDATE def_hledradek SET VYCETPOLI = 'ROK_VYDANI' where VYCETPOLI = 'REZS_ROK' and FK_HLED in (103,102)"));
        add(801, f.insertMessage("loan.NelzeProdlouzitCisloJeJizRezervovano", "Nelze prodloužit, číslo je již zarezervováno", "Cannot renew, issue is reserved"));
        add(802, f.insertMessage("loan.NelzeProdlouzitExterniVypujcku", "Nelze prodloužit externí výpůjčku", "Cannot renew external loan"));
        add(803, f.insertMessage("loan.NelzeVratitExterniVypujcku", "Nelze vrátit externí výpůjčku", "Cannot return external loan"));
        add(804, f.disableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(804, f.sql("UPDATE OPAC_SEARCH_KEYS SET NAZEV = 'FOND_DOK', TYP_HODN = 'FOND_DOK' WHERE NAZEV = 'FOND_DOKUMENTU'"));
        add(805, f.sql("UPDATE OPAC_SEARCH_KEYS SET NAZEV = 'FOND_AUT', TYP_HODN = 'FOND_AUT' WHERE NAZEV = 'FOND_AUTORIT'"));
        add(806, f.sql("UPDATE def_hledradek SET VYCETPOLI = 'FOND_DOK' where VYCETPOLI = 'FOND_DOKUMENTU' and FK_HLED in (103,102)"));
        add(807, f.sql("UPDATE def_hledradek SET VYCETPOLI = 'FOND_AUT' where VYCETPOLI = 'FOND_AUTORIT' and FK_HLED in (103,102)"));
        add(808, f.updateIniType("OPAC", "FondyDokumentu", "LIST", "FOND_DOK"));
        add(809, f.updateIniType("OPAC", "FondyAutorit", "LIST", "FOND_AUT"));
        add(810, f.updateIniType("OPAC", "OBECNE-ZAKAZANE_STATUSY_DOKUMENTU", "LIST", "STATUS_DOK"));
        add(811, f.updateIniType("OPAC", "STATUSY_ZOBRAZOVANYCH_EXEMPLARU", "LIST", "STATUS_EX"));
        add(812, f.updateIniType("OPAC", "NOVINKY-POUZE_STATUS", "LIST", "STATUS_EX"));
        add(813, f.updateIniType("OPAC", "BudovyPasuNovinek", "LIST", "BUDOVA"));
        add(814, f.insertMessage("seznam.novinkyOdXDoY", "Novinky od {0} do {1}", "News from {0} to {1}"));
        add(815, f.insertMessage("loan.VypujcitEBook", "Vypůjčit e-Knihu", "Lend e-Book"));
        add(816, f.insertMessage("loan.eVypujcka", "eVýpůjčka", "eLoan"));
        add(817, new ExceptionCatchingUpdate(f.grantSelect("HL_KOLEKCE", "opac")));
        add(818, new ExceptionCatchingUpdate(f.grantSelect("POL_KOLEKCE", "opac")));
        add(819, new ExceptionCatchingUpdate(f.grantSelect("POM_KOLEKCE", "opac")));
        add(820, f.updateMessageKey("exemplar.majitel", "exemplar.owner"));
        add(821, f.updateMessageKey("exemplar.zpusobNabyti", "exemplar.acquisitionWay"));
        add(822, f.updateMessageKey("exemplar.cena", "exemplar.price"));
        add(823, f.deleteMessage("exemplar.umisteni"));
        add(824, f.updateMessageKey("commons.umisteni", "exemplar.placement"));
        add(825, f.updateMessageKey("exemplar.budova", "exemplar.building"));
        add(826, new ExceptionCatchingUpdate(f.insertMessage("exemplar.placement", "Regál", "Regal")));
        add(827, f.insertMessage("loan.ereading.VypujckaProvedenaNaEmailX", "Výpůjčka přes eReading úspěšně provedena, knihu stáhnete <a href=\"https://www.ereading.cz/cs/moje-evypujcky\" target=\"_blank\">v kontě eReading</a>.<br/>Pro přihlášení (registraci) použijte email {0}.", "eReading load was successful, book is to download at <a href=\"https://www.ereading.cz/cs/moje-evypujcky\" target=\"_blank\">your eReading account</a>.<br/>For login (registration) use email {0}."));
        add(828, f.deleteMessage("message.exception.nenastavenaIniHodnota"));
        add(829, f.deleteMessage("message.exception.prazdnaHodnotaProZkonvertovani"));
        add(830, f.deleteMessage("message.exception.chybnaNeboNepodporovanaIniHodnota"));
        add(831, f.deleteMessage("message.exception.nacteniDokumentu"));
        add(832, f.deleteMessage("message.exception.nacteniDetailuDokumentu"));
        add(833, f.deleteMessage("message.exception.savingCtenar"));
        add(834, f.insertMessage("commons.DataSavingError", "Chyba při ukládání dat", "Error while data saving"));
        add(835, f.deleteMessage("message.validation.prijmeniNotMatch"));
        add(836, f.updateMessageTranslation("commons.rezervovat", "text_eng", "Request a hold"));
        add(841, () -> {
            try {
                List<String> duplicatedUsernames = jdbcTemplate.queryForList("select username from opac_pin group by username having count(username) > 1 order by count(username) desc", String.class);
                if (!duplicatedUsernames.isEmpty()) {
                    log.error("OPAC_PIN allows duplicate usernames!! Check primary key constraint on username column!");
                    for (String username : duplicatedUsernames) {
                        List<Integer> readerIds = jdbcTemplate.queryForList(String.format("SELECT fk_cten FROM opac_pin WHERE username = '%s'", username), Integer.class);
                        for (int i = 1; i < readerIds.size(); i++) { //smazeme vsechny krome prvniho
                            log.warn("deleting duplicated username " +username+ " (reader id " +readerIds.get(i)+ ")");
                            jdbcTemplate.update(String.format("DELETE FROM opac_pin WHERE fk_cten = %d", readerIds.get(i)));
                        }
                        log.warn("Adding USERNAME primary key constraint to OPAC_PIN");
                        jdbcTemplate.update("ALTER TABLE OPAC_PIN ADD CONSTRAINT PK_OPAC_PIN PRIMARY KEY (USERNAME)");
                    }
                }
            } catch (RuntimeException e) {
                log.error("error while repairing duplicate usernames", e);
            }
        });
        add(846, f.enableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(850, f.updateMessageByKeyAndCzech("login.jsteTuPoprve", "Jste tu poprvé?", "Nejste registrován?", "Not registered?"));
        add(851, f.deleteMessage("login.jsteTuPoprve"));
        add(852, f.updateMessageByKeyAndCzech("login.prvniPrihlaseni", "První přihlášení", "Nemám registraci", "Not have account"));
        add(853, f.updateMessage("ctenari.pocatecniPismenaCtenare", "První písmena nebo čár.kód čtenáře", "First letters or BC"));
        add(854, f.insertMessage("konto.MvsZadankyNaX", "MVS žádanky na {0}", "ILL requests to {0}"));
        add(855, f.insertMessage("konto.MvsZadankyZX", "MVS žádanky z {0}", "ILL requests from {0}"));
        add(856, f.insertMessage("commons.Zadatel", "Žadatel", "Requester"));
        add(857, f.insertMessage("mvs.executor", "Vyřizující knihovna", "Executing library"));
        add(858, f.sql("INSERT INTO INI_SEKCE (poradi, id_sekce, suffix_num, limit_od, limit_do) values (35, 'OPAC_LOAN', 0, null, null)"));
        add(859, f.insertIniKey("OPAC_LOAN", "MvsDefaultPocetDniZajmu", 400, "Výchozí hodnota počtu dní, po které má čtenář o MVS zájem", null, "100", "SCALAR", "NUMBER"));
        add(860, f.disableIniTriggers());
        add(860, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_LOAN', FK_KLIC = 'MvsDefaultPocetDniZajmu' WHERE FK_KLIC = 'KONTO-VYCHOZI_POCET_DNI_ZAJMU_MVS'"));
        add(861, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'KONTO-VYCHOZI_POCET_DNI_ZAJMU_MVS'"));
        add(862, f.insertIniKey("OPAC_LOAN", "DefaultPocetDniZajmu", 200, "Výchozí hodnota počtu dní, po které má čtenář o objednávku/rezervaci zájem", null, "30", "SCALAR", "NUMBER"));
        add(863, f.disableIniTriggers());
        add(863, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_LOAN', FK_KLIC = 'DefaultPocetDniZajmu' WHERE FK_KLIC = 'KONTO-VYCHOZI_POCET_DNI_ZAJMU'"));
        add(864, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'KONTO-VYCHOZI_POCET_DNI_ZAJMU'"));
        add(865, f.sql("INSERT INTO INI_SEKCE (poradi, id_sekce, suffix_num, limit_od, limit_do) values (36, 'OPAC_USER', 0, null, null)"));
        add(866, f.insertIniKey("OPAC_USER", "ZmenaHesla", 200, "Zda si může čtenář sám měnit heslo", 1, "ANO", "SCALAR", "VALHOD"));
        add(866, f.disableIniTriggers());
        add(866, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_USER', FK_KLIC = 'ZmenaHesla' WHERE FK_KLIC = 'KONTO-POVOLENA_ZMENA_HESLA'"));
        add(866, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'KONTO-POVOLENA_ZMENA_HESLA'"));
        add(866, f.insertIniKey("OPAC_USER", "Login", 150, "Způsob klasického přhlašování čtenářů (tzn. z databáze). Může jich být i více, pak bude systém dané způsoby zkoušet postupně dokud se nepřihlásí.", null, "Username-Password", "LIST", "TEXT"));
        add(866, f.disableIniTriggers());
        add(866, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_USER', FK_KLIC = 'Login' WHERE FK_KLIC = 'ZPUSOB_PRIHLASENI_CTENARU'"));
        add(867, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'ZPUSOB_PRIHLASENI_CTENARU'"));
        add(868, f.insertIniKey("OPAC_USER", "ExternalLogin", 160, "Seznam externích přihlašovacích služeb. Podporované jsou JA-SIG, syntaxe: jasig(https://xxx.cz/jasig, https://xxx.cz/jasig/login)", null, null, "LIST", "TEXT"));
        add(869, f.disableIniTriggers());
        add(869, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_USER', FK_KLIC = 'ExternalLogin' WHERE FK_KLIC = 'OBECNE-EXTERNI_PRIHLASOVACI_SYSTEMY'"));
        add(870, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'OBECNE-EXTERNI_PRIHLASOVACI_SYSTEMY'"));
        add(871, f.insertMessage("commons.Oznaceni", "Označení", "Designation"));
        add(872, () -> {
            File messagesDirectory = new File(customFolderPath, "messages");
            if (messagesDirectory.exists()) {
                try {
                    org.apache.commons.io.FileUtils.deleteDirectory(messagesDirectory);
                    log.warn("Successfully deleted custom messages directory (file {})", messagesDirectory.getAbsolutePath());
                } catch (IOException e) {
                    log.error("Directory messages not deleted (file {})", messagesDirectory.getAbsolutePath(),e);
                }
            } else {
                log.warn("Custom messages directory (file {}) is not in custom folder", messagesDirectory.getAbsolutePath());
            }
        });
        add(872, f.deleteMessage("hledani.dalsiParametry"));
        add(873, f.deleteMessage("hledani.vyhledavanyDotazJeVPoradku"));
        add(874, f.deleteMessage("hledani.vyhledavanyDotazObsahujeChybu"));
        add(875, f.disableIniTriggers());
        add(875, () -> {
            String oldValue = ListUtil.firstOrNull(jdbcTemplate.queryForList("SELECT HODNOTA from INI_FILE where fk_klic = 'Login' and fk_sekce = 'OPAC_USER'", String.class));
            if (oldValue != null) {
                @SuppressWarnings("ExtractMethodRecommender") String newValue = oldValue;
                newValue = newValue.replace("Username-CarovyKod", "Username-BC");
                newValue = newValue.replace("Username-CisloLegitimace", "Username-CN");
                newValue = newValue.replace("CarovyKod-RokNarozeni", "BC-BirthYear");
                newValue = newValue.replace("CisloLegitimaceNeboCarovyKod-Prijmeni", "CNOrBC-LastName");
                newValue = newValue.replace("CisloLegitimace-Password", "CN-Password");
                newValue = newValue.replace("CarovyKod-Password", "BC-Password");
                newValue = newValue.replace("Prijmeni-CisloLegitimace", "LastName-CN");
                newValue = newValue.replace("Prijmeni-CarovyKod", "LastName-BC");
                newValue = newValue.replace("CisloLegitimace-", "CN-");
                newValue = newValue.replace("CarovyKod-", "BC-");
                if (!newValue.equals(oldValue)) {
                    log.warn("Changing ini OPAC_USER.Login value \"{}\" to \"{}\"", oldValue, newValue);
                    jdbcTemplate.update(String.format("UPDATE INI_FILE SET HODNOTA = '%s' where fk_klic = 'Login' and fk_sekce = 'OPAC_USER'", newValue));
                }
            }
        });
        add(876, f.insertIniValhod(37, 1, "-", "Bez přihlášení"));
        add(877, f.insertIniValhod(37, 2, "Username-Password", "Uživ. jméno - heslo"));
        add(878, f.insertIniValhod(37, 3, "UsernameTrimmingEAN8CheckDigit-Password", "EAN8 trimující ch.d. - heslo"));
        add(878, f.insertIniValhod(37, 4, "Username-BC", "Uživ. jméno - čárový kód"));
        add(880, f.insertIniValhod(37, 5, "Username-CN", "Uživ. jméno - číslo legitimace"));
        add(881, f.insertIniValhod(37, 6, "BC-BirthYear", "Čár. kód - rok narození"));
        add(882, f.insertIniValhod(37, 7, "CNOrBC-LastName", "Čís. leg. nebo čár. kód - příjmení"));
        add(883, f.insertIniValhod(37, 8, "CN-Password", "Čís. leg. - heslo"));
        add(884, f.insertIniValhod(37, 9, "BC-Password", "Čár. kód - heslo"));
        add(885, f.insertIniValhod(37, 10, "LastName-CN", "Příjmení - čís. leg."));
        add(886, f.insertIniValhod(37, 11, "LastName-BC", "Příjmení - čár. kód"));
        add(887, f.insertIniValhod(37, 12, "Username-", "Uživ. jméno"));
        add(888, f.insertIniValhod(37, 13, "CN-", "Čís. leg."));
        add(889, f.insertIniValhod(37, 14, "BC-", "Čár. kód"));
        add(890, f.insertIniValhod(37, 15, "Librarian", "Přihlášení knihovníků"));
        add(891, f.updateIniKeysValhod("OPAC_USER", "Login", 37));
        add(892, f.updateIniType("OPAC_USER", "Login", "LIST", "VALHOD"));
        add(893, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE OPAC_SDI ALTER COLUMN JE_POVOL TO JE_AKTIV"},
                new String[]{})));
        add(894, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE OPAC_SDI ADD JE_EXIST BOOLEAN DEFAULT 1"},
                new String[]{})));
        add(895, f.updateMessageKey("commons.smazat", "commons.Smazat"));
        add(896, f.insertMessage("sdi.PoznamkaFormuSEditaciSDI",
                "Tato služba vám bude v zadaném intervalu posílat novinky, jež se v tomto vyhledávání (tzn. s těmito klíčovými slovy) naleznou.<br/>Například, pokud vyhledáte \"rodina\" a necháte si z vyhledávání týdně odesílat novinky, tak pokud v průběhu týdne knihovna nakoupí knihy s rodinnou tématikou, automaticky Vám dorazí na email.",
                "This service will send in selected interval to your email new books, which are found in this search (with these keywords).</br>For exammple, if you search \"family\" and let the service to send news weekly, then if library will buy books with family thematics, they will be sent to your email automatically."));
        add(897, f.insertMessage("commons.Adresa", Map.of("text_cze", "Adresa", "text_eng", "Address", "text_ger", "Adresse")));
        add(898, f.updateMessageKey("ctenar.smsCislo", "ctenar.smsPhoneNumber"));
        add(899, f.updateMessageKey("ctenar.telefon", "ctenar.phoneNumber"));
        add(900, f.updateMessageKey("ctenar.trvaleMesto", "ctenar.permanentCity"));
        add(901, f.updateMessageKey("ctenar.trvalaUlice", "ctenar.permanentStreet"));
        add(902, f.updateMessageKey("ctenar.trvalePSC", "ctenar.permanentPostalCode"));
        add(903, f.updateMessageKey("ctenar.prechodneMesto", "ctenar.temporaryCity"));
        add(904, f.updateMessageKey("ctenar.prechodnaUlice", "ctenar.temporaryStreet"));
        add(905, f.updateMessageKey("ctenar.prechodnePSC", "ctenar.temporaryPostalCode"));
        add(906, f.insertMessage("commons.Telefon", Map.of("text_cze", "Telefon", "text_eng", "Phone", "text_ger", "Telefon")));
        add(907, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace("hledani/jednoradkove/vysledek", "search/global");
            newContent = newContent.replace("hledani/complex/form", "search/complex-form");
            newContent = newContent.replace("class=\"form-control index-search-input\" name=\"query\"", "class=\"form-control index-search-input\" name=\"q\"");
            newContent = newContent.replace("<div class=\"portaroText\"></div>", "<div><h1>Hledej v katalogu</h1></div>");
            newContent = newContent.replace("<input type=\"hidden\" name=\"fond\"", "<input type=\"hidden\" name=\"fonds\"");
            newContent = newContent.replace("hledani/complex/vysledek", "search");
            newContent = newContent.replace("hledani/autoritni", "search/authorities");
            return newContent;
        }));
        add(908, f.deleteMessage("loan.VypujckaCisloXBylaProdlouzenaOYDniJednaSeOZProdlouzeni"));
        add(909, f.deleteMessage("loans.NeboNaJinychBudovachRezervovat"));
        add(910, f.insertMessage("loan.ProdlouzitOXDni", "Prodloužit o {0} dní", "Renew by {0} days"));
        add(913, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source.replace("hledani/simple/form", "search/simple-form")));
        add(919, new ExceptionCatchingUpdate(f.sql("DROP TABLE OPAC_USER_PREFS")));
        add(920, new ExceptionCatchingUpdate(f.sql("DROP TABLE OPAC_USER_PREFS_KEYS")));
        add(921, f.dbDependentSql(
                new String[] {
                        """
                        CREATE TABLE OPAC_USER_PREFS_KEYS (
                           ID_KEY INT_NONULL,
                           NAME STRING_30 NOT NULL,
                           DEFVAL UTF_250,
                           DATATYPE STRING_20 DEFAULT 'TEXT' NOT NULL,
                           STRUCTURE STRING_10 DEFAULT 'SCALAR' NOT NULL,
                           FK_VALHOD SMALL_NULL,
                           AUTO_GENERATED BOOLEAN DEFAULT 0
                        )"""
                },
                new String[]{}));
        add(922, f.grantSelect("OPAC_USER_PREFS_KEYS", "opac"));
        add(923, f.dbDependentSql(
                new String[] {
                        """
                        CREATE TABLE OPAC_USER_PREFS (
                           FK_KEY INT_NONULL NOT NULL,
                           FK_CTEN INT_NONULL NOT NULL,
                           FK_UZIV SMALL_NULL NOT NULL,
                           VAL UTF_250 NOT NULL,
                           EDIT_TIME DATETIME NOT NULL
                        )"""
                },
                new String[]{}));
        add(924, f.grantSelect("OPAC_USER_PREFS", "opac"));
        add(925, f.sql("INSERT INTO OPAC_USER_PREFS_KEYS (ID_KEY, NAME, DEFVAL, DATATYPE, STRUCTURE, FK_VALHOD) values (1, 'Prohledávané budovy', '', 'BUDOVA', 'LIST', null)"));
        add(926, f.insertMessage("commons.UzivatelskeNastaveni", "Uživatelské nastavení", "User preferences"));
        add(927, f.dbDependentSql(
                new String[] {"ALTER TABLE OPAC_SEARCH_KEYS ALTER COLUMN TYP_HODN TO DATATYPE"},
                new String[]{}));
        add(928, f.insertMessage("commons.Vse", "Vše", "All"));
        add(929, f.deleteMessage("konto.kategorieCtenare"));
        add(930, f.updateMessageKey("ctenar.kategorieCtenare", "ctenar.readerCategory"));
        add(931, f.updateCustomFilesContent(customFolderPath, new String[]{"vm"}, source -> source.replace(".kategorieCtenare", ".readerCategory")));
        add(932, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source.replace("hledani/obecne/vysledek", "search")));
        add(933, f.updateFilesContent(new ProxiedList<File>() {
            @Override
            protected List<File> getList() {
                if (StringUtil.isNullOrEmpty(customFolderPath)) {
                    throw new IllegalStateException("Cannot get customFolderPath in no-custom-folder environment");
                }
                String updaterBatFilePath = new File(customFolderPath).getParentFile().getParent()+"/updater.bat";
                return List.of(new File(updaterBatFilePath));
            }
        }, source -> source.replace(
                "curl -z Portaro.war.new ftp://portaro.kpsys.cz:<EMAIL>/Portaro-1.0-SNAPSHOT.war -o Portaro.war",
                "curl -o Portaro.war portaro.kpsys.cz/Portaro-1.0-SNAPSHOT.war")));
        add(934, f.dbDependentSql(
                new String[] {
                        """
                        CREATE TABLE OPAC_PLACES_HISTORY (
                           FK_AUT GENERATORY,
                           TITLE UTF_121 NOT NULL,
                           TYPE SMALL_NOTNULL,
                           YEAR_FROM SMALL_NOTNULL,
                           YEAR_TO SMALL_NULL,
                           LAT DECIMAL(10,6) NOT NULL,
                           LNG DECIMAL(10,6) NOT NULL
                        )"""
                },
                new String[]{}));
        add(935, f.grantAll("OPAC_PLACES_HISTORY", "opac"));
        add(936, f.deleteMessage("hledani.title.vyhledavaniX"));
        add(937, f.updateIniDefaultValue("OPAC_SEARCH", "VzorGlobalniHledaniPokus1", "whole(P245:\"?\"^7) OR and(P245A:?^6 OR P245:?^5 OR P245:?*^4 OR P650:?*^2 OR P653:?*^2 OR PALL:?* OR PFULLTEXT:?*)"));
        add(938, f.insertMessage("availability.ExemplarJeVeStavuNevyrizenaObjednavka", "Exemplář je zarezervován", "Exemplar is reserved"));
        add(939, f.insertMessage("availability.ExemplarJeVeStavuNeodeslanaNeboOdeslanaRezervace", "Exemplář je zarezervován", "Exemplar is reserved"));
        add(940, f.updateMessageKey("message.appServer.VolnyAleStatusNeumoznujeObjednavat", "availability.VolnyAleStatusNeumoznujeObjednavat"));
        add(941, f.updateMessageKey("message.appServer.VolnyAleVTetoLokaciNelzeObjednavat", "availability.VolnyAleVTetoLokaciNelzeObjednavat"));
        add(942, f.updateMessageKey("message.appServer.VolnyAleTentoZpNabNelzeObjednavat", "availability.VolnyAleTentoZpNabNelzeObjednavat"));
        add(943, f.updateMessageKey("message.appServer.VolnyAleTutoTemSkupNelzeObjednavat", "availability.VolnyAleTutoTemSkupNelzeObjednavat"));
        add(944, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky", "availability.StatusExemplareNeumoznujeVypujcky"));
        add(945, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.1", "availability.StatusExemplareNeumoznujeVypujcky.1"));
        add(946, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.2", "availability.StatusExemplareNeumoznujeVypujcky.2"));
        add(947, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.3", "availability.StatusExemplareNeumoznujeVypujcky.3"));
        add(948, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.4", "availability.StatusExemplareNeumoznujeVypujcky.4"));
        add(949, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.5", "availability.StatusExemplareNeumoznujeVypujcky.5"));
        add(950, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.6", "availability.StatusExemplareNeumoznujeVypujcky.6"));
        add(951, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.7", "availability.StatusExemplareNeumoznujeVypujcky.7"));
        add(952, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.8", "availability.StatusExemplareNeumoznujeVypujcky.8"));
        add(953, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.9", "availability.StatusExemplareNeumoznujeVypujcky.9"));
        add(954, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.10", "availability.StatusExemplareNeumoznujeVypujcky.10"));
        add(955, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.11", "availability.StatusExemplareNeumoznujeVypujcky.11"));
        add(956, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.12", "availability.StatusExemplareNeumoznujeVypujcky.12"));
        add(957, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.13", "availability.StatusExemplareNeumoznujeVypujcky.13"));
        add(958, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.14", "availability.StatusExemplareNeumoznujeVypujcky.14"));
        add(959, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.15", "availability.StatusExemplareNeumoznujeVypujcky.15"));
        add(960, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.16", "availability.StatusExemplareNeumoznujeVypujcky.16"));
        add(961, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.17", "availability.StatusExemplareNeumoznujeVypujcky.17"));
        add(962, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.18", "availability.StatusExemplareNeumoznujeVypujcky.18"));
        add(963, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeVypujcky.19", "availability.StatusExemplareNeumoznujeVypujcky.19"));
        add(964, f.updateMessageKey("message.appServer.naTentoExemplarJsteOdeslalPozadavek", "loan.NaTentoExemplarJsteOdeslalPozadavek"));
        add(965, f.updateMessageKey("message.appServer.naTentoExemplarJizMateVypujckuNeboRezervaci", "loan.NaTentoExemplarJizMateVypujckuNeboRezervaci"));
        add(966, f.updateMessageKey("message.appServer.vTetoLokaciNelzeRezervovat", "loan.VTetoLokaciNelzeRezervovat"));
        add(967, f.updateMessageKey("message.appServer.dokumentSTimtoZpNabNelzeRezervovat", "loan.DokumentSTimtoZpNabNelzeRezervovat"));
        add(968, f.updateMessageKey("message.appServer.tutoTemSkupNelzeRezervovat", "loan.TutoTemSkupNelzeRezervovat"));
        add(969, f.updateMessageKey("message.appServer.kategorieCtenareNeumoznujeVypujcky", "loan.KategorieCtenareNeumoznujeVypujcky"));
        add(970, f.updateMessageKey("message.appServer.kombinaceKategorieCtenareAVypujckyNeumoznujeRezervaci", "loan.KombinaceKategorieCtenareAVypujckyNeumoznujeRezervaci"));
        add(971, f.updateMessageKey("message.appServer.katgorieVypujckyNeumoznujeRezervaci", "loan.KategorieVypujckyNeumoznujeRezervaci"));
        add(972, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci", "loan.StatusExemplareNeumoznujeRezervaci"));
        add(973, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.1", "loan.StatusExemplareNeumoznujeRezervaci.1"));
        add(974, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.2", "loan.StatusExemplareNeumoznujeRezervaci.2"));
        add(975, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.3", "loan.StatusExemplareNeumoznujeRezervaci.3"));
        add(976, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.4", "loan.StatusExemplareNeumoznujeRezervaci.4"));
        add(977, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.5", "loan.StatusExemplareNeumoznujeRezervaci.5"));
        add(978, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.6", "loan.StatusExemplareNeumoznujeRezervaci.6"));
        add(979, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.7", "loan.StatusExemplareNeumoznujeRezervaci.7"));
        add(980, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.8", "loan.StatusExemplareNeumoznujeRezervaci.8"));
        add(981, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.9", "loan.StatusExemplareNeumoznujeRezervaci.9"));
        add(982, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.10", "loan.StatusExemplareNeumoznujeRezervaci.10"));
        add(983, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.11", "loan.StatusExemplareNeumoznujeRezervaci.11"));
        add(984, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.12", "loan.StatusExemplareNeumoznujeRezervaci.12"));
        add(985, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.13", "loan.StatusExemplareNeumoznujeRezervaci.13"));
        add(986, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.14", "loan.StatusExemplareNeumoznujeRezervaci.14"));
        add(987, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.15", "loan.StatusExemplareNeumoznujeRezervaci.15"));
        add(988, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.16", "loan.StatusExemplareNeumoznujeRezervaci.16"));
        add(989, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.17", "loan.StatusExemplareNeumoznujeRezervaci.17"));
        add(990, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.18", "loan.StatusExemplareNeumoznujeRezervaci.18"));
        add(991, f.updateMessageKey("message.appServer.statusExemplareNeumoznujeRezervaci.19", "loan.StatusExemplareNeumoznujeRezervaci.19"));
        add(992, f.updateMessageKey("message.appServer.neniNaOnlineBudove", "loan.NeniNaOnlineBudove"));
        add(993, f.updateMessageKey("message.appServer.idZazJePeriodikumAleIdExJeNula", "loan.IdZazJePeriodikumAleIdExJeNula"));
        add(994, f.updateMessageKey("message.appServer.exemplarNeniNaValidniBudove", "loan.ExemplarNeniNaValidniBudove"));
        add(995, f.updateMessageKey("message.appServer.rezervaceVolnychTituluNeboExemplaruNeniPovolena", "loan.RezervaceVolnychTituluNeboExemplaruNeniPovolena"));
        add(996, f.updateMessageKey("message.appServer.titulNemaZadneRezervovatelnePrirustky", "loan.TitulNemaZadneRezervovatelnePrirustky"));
        add(997, f.updateMessageKey("message.appServer.nelzeDohledatNevyrizenyPozadavekNaRezervaci", "loan.NelzeDohledatNevyrizenyPozadavekNaRezervaci"));
        add(998, f.updateMessageKey("message.appServer.ctenarBlokovanNeboSmazan", "loan.CtenarBlokovanNeboSmazan"));
        add(999, f.updateMessageKey("message.appServer.prekrocenMaximalniPocetRezervaci", "loan.PrekrocenMaximalniPocetRezervaci"));
        add(1000, f.updateMessageKey("message.appServer.nelzeRezervovatVolne", "loan.NelzeRezervovatVolne"));
        add(1001, f.updateMessageKey("message.appServer.ctenarMaTitulJizVypujcenNeboRezervovan", "loan.CtenarMaTitulJizVypujcenNeboRezervovan"));
        add(1002, f.updateMessageKey("message.appServer.nelzeProlongovatNeposunulUzBySeTerminVraceni", "loan.NelzeProlongovatNeposunulUzBySeTerminVraceni"));
        add(1003, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%gigablastopensource%'")));
        add(1004, f.dbDependentSql(
                new String[] {
                        "CREATE TABLE OPAC_GEOCODING (\n" +
                                "   COUNTRY UTF_121,\n" +
                                "   CITY UTF_121,\n" +
                                "   STREET UTF_121,\n" +
                                "   LAT DECIMAL(10,6) NOT NULL,\n" +
                                "   LNG DECIMAL(10,6) NOT NULL\n" +
                                ")"
                },
                new String[]{}));
        add(1005, f.grantAll("OPAC_GEOCODING", "opac"));
        add(1006, MultipleUpdate.of(
                f.updateMessage("message.rezy.REZS_BUDOVA", "Budovy s exempláři", "Buildings with items"),
                f.updateMessageTranslation("message.rezy.REZS_BUDOVA", "text_ger", "Haus, wo es Exemplare gibt")
        ));
        add(1007, f.insertMessage("commons.ExemplaroveStatusy", "Exemplářové statusy", "Exemplar statuses"));
        add(1008, f.updateMessageKey("ctenar.cisloLegitimace", "ctenar.cardNumber"));
        add(1009, f.updateCustomFilesContent(customFolderPath, new String[]{"vm"}, source -> source.replace(".cisloLegitimace", ".cardNumber")));
        add(1010, f.sql("UPDATE OPAC_LOG_SESSIONS SET CAS_VYTVORENI = CAS_PRIHLASENI WHERE CAS_VYTVORENI IS NULL AND CAS_PRIHLASENI IS NOT NULL"));
        add(1011, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"UPDATE OPAC_LOG_SESSIONS SET CAS_VYTVORENI = dateadd (-30 minute to CAS_SKONCENI) WHERE CAS_VYTVORENI IS NULL AND CAS_SKONCENI IS NOT NULL"},
                new String[]{})));
        add(1012, f.updateMessageKey("availability.ExemplarJeVeStavuNevyrizenaObjednavka", "availability.ExemplarJeVeStavuVyrizenaObjednavka"));
        add(1013, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE OPAC_SDI ADD LZE_SMAZAT BOOLEAN DEFAULT 1"},
                new String[]{})));
        add(1013, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%metaspider%'")));
        add(1014, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%publiclibraryarchive%'")));
        add(1015, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%visionutils%'")));
        add(1016, f.insertMessage("loan.MoznostiVypujceniNeboEBook", "Možnosti vypůjčení / e-Kniha", "Lend options / e-Book"));
        add(1017, f.updateCustomFilesContent(customFolderPath, new String[]{"vm"}, source -> source
                .replace("getRawValue()", "getRaw()")
                .replace("rawValue", "raw")
                .replace("getDataField", "getDatafield")
                .replace("dataFields", "datafields")
                .replace("getControlField", "getControlfield")
                .replace("controlFields", "controlfields")));
        add(1018, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_NADPISU", source -> source
                .replace("getRawValue()", "getRaw()")
                .replace("rawValue", "raw")
                .replace("getDataField", "getDatafield")
                .replace("dataFields", "datafields")
                .replace("getControlField", "getControlfield")
                .replace("controlFields", "controlfields")));
        add(1019, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY", source -> source
                .replace("getRawValue()", "getRaw()")
                .replace("rawValue", "raw")
                .replace("getDataField", "getDatafield")
                .replace("dataFields", "datafields")
                .replace("getControlField", "getControlfield")
                .replace("controlFields", "controlfields")));
        add(1020, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-POD_OBALKOU", source -> source
                .replace("getRawValue()", "getRaw()")
                .replace("rawValue", "raw")
                .replace("getDataField", "getDatafield")
                .replace("dataFields", "datafields")
                .replace("getControlField", "getControlfield")
                .replace("controlFields", "controlfields")));
        add(1021, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-AUTORITA", source -> source
                .replace("getRawValue()", "getRaw()")
                .replace("rawValue", "raw")
                .replace("getDataField", "getDatafield")
                .replace("dataFields", "datafields")
                .replace("getControlField", "getControlfield")
                .replace("controlFields", "controlfields")));
        add(1022, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentPlne", source -> source
                .replace("getRawValue()", "getRaw()")
                .replace("rawValue", "raw")
                .replace("getDataField", "getDatafield")
                .replace("dataFields", "datafields")
                .replace("getControlField", "getControlfield")
                .replace("controlFields", "controlfields")));
        add(1023, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentKompakt", source -> source
                .replace("getRawValue()", "getRaw()")
                .replace("rawValue", "raw")
                .replace("getDataField", "getDatafield")
                .replace("dataFields", "datafields")
                .replace("getControlField", "getControlfield")
                .replace("controlFields", "controlfields")));
        add(1024, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentVice", source -> source
                .replace("getRawValue()", "getRaw()")
                .replace("rawValue", "raw")
                .replace("getDataField", "getDatafield")
                .replace("dataFields", "datafields")
                .replace("getControlField", "getControlfield")
                .replace("controlFields", "controlfields")));
        add(1025, f.updateMessageKey("vysledky.nacistDalsichXZaznamu", "vysledky.NacistDalsichX"));
        add(1026, MultipleUpdate.of(
                f.updateMessage("vysledky.NacistDalsichX", "Načíst dalších {0}", "Load next {0}"),
                f.updateMessageTranslation("vysledky.NacistDalsichX", "text_ger", "Weitere {0} laden")
        ));
        add(1027, f.insertMessage("vysledky.NacistPredchozichX", Map.of("text_cze", "Načíst předchozích {0}", "text_eng", "Load previous {0}", "text_ger", "Vorherige {0} laden")));
        add(1028, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '**************'")));
        add(1029, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '**************'")));
        add(1030, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '**************'")));
        add(1031, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '**************'")));
        add(1032, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '*************'")));
        add(1033, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '**************'")));
        add(1034, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '**************'")));
        add(1035, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1036, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1037, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1038, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1039, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1040, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1041, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1042, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1043, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1044, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1045, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA = '************'")));
        add(1046, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA like '150.70.172.%'")));
        add(1047, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE IP_ADRESA like '150.70.173.%'")));
        add(1048, f.updateCustomFilesContent(customFolderPath, new String[]{"vm"},
                source -> source
                        .replace("barCode.value", "barCode")
                        .replace("barCode.getValue()", "barCode")));
        add(1049, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_NADPISU", source -> source.replace("barCode.value", "barCode").replace("barCode.getValue()", "barCode")));
        add(1049, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY", source -> source.replace("barCode.value", "barCode").replace("barCode.getValue()", "barCode")));
        add(1049, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-POD_OBALKOU", source -> source.replace("barCode.value", "barCode").replace("barCode.getValue()", "barCode")));
        add(1049, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-AUTORITA", source -> source.replace("barCode.value", "barCode").replace("barCode.getValue()", "barCode")));
        add(1049, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentPlne", source -> source.replace("barCode.value", "barCode").replace("barCode.getValue()", "barCode")));
        add(1049, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentKompakt", source -> source.replace("barCode.value", "barCode").replace("barCode.getValue()", "barCode")));
        add(1049, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentVice", source -> source.replace("barCode.value", "barCode").replace("barCode.getValue()", "barCode")));
        add(1050, f.insertMessage("commons.Smazano", Map.of("text_cze", "Smazáno", "text_eng", "Deleted", "text_ger", "Gelöscht")));
        add(1051, f.insertMessage("commons.NelzeSmazatPolozkaJePouzivana", "Nelze smazat, položka je používána", "Cannot delete, item is being used"));
        add(1052, f.insertMessage("loan.NelzeVypujcitProslaRegistrace", "Nelze vypůjčit - prošlá registrace", "Cannot lend - expired registration"));
        add(1054, f.insertIniValhod(38, 1, "BUDOVA", "Budova"));
        add(1055, f.insertIniValhod(38, 2, "-BUDOVA", "Budova sest."));
        add(1056, f.insertIniValhod(38, 3, "LOKACE", "Lokace"));
        add(1057, f.insertIniValhod(38, 4, "-LOKACE", "Lokace sest."));
        add(1058, f.insertIniValhod(38, 5, "PRIR_CISLO", "Přírůstkové číslo"));
        add(1059, f.insertIniValhod(38, 6, "-PRIR_CISLO", "Přírůstkové číslo sest."));
        add(1060, f.insertIniValhod(38, 7, "ROK_PRIR", "Rok přírůstku"));
        add(1061, f.insertIniValhod(38, 8, "-ROK_PRIR", "Rok přírůstku sest."));
        add(1062, f.insertIniValhod(38, 9, "PORADI", "Pořadí"));
        add(1063, f.insertIniValhod(38, 10, "-PORADI", "Pořadí sest."));
        add(1064, f.insertIniValhod(38, 11, "DATUM", "Datum"));
        add(1065, f.insertIniValhod(38, 12, "-DATUM", "Datum sest."));
        add(1066, f.insertIniKey("OPAC", "RazeniExemplaru", 318, "Sekvence vlastností exempláře, podle kterých se exempláře budou řadit", 38, "BUDOVA; LOKACE; PORADI; PRIR_CISLO", "LIST", "VALHOD"));
        add(1067, f.insertIniKey("OPAC", "RazeniSvazku", 319, "Sekvence vlastností exempláře, podle kterých se svazky budou řadit", 38, "BUDOVA; LOKACE; PORADI; PRIR_CISLO", "LIST", "VALHOD"));
        add(1068, f.insertIniValhod(35, 21, "PORADI", "Pořadí"));
        add(1070, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%feedfetcher-google%'")));
        add(1071, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'NeohapsisLab'")));
        add(1072, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Mediapartners-Google'")));
        add(1073, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'shellshock-scan'")));
        add(1075, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'mifetcher/1.0'")));
        add(1076, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Jyxobot/1'")));
        add(1077, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Morfeus Fucking Scanner'")));
        add(1078, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'listicka'")));
        add(1079, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'the beast'")));
        add(1080, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'betaBot'")));
        add(1081, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Phantom.js bot'")));
        add(1082, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'ContextAd Bot 1.0'")));
        add(1083, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'revolt'")));
        add(1084, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Seznam.cz open proxy check bot'")));
        add(1085, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Ruby'")));
        add(1086, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'YisouSpider'")));
        add(1087, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'LinkSaver/2.0'")));
        add(1088, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'LinkParser/2.0'")));
        add(1089, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'ADmantX Platform Semantic Analyzer - ADmantX Inc. - www.admantx.com - <EMAIL>'")));
        add(1090, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Dragonfly File Reader'")));
        add(1091, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'netscan.gtisc.gatech.edu'")));
        add(1092, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'BOT/0.1 (BOT for JCE)'")));
        add(1093, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Twitterbot/1.0'")));
        add(1094, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'ltx71 - (http://ltx71.com/)'")));
        add(1095, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Zend_Http_Client'")));
        add(1096, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'eBot/1.0 (+http://alfa.elchron.cz)'")));
        add(1097, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'bitlybot/2.0'")));
        add(1098, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'iZSearch.com'")));
        add(1099, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE LOWER(USER_AGENT) like '%/usr/bin/%'")));
        add(1100, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Mozilla/5.0 (X11; U; en-US; Fortinet'")));
        add(1101, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Mozilla/5.0 (compatible; worldwebheritage.org/1.0; +<EMAIL>)'")));
        add(1102, f.sql("DELETE FROM LOKALIZACE WHERE ID_LOKALIZACE like 'DEF_HLEDRADEK.DESCRIPTION.%'"));
        add(1103, f.updateMessageKey("ctenar.budovy", "ctenar.buildings"));
        add(1104, f.deleteMessage("message.rezy.REZS_DB"));
        add(1105, f.insertMessage("message.rezy.REZS_DB", "Knihovna", "Library"));
        add(1106, f.insertIniKey("OPAC", "RazeniCisel", 320, "Sekvence vlastností, podle kterých se budou řadit čísla", 38, "BUDOVA; LOKACE; -PORADI; -CISLO; PRIR_CISLO", "LIST", "VALHOD"));
        add(1107, f.insertIniValhod(38, 13, "CISLO", "Číslo"));
        add(1108, f.insertIniValhod(38, 14, "-CISLO", "Číslo sest."));
        add(1109, f.insertMessage("centralIndex.NajdeteVDatabazich", "Najdete v databázích", "See in databases"));
        add(1110, new ExceptionCatchingUpdate(f.sql("INSERT INTO INI_SEKCE (poradi, id_sekce, suffix_num, limit_od, limit_do) values (35, 'CENTRAL_INDEX', 0, null, null)")));
        add(1111, new ExceptionCatchingUpdate(f.sql("INSERT INTO INI_KEYS (poradi, fk_sekce, id_klic, popis, fk_valhod, defval, STRUKTURA, DATOVY_TYP) VALUES (1, 'CENTRAL_INDEX', 'Zapnuto', 'Zda je databáze centrálním indexem', 1, 'NE', 'SCALAR', 'VALHOD')")));
        add(1112, f.insertIniValhod(38, 15, "ID", "ID"));
        add(1113, f.insertIniValhod(38, 16, "-ID", "ID sest."));
        add(1114, f.updateIniCustomValue("OPAC", "ZAZNAM-SLOUPCE_CISLA", source -> source
                .replace("CLANKY; ", "")
                .replace(" CLANKY;", "")
                .replace("CLANKY;", "")
                .replace(" CLANKY", "")
                .replace("CLANKY", "")));
        add(1115, f.sql("DELETE FROM INI_VALHOD WHERE ID_VALHOD = 36 AND PORADI = 21"));
        add(1116, f.updateCustomFilesContent(customFolderPath, new String[]{"vm"}, source -> source
                .replace("email.value", "email")
                .replace("email.getValue()", "email")
                .replace("cardNumber.value", "cardNumber")
                .replace("cardNumber.getValue()", "cardNumber")
                .replace("datumNarozeni.value", "birthDate")
                .replace("datumNarozeni.getValue()", "birthDate")));
        add(1117, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_NADPISU",
                source -> source
                        .replace("email.value", "email")
                        .replace("email.getValue()", "email")
                        .replace("cardNumber.value", "cardNumber")
                        .replace("cardNumber.getValue()", "cardNumber")
                        .replace("datumNarozeni.value", "birthDate")
                        .replace("datumNarozeni.getValue()", "birthDate")
        ));
        add(1117, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY",
                source -> source
                        .replace("email.value", "email")
                        .replace("email.getValue()", "email")
                        .replace("cardNumber.value", "cardNumber")
                        .replace("cardNumber.getValue()", "cardNumber")
                        .replace("datumNarozeni.value", "birthDate")
                        .replace("datumNarozeni.getValue()", "birthDate")
        ));
        add(1117, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-POD_OBALKOU",
                source -> source
                        .replace("email.value", "email")
                        .replace("email.getValue()", "email")
                        .replace("cardNumber.value", "cardNumber")
                        .replace("cardNumber.getValue()", "cardNumber")
                        .replace("datumNarozeni.value", "birthDate")
                        .replace("datumNarozeni.getValue()", "birthDate")
        ));
        add(1117, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-AUTORITA",
                source -> source
                        .replace("email.value", "email")
                        .replace("email.getValue()", "email")
                        .replace("cardNumber.value", "cardNumber")
                        .replace("cardNumber.getValue()", "cardNumber")
                        .replace("datumNarozeni.value", "birthDate")
                        .replace("datumNarozeni.getValue()", "birthDate")
        ));
        add(1117, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentPlne",
                source -> source
                        .replace("email.value", "email")
                        .replace("email.getValue()", "email")
                        .replace("cardNumber.value", "cardNumber")
                        .replace("cardNumber.getValue()", "cardNumber")
                        .replace("datumNarozeni.value", "birthDate")
                        .replace("datumNarozeni.getValue()", "birthDate")
        ));
        add(1117, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentKompakt",
                source -> source
                        .replace("email.value", "email")
                        .replace("email.getValue()", "email")
                        .replace("cardNumber.value", "cardNumber")
                        .replace("cardNumber.getValue()", "cardNumber")
                        .replace("datumNarozeni.value", "birthDate")
                        .replace("datumNarozeni.getValue()", "birthDate")
        ));
        add(1117, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentVice",
                source -> source
                        .replace("email.value", "email")
                        .replace("email.getValue()", "email")
                        .replace("cardNumber.value", "cardNumber")
                        .replace("cardNumber.getValue()", "cardNumber")
                        .replace("datumNarozeni.value", "birthDate")
                        .replace("datumNarozeni.getValue()", "birthDate")
        ));
        add(1119, f.updateCustomFilesContent(customFolderPath, new String[]{"vm"},
                source -> source
                        .replace("datumRegistrace.value", "registrationDate")
                        .replace("datumRegistrace.getValue()", "registrationDate")
                        .replace("datumVyprseniRegistrace.value", "expirationDate")
                        .replace("datumVyprseniRegistrace.getValue()", "expirationDate")
                        .replace("bundledVolumeNumber.value", "bundledVolumeNumber")
                        .replace("bundledVolumeNumber.getValue()", "bundledVolumeNumber")
                        .replace("bundledVolumeYear.value", "bundledVolumeYear")
                        .replace("bundledVolumeYear.getValue()", "bundledVolumeYear")
                        .replace("bundledVolumeIssueRange.value", "bundledVolumeIssueRange")
                        .replace("bundledVolumeIssueRange.getValue()", "bundledVolumeIssueRange")
                        .replace("bindingIssueRange.value", "bindingIssueRange")
                        .replace("bindingIssueRange.getValue()", "bindingIssueRange")
                        .replace("accessNumber.value", "accessNumber")
                        .replace("accessNumber.getValue()", "accessNumber")
                        .replace("signature.value", "signature")
                        .replace("signature.getValue()", "signature")
                        .replace("signatura.value", "signature")
                        .replace("signatura.getValue()", "signature")));
        add(1120, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_NADPISU",
                source -> source
                        .replace("datumRegistrace.value", "registrationDate")
                        .replace("datumRegistrace.getValue()", "registrationDate")
                        .replace("datumVyprseniRegistrace.value", "expirationDate")
                        .replace("datumVyprseniRegistrace.getValue()", "expirationDate")
                        .replace("bundledVolumeNumber.value", "bundledVolumeNumber")
                        .replace("bundledVolumeNumber.getValue()", "bundledVolumeNumber")
                        .replace("bundledVolumeYear.value", "bundledVolumeYear")
                        .replace("bundledVolumeYear.getValue()", "bundledVolumeYear")
                        .replace("bundledVolumeIssueRange.value", "bundledVolumeIssueRange")
                        .replace("bundledVolumeIssueRange.getValue()", "bundledVolumeIssueRange")
                        .replace("bindingIssueRange.value", "bindingIssueRange")
                        .replace("bindingIssueRange.getValue()", "bindingIssueRange")
                        .replace("accessNumber.value", "accessNumber")
                        .replace("accessNumber.getValue()", "accessNumber")
                        .replace("signature.value", "signature")
                        .replace("signature.getValue()", "signature")
                        .replace("signatura.value", "signature")
                        .replace("signatura.getValue()", "signature")
        ));
        add(1120, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY",
                source -> source
                        .replace("datumRegistrace.value", "registrationDate")
                        .replace("datumRegistrace.getValue()", "registrationDate")
                        .replace("datumVyprseniRegistrace.value", "expirationDate")
                        .replace("datumVyprseniRegistrace.getValue()", "expirationDate")
                        .replace("bundledVolumeNumber.value", "bundledVolumeNumber")
                        .replace("bundledVolumeNumber.getValue()", "bundledVolumeNumber")
                        .replace("bundledVolumeYear.value", "bundledVolumeYear")
                        .replace("bundledVolumeYear.getValue()", "bundledVolumeYear")
                        .replace("bundledVolumeIssueRange.value", "bundledVolumeIssueRange")
                        .replace("bundledVolumeIssueRange.getValue()", "bundledVolumeIssueRange")
                        .replace("bindingIssueRange.value", "bindingIssueRange")
                        .replace("bindingIssueRange.getValue()", "bindingIssueRange")
                        .replace("accessNumber.value", "accessNumber")
                        .replace("accessNumber.getValue()", "accessNumber")
                        .replace("signature.value", "signature")
                        .replace("signature.getValue()", "signature")
                        .replace("signatura.value", "signature")
                        .replace("signatura.getValue()", "signature")
        ));
        add(1120, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-POD_OBALKOU",
                source -> source
                        .replace("datumRegistrace.value", "registrationDate")
                        .replace("datumRegistrace.getValue()", "registrationDate")
                        .replace("datumVyprseniRegistrace.value", "expirationDate")
                        .replace("datumVyprseniRegistrace.getValue()", "expirationDate")
                        .replace("bundledVolumeNumber.value", "bundledVolumeNumber")
                        .replace("bundledVolumeNumber.getValue()", "bundledVolumeNumber")
                        .replace("bundledVolumeYear.value", "bundledVolumeYear")
                        .replace("bundledVolumeYear.getValue()", "bundledVolumeYear")
                        .replace("bundledVolumeIssueRange.value", "bundledVolumeIssueRange")
                        .replace("bundledVolumeIssueRange.getValue()", "bundledVolumeIssueRange")
                        .replace("bindingIssueRange.value", "bindingIssueRange")
                        .replace("bindingIssueRange.getValue()", "bindingIssueRange")
                        .replace("accessNumber.value", "accessNumber")
                        .replace("accessNumber.getValue()", "accessNumber")
                        .replace("signature.value", "signature")
                        .replace("signature.getValue()", "signature")
                        .replace("signatura.value", "signature")
                        .replace("signatura.getValue()", "signature")
        ));
        add(1120, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-AUTORITA",
                source -> source
                        .replace("datumRegistrace.value", "registrationDate")
                        .replace("datumRegistrace.getValue()", "registrationDate")
                        .replace("datumVyprseniRegistrace.value", "expirationDate")
                        .replace("datumVyprseniRegistrace.getValue()", "expirationDate")
                        .replace("bundledVolumeNumber.value", "bundledVolumeNumber")
                        .replace("bundledVolumeNumber.getValue()", "bundledVolumeNumber")
                        .replace("bundledVolumeYear.value", "bundledVolumeYear")
                        .replace("bundledVolumeYear.getValue()", "bundledVolumeYear")
                        .replace("bundledVolumeIssueRange.value", "bundledVolumeIssueRange")
                        .replace("bundledVolumeIssueRange.getValue()", "bundledVolumeIssueRange")
                        .replace("bindingIssueRange.value", "bindingIssueRange")
                        .replace("bindingIssueRange.getValue()", "bindingIssueRange")
                        .replace("accessNumber.value", "accessNumber")
                        .replace("accessNumber.getValue()", "accessNumber")
                        .replace("signature.value", "signature")
                        .replace("signature.getValue()", "signature")
                        .replace("signatura.value", "signature")
                        .replace("signatura.getValue()", "signature")
        ));
        add(1120, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentPlne",
                source -> source
                        .replace("datumRegistrace.value", "registrationDate")
                        .replace("datumRegistrace.getValue()", "registrationDate")
                        .replace("datumVyprseniRegistrace.value", "expirationDate")
                        .replace("datumVyprseniRegistrace.getValue()", "expirationDate")
                        .replace("bundledVolumeNumber.value", "bundledVolumeNumber")
                        .replace("bundledVolumeNumber.getValue()", "bundledVolumeNumber")
                        .replace("bundledVolumeYear.value", "bundledVolumeYear")
                        .replace("bundledVolumeYear.getValue()", "bundledVolumeYear")
                        .replace("bundledVolumeIssueRange.value", "bundledVolumeIssueRange")
                        .replace("bundledVolumeIssueRange.getValue()", "bundledVolumeIssueRange")
                        .replace("bindingIssueRange.value", "bindingIssueRange")
                        .replace("bindingIssueRange.getValue()", "bindingIssueRange")
                        .replace("accessNumber.value", "accessNumber")
                        .replace("accessNumber.getValue()", "accessNumber")
                        .replace("signature.value", "signature")
                        .replace("signature.getValue()", "signature")
                        .replace("signatura.value", "signature")
                        .replace("signatura.getValue()", "signature")
        ));
        add(1120, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentKompakt",
                source -> source
                        .replace("datumRegistrace.value", "registrationDate")
                        .replace("datumRegistrace.getValue()", "registrationDate")
                        .replace("datumVyprseniRegistrace.value", "expirationDate")
                        .replace("datumVyprseniRegistrace.getValue()", "expirationDate")
                        .replace("bundledVolumeNumber.value", "bundledVolumeNumber")
                        .replace("bundledVolumeNumber.getValue()", "bundledVolumeNumber")
                        .replace("bundledVolumeYear.value", "bundledVolumeYear")
                        .replace("bundledVolumeYear.getValue()", "bundledVolumeYear")
                        .replace("bundledVolumeIssueRange.value", "bundledVolumeIssueRange")
                        .replace("bundledVolumeIssueRange.getValue()", "bundledVolumeIssueRange")
                        .replace("bindingIssueRange.value", "bindingIssueRange")
                        .replace("bindingIssueRange.getValue()", "bindingIssueRange")
                        .replace("accessNumber.value", "accessNumber")
                        .replace("accessNumber.getValue()", "accessNumber")
                        .replace("signature.value", "signature")
                        .replace("signature.getValue()", "signature")
                        .replace("signatura.value", "signature")
                        .replace("signatura.getValue()", "signature")
        ));
        add(1120, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentVice",
                source -> source
                        .replace("datumRegistrace.value", "registrationDate")
                        .replace("datumRegistrace.getValue()", "registrationDate")
                        .replace("datumVyprseniRegistrace.value", "expirationDate")
                        .replace("datumVyprseniRegistrace.getValue()", "expirationDate")
                        .replace("bundledVolumeNumber.value", "bundledVolumeNumber")
                        .replace("bundledVolumeNumber.getValue()", "bundledVolumeNumber")
                        .replace("bundledVolumeYear.value", "bundledVolumeYear")
                        .replace("bundledVolumeYear.getValue()", "bundledVolumeYear")
                        .replace("bundledVolumeIssueRange.value", "bundledVolumeIssueRange")
                        .replace("bundledVolumeIssueRange.getValue()", "bundledVolumeIssueRange")
                        .replace("bindingIssueRange.value", "bindingIssueRange")
                        .replace("bindingIssueRange.getValue()", "bindingIssueRange")
                        .replace("accessNumber.value", "accessNumber")
                        .replace("accessNumber.getValue()", "accessNumber")
                        .replace("signature.value", "signature")
                        .replace("signature.getValue()", "signature")
                        .replace("signatura.value", "signature")
                        .replace("signatura.getValue()", "signature")
        ));
        add(1121, f.updateMessageKey("ctenar.datumVyprseniRegistrace", "ctenar.expirationDate"));
        add(1122, f.updateMessageKey("ctenar.datumRegistrace", "ctenar.registrationDate"));
        add(1123, f.deleteMessage("message.exception.someRequiredValuesNotExists"));
        add(1124, f.updateMessageKey("ctenar.datumNarozeni", "ctenar.birthDate"));
        add(1125, f.updateMessageKey("ctenar.prechodnePsc", "ctenar.temporaryPostalCode"));
        add(1126, f.deleteMessage("editace.zobrazitKontoCtenare"));
        add(1127, f.deleteMessage("editace.editaceCtenare"));
        add(1128, f.insertMessage("commons.Uprava", Map.of("text_cze", "Úprava", "text_eng", "Editation", "text_ger", "Bearbeiten")));
        add(1129, f.insertMessage("commons.Vytvoreni", Map.of("text_cze", "Vytvoření", "text_eng", "Creation", "text_ger", "Schaffung")));
        add(1130, f.insertMessage("ctenar.VytvoritNovouKnihovnu", "Vytvořit novou MVS knihovnu", "Create new ILL library"));
        add(1131, f.updateMessageKey("editace.VytvoritNovehoCtenare", "ctenar.VytvoritNovehoCtenare"));
        add(1132, f.updateMessageKey("ctenari.hledatNaVsechBudovach", "ctenar.HledatNaVsechBudovach"));
        add(1133, f.updateMessageKey("ctenari.pocatecniPismenaCtenare", "ctenar.PocatecniPismenaCtenare"));
        add(1134, f.updateMessageKey("ctenari.vypujcniModul", "commons.VypujcniModul"));
        add(1135, f.updateMessageKey("exemplar.rokPrirustku", "exemplar.acquisitionYear"));
        add(1136, f.updateMessageKey("exemplar.datumPorizeni", "exemplar.creationDte"));
        add(1137, f.updateMessageKey("exemplar.casPosledniZmeny", "exemplar.lastModificationDate"));
        add(1138, f.updateMessageKey("exemplar.ucet", "exemplar.account"));
        add(1139, f.updateMessageKey("exemplar.pocetKusu", "exemplar.quantity"));
        add(1140, f.insertMessage("editace.VyberRady", "Výběr řady", "Sequence selection"));
        add(1141, f.deleteMessage("editace.editaceExemplare"));
        add(1142, f.deleteMessage("editace.editaceExemplareSCarovymKodem"));
        add(1143, f.deleteMessage("commons.vlastnost"));
        add(1144, f.deleteMessage("commons.hodnota"));
        add(1145, f.dbDependentSql(
                new String[] {"ALTER TABLE OPAC_SDI_SENDINGS ADD ID_OPAC_SDI_SENDING GENERATORY NOT NULL"},
                new String[]{}));
        add(1146, f.sql("CREATE SEQUENCE SEQ_ID_OPAC_SDI_SENDING"));
        add(1147, f.dbDependentSql(
                new String[] {"UPDATE OPAC_SDI_SENDINGS SET ID_OPAC_SDI_SENDING = GEN_ID(SEQ_ID_OPAC_SDI_SENDING, 1)"},
                new String[]{}));
        add(1148, new ExceptionCatchingUpdate(f.sql("ALTER TABLE OPAC_SDI_SENDINGS ADD CONSTRAINT PK_OPAC_SDI_SENDING PRIMARY KEY(ID_OPAC_SDI_SENDING)")));
        add(1149, f.dbDependentSql(
                new String[] {
                        """
                        CREATE TRIGGER TRG_OPAC_SDI_SENDINGS_BI0 for OPAC_SDI_SENDINGS
                        active before insert position 0
                        as
                        begin
                           new.ID_OPAC_SDI_SENDING = next value for SEQ_ID_OPAC_SDI_SENDING;
                        end"""
                },
                new String[]{}));
        add(1151, f.insertMessage("commons.PlnyText", "Plný text", "Fulltext"));
        add(1152, f.updateIniDefaultValue("OPAC", "ZAZNAM-SLOUPCE_CISLA", "CISLO; BAR_COD; SIGNATURA; BUDOVA; LOKACE; KATEGORIE; DOSTUPNOST; FULLTEXT;"));
        add(1153, f.updateIniDefaultValue("OPAC", "ZAZNAM-ZOBRAZOVANA_POLE", "100^acd; 245; 246; 700^ade; 20; 250; 260; 264; 650; 653; 773^atg; 300; 500; 520; 856; 80^a; 910^b;"));
        add(1154, f.updateIniCustomValue("OPAC", "ZAZNAM-ZOBRAZOVANA_POLE",
                source -> {
                    if (source.contains("260;") && !source.contains("264;")) {
                        source = source.replace("260;", "260; 264;");
                    }
                    return source;
                }
        ));
        add(1154, f.updateIniCustomValue("OPAC", "ZobrazovanaPoleDokumentu",
                source -> {
                    if (source.contains("260;") && !source.contains("264;")) {
                        source = source.replace("260;", "260; 264;");
                    }
                    return source;
                }
        ));
        add(1155, f.insertIniValhod(36, 21, "FULLTEXT", "Plný text"));
        add(1156, f.insertIniValhod(35, 22, "FULLTEXT", "Plný text"));
        add(1157, f.updateIniDefaultValue("OPAC", "ZAZNAM-SLOUPCE_EXEMPLARE", "PRIR_CISLO; SIGNATURA; LOKACE; KATEGORIE; DOSTUPNOST; FULLTEXT;"));
        add(1158, f.updateIniDefaultValue("OPAC", "ExemplarColumns", "3(PRIR_CISLO; SIGNATURA; ROCNIK; ROK; ROZMEZI_CISEL; LOKACE; KATEGORIE; DOSTUPNOST; FULLTEXT;)"));
        add(1159, f.insertMessage("exemplar.fulltext", "Plný text", "Fulltext"));
        add(1160, f.insertMessage("loan.ereading.VypujcitDoCteckyPresEreading", "Vypůjčit do čtečky přes eReading.cz", "Lend to e-book reader via eReading.cz"));
        add(1161, f.insertMessage("loan.ereading.JakPujcovat", "Jak půjčovat elektronické knihy přes eReading.cz?", "How to lend e-books via eReading.cz?"));
        add(1162, f.insertMessage("loan.ereading.KlikneteNaVypujcit", "Klikněte na tlačítko \"Vypůjčit\" níže.", "Click on \"Lend\" button."));
        add(1163, f.insertMessage("loan.ereading.ZaregistrujteNaXSeSEmailemY", "Zaregistrujte se na {0} s emailem {1}.", "Register on {0} with email {1}."));
        add(1164, f.insertMessage("loan.ereading.VKonteXNajdeteVypujcku", "V kontě {0} najdete e-knihu ke stažení.", "In {0} account will be your e-book for download."));
        add(1165, f.insertMessage("commons.ViceInformaci", "Více informací", "More info"));
        add(1166, f.insertMessage("loan.ereading.EVypujckaJeDostupnaProTatoZarizeni", "eVýpůjčka je dostupná pro tato zařízení", "E-book loan is available for these devices"));
        add(1167, f.insertMessage("loan.ereading.ZarizeniSAndroidem", "Android zařízení", "Android device"));
        add(1168, f.insertMessage("loan.ereading.pomociAplikace", "pomocí aplikace", "via app"));
        add(1169, f.insertMessage("loan.ereading.ZarizeniSIOs", "iOS zařízení", "iOS device"));
        add(1170, f.insertMessage("loan.ereading.ZarizeniEreading", "e-ink čteček eReading.cz START 2 a 3", "e-ink reader eReading.cz START 2 and 3"));
        add(1171, f.insertMessage("loan.ereading.ZobrazitKnihuNaEreading", "Zobrazit knihu na eReading.cz", "Show book in eReading.cz"));
        add(1172, f.insertMessage("commons.Dnes", "Dnes", "Today"));
        add(1173, f.insertMessage("commons.Vcera", "Včera", "Yesterday"));
        add(1174, f.insertMessage("commons.Databaze", "Databáze", "Database"));
        add(1175, f.insertMessage("statistiky.StatistikyDatabazi", "Statistiky databází", "Database statistics"));
        add(1176, f.disableIniTriggers());
        add(1176, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'PerzistentniOblibene'"));
        add(1177, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'PerzistentniOblibene'"));
        add(1179, new ExceptionCatchingUpdate(f.sql("ALTER TABLE OPAC_USER_PREFS_KEYS ADD CONSTRAINT PK_OPAC_USER_PREFS_KEYS PRIMARY KEY (ID_KEY)"))); //nekde uz toto je, proto nevyhazujeme vyjimku
        add(1180, new ExceptionCatchingUpdate(f.sql("ALTER TABLE OPAC_USER_PREFS ADD CONSTRAINT FK_OPAC_USER_PREFS_KEY FOREIGN KEY (FK_KEY) REFERENCES OPAC_USER_PREFS_KEYS (ID_KEY) ON DELETE CASCADE"))); //nekde uz toto je, proto nevyhazujeme vyjimku
        add(1181, f.insertMessage("commons.RocnikX", "Ročník {0}", "Volume {0}"));
        add(1182, f.dbDependentSql(
                new String[] {
                        """
                        CREATE TABLE OPAC_FILTERS (
                           ID_OPAC_FILTER INT_NONULL NOT NULL,
                           FK_OPAC_FILTER INT_NULL,
                           FK_ROOT INT_NONULL,
                           PORADI INT_NONULL,
                           TYP STRING_30 NOT NULL,
                           HODNOTA UTF_250,
                           MATCH_RESULT SMALL_NULL,
                           MISMATCH_RESULT SMALL_NULL,
                           DENY_MESSAGE UTF_121
                        )"""
                },
                new String[]{}));
        add(1183, f.grantAll("OPAC_FILTERS", "opac"));
        add(1184, f.sql("INSERT INTO OPAC_FILTERS (" +
                "ID_OPAC_FILTER, FK_OPAC_FILTER, FK_ROOT, PORADI, TYP, HODNOTA, MATCH_RESULT, MISMATCH_RESULT, DENY_MESSAGE) values " +
                "(1, null, 1, 1, 'FirstExact', null, null, null, null)"));
        add(1185, f.insertMessage("loan.ereading.DodatecneInformace", "Délka výpůjčky je 21 dní."));
        add(1186, f.insertMessage("loan.ereading.KliknutimPotvrzujeteSouhlasSVOP", "Kliknutím na tlačítko potvrzujete souhlas s <a href=\"http://www.ereading.cz/cs/nakupni-rad\">všeobecnými obchodními podmínkami eReading.cz</a>"));
        add(1187, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY",
                """
                        #fond()
                        <br/>
                        #sf(773 '''' ''{detail.PublishedIn} '' ''<br/>'')
                        #sf(250 '''' '''' ''<br/>'')
                        #sf(260 ''ab'')
                        #sf(260 ''cdefg'')
                        #if($record.query(''260'').any)<br/>#end
                        #sf(300 '''' '''' ''<br/>'')
                        #sf(700 ''a'' ''{detail.ostatniAutori}: '' ''<br/>'')\s
                        #sf(44)"""
        ));
        add(1188, f.insertMessage("detail.VsechnyDily", "Všechny díly", "All parts"));
        add(1189, f.updateMessageTranslation("detail.VsechnyDily", "text_svk", "Všetky diely"));
        add(1190, f.updateMessageKey("oblibene.zobrazitVse", "commons.ZobrazitVse"));
        add(1191, f.deleteMessage("message.exception.vysledekHledaniObsahujeDuplicity"));
        add(1192, f.deleteMessage("message.exception.sqlGrammar"));
        add(1193, f.updateMessageKey("message.exception.rekonstrukceZMarcXml", "record.ErrorWhileFromMarcXmlReconstruction"));
        add(1194, f.deleteMessage("message.exception.default"));
        add(1195, f.updateMessageKey("message.appServer.nelzeSmazatExistujiExemplare", "record.deletion.NelzeSmazatExistujiExemplare"));
        add(1196, f.updateMessageKey("message.appServer.nelzeSmazatVazbaPresZdrojovyDokument", "record.deletion.NelzeSmazatVazbaPresZdrojovyDokument"));
        add(1197, f.updateMessageKey("message.appServer.nelzeSmazatJizSmazano", "record.deletion.NelzeSmazatJizSmazano"));
        add(1198, f.updateMessageKey("message.appServer.nelzeSmazatNedostatecnaPrava", "record.deletion.NelzeSmazatNedostatecnaPrava"));
        add(1199, f.updateMessageKey("message.appServer.nelzeSmazatZaznamObsahujeChranenaPole", "record.deletion.NelzeSmazatZaznamObsahujeChranenaPole"));
        add(1200, f.updateMessageKey("message.appServer.opravduSmazatZaznamMaRocniky", "record.deletion.OpravduSmazatZaznamMaRocniky"));
        add(1201, f.updateMessageKey("message.appServer.opravduSmazatAktivniObjednavky", "record.deletion.OpravduSmazatAktivniObjednavky"));
        add(1202, f.updateMessageKey("message.appServer.opravduSmazatAktivniFaktury", "record.deletion.OpravduSmazatAktivniFaktury"));
        add(1203, f.updateMessageKey("message.appServer.jeVNevyrizeneMvsZrusit", "record.deletion.JeVNevyrizeneMvsZrusit"));
        add(1204, f.updateMessageKey("message.appServer.bylOdeslanDoCaslinZrusit", "record.deletion.BylOdeslanDoCaslinZrusit"));
        add(1205, f.updateMessageKey("message.appServer.nelzeVyraditExistujiNevyrazeneExemplare", "record.discardion.NelzeVyraditExistujiNevyrazeneExemplare"));
        add(1206, f.updateMessageKey("message.appServer.nelzeVyratitUzJeVyrazenNeboSmazan", "record.discardion.NelzeVyratitUzJeVyrazenNeboSmazan"));
        add(1207, f.updateMessageKey("message.appServer.tentoSeNekontroluje", "record.discardion.TentoSeNekontroluje"));
        add(1208, f.deleteMessage("message.exception.cannotEstablishConnection"));
        add(1209, f.deleteMessage("message.exception.connection"));
        add(1210, f.updateMessageKey("message.exception.connnectionException", "commons.ConnectionError"));
        add(1211, f.updateMessageKey("message.exception.keyCannotBeEmpty", "commons.KeyCannotBeEmpty"));
        add(1212, f.updateMessageKey("message.warning.unsupportedOperation", "commons.UnsupportedOperation"));
        add(1213, f.updateMessageKey("message.validation.wrongEmailFormat", "commons.ChybneZadanyEmail"));
        add(1214, f.grantSelect("POL_OBJED", "opac"));
        add(1215, f.grantSelect("HL_OBJED", "opac"));
        add(1216, f.grantSelect("DEF_MENY", "opac"));
        add(1217, f.insertMessage("export.RisButton", "RIS (citační formát)", "RIS (citation format)"));
        add(1218, f.insertMessage("export.CsvButton", "CSV (zobrazitelný např. Excelem)", "CSV (openable e.g. in Excel)"));
        add(1219, f.insertMessage("detail.ZnovuvyhledatObalku", "Pokusit se znovuvyhledat obálku", "Try to re-lookup for cover"));
        add(1220, f.insertMessage("detail.SmazatObalku", "Smazat obálku", "Delete cover"));
        add(1221, f.insertMessage("oblibene.OblibeneDokumenty", "Oblíbené dokumenty", "Favourite documents"));
        add(1222, f.insertMessage("record.OdkazNaZaznam", "Odkaz na záznam", "Link to record"));
        add(1223, f.deleteMessage("knihovnicka.doKnihovnicky"));
        add(1224, f.deleteMessage("knihovnicka.knihovnicka"));
        add(1225, f.deleteMessage("knihovnicka.vKnihovnicceNejsouZadneZaznamy"));
        add(1226, f.deleteMessage("knihovnicka.vyprazdnitKnihovnicku"));
        add(1227, f.deleteMessage("knihovnicka.zKnihovnicky"));
        add(1228, f.deleteMessage("knihovnicka.zaznamVKnihovnicce"));
        add(1229, f.deleteMessage("knihovnicka.zaznamuVKnihovnicce"));
        add(1230, f.deleteMessage("knihovnicka.zaznamyVKnihovnicce"));
        add(1231, f.deleteMessage("schranka.doSchranky"));
        add(1232, f.deleteMessage("schranka.pridatVseVyhledane"));
        add(1233, f.deleteMessage("schranka.schranka"));
        add(1234, f.deleteMessage("schranka.veSchranceNejsouZadneZaznamy"));
        add(1235, f.deleteMessage("schranka.vseDoKnihovnicky"));
        add(1236, f.deleteMessage("schranka.vyprazdnit"));
        add(1237, f.deleteMessage("schranka.vyprazdnitSchranku"));
        add(1238, f.deleteMessage("schranka.zaznamVeSchrance"));
        add(1239, f.deleteMessage("schranka.zaznamuVeSchrance"));
        add(1240, f.deleteMessage("schranka.zaznamyVeSchrance"));
        add(1241, f.deleteMessage("schranka.zeSchranky"));
        add(1242, f.deleteMessage("schranka.zobrazitSchranku"));
        add(1243, f.insertMessage("loan.PrezencneNeboEBook", "Prezenčně nebo e-Výpůjčka", "In place or e-Book"));
        add(1244, f.updateMessageKey("detail.pouzePrezencne", "loan.PouzePrezencne"));
        add(1245, f.updateMessageKey("detail.nelzeVypujcit", "loan.NelzeVypujcit"));
        add(1246, f.updateMessageKey("detail.nemuzetePujcovat", "loan.NemuzetePujcovat"));
        add(1247, f.insertMessage("login.PrihlaseniEzakem", "Přihlášení dodavatele přes E-ZAK", "Login of supplier via E-ZAK"));
        add(1248, f.insertMessage("login.PrihlasitSeEzakem", "Přihlásit se přes E-ZAK", "Login via E-ZAK"));
        add(1249, f.insertMessage("commons.Nahled", Map.of("text_cze", "Náhled", "text_eng", "Preview", "text_ger", "Vorschau")));
        add(1250, f.deleteMessage("commons.VypujcniModul"));
        add(1251, f.insertMessage("commons.Ctenari", Map.of("text_cze", "Čtenáři", "text_eng", "Readers", "text_ger", "Vorschau")));
        add(1252, f.insertMessage("commons.NapovedaKeKatalogu", Map.of("text_cze", "Nápověda ke katalogu", "text_eng", "System manual", "text_ger", "Die Betriebsanleitung")));
        add(1253, f.insertMessage("commons.VerzeSystemu", Map.of("text_cze", "Verze systému", "text_eng", "System version", "text_ger", "Systemversion")));
        add(1254, f.dbDependentSql(
                new String[] {
                        """
                        CREATE TABLE OPAC_MENU (
                            ID_OPAC_MENU    GENERATORY NOT NULL PRIMARY KEY,
                            FK_OPAC_MENU    GENERATORY,
                            PORADI          INT_NONULL,
                            TEXT            UTF_121,
                            TARGET          UTF_250,
                            LOGIN_REQUIRED  BOOLEAN DEFAULT 0,
                            PERMISSION      UTF_121,
                            GEN             UTF_50
                        )"""
                },
                new String[]{}));
        add(1255, f.sql("ALTER TABLE OPAC_MENU ADD CONSTRAINT FK_OPAC_MENU_OPAC_MENU FOREIGN KEY (FK_OPAC_MENU) REFERENCES OPAC_MENU (ID_OPAC_MENU) ON DELETE CASCADE"));
        add(1256, f.grantAll("OPAC_MENU", "opac"));
        add(1257, f.updateMessageKey("hledani.title.vyhledavani", "hledani.Vyhledavani"));
        add(1258, f.updateMessageKey("hledani.title.jednoducheVyhledavani", "hledani.JednoducheVyhledavani"));
        add(1259, f.updateMessageKey("hledani.title.rozsireneVyhledavani", "hledani.RozsireneVyhledavani"));
        add(1260, f.updateMessageKey("editace.novyDokument", "editace.NovyDokument"));
        add(1261, f.updateMessageKey("editace.novaAutorita", "editace.NovaAutorita"));
        add(1262, f.updateMessageKey("oblibene.oblibene", "oblibene.Oblibene"));
        add(1263, f.updateMessageKey("rejstrik.rejstriky", "rejstrik.Rejstriky"));
        add(1264, f.updateMessageKey("rejstrik.rejstrikNazvuDokumentu", "rejstrik.RejstrikNazvuDokumentu"));
        add(1265, f.updateMessageKey("commons.zajimaveTituly", "commons.ZajimaveTituly"));
        add(1266, f.updateMessageKey("seznam.novinky", "seznam.Novinky"));
        add(1267, f.updateMessageKey("seznam.nejsledovanejsi", "seznam.Nejsledovanejsi"));
        add(1268, f.updateMessageKey("seznam.nejlepeHodnocene", "seznam.NejlepeHodnocene"));
        add(1269, f.updateMessageKey("seznam.nejpujcovanejsi", "seznam.Nejpujcovanejsi"));
        add(1270, f.updateMessageKey("commons.statistiky", "commons.Statistiky"));
        add(1271, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (1, NULL, 1, 'commons.domu', '/', 0, NULL, NULL)"));
        add(1272, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (2, NULL, 2, 'hledani.Vyhledavani', NULL, 0, NULL, NULL)"));
        add(1273, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (3, 2, 1, 'hledani.JednoducheVyhledavani', '/search/simple-form', 0, NULL, NULL)"));
        add(1274, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (4, 2, 2, 'hledani.RozsireneVyhledavani', '/search/complex-form', 0, NULL, NULL)"));
        add(1275, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (5, NULL, 3, 'editace.Katalogizace', NULL, 0, NULL, NULL)"));
        add(1276, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (6, 5, 1, 'editace.NovyDokument', '/edit/records/documents/new/start', 0, 'DocumentCreate', NULL)"));
        add(1277, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (7, 5, 2, 'editace.NovaAutorita', '/edit/records/authorities/new', 0, 'AuthorityCreate', NULL)"));
        add(1278, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (8, NULL, 4, 'commons.Ctenari', '/readers', 0, 'ReadersShow', NULL)"));
        add(1279, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (9, NULL, 5, 'konto.mojeKonto', '/users/current', 1, 'AccountUse', NULL)"));
        add(1280, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (10, NULL, 6, 'oblibene.Oblibene', '/favourites', 0, 'FavouritesUse', NULL)"));
        add(1281, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (11, NULL, 7, 'rejstrik.Rejstriky', NULL, 0, NULL, NULL)"));
        add(1282, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (12, 11, 1, 'rejstrik.RejstrikNazvuDokumentu', '/indexes/document?source=menu', 0, NULL, NULL)"));
        add(1283, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (13, 11, 2, NULL, NULL, 0, NULL, 'FOND_AUT')"));
        add(1284, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (14, NULL, 8, 'commons.ZajimaveTituly', NULL, 0, NULL, NULL)"));
        add(1285, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (15, 14, 1, 'seznam.Novinky', '/lists/newest-form', 0, NULL, NULL)"));
        add(1286, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (16, 14, 2, 'seznam.Nejsledovanejsi', '/lists/most-watched-form', 0, NULL, NULL)"));
        add(1287, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (17, 14, 3, 'seznam.NejlepeHodnocene', '/lists/top-rated-form', 0, NULL, NULL)"));
        add(1288, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (18, 14, 4, 'seznam.Nejpujcovanejsi', '/lists/most-lent-form', 0, NULL, NULL)"));
        add(1289, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (19, NULL, 9, 'commons.Statistiky', '/stats', 0, NULL, NULL)"));
        add(1290, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (20, 19, 1, 'statistiky.PrirustkovySeznam', '/stats/exemplars', 0, 'StatsShow', NULL)"));
        add(1291, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (21, 19, 2, 'statistiky.MistniSeznam', '/stats/exemplar-signatures', 0, 'StatsShow', NULL)"));
        add(1292, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (22, 19, 3, 'statistiky.UbytkovySeznam', '/stats/exemplar-decreases', 0, 'StatsShow', NULL)"));
        add(1293, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (23, 19, 4, 'statistiky.OverallStats', '/stats/overall/form', 0, 'StatsShow', NULL)"));
        add(1294, f.sql("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES (24, 19, 5, 'statistiky.statistikyPouzivaniZKlienta', '/stats/z', 0, 'StatsShow', NULL)"));
        add(1295, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, oldContent -> oldContent.replace("hledani.title.rozsireneVyhledavani", "hledani.RozsireneVyhledavani")));
        add(1296, f.disableIniTriggers());
        add(1296, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'TypyHledani'"));
        add(1297, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'TypyHledani'"));
        add(1298, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> {
            return source
                    .replace("menuItem_10", "menu-19") //statistiky - dvojciferne nahore kvuli moznemu prepisu (1 a 11).
                    .replace("menuItem_11", "menu-toto-je-treba-smazat") //napoveda
                    .replace("menuItem_0", "menu-1") //home
                    .replace("menuItem_1", "menu-2") //vyhledavani
                    .replace("menuItem_2", "menu-5") //katalogizace
                    .replace("menuItem_3", "menu-8") //vypujcni modul
                    .replace("menuItem_4", "menu-9") //konto ctenare
                    .replace("menuItem_5", "menu-12001") //util
                    .replace("menuItem_6", "menu-10001") //nastaveni
                    .replace("menuItem_7", "menu-") //oblibene
                    .replace("menuItem_8", "menu-11") //rejstriky
                    .replace("menuItem_9", "menu-14"); //zajimave
        }));
        add(1299, f.deleteMessage("commons.fond.1"));
        add(1300, f.deleteMessage("commons.fond.3"));
        add(1301, f.deleteMessage("commons.fond.31"));
        add(1302, f.deleteMessage("commons.fond.51"));
        add(1303, f.deleteMessage("commons.mobile.textVLogu"));
        add(1304, f.deleteMessage("commons.pole.100"));
        add(1305, f.deleteMessage("commons.pole.20"));
        add(1306, f.deleteMessage("commons.pole.250"));
        add(1307, f.deleteMessage("commons.pole.260"));
        add(1308, f.deleteMessage("commons.pole.300"));
        add(1309, f.deleteMessage("commons.pole.500"));
        add(1310, f.deleteMessage("commons.pole.650"));
        add(1311, f.deleteMessage("commons.pole.700"));
        add(1312, f.deleteMessage("commons.pole.80"));
        add(1313, f.deleteMessage("commons.pole.910"));
        add(1314, f.deleteMessage("commons.prirCislo"));
        add(1315, f.deleteMessage("commons.sign"));
        add(1316, f.insertIniValhod(38, 17, "SIGNATURA", "Signatura"));
        add(1317, f.insertIniValhod(38, 18, "-SIGNATURA", "Signatura sest."));
        add(1318, f.insertIniValhod(35, 23, "ROCNIK", "Ročník"));
        add(1319, f.insertIniValhod(35, 24, "ROK", "Rok"));
        add(1320, f.insertIniValhod(35, 25, "ROZMEZI_CISEL", "Rozmezí čísel"));
        add(1321, f.insertIniValhod(36, 23, "ROCNIK", "Ročník"));
        add(1322, f.insertIniValhod(36, 24, "ROK", "Rok"));
        add(1323, f.insertIniValhod(36, 25, "ROZMEZI_CISEL", "Rozmezí čísel"));
        add(1324, f.insertMessage("extZdroje.NepodporovanyTyp", "Nepodporovaný typ externího zdroje", "Unsupported external loan type"));
        add(1325, f.insertMessage("extZdroje.ServerovyAPlatnyCasSeLisi", "Lokální čas se od platného liší o více jak 10 minut", "More than 10 minutes difference between local and real time"));
        add(1326, f.insertMessage("extZdroje.NepodariloSeZapsatVypujcku", "Nepodařilo se zapsat provedenou výpůjčku do databáze", "Error while writting loan to database"));
        add(1327, f.insertMessage("extZdroje.NenastavenaKategorieVypujcky", "Kategorie výpůjčky není nastavená nebo platná. Obraťte se, prosím, na knihovnu.", "Loan category is not set. Please, contact library."));
        add(1328, f.insertMessage("extZdroje.KategorieVypujckyACtenareZakazujePujceni", "Kombinace kategorie výpůjčky a čtenáře zakazuje výpůjčku provést. Obraťte se, prosím, na knihovnu.", "Loan and reader categories combination prohibits loan. Please, contact library."));
        add(1329, f.insertMessage("extZdroje.PrekrocenLimitVypujcek", "Překročen maximální počet výpůjček", "Maximum loans limit exceeded"));
        add(1329, f.insertMessage("extZdroje.CtenarJeBlokovanNeboDluzi", "Máte dluh nebo jste zablokován. Obraťte se, prosím, na knihovnu.", "You have debt or you are blocked. Please, contact library."));
        add(1330, f.insertMessage("extZdroje.JizMatePujceno", "Knížku již máte vypůjčenou", "You already have book lent"));
        add(1331, f.insertMessage("loan.OpravduVratitZvlastniInfoKPomocnemuPoli", "Pozor - zvláštní informace: {0} ...Opravdu vrátit?"));
        add(1332, f.insertMessage("loan.OpravduVypujcitZvlastniInfoKPomocnemuPoli", "Pozor - zvláštní informace: {0} ...Opravdu vypůjčit?"));
        add(1333, f.updateMessageKey("message.warning.badLoginCredentials", "login.BadLoginCredentials"));
        add(1334, f.updateMessageKey("message.warning.overenyUzivatelNeniEvidovan", "login.OverenyUzivatelNeniEvidovan"));
        add(1335, f.updateMessageKey("message.warning.prihlasenyUzivatelNeniCtenarException", "login.PrihlasenyUzivatelNeniCtenar"));
        add(1336, f.deleteMessage("message.warning.proZjisteniDostupnostiSePrihlaste"));
        add(1337, f.updateMessageKey("message.warning.neuvedenRokNarozeni", "login.NeuvedenRokNarozeni"));
        add(1338, f.updateMessageKey("message.warning.alreadyEvaluated", "rating.YouAlreadyEvaluated"));
        add(1339, f.updateMessageKey("message.warning.accessDeniedException", "commons.AccessDenied"));
        add(1340, f.updateMessageKey("message.validation.wrongOldPassword", "login.WrongOldPassword"));
        add(1341, f.updateMessageKey("message.validation.usernameTooShort", "login.UsernameTooShort"));
        add(1342, f.updateMessageKey("message.validation.someFieldsNotFilled", "commons.SomeFieldsNotFilled"));
        add(1343, f.updateMessageKey("message.validation.passwordsNotEquals", "login.PasswordsNotEqual"));
        add(1344, f.updateMessageKey("message.validation.passwordTooShort", "login.PasswordTooShort"));
        add(1345, f.updateMessageKey("message.validation.passwordTooLong", "login.PasswordTooLong"));
        add(1346, f.updateMessageKey("message.validation.emptyKomentar", "detail.NezadanTextKomentare"));
        add(1347, f.updateMessageKey("message.validation.ctenarWithThisCisLegOrBarCodAndPrijmeniNotExists", "ctenar.ReaderWithThisCisLegOrBarCodAndLastNameDoesNotExist"));
        add(1348, f.updateMessageKey("message.validation.ctenarWithThisCisLegOrBarCodAlreadyHasRegistration", "ctenar.ReaderWithThisCisLegOrBarCodAlreadyHasRegistration"));
        add(1347, f.updateMessageKey("reader.CannotExtendRegistrationWhichIsNotExpired", "ctenar.CannotExtendRegistrationWhichIsNotExpired"));
        add(1348, f.updateMessageKey("message.validation.chybneVyhledavaciQuery", "hledani.ChybneVyhledavaciQuery"));
        add(1349, f.updateMessageKey("message.validation.ValidationCodeIsNotValid", "commons.ValidationCodeIsNotValid"));
        add(1350, f.updateMessageKey("message.validation.ThisValueIsAlreadyUsed", "commons.ThisValueIsAlreadyUsed"));
        add(1351, f.deleteMessage("message.exception.xmlParsing"));
        add(1352, f.updateMessageKey("message.exception.queryNotExists", "hledani.QueryDoesNotExist"));
        add(1353, f.deleteMessage("message.appServer.ctenarJeBlokovan"));
        add(1354, f.deleteMessage("message.appServer.dotazOpravduVypujcitDokumentJeZJinePujcovny"));
        add(1355, f.deleteMessage("message.appServer.dotazOpravduVypujcitCtenarMaBlokovanyTransakce"));
        add(1356, f.updateMessageKey("message.appServer.nelzeSmazatJeVypujceno", "exemplar.NelzeSmazatJeVypujceno"));
        add(1357, f.updateMessageKey("message.appServer.nelzeSmazatVymenneFondy", "exemplar.NelzeSmazatVymenneFondy"));
        add(1358, f.updateMessageKey("message.appServer.nelzeSmazatJeSvazany", "exemplar.NelzeSmazatJeSvazany"));
        add(1359, f.updateMessageKey("message.appServer.nelzeSmazatNeprazdnaVazba", "exemplar.NelzeSmazatNeprazdnaVazba"));
        add(1360, f.updateMessageKey("message.appServer.nelzeSmazatStatusNeniNula", "exemplar.NelzeSmazatStatusNeniNula"));
        add(1361, f.updateMessageKey("message.appServer.opravduSmazatJeVArchivuVypujcek", "exemplar.OpravduSmazatJeVArchivuVypujcek"));
        add(1362, f.updateMessageKey("message.appServer.opravduSmazatJeVArchivuVymFondu", "exemplar.oOravduSmazatJeVArchivuVymFondu"));
        add(1363, f.updateMessageKey("message.appServer.opravduSmazatJeVArchivuDodavanychCisel", "exemplar.OpravduSmazatJeVArchivuDodavanychCisel"));
        add(1364, f.deleteMessage("message.exception.appserverConnection"));
        add(1365, f.updateMessageKey("message.exception.coverLoad", "detail.CoverLoadProblem"));
        add(1366, f.updateMessageKey("message.exception.coverLoad.connectionProblem", "detail.CoverLoadProblem.connectionProblem"));
        add(1367, f.updateMessageKey("message.exception.coverLoad.noCover", "detail.CoverLoadProblem.noCover"));
        add(1368, f.updateMessageKey("message.exception.coverLoad.noIsbn", "detail.CoverLoadProblem.noIsbn"));
        add(1369, f.updateMessageKey("message.exception.coverLoad.temporaryBlocked", "detail.CoverLoadProblem.temporaryBlocked"));
        add(1369, f.updateMessageKey("message.exception.dataOUmisteniExNejsouKDispozici", "exemplar.DataOUmisteniExNejsouKDispozici"));
        add(1370, f.deleteMessage("message.exception.emptyResultDataAccessException"));
        add(1371, f.deleteMessage("message.exception.nezadanDotaz"));
        add(1372, f.updateMessageKey("message.exception.nacteni", "commons.DataLoadError"));
        add(1373, f.updateMessageKey("message.exception.iniKlicNeexistuje", "util.IniKlicNeexistuje"));
        add(1374, f.deleteMessage("message.exception.httpConnection"));
        add(1374, f.updateMessageKey("message.exception.hledaniVyprselo", "hledani.HledaniVyprselo"));
        add(1375, f.updateMessageKey("message.exception.error403", "commons.NaTutoStrankuNematePovolenPristup"));
        add(1376, f.updateMessageKey("message.exception.error404", "commons.PozadovanaStrankaNebylaNalezena"));
        add(1377, f.updateMessageKey("message.exception.error500", "commons.NastalaChybaNaServeru"));
        add(1378, f.updateMessageKey("message.exception.proTentoFondNeniNastavenFiltrPoli", "record.ProTentoFondNeniNastavenFiltrPoli"));
        add(1379, f.updateMessageKey("message.warning.notLoggedIn", "commons.LoginToAccess"));
        add(1380, () -> {
            List<String> oldFacetsMessageKeys = jdbcTemplate.queryForList("SELECT ID_LOKALIZACE from LOKALIZACE where ID_LOKALIZACE like 'message.rezy.%'", String.class);
            for (String oldFacetMessageKey : oldFacetsMessageKeys) {
                String newMessageKey = oldFacetMessageKey.replace("message.rezy.", "hledani.facet.");
                log.warn("Changing ini message key \"{}\" to \"{}\"", oldFacetMessageKey, newMessageKey);
                jdbcTemplate.update("UPDATE LOKALIZACE SET ID_LOKALIZACE = '%s' where ID_LOKALIZACE = '%s'".formatted(newMessageKey, oldFacetMessageKey));
            }
        });
        add(1381, f.dbDependentSql(
                new String[] {
                        """
                        CREATE TABLE OAI_SETY (
                            ID_OAI_SET     GENERATORY NOT NULL PRIMARY KEY,
                            NAZEV          UTF_121 NOT NULL,
                            SQL_COMMAND    UTF_4000 NOT NULL
                        )"""
                },
                new String[]{}));
        add(1382, f.grantAll("OAI_SETY", "opac"));
        add(1383, f.sql("INSERT INTO OAI_SETY (ID_OAI_SET, NAZEV, SQL_COMMAND) VALUES (0, 'Default', 'SELECT FK_ZAZ, MAX(DATCAS) CAS FROM VYKONY_DOK WHERE FK_VYKON IN (1,2,3,4,5,7,8,9,10,11,12,15,17,18) AND DATCAS >= {dateFrom} AND DATCAS <= {dateTo} GROUP BY FK_ZAZ ORDER BY FK_ZAZ')"));
        add(1384, f.sql("INSERT INTO OAI_SETY (ID_OAI_SET, NAZEV, SQL_COMMAND) VALUES (1, 'CASLIN', 'SELECT FK_ZAZ, MAX(DATCAS) CAS FROM VYKONY_DOK WHERE FK_VYKON = 4 AND DATCAS >= {dateFrom} AND DATCAS <= {dateTo} GROUP BY FK_ZAZ ORDER BY FK_ZAZ')"));
        add(1385, f.sql("INSERT INTO OAI_SETY (ID_OAI_SET, NAZEV, SQL_COMMAND) VALUES (2, 'Monografie', 'SELECT FK_ZAZ, MAX(DATCAS) CAS FROM VYKONY_DOK WHERE FK_DOKFOND = 1 AND FK_VYKON IN (1,2,3,4,5,7,8,9,10,11,12,15,17,18) AND DATCAS >= {dateFrom} AND DATCAS <= {dateTo} GROUP BY FK_ZAZ ORDER BY FK_ZAZ')"));
        add(1386, f.sql("INSERT INTO OAI_SETY (ID_OAI_SET, NAZEV, SQL_COMMAND) VALUES (3, 'Periodika', 'SELECT FK_ZAZ, MAX(DATCAS) CAS FROM VYKONY_DOK JOIN DEF_DOKFOND ON (ID_DOKFOND = FK_DOKFOND) WHERE JE_PERIO = 1 AND FK_VYKON IN (1,2,3,4,5,7,8,9,10,11,12,15,17,18) AND DATCAS >= {dateFrom} AND DATCAS <= {dateTo} GROUP BY FK_ZAZ ORDER BY FK_ZAZ')"));
        add(1387, f.sql("CREATE INDEX IDX_VYKONY_DOK_DATCAS ON VYKONY_DOK (DATCAS)"));
        add(1388, f.disableIniTriggers());
        add(1388, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'ZmenaHesla'"));
        add(1389, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'ZmenaHesla'"));
        add(1390, f.insertIniKey("OPAC", "VnejsiIPAdresy", 340, "Seznam klientských ip adres, ktere jsou explicitne nastavene jako vnejsi (napr. pri pouzivani proxy serveru nastavime, ze vse co bude mit adresu proxy serveru, bude zvenku)", null, null, "LIST", "TEXT"));
        add(1391, f.disableIniTriggers());
        add(1391, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC', FK_KLIC = 'VnejsiIPAdresy' WHERE FK_KLIC = 'STATISTIKY-EXPLICITNE_VNEJSI_IP_ADRESY'"));
        add(1392, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'STATISTIKY-EXPLICITNE_VNEJSI_IP_ADRESY'"));
        add(1393, f.insertIniKey("OPAC", "VnitrniIPAdresy", 330, "Seznam klientských ip adres, ktere jsou explicitne nastavene jako vnitrni (nejcasteji to bude pouze loopback - 127.0.0.1, ale pokud budeme chtit nastavit jako vnitrni například i jine pobocky, tak zde budou jejich konkretni ip)", null, "127.0.0.1", "LIST", "TEXT"));
        add(1394, f.disableIniTriggers());
        add(1394, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC', FK_KLIC = 'VnitrniIPAdresy' WHERE FK_KLIC = 'STATISTIKY-EXPLICITNE_VNITRNI_IP_ADRESY'"));
        add(1395, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'STATISTIKY-EXPLICITNE_VNITRNI_IP_ADRESY'"));
        add(1396, f.insertIniKey("OPAC", "VnitrniIPAdresyRozsah", 333, "Rozsah klientských ip adres vnitřní sítě, neboli rozsah adres, které mají být považovány jako adresy uvnitř knihovny. Je to většinou rozsah adres vnitřní sítě (např. *********** až *************) Jedná se o dvě adresy označující začátek a konec rozsahu oddělené středníkem.", null, null, "LIST", "TEXT"));
        add(1397, f.disableIniTriggers());
        add(1397, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC', FK_KLIC = 'VnitrniIPAdresyRozsah' WHERE FK_KLIC = 'STATISTIKY-ROZSAH_VNITRNICH_IP_ADRES'"));
        add(1398, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'STATISTIKY-ROZSAH_VNITRNICH_IP_ADRES'"));
        add(1399, f.insertMessage("ctenar.SmazatUzivatelskeJmenoAHeslo", "Smazat uživatelské jméno a heslo", "Delete user credentials"));
        add(1400, f.deleteMessage("hledani.facet.REZS_EXTERNI"));
        add(1401, f.insertMessage("hledani.facet.REZS_EXTERNI", "eVýpůjčka", "eLoan"));
        add(1402, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source
                .replace("REZS_EXTERNI%3A&quot;EREADING&quot;", "REZS_EXTERNI%3ADEF_EXTERNI_TYP.ID_EXTERNI_TYP.EREADING")));
        add(1403, f.insertMessage("loan.flexibooks.JakPujcovat", "Jak půjčovat elektronické knihy přes Flexibooks?", "How to lend e-books via Flexibooks?"));
        add(1404, f.insertMessage("loan.flexibooks.KlikneteNaVypujcit", "Klikněte na tlačítko \"Vypůjčit\" níže.", "Click on \"Lend\" button."));
        add(1405, f.insertMessage("loan.flexibooks.ZaregistrujteNaXSeSEmailemY", "Zaregistrujte se na {0} s emailem {1}.", "Register on {0} with email {1}."));
        add(1406, f.insertMessage("loan.flexibooks.VKonteXNajdeteVypujcku", "V kontě {0} najdete e-knihu ke stažení.", "In {0} account will be your e-book for download."));
        add(1407, f.updateMessageKey("loan.ereading.EVypujckaJeDostupnaProTatoZarizeni", "loan.ext.EVypujckaJeDostupnaProTatoZarizeni"));
        add(1408, f.updateMessageKey("loan.flexibooks.ZarizeniSAndroidem", "loan.ext.ZarizeniSAndroidem"));
        add(1409, f.updateMessageKey("loan.flexibooks.ZarizeniSIOs", "loan.ext.ZarizeniSIOs"));
        add(1410, f.insertMessage("loan.ext.ZarizeniSWindows", "Windows zařízení", "Windows device"));
        add(1411, f.updateMessageKey("loan.ereading.pomociAplikace", "loan.ext.pomociAplikace"));
        add(1412, f.insertMessage("loan.flexibooks.ZobrazitKnihuNaFlexibooks", "Zobrazit knihu na flexibooks.cz", "Show book in flexibooks.cz"));
        add(1413, f.insertMessage("loan.flexibooks.VypujcitDoCteckyPresEreading", "Vypůjčit do čtečky přes Flexibooks", "Lend to e-book reader via Flexibooks"));

        add(1414, f.insertIniKey("OPAC_LOAN", "MvsActive", 350, "Zda jsou zapnuty aktivní MVS", 1, "NE", "SCALAR", "VALHOD"));
        add(1415, f.disableIniTriggers());
        add(1415, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_LOAN' WHERE FK_KLIC = 'MvsActive'"));
        add(1416, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'MvsActive' AND FK_SEKCE = 'OPAC'"));
        add(1417, f.insertIniKey("OPAC_LOAN", "MvsPassive", 360, "Zda jsou zapnuty pasivní MVS", 1, "NE", "SCALAR", "VALHOD"));
        add(1418, f.disableIniTriggers());
        add(1418, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_LOAN' WHERE FK_KLIC = 'MvsPassive'"));
        add(1419, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'MvsPassive' AND FK_SEKCE = 'OPAC'"));
        add(1420, f.insertIniKey("OPAC_USER", "CredentialsRegistration", 180, "Typ registrace přihlašovacích údajů (tzn. uživatelského jména a hesla do katalogu, čtenář již musí být v systému zaevidován)", 33, "username+password", "SCALAR", "VALHOD"));
        add(1421, f.disableIniTriggers());
        add(1421, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_USER' WHERE FK_KLIC = 'CredentialsRegistration'"));
        add(1422, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'CredentialsRegistration' AND FK_SEKCE = 'OPAC'"));
        add(1423, f.insertIniKey("OPAC_USER", "FullRegistration", 200, "Plná registrace (tzn. zaevidování čtenáře a jeho údajů do systému)", 1, "NE", "SCALAR", "VALHOD"));
        add(1424, f.disableIniTriggers());
        add(1424, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_USER' WHERE FK_KLIC = 'FullRegistration'"));
        add(1425, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'FullRegistration' AND FK_SEKCE = 'OPAC'"));
        add(1426, f.insertIniKey("OPAC_USER", "ForgottenCredentialsEnabled", 220, "Zda má být zapnuta moznost obnovení zapomenutého hesla čtenářů", 1, "ANO", "SCALAR", "VALHOD"));
        add(1427, f.disableIniTriggers());
        add(1427, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_USER' WHERE FK_KLIC = 'ForgottenCredentialsEnabled'"));
        add(1428, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'ForgottenCredentialsEnabled' AND FK_SEKCE = 'OPAC'"));
        add(1429, f.insertIniKey("OPAC_USER", "CtenaremEditovatelneVlastnostiCtenare", 400, "Vlastnosti čtenáře, které si sám čtenář může editovat. Možné hodnoty jsou: JMENO, PRIJMENI, USERNAME, TITUL, TRV_MI, TRV_UL, TRV_PSC, PRE_MI, PRE_UL, PRE_PSC, ZAMESTNANI, ADR_ZAM, VZDELANI, TRIDA, CISOP, EMAIL, SMSCISLO, TELEFON, TYP_TISK_REZE, TYP_TISK_UPOM", 34, "", "LIST", "VALHOD"));
        add(1430, f.disableIniTriggers());
        add(1430, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_USER' WHERE FK_KLIC = 'CtenaremEditovatelneVlastnostiCtenare'"));
        add(1431, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'CtenaremEditovatelneVlastnostiCtenare' AND FK_SEKCE = 'OPAC'"));
        add(1432, f.insertIniKey("OPAC_USER", "VlastnostiCtenareVPlneRegistraci", 420, "Vlastnosti čtenáře zobrazované v plné registraci. Možné hodnoty jsou: JMENO, PRIJMENI, USERNAME, TITUL, TRV_MI, TRV_UL, TRV_PSC, PRE_MI, PRE_UL, PRE_PSC, ZAMESTNANI, ADR_ZAM, VZDELANI, TRIDA, CISOP, EMAIL, SMSCISLO, TELEFON, TYP_TISK_REZE, TYP_TISK_UPOM", 34, "TITUL; JMENO; PRIJMENI; EMAIL; USERNAME; PASSWORD; TELEFON;", "LIST", "VALHOD"));
        add(1433, f.disableIniTriggers());
        add(1433, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_USER' WHERE FK_KLIC = 'VlastnostiCtenareVPlneRegistraci'"));
        add(1434, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'VlastnostiCtenareVPlneRegistraci' AND FK_SEKCE = 'OPAC'"));
        add(1435, f.insertIniKey("OPAC_LOAN", "Loans", 20, "Nastavení půjčování v celém katalogu. ANO = zapnuto", 1, "ANO", "SCALAR", "VALHOD"));
        add(1436, f.disableIniTriggers());
        add(1436, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_LOAN' WHERE FK_KLIC = 'Loans'"));
        add(1437, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'Loans' AND FK_SEKCE = 'OPAC'"));
        add(1438, f.insertIniKey("OPAC_LOAN", "ZpusobObjednavani", 40, "Způsob, jakým se bude na webu objednávat. Aby se tento klíč bral v úvahu, musí být zapnuto VYPUC.POVOLIT_OBJEDNAVKY.", 25, "classic", "SCALAR", "VALHOD"));
        add(1439, f.disableIniTriggers());
        add(1439, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_LOAN' WHERE FK_KLIC = 'ZpusobObjednavani'"));
        add(1440, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'ZpusobObjednavani' AND FK_SEKCE = 'OPAC'"));
        add(1441, f.insertIniKey("OPAC_LOAN", "ZpusobRezervovani", 50, "Způsob, jakým se bude na webu rezervovat.", 27, "classic", "SCALAR", "VALHOD"));
        add(1442, f.disableIniTriggers());
        add(1442, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_LOAN' WHERE FK_KLIC = 'ZpusobRezervovani'"));
        add(1443, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'ZpusobRezervovani' AND FK_SEKCE = 'OPAC'"));
        add(1444, f.insertIniKey("OPAC_LOAN", "Renewal", 80, "Zda je možné z portara prodlužovat výpůjčky", 1, "ANO", "SCALAR", "VALHOD"));
        add(1445, f.disableIniTriggers());
        add(1445, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_LOAN' WHERE FK_KLIC = 'Renewal'"));
        add(1446, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'Renewal' AND FK_SEKCE = 'OPAC'"));
        add(1447, f.insertIniKey("OPAC_LOAN", "RenewalOfDelayed", 90, "Zda čtenáři mohou prodlužovat výpůjčky s prošlou výpůjční lhůtou, pokud na ně ještě nebyla vygenerovaná upomínka.", 1, "ANO", "SCALAR", "VALHOD"));
        add(1448, f.disableIniTriggers());
        add(1448, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_LOAN' WHERE FK_KLIC = 'RenewalOfDelayed'"));
        add(1449, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'RenewalOfDelayed' AND FK_SEKCE = 'OPAC'"));
        add(1450, f.insertIniKey("OPAC_LOAN", "EmailPrijemceObjednavek", 140, "Emailová adresa, na kterou se budou posílat objednávky. Aby se tento klíč bral v úvahu, musí být klíč OPAC.ZpusobObjednavani nastaven na mail.", null, null, "SCALAR", "TEXT"));
        add(1451, f.disableIniTriggers());
        add(1451, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_LOAN' WHERE FK_KLIC = 'EmailPrijemceObjednavek'"));
        add(1452, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'EmailPrijemceObjednavek' AND FK_SEKCE = 'OPAC'"));

        add(1453, f.sql("UPDATE INI_VALHOD SET OBSAH = 'title' WHERE ID_VALHOD = 29 AND PORADI = 2")); //nazev -> title
        add(1454, f.sql("UPDATE INI_VALHOD SET OBSAH = 'author' WHERE ID_VALHOD = 29 AND PORADI = 3")); //autor -> author
        add(1455, f.sql("UPDATE INI_VALHOD SET OBSAH = 'date' WHERE ID_VALHOD = 29 AND PORADI = 4")); //datum -> date
        add(1456, f.sql("DELETE FROM INI_VALHOD WHERE ID_VALHOD = 29 AND PORADI = 5")); //smazani -nazev (-title)
        add(1457, f.sql("DELETE FROM INI_VALHOD WHERE ID_VALHOD = 29 AND PORADI = 6")); //smazani -autor (-author)
        add(1458, f.sql("UPDATE INI_VALHOD SET OBSAH = '-date' WHERE ID_VALHOD = 29 AND PORADI = 7")); //-datum -> -date
        add(1489, f.updateIniCustomValue("OPAC_SEARCH", "DefaultRazeni", source -> switch (source) {
            case "relevance" -> "relevance";
            case "nazev" -> "title";
            case "autor" -> "author";
            case "datum" -> "date";
            case "-nazev" -> "relevance";
            case "-autor" -> "relevance";
            case "-datum" -> "-date";
            default -> "relevance"; //toto by nemelo nastat
        }));
        add(1490, f.deleteMessage("hledani.raditPodle"));
        add(1491, f.updateMessageKey("detail.predchozi", "commons.Predchozi"));
        add(1492, f.updateMessageKey("detail.nasledujici", "commons.Nasledujici"));
        add(1493, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, oldContent -> {
            StringBuilder sb = new StringBuilder(oldContent);

            //pridame vsem formularum na search/global skryty input type=global
            int i = 0;
            while (i != -1 && i <= sb.length()) {
                i = sb.indexOf("action=\"search/global\"", i);
                if (i != -1) {
                    i = sb.indexOf(">", i);
                    sb.insert(i + 1, "\n                <input type=\"hidden\" name=\"type\" value=\"global\" />");
                }
            }

            //nakonec smazeme vsechny "/global"
            String newContent = sb.toString().replace("search/global", "search");
            return newContent;
        }));
        add(1944, f.sql("UPDATE OPAC_MENU SET TARGET = '/lists/newest?source=menu' WHERE TARGET = '/lists/newest-form'"));
        add(1945, f.sql("UPDATE OPAC_MENU SET TARGET = '/lists/most-watched?source=menu' WHERE TARGET = '/lists/most-watched-form'"));
        add(1946, f.sql("UPDATE OPAC_MENU SET TARGET = '/lists/top-rated?source=menu' WHERE TARGET = '/lists/top-rated-form'"));
        add(1947, f.sql("UPDATE OPAC_MENU SET TARGET = '/lists/most-lent?source=menu' WHERE TARGET = '/lists/most-lent-form'"));
        add(1948, f.sql("UPDATE OPAC_MENU SET TARGET = '/lists/document-index?source=menu' WHERE TARGET = '/indexes/document?source=menu'"));
        add(1950, f.deleteMessage("rejstrik.zobrazitRejstrik"));
        add(1951, f.deleteMessage("seznam.zobrazitSeznam"));
        add(1952, f.deleteMessage("commons.parametry"));
        add(1953, f.updateMessageKey("loan.flexibooks.VypujcitDoCteckyPresEreading", "loan.flexibooks.VypujcitDoCteckyPresFlexibooks"));
        add(1954, f.updateMessageKey("loan.ereading.ZarizeniSAndroidem", "loan.ext.ZarizeniSAndroidem"));
        add(1955, f.updateMessageKey("loan.ereading.ZarizeniSIOs", "loan.ext.ZarizeniSIOs"));
        add(1956, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY",
                """
                        #fond()

                        <br/>

                        #sf(773 '''' ''{detail.PublishedIn} '' ''<br/>'')

                        #sf(250 '''' '''' ''<br/>'')

                        #sf(260 ''ab'')

                        #sf(260 ''cdefg'')

                        #sf(264)

                        #if($record.query(''260'').any)<br/>#end

                        #sf(300 '''' '''' ''<br/>'')

                        #sf(700 ''a'' ''{detail.ostatniAutori}: '' ''<br/>'')\s

                        #sf(44)"""
        ));
        add(1957, f.updateIniDefaultValue("OPAC_SEARCH", "TextDefDokumentPlne",
                """
                        #fond()

                        <br/>

                        #sf(773 '''' ''{detail.PublishedIn} '' ''<br/>'')

                        #sf(260)

                        #sf(264)

                        #sf(910 ''b'' ''{detail.signatura}: '')"""
        ));
        add(1958, f.insertMessage("volume.NasobekPeriodicity", "Násobek periody", "Multiple of period"));
        add(1959, f.grantSelect("DEF_CTEN_CISLEG", "opac"));
        add(1960, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace("/indexes/document", "/lists/document-index");
            newContent = newContent.replace("/indexes/authority", "/lists/authority-index");
            return newContent;
        }));
        add(1961, f.insertMessage("detail.PouzitoVTechtoDokumentech", "Použito v těchto dokumentech", "Used in these documents"));
        add(1962, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace("startPos=", "prefix=");
            newContent = newContent.replace("hledani/simple/form", "search/simple-form");
            newContent = newContent.replace("hledani/complex/form", "search/complex-form");
            return newContent;
        }));
        add(1963, f.insertMessage("loan.flexibooks.NaEmailXObdrzitePoukaz", "Na váš email ({0}) obdržíte poukaz pro uskutečnění výpůjčky. Po jeho uplatnění na e-shopu Flexibooks si vypůjčenou e-knihu můžete stáhnout do své čtečky.", "You will get e-mail message ({0}) with voucher to acces your e-loan on Flexibooks."));
        add(1964, new ExceptionCatchingUpdate(f.grantSelect("EXTERNI_ZDROJE", "opac")));
        add(1966, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, oldContent -> oldContent.replace("hledani.title.rozsireneVyhledavani", "hledani.RozsireneVyhledavani")));
        add(1968, f.deleteMessage("detail.PouzitoVTechtoDokumentech"));
        add(1969, f.insertMessage("detail.NalezenoVTechtoDokumentech", "Nalezeno v těchto dokumentech", "Found in these documents"));
        add(1970, f.insertIniValhod(38, 19, "ROZSAH", "Rozsah vazby"));
        add(1971, f.insertIniValhod(38, 20, "-ROZSAH", "Rozsah vazby sest."));
        add(1972, f.insertIniValhod(38, 21, "ROCNIK", "Ročník vazby"));
        add(1973, f.insertIniValhod(38, 22, "-ROCNIK", "Ročník vazby sest."));
        add(1974, f.insertIniValhod(38, 23, "ROK", "Rok vazby"));
        add(1975, f.insertIniValhod(38, 24, "-ROK", "Rok vazby sest."));
        add(1976, f.insertIniValhod(38, 25, "ROZMEZI_CISEL", "Rozmezí čísel vazby"));
        add(1977, f.insertIniValhod(38, 26, "-ROZMEZI_CISEL", "Rozmezí čísel sest."));
        add(1978, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE OAI_PROVIDER ALTER COLUMN IP_ADDR TYPE STRING_50"},
                new String[]{}))); //zvetseni sloupce pro IP adresu, aby se do ni vesla IPv6
        add(1979, f.sql("INSERT INTO INI_SEKCE (poradi, id_sekce, suffix_num, limit_od, limit_do) values (38, 'OAI_PROVIDER', 0, null, null)"));
        add(1980, f.insertIniKey("OAI_PROVIDER", "VzorIdDokumentu", 10, "Vzor (šablona) pro generování identifikátoru dokumentu, například oai:kpsys.cz:{recordId}", null, "oai:{recordId}", "SCALAR", "TEXT"));
        add(1981, f.insertIniKey("OAI_PROVIDER", "VzorIdAutority", 20, "Vzor (šablona) pro generování identifikátoru autority, například oai:kpsys.cz:A{recordId}", null, "oai:A{recordId}", "SCALAR", "TEXT"));
        add(1982, f.updateMessageByKeyAndCzech("konto.mojeKonto", "Konto čtenáře", "Můj účet", "My account"));
        add(1983, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace("reader-barcode-row", "reader-barCode-row");
            return newContent;
        }));
        add(1984, f.updateMessageKey("exemplar.creationDte", "exemplar.creationDate"));
        add(1985, f.insertIniValhod(35, 26, "PRILOHY", "Přílohy"));
        add(1986, f.insertIniValhod(36, 26, "PRILOHY", "Přílohy"));
        add(1987, f.insertIniValhod(35, 27, "CISLO_FAKTURY", "Číslo faktury"));
        add(1988, f.insertIniValhod(36, 27, "CISLO_FAKTURY", "Číslo faktury"));
        add(1989, f.insertIniValhod(35, 28, "DODAVATEL", "Dodavatel"));
        add(1990, f.insertIniValhod(36, 28, "DODAVATEL", "Dodavatel"));
        add(1991, f.insertIniValhod(35, 29, "CISLO", "Číslo"));
        add(1992, f.sql("UPDATE INI_KEYS SET FK_VALHOD = 35 WHERE FK_VALHOD = 36"));
        add(1993, f.sql("DELETE FROM INI_VALHOD WHERE ID_VALHOD = 36"));
        add(1994, f.insertMessage("ctenar.bulkEmailsAllowed", "Posílat hromadné emaily", "Sending of bulk emails"));
        add(1995, f.deleteMessage("vysledky.plneZobrazeni"));
        add(1996, f.deleteMessage("vysledky.kompaktniZobrazeni"));
        add(1997, f.deleteMessage("vysledky.byloNalezenoPrilisMnohoZaznamuUpresneteHledani"));
        add(1998, f.deleteMessage("commons.DataSavingError"));
        add(1999, f.deleteMessage("commons.ConnectionError"));
        add(2000, f.disableIniTriggers());
        add(2000, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'TextDefDokumentKompakt'"));
        add(2001, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'TextDefDokumentKompakt'"));
        add(2002, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'VelikostStrankyDokumentuKompakt'"));
        add(2003, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'VelikostStrankyDokumentuKompakt'"));
        add(2004, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'DefaultKompaktniZobrazeni'"));
        add(2005, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'DefaultKompaktniZobrazeni'"));
        add(2006, f.renameFile(path(customFolderPath, "html"), "PrintReceiptOfPayment.vm", "payments-receipt.html.vtl"));
        add(2007, f.renameFile(path(customFolderPath, "html"), "PrintRegistrationAgreement.vm", "user-registration-agreement.html.vtl"));
        add(2008, f.renameFile(path(customFolderPath, "html"), "PrintPayments.vm", "payments.html.vtl"));
        add(2009, f.renameFile(path(customFolderPath, "html"), "MailDocument.vm", "document-mail.html.vm"));
        add(2010, f.renameFile(path(customFolderPath, "html"), "CitationDocument.vm", "document-citation.html.vtl"));
        add(2011, f.renameFile(path(customFolderPath, "html"), "CitationDocumentCustom.vm", "document-citation.html.vtl"));
        add(2012, f.disableIniTriggers());
        add(2012, new ExceptionCatchingUpdate(f.sql("UPDATE INI_FILE SET HODNOTA = 'Template' where HODNOTA = 'CustomTemplate' and fk_klic = 'CitationService'")));
        add(2013, new ExceptionCatchingUpdate(f.sql("DELETE FROM INI_VALHOD WHERE ID_VALHOD = 31 AND PORADI = 4")));
        add(2014, f.renameFile(path(customFolderPath, "html"), "MailAfterFullRegistration.vm", "user-registration-success.html.vtl"));
        add(2015, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace("#parse(\"Document.vm\")", "#parse(\"document.html.vtl\")");
            newContent = newContent.replace(".vm\")", ".vtl\")");
            return newContent;
        }));
        add(2016, f.renameFile(path(customFolderPath, "html"), "MailDocuments.vm", "documents-mail.html.vtl"));
        add(2017, f.renameFile(path(customFolderPath, "html"), "MailFavourites.vm", "favourites-mail.html.vtl"));
        add(2018, f.updateMessageKey("templates.MailFavourites", "templates.favourites-mail.html"));
        add(2019, f.renameFile(path(customFolderPath, "html"), "MailLoanRequest.vm", "loan-request-mail.html.vtl"));
        add(2020, f.renameFile(path(customFolderPath, "html"), "MailResetPassword.vm", "user-password-reset-mail.html.vtl"));
        add(2021, f.updateMessageKey("templates.PrintFavourites", "templates.favourites.html"));
        add(2022, f.updateFiles(new ProxiedList<>() {
            @Override
            protected List<File> getList() {
                File customFolder = new File(customFolderPath);
                return new ArrayList<>(listFiles(customFolder, new String[]{"vm"}, true));
            }
        }, oldFile -> {
            File newFile = new File(oldFile.getParent(), oldFile.getName().replace(".vm", ".vtl"));
            try {
                boolean renamed = oldFile.renameTo(newFile);
                if (renamed) {
                    log.info(String.format("File %s successfully renamed to %s", oldFile.getAbsolutePath(), newFile.getName()));
                } else {
                    log.warn(String.format("File %s was not renamed to %s", oldFile.getAbsolutePath(), newFile.getName()));
                }
                return newFile;
            } catch (Exception e) {
                log.warn(String.format("Error while renaming file %s to %s", oldFile.getAbsolutePath(), newFile.getName()), e);
                return oldFile;
            }
        }));
        add(2023, f.disableIniTriggers());
        add(2023, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'BuiltInTemplates'"));
        add(2024, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'BuiltInTemplates'"));
        add(2025, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace("#parse(\"CitationDocument.vtl\")", "#parse(\"document-citation.html.vtl\")");
            return newContent;
        }));
        add(2026, f.renameFile(path(customFolderPath, "html"), "PrintFavourites.vm", "favourites.html.vtl"));
        add(2027, f.updateMessageKey("templates.MailDocument", "templates.document-mail"));
        add(2028, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace("#parse(\"document.html.vtl\")", "#parse(\"document-print.vtl\")");
            newContent = newContent.replace(".html.vtl", ".vtl");
            newContent = newContent.replace(".ris.vtl", "-ris.vtl");
            newContent = newContent.replace(".csv.vtl", "-csv.vtl");
            return newContent;
        }));
        add(2029, f.updateMessageKey("templates.favourites.html", "templates.favourites-print"));
        add(2030, f.updateMessageKey("templates.favourites-mail.html", "templates.favourites-mail"));
        add(2031, f.renameFile(path(customFolderPath, "html"), "document-citation.html.vtl", "document-citation.vtl"));
        add(2032, f.renameFile(path(customFolderPath, "html"), "document.html.vtl", "document-print.vtl"));
        add(2033, f.renameFile(path(customFolderPath, "html"), "loan-request-mail.html.vtl", "loan-request-mail.vtl"));
        add(2034, f.renameFile(path(customFolderPath, "html"), "user-password-reset-mail.html.vtl", "user-password-reset-mail.vtl"));
        add(2035, f.renameFile(path(customFolderPath, "html"), "documents-mail.html.vtl", "documents-mail.vtl"));
        add(2036, f.renameFile(path(customFolderPath, "html"), "favourites-mail.html.vtl", "favourites-mail.vtl"));
        add(2037, f.renameFile(path(customFolderPath, "html"), "favourites.html.vtl", "favourites-print.vtl"));
        add(2038, f.renameFile(path(customFolderPath, "html"), "payments-receipt.html.vtl", "payments-receipt-print.vtl"));
        add(2039, f.renameFile(path(customFolderPath, "html"), "user-registration-agreement.html.vtl", "user-registration-agreement-print.vtl"));
        add(2040, f.renameFile(path(customFolderPath, "html"), "payments.html.vtl", "payments-print.vtl"));
        add(2041, f.renameFile(path(customFolderPath, "html"), "document-mail.html.vm", "document-mail.vtl"));
        add(2042, f.renameFile(path(customFolderPath, "html"), "user-registration-success.html.vtl", "user-registration-success-mail.vtl"));
        add(2043, f.updateCustomFilesContent(customFolderPath, new String[] {"css", "html", "htm", "txt", "properties", "vm", "vtl"}, oldContent -> {
            String newContent = oldContent;

            Matcher m = Pattern.compile("(?:[^\\$\\{])(\\{[0-9a-zA-z\\.]*\\})").matcher(newContent);

            List<String> messageCodesWithBrackets = new ArrayList<>();
            while (m.find()) {
                String group = m.group(1);
                messageCodesWithBrackets.add(group);
            }

            //nalezene message kody nahradime makrem
            for (String messageCodeWithBrackets : messageCodesWithBrackets) {
                String messageCode = messageCodeWithBrackets.substring(1, messageCodeWithBrackets.length()-1);
                newContent = newContent.replace(messageCodeWithBrackets, "#loc('"+messageCode+"')");
            }

            return newContent;
        }));
        add(2044, f.updateCustomFilesContent(customFolderPath, new String[] {"vm", "vtl"}, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace(".getAllSubfields(", ".getSubfields(");
            return newContent;
        }));
        add(2045, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_NADPISU", source -> source.replace(".getAllSubfields(", ".getSubfields(")));
        add(2045, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY", source -> source.replace(".getAllSubfields(", ".getSubfields(")));
        add(2045, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-POD_OBALKOU", source -> source.replace(".getAllSubfields(", ".getSubfields(")));
        add(2045, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-AUTORITA", source -> source.replace(".getAllSubfields(", ".getSubfields(")));
        add(2045, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentPlne", source -> source.replace(".getAllSubfields(", ".getSubfields(")));
        add(2045, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentVice", source -> source.replace(".getAllSubfields(", ".getSubfields(")));
        add(2046, f.updateMessage("templates.document-mail", "Výchozí", "Default"));
        add(2047, f.updateMessage("templates.favourites-mail", "Výchozí", "Default"));
        add(2048, f.updateMessage("templates.favourites-print", "Výchozí", "Default"));
        add(2049, f.insertMessage("commons.CisloPerX", "Číslo {0}", "Issue {0}"));
        add(2050, f.grantAll("OPAC_NENALEZENE_OBALKY", "opac"));
        add(2051, f.insertIniKey("OPAC_SEARCH", "MostLentIncludePerio", 500, "Zda má seznam nejpůjčovanějších obsahovat i periodika", 1, "NE", "SCALAR", "VALHOD"));
        add(2052, f.updateCustomFilesContent(customFolderPath, new String[] {"css"}, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace(".repeatButton", ".field-repeat-button");
            return newContent;
        }));
        add(2053, f.updateCustomFilesContent(customFolderPath, new String[] {"css", "html", "htm", "txt", "properties", "vm", "vtl", "js"}, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace("getNumber3Char", "getNumber");
            newContent = newContent.replace("number3Char", "number");
            return newContent;
        }));
        add(2054, f.deleteMessage("commons.druhDokumentu"));
        add(2055, f.deleteMessage("commons.druhDokumentu"));
        add(2056, f.insertMessage("commons.VytvoritDalsi", Map.of("text_cze", "Vytvořit další", "text_eng", "Create new", "text_ger", "Neu erstellen")));
        add(2057, f.updateIniDefaultValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY",
                """
                        #fond()

                        <br/>

                        #sf(773 '''' ''{detail.PublishedIn} '' ''<br/>'')

                        #sf(250 '''' '''' ''<br/>'')

                        #sf(260 ''ab'')

                        #sf(260 ''cdefg'' '''' '';'')

                        #sf(264 '''' '''' '';'')

                        #if($record.query(''260'').any)<br/>#end

                        #sf(300 '''' '''' ''<br/>'')

                        #sf(700 ''a'' ''{detail.ostatniAutori}: '' ''<br/>'')\s

                        #sf(44)"""
        ));
        add(2058, f.deleteMessage("editace.opakovatPole"));
        add(2059, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> {
            return source
                    .replace("menuItem_10", "menu-19") //statistiky - dvojciferne nahore kvuli moznemu prepisu (1 a 11).
                    .replace("menuItem_11", "menu-toto-je-treba-smazat") //napoveda
                    .replace("menuItem_0", "menu-1") //home
                    .replace("menuItem_1", "menu-2") //vyhledavani
                    .replace("menuItem_2", "menu-5") //katalogizace
                    .replace("menuItem_3", "menu-8") //vypujcni modul
                    .replace("menuItem_4", "menu-9") //konto ctenare
                    .replace("menuItem_5", "menu-12001") //util
                    .replace("menuItem_6", "menu-10001") //nastaveni
                    .replace("menuItem_7", "menu-") //oblibene
                    .replace("menuItem_8", "menu-11") //rejstriky
                    .replace("menuItem_9", "menu-14"); //zajimave
        }));
        add(2060, f.updateMessageKey("hledani.rovno", "hledani.je"));
        add(2061, f.updateMessage("hledani.je", "je", "is"));
        add(2062, MultipleUpdate.of(
                f.updateMessage("hledani.zacinaNa", "začíná na", "starts with"),
                f.updateMessageTranslation("hledani.zacinaNa", "text_ger", "beginnt mit")
        ));
        add(2063, f.deleteMessage("uuuu"));
        add(2064, f.insertMessage("uuuu", "neznámé", "unknown"));
        add(2065, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source
                .replace("indicatorsEditation", "indicators-editation")
                .replace("field-number", "field-type-number")
                .replace("subfield-number", "subfield-type-code")
                .replace("pridatButton", "add-to-favourites-button")
                .replace("odebratButton", "remove-from-favourites-button")
                .replace("zADoOblibenychButtonContainer", "jp-favourites-button-container")));
        add(2066, f.deleteMessage("hledani.od"));
        add(2067, f.deleteMessage("hledani.do"));
        add(2068, f.sql("UPDATE OPAC_MENU SET TARGET = '/documents/new?source=menu' WHERE TARGET = '/edit/records/documents/new/start'"));
        add(2069, f.sql("UPDATE OPAC_MENU SET TARGET = '/authorities/new?source=menu' WHERE TARGET = '/edit/records/authorities/new'"));
        add(2070, f.insertMessage("commons.Pouzit", "Použít", "Use"));
        add(2071, f.insertMessage("loan.VypujckaBylaVracenaADalsiRezervaceCtenaremXIdY", "Výpůjčka byla vrácena. Na dokument je čekající rezervace čtenářem {0} ({1}).", "Hold was returned. Document is reserved by reader {0} ({1})."));
        add(2072, f.updateMessageKey("konto.vypujckaVracena", "loan.VypujckaBylaVracena"));
        add(2073, f.updateMessageKey("loan.OpravduVratitBudeVygenerovanaPokuta", "loan.OpravduVratitBudeVygenerovanaPokutaX"));
        add(2074, f.updateMessage("loan.OpravduVratitBudeVygenerovanaPokutaX", "Opravdu vrátit? Bude vygenerována pokuta ({0}Kč)", "Really return? Penalty ({0}Kč) will be generated."));
        add(2075, f.updateMessageKey("ctenar.typTiskuUpominek", "ctenar.overdueNoticesPrintType"));
        add(2076, f.updateMessageKey("ctenar.typTiskuRezervaci", "ctenar.reservationsPrintType"));
        add(2077, f.deleteMessage("hledaneDotazy.ulozeneDotazy"));
        add(2078, f.deleteMessage("hledaneDotazy.datumUlozeni"));
        add(2079, f.deleteMessage("hledaneDotazy.proZobrazeniUlozenychSePrihlaste"));
        add(2080, f.deleteMessage("hledaneDotazy.smazatHistorii"));
        add(2081, f.deleteMessage("hledaneDotazy.ulozitDotaz"));
        add(2082, f.deleteMessage("hledaneDotazy.dotazyHledani"));
        add(2083, f.updateMessageKey("ctenar.tiskPostou", "ctenar.printType.Postou"));
        add(2084, f.updateMessageKey("ctenar.tiskEmailem", "ctenar.printType.Emailem"));
        add(2085, f.updateMessageKey("ctenar.tiskSMSZpravou", "ctenar.printType.SmsZpravou"));
        add(2086, f.insertMessage("ctenar.printType.EmailemASmsZpravou", "Emailem a SMS právou", "By email and sms"));
        add(2087, f.insertMessage("commons.Stahnout", "Stáhnout", "Download"));
        add(2088, MultipleUpdate.of(
                f.updateMessage("mail.SenderEmail", "Váš e-mail", "Your e-mail"),
                f.updateMessageTranslation("mail.SenderEmail", "text_ger", "Ihre e-mail")
        ));
        add(2089, f.updateMessageKey("mail.SenderEmail", "mail.YourEmail"));
        add(2090, f.grantSelect("VAZ_UZIV_BUDOVA", "opac"));
        add(2091, f.disableIniTriggers());
        add(2091, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'OBECNE-SEZNAM_VALIDOVANYCH_POLI'")); //melo by byt uz davno smazane, ale nasel jsem to v huavu
        add(2092, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'OBECNE-SEZNAM_VALIDOVANYCH_POLI'"));
        add(2093, new ExceptionCatchingUpdate(f.sql("DROP TABLE OPAC_ULOZENE_DOTAZY")));
        add(2093, new ExceptionCatchingUpdate(f.sql("ALTER TABLE LOKALIZACE ADD TEXT_FRA UTF_2000")));
        add(2093, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE LOKALIZACE ADD TEXT_FRA UTF_2000"},
                new String[]{})));
    }



    private void fill2() {
        add(2094, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Extrait simple' WHERE ID_LOKALIZACE = 'templates.favourites-print'"));
        add(2095, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statut n´autorise pas de pret' WHERE ID_LOKALIZACE = 'loan.StatusNepovolujePujceni'"));
        add(2096, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de prolonger, l´exemplaire sera déplacé' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitExemplarVCirkulaci'"));
        add(2097, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fermer' WHERE ID_LOKALIZACE = 'AKCE.0000.A0_ZavriSheet'"));
        add(2098, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fermer l´onglet actuel' WHERE ID_LOKALIZACE = 'AKCE.0000.A0_ZavriSheet.HINT'"));
        add(2099, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Changer de lecteur/ choisir un autre lecteur' WHERE ID_LOKALIZACE = 'AKCE.0101.A1_PrepniUziv'"));
        add(2100, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Parametres usager?' WHERE ID_LOKALIZACE = 'AKCE.0102.A1_UzivDef'"));
        add(2101, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Preferences du lecteur' WHERE ID_LOKALIZACE = 'AKCE.0102.SETUP'"));
        add(2102, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Installer la licence' WHERE ID_LOKALIZACE = 'AKCE.0103.A1_NastavLicence'"));
        add(2103, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mise a jour du programme' WHERE ID_LOKALIZACE = 'AKCE.0104.A1_AktualizovatProg'"));
        add(2104, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Actualiser la transaction' WHERE ID_LOKALIZACE = 'AKCE.0105.A1_AktualizovatTrans'"));
        add(2105, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fermer' WHERE ID_LOKALIZACE = 'AKCE.0106.A0_ZavriSheet'"));
        add(2106, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue' WHERE ID_LOKALIZACE = 'AKCE.1000.A0_OpenKatgFond'"));
        add(2107, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fond ouvert' WHERE ID_LOKALIZACE = 'AKCE.1000.A0_OpenKatgFond.HINT'"));
        add(2108, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Rechercher' WHERE ID_LOKALIZACE = 'AKCE.1001.A4_HledaniPole'"));
        add(2109, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Suppression de choix' WHERE ID_LOKALIZACE = 'AKCE.1002.A4_HledaniZrus'"));
        add(2110, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Controler l´accessibilté' WHERE ID_LOKALIZACE = 'AKCE.1003.A4_Dostupnost'"));
        add(2111, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par autorité' WHERE ID_LOKALIZACE = 'AKCE.1005.A4_HledAut'"));
        add(2112, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue' WHERE ID_LOKALIZACE = 'AKCE.1100.A0_OpenKatalog'"));
        add(2113, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retourner autre document' WHERE ID_LOKALIZACE = 'loan.VratitDalsi'"));
        add(2114, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Publié/Paru dans' WHERE ID_LOKALIZACE = 'detail.PublishedIn'"));
        add(2115, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prolonger l´enregistrement' WHERE ID_LOKALIZACE = 'ctenar.ProdlouzitRegistraci'"));
        add(2116, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Question de recherche' WHERE ID_LOKALIZACE = 'hledaneDotazy.vyhledavanyDotaz'"));
        add(2117, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accès a été refusé!' WHERE ID_LOKALIZACE = 'commons.AccessDenied'"));
        add(2118, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ancien mot de passe est incorrect' WHERE ID_LOKALIZACE = 'login.WrongOldPassword'"));
        add(2119, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Titre' WHERE ID_LOKALIZACE = 'hledani.nazvu'"));
        add(2120, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Titre' WHERE ID_LOKALIZACE = 'commons.nazev'"));
        add(2121, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Combinaison de catégories d´utilisateur et de prets n´autorise pas de pret' WHERE ID_LOKALIZACE = 'loan.KombinaceCtenKatAVypKatNepovolujePujceni'"));
        add(2122, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Opération n´est pas supportée!' WHERE ID_LOKALIZACE = 'commons.UnsupportedOperation'"));
        add(2123, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro de carte de lecture' WHERE ID_LOKALIZACE = 'commons.cisLeg'"));
        add(2124, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Démarche 1' WHERE ID_LOKALIZACE = 'registrace.krok1'"));
        add(2125, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de prolonger PEB' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitMvs'"));
        add(2126, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossile de réserver' WHERE ID_LOKALIZACE = 'loan.KombinaceKategorieCtenareAVypujckyNeumoznujeRezervaci'"));
        add(2127, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Cliquer ici et écrirez votre question de recherche' WHERE ID_LOKALIZACE = 'hledani.kliknutimZacnetePsatKriteria'"));
        add(2128, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Utilisateur anonyme' WHERE ID_LOKALIZACE = 'commons.anonymniCtenar'"));
        add(2129, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'De maniere dégressive' WHERE ID_LOKALIZACE = 'hledani.sestupne'"));
        add(2130, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Année' WHERE ID_LOKALIZACE = 'commons.rok'"));
        add(2131, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom du champ' WHERE ID_LOKALIZACE = 'detail.nazevPole'"));
        add(2132, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Les plus suivis' WHERE ID_LOKALIZACE = 'seznam.Nejsledovanejsi'"));
        add(2133, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'et à la fois' WHERE ID_LOKALIZACE = 'hledani.aZaroven'"));
        add(2134, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche finie, vous pouvez lancer une nouvelle recherche' WHERE ID_LOKALIZACE = 'hledani.HledaniVyprselo'"));
        add(2135, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Démarche 2' WHERE ID_LOKALIZACE = 'registrace.krok2'"));
        add(2136, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de commande' WHERE ID_LOKALIZACE = 'mvs.datumObjednani'"));
        add(2137, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réservation non envoyée' WHERE ID_LOKALIZACE = 'loan.UnsentReservation'"));
        add(2138, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par autorités' WHERE ID_LOKALIZACE = 'hledani.hledatAutority'"));
        add(2139, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Afficher les autres  {0} notices' WHERE ID_LOKALIZACE = 'vysledky.NacistDalsichX'"));
        add(2140, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Loading des exemplaires' WHERE ID_LOKALIZACE = 'detail.nacitamExemplare'"));
        add(2141, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Paramètres' WHERE ID_LOKALIZACE = 'commons.nastaveni'"));
        add(2142, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notion recherchée' WHERE ID_LOKALIZACE = 'hledani.vyhledavanyVyraz'"));
        add(2143, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Rangé par auteur' WHERE ID_LOKALIZACE = 'vysledky.razenoPodleAutora'"));
        add(2144, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de prolonger ce pret' WHERE ID_LOKALIZACE = 'loan.NelzeVicekratProdlouzit'"));
        add(2145, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro de carte de lecteur ou Code-barres' WHERE ID_LOKALIZACE = 'registrace.cisLegOrBarCodInputLabel'"));
        add(2146, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de documents contenant cette vedette d´autorité' WHERE ID_LOKALIZACE = 'rejstrik.pocetDokumentuObsahujicichTutoAutoritu'"));
        add(2147, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de supprimer' WHERE ID_LOKALIZACE = 'loan.CannotBeCancelled'"));
        add(2148, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Cote de rangement' WHERE ID_LOKALIZACE = 'detail.signatura'"));
        add(2149, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucunes acquisitions de ce titre pour la réservation' WHERE ID_LOKALIZACE = 'loan.TitulNemaZadneRezervovatelnePrirustky'"));
        add(2150, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date d´envoi' WHERE ID_LOKALIZACE = 'mvs.datumPodani'"));
        add(2151, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Utilisateur connecté n´est pas lecteur!' WHERE ID_LOKALIZACE = 'login.PrihlasenyUzivatelNeniCtenar'"));
        add(2152, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Interrogation de recherche pas insérée' WHERE ID_LOKALIZACE = 'hledani.QueryDoesNotExist'"));
        add(2153, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Volumes reliés' WHERE ID_LOKALIZACE = 'detail.svazaneRocniky'"));
        add(2154, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Encore non autorisé' WHERE ID_LOKALIZACE = 'loan.NotApprovedYet'"));
        add(2155, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Informations sur la localisation de cet exemplaire ne sont pas accessibles' WHERE ID_LOKALIZACE = 'exemplar.DataOUmisteniExNejsouKDispozici'"));
        add(2156, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessible dans le bâtiment' WHERE ID_LOKALIZACE = 'loan.VolnyNaBudove'"));
        add(2157, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrez vous d´abord' WHERE ID_LOKALIZACE = 'commons.LoginToAccess'"));
        add(2158, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catégorie' WHERE ID_LOKALIZACE = 'commons.kategorie'"));
        add(2159, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document' WHERE ID_LOKALIZACE = 'commons.dokument'"));
        add(2160, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer - lié au document source' WHERE ID_LOKALIZACE = 'record.deletion.NelzeSmazatVazbaPresZdrojovyDokument'"));
        add(2161, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Commandes' WHERE ID_LOKALIZACE = 'commons.objednavky'"));
        add(2162, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Type de document' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_FOND'"));
        add(2163, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Simple' WHERE ID_LOKALIZACE = 'statistiky.JEDNODUCHE'"));
        add(2164, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mot de passe' WHERE ID_LOKALIZACE = 'login.passwordLabel'"));
        add(2165, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vous n´êtes pas enregistré. Si vous ne voulez pas que le commentaire reste anonyme, faites votre enregistrement' WHERE ID_LOKALIZACE = 'detail.nejstePrihlasenProNeanonymniKomentarSePrihaste'"));
        add(2166, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prolongation dernière' WHERE ID_LOKALIZACE = 'konto.posledniProlongace'"));
        add(2167, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accepté' WHERE ID_LOKALIZACE = 'commons.prijata'"));
        add(2168, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Remise à zéro' WHERE ID_LOKALIZACE = 'commons.reset'"));
        add(2169, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Suivant' WHERE ID_LOKALIZACE = 'commons.Nasledujici'"));
        add(2170, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Salle d´etudes ou e-book' WHERE ID_LOKALIZACE = 'loan.PrezencneNeboEBook'"));
        add(2171, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Valeur individuelle' WHERE ID_LOKALIZACE = 'exemplar.customValue'"));
        add(2172, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement du fournisseur via E-ZAK' WHERE ID_LOKALIZACE = 'login.PrihlaseniEzakem'"));
        add(2173, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Égale' WHERE ID_LOKALIZACE = 'hledani.je'"));
        add(2174, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Code de procédure' WHERE ID_LOKALIZACE = 'mvs.jednaciCislo'"));
        add(2175, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Articles' WHERE ID_LOKALIZACE = 'detail.clanky'"));
        add(2176, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Erreur de lecture des données' WHERE ID_LOKALIZACE = 'commons.DataLoadError'"));
        add(2177, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Relevance' WHERE ID_LOKALIZACE = 'hledani.relevance'"));
        add(2178, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucuns numéros à  partir de ce volume' WHERE ID_LOKALIZACE = 'detail.odTohotoRocnikuNejsouCisla'"));
        add(2179, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´annuler la réservation est exécutée' WHERE ID_LOKALIZACE = 'loan.NelzeRusitPripraveneRezervace'"));
        add(2180, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Facette' WHERE ID_LOKALIZACE = 'statistiky.REZ'"));
        add(2181, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Rangé par date de publication' WHERE ID_LOKALIZACE = 'vysledky.razenoPodleRokuVydani'"));
        add(2182, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro d´acquisition' WHERE ID_LOKALIZACE = 'exemplar.accessNumber'"));
        add(2183, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Type' WHERE ID_LOKALIZACE = 'konto.typ'"));
        add(2184, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Note' WHERE ID_LOKALIZACE = 'commons.poznamka'"));
        add(2185, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Envoyer sur e-mail' WHERE ID_LOKALIZACE = 'detail.odeslatEmailem'"));
        add(2186, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lien permanent' WHERE ID_LOKALIZACE = 'detail.permantentniLink'"));
        add(2187, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Décembre' WHERE ID_LOKALIZACE = 'commons.mesic.12'"));
        add(2188, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par livre, par auteur, par sujet' WHERE ID_LOKALIZACE = 'hledani.hledatKnihyAutoryTemata'"));
        add(2189, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Liste de lecteurs' WHERE ID_LOKALIZACE = 'commons.seznamCtenaru'"));
        add(2190, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer - il est preté' WHERE ID_LOKALIZACE = 'exemplar.NelzeSmazatJeVypujceno'"));
        add(2191, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'pièces' WHERE ID_LOKALIZACE = 'commons.ks'"));
        add(2192, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de publication' WHERE ID_LOKALIZACE = 'commons.rokVydani'"));
        add(2193, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'S´enregistrer via E-ZAK' WHERE ID_LOKALIZACE = 'login.PrihlasitSeEzakem'"));
        add(2194, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Emprunté' WHERE ID_LOKALIZACE = 'commons.pujcena'"));
        add(2195, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pas de contrôle' WHERE ID_LOKALIZACE = 'record.discardion.TentoSeNekontroluje'"));
        add(2196, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Somme' WHERE ID_LOKALIZACE = 'commons.soucet'"));
        add(2197, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement par système central' WHERE ID_LOKALIZACE = 'login.prihlaseniJasigem'"));
        add(2198, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Commande' WHERE ID_LOKALIZACE = 'commons.objednavka'"));
        add(2199, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catégorie de lecteur ne permet pas le pret' WHERE ID_LOKALIZACE = 'loan.KategorieCtenareNeumoznujeVypujcky'"));
        add(2200, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Portaro - catalogue de la bibliothèque' WHERE ID_LOKALIZACE = 'commons.title'"));
        add(2201, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Histoire de recherches' WHERE ID_LOKALIZACE = 'commons.zobrazitHledaneDotazy'"));
        add(2202, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Localisation' WHERE ID_LOKALIZACE = 'exemplar.location'"));
        add(2203, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vous avez déjà voté' WHERE ID_LOKALIZACE = 'rating.YouAlreadyEvaluated'"));
        add(2204, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de réception' WHERE ID_LOKALIZACE = 'mvs.datumPrijeti'"));
        add(2205, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réservation réglée' WHERE ID_LOKALIZACE = 'loan.SentReservation'"));
        add(2206, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vous utilisez le navigateur désuet' WHERE ID_LOKALIZACE = 'commons.pouzivateZastaralyProhlizec'"));
        add(2207, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Localisation' WHERE ID_LOKALIZACE = 'exemplar.placement'"));
        add(2208, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Les plus suivis' WHERE ID_LOKALIZACE = 'statistiky.NEJSLEDOVANEJSI'"));
        add(2209, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'PEB' WHERE ID_LOKALIZACE = 'commons.odeslatMvsZadanku'"));
        add(2210, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret a été déjà prolongé aujourd´hui' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitDnesJizProdlouzeno'"));
        add(2211, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche' WHERE ID_LOKALIZACE = 'hledani.title.hledani'"));
        add(2212, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de publication de l´oeuvre' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_ROK'"));
        add(2213, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Code-barres' WHERE ID_LOKALIZACE = 'commons.barCod'"));
        add(2214, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Documents à télécharger' WHERE ID_LOKALIZACE = 'detail.prilohyDokumentu'"));
        add(2215, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Éliminer' WHERE ID_LOKALIZACE = 'commons.vyradit'"));
        add(2216, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche' WHERE ID_LOKALIZACE = 'commons.hledat'"));
        add(2217, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'moins' WHERE ID_LOKALIZACE = 'commons.less'"));
        add(2218, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Collections' WHERE ID_LOKALIZACE = 'commons.fondy'"));
        add(2219, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Emprunt a été retourné' WHERE ID_LOKALIZACE = 'loan.VypujckaBylaVracena'"));
        add(2220, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Premier enregistrement' WHERE ID_LOKALIZACE = 'login.prvniPrihlaseni'"));
        add(2221, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement impossible, vous n´avez pas inséré la date de naissance!' WHERE ID_LOKALIZACE = 'login.NeuvedenRokNarozeni'"));
        add(2222, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Titre ou ISBN du document' WHERE ID_LOKALIZACE = 'hledani.nazevNeboIsbn'"));
        add(2223, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statistiques mensuelles' WHERE ID_LOKALIZACE = 'statistiky.mesicniStatistiky'"));
        add(2224, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Novembre' WHERE ID_LOKALIZACE = 'commons.mesic.11'"));
        add(2225, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Octobre' WHERE ID_LOKALIZACE = 'commons.mesic.10'"));
        add(2226, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Acquisition standarde' WHERE ID_LOKALIZACE = 'exemplar.typCisla.StandardniPrirustek'"));
        add(2227, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mon panier' WHERE ID_LOKALIZACE = 'statistiky.SCHRANKA'"));
        add(2228, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de documents' WHERE ID_LOKALIZACE = 'statistiky.pocetDokumentu'"));
        add(2229, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Volumes' WHERE ID_LOKALIZACE = 'commons.rocniky'"));
        add(2230, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Emprunter le livre' WHERE ID_LOKALIZACE = 'commons.vypujcitKnihu'"));
        add(2231, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveau mot de passe (confirmation)' WHERE ID_LOKALIZACE = 'registrace.passwordConfirmLabel'"));
        add(2232, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Cliquez les lettres initiales' WHERE ID_LOKALIZACE = 'rejstrik.zadejtePocatecniPismena'"));
        add(2233, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'PEB' WHERE ID_LOKALIZACE = 'commons.mvs'"));
        add(2234, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Autres auteurs' WHERE ID_LOKALIZACE = 'detail.ostatniAutori'"));
        add(2235, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réservation - impossible de prolonger' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitRezervaci'"));
        add(2236, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = '{0} à prêter {1} et a etudier sur place {2}' WHERE ID_LOKALIZACE = 'loan.XKVypujceniAYPrezencneZZ'"));
        add(2237, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucunes dettes et obligations' WHERE ID_LOKALIZACE = 'konto.zadneDluhy'"));
        add(2238, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document preté a un rappel, prolongation interdite' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitVypujckaJeUpominana'"));
        add(2239, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Commence par' WHERE ID_LOKALIZACE = 'hledani.zacinaNa'"));
        add(2240, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Dans les autres bâtiments' WHERE ID_LOKALIZACE = 'loan.NaOstatnichBudovach'"));
        add(2241, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réservation supprimée' WHERE ID_LOKALIZACE = 'loan.CancelledReservation'"));
        add(2242, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'À  la page d''accueil' WHERE ID_LOKALIZACE = 'commons.domu'"));
        add(2243, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Emprunter' WHERE ID_LOKALIZACE = 'commons.vypujcit'"));
        add(2244, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retour à la page d´accueil' WHERE ID_LOKALIZACE = 'commons.backToIndexButton'"));
        add(2245, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vraiment effacer? Les factures sont actives.' WHERE ID_LOKALIZACE = 'record.deletion.OpravduSmazatAktivniFaktury'"));
        add(2246, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Article demandé' WHERE ID_LOKALIZACE = 'mvs.pozadovanyClanek'"));
        add(2247, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Affichage du texte intégral' WHERE ID_LOKALIZACE = 'commons.zobrazitPlnyText'"));
        add(2248, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Rechercher ailleurs' WHERE ID_LOKALIZACE = 'detail.vyhledatJinde'"));
        add(2249, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessibilité' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_VYPKAT'"));
        add(2250, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de notices trouvées' WHERE ID_LOKALIZACE = 'hledaneDotazy.pocetVyhledanychZaznamu'"));
        add(2251, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notes' WHERE ID_LOKALIZACE = 'commons.pozn'"));
        add(2252, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'D´une ligne' WHERE ID_LOKALIZACE = 'statistiky.JEDNORADKOVE'"));
        add(2253, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Volume' WHERE ID_LOKALIZACE = 'commons.rocnik'"));
        add(2254, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'ID de lecteur incorrect' WHERE ID_LOKALIZACE = 'loan.CtenarSTimtoIdNeexistuje'"));
        add(2255, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Année demandée' WHERE ID_LOKALIZACE = 'mvs.pozadovanyRocnik'"));
        add(2256, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retourer le livre' WHERE ID_LOKALIZACE = 'commons.vratitKnihu'"));
        add(2257, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statistiques' WHERE ID_LOKALIZACE = 'commons.Statistiky'"));
        add(2258, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Bâtiment' WHERE ID_LOKALIZACE = 'commons.budova'"));
        add(2259, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveau mot de passe (4 chiffres)' WHERE ID_LOKALIZACE = 'registrace.passwordLabel'"));
        add(2260, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mot de passe est trop long (il faut 4 chiffres)' WHERE ID_LOKALIZACE = 'login.PasswordTooLong'"));
        add(2261, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Renover cache' WHERE ID_LOKALIZACE = 'util.refreshujCache'"));
        add(2262, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'CN - Numéro de carte de lecture' WHERE ID_LOKALIZACE = 'commons.cisLeg.zkratka'"));
        add(2263, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Annuel' WHERE ID_LOKALIZACE = 'commons.rocni'"));
        add(2264, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Et autres' WHERE ID_LOKALIZACE = 'commons.aDalsi'"));
        add(2265, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Clé demandée n´existe pas' WHERE ID_LOKALIZACE = 'util.IniKlicNeexistuje'"));
        add(2266, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Envoyer' WHERE ID_LOKALIZACE = 'commons.odeslat'"));
        add(2267, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Entrées des lecteurs de la bibliothèque et hors d´elle' WHERE ID_LOKALIZACE = 'statistiky.vstupyCtenaruZKnihovnyAMimo'"));
        add(2268, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Effacer cache' WHERE ID_LOKALIZACE = 'util.smazCache'"));
        add(2269, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document n´a pas de volumes reliés' WHERE ID_LOKALIZACE = 'detail.dokumentNemaZadneSvazaneRocniky'"));
        add(2270, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveautés de {0}' WHERE ID_LOKALIZACE = 'seznam.novinkyOdX'"));
        add(2271, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Titres intéressants' WHERE ID_LOKALIZACE = 'commons.ZajimaveTituly'"));
        add(2272, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Année d´acquisition' WHERE ID_LOKALIZACE = 'exemplar.acquisitionYear'"));
        add(2273, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Effacer' WHERE ID_LOKALIZACE = 'commons.Smazat'"));
        add(2274, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réserver' WHERE ID_LOKALIZACE = 'commons.rezervovat'"));
        add(2275, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date d´exécution' WHERE ID_LOKALIZACE = 'mvs.datumVyrizeni'"));
        add(2276, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Index des vedettes d´autorités' WHERE ID_LOKALIZACE = 'statistiky.REJSTRIK_AUTORIT'"));
        add(2277, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Possibilité de livrer en photocopies' WHERE ID_LOKALIZACE = 'mvs.lzeVyresitFotokopii'"));
        add(2278, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Les meilleures évaluations' WHERE ID_LOKALIZACE = 'statistiky.NEJLEPE_HODNOCENE'"));
        add(2279, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retourner' WHERE ID_LOKALIZACE = 'konto.vratit'"));
        add(2280, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mouvements de lecteurs' WHERE ID_LOKALIZACE = 'statistiky.pohybyCtenaru'"));
        add(2281, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveautés' WHERE ID_LOKALIZACE = 'seznam.Novinky'"));
        add(2282, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro' WHERE ID_LOKALIZACE = 'commons.cislo'"));
        add(2283, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucuns documents' WHERE ID_LOKALIZACE = 'commons.zadneDokumenty'"));
        add(2284, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date d´enregistrement' WHERE ID_LOKALIZACE = 'konto.datumRegistrace'"));
        add(2285, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre d´entrées' WHERE ID_LOKALIZACE = 'statistiky.pocetVstupu'"));
        add(2286, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Éditeur' WHERE ID_LOKALIZACE = 'commons.nakladatel'"));
        add(2287, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessibilité' WHERE ID_LOKALIZACE = 'exemplar.availability'"));
        add(2288, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de lecteurs enregistrés' WHERE ID_LOKALIZACE = 'statistiky.pocetZaregistrovanychCtenaru'"));
        add(2289, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'll n´est pas dans le bâtiment online' WHERE ID_LOKALIZACE = 'loan.NeniNaOnlineBudove'"));
        add(2290, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notices trouvées' WHERE ID_LOKALIZACE = 'vysledky.nalezenychZaznamu'"));
        add(2291, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Similaire' WHERE ID_LOKALIZACE = 'commons.podobne'"));
        add(2292, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Votre enregistrement est déjà expiré' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitMateProslouRegistraci'"));
        add(2293, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Intérêt a la date' WHERE ID_LOKALIZACE = 'mvs.zajemDoData'"));
        add(2294, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Localisations des exemplaires' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_LOKACE'"));
        add(2295, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de réservation' WHERE ID_LOKALIZACE = 'konto.datumRezervace'"));
        add(2296, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveautés' WHERE ID_LOKALIZACE = 'statistiky.NOVINKY'"));
        add(2297, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérez votre commentaire ici' WHERE ID_LOKALIZACE = 'detail.semZadejteKomentar'"));
        add(2298, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'En ligne ascendante' WHERE ID_LOKALIZACE = 'hledani.vzestupne'"));
        add(2299, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Les mieux notés' WHERE ID_LOKALIZACE = 'seznam.NejlepeHodnocene'"));
        add(2300, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de recherche des donnés' WHERE ID_LOKALIZACE = 'hledaneDotazy.datumVyhledani'"));
        add(2301, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer' WHERE ID_LOKALIZACE = 'exemplar.NelzeSmazatJeSvazany'"));
        add(2302, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Trouver en Z-serveur' WHERE ID_LOKALIZACE = 'editace.vyhledatVZServeru'"));
        add(2303, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro' WHERE ID_LOKALIZACE = 'exemplar.issueName'"));
        add(2304, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Collection' WHERE ID_LOKALIZACE = 'commons.fond'"));
        add(2305, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notices par page' WHERE ID_LOKALIZACE = 'vysledky.zaznamuNaStranku'"));
        add(2306, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retour aux documents recherchés' WHERE ID_LOKALIZACE = 'detail.zpetNaVyhledane'"));
        add(2307, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'État' WHERE ID_LOKALIZACE = 'commons.stav'"));
        add(2308, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ajouter un commentaire' WHERE ID_LOKALIZACE = 'detail.pridatKomentar'"));
        add(2309, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Contenu' WHERE ID_LOKALIZACE = 'detail.obsah'"));
        add(2310, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ajouter un critère' WHERE ID_LOKALIZACE = 'hledani.pridatKriterium'"));
        add(2311, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Il s´agit de prolongation d´emprunt {0}' WHERE ID_LOKALIZACE = 'konto.jednaSeOXProdlouzeni'"));
        add(2312, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Avertissements de localisation' WHERE ID_LOKALIZACE = 'commons.lokalizacniHlasky'"));
        add(2313, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de prets actifs' WHERE ID_LOKALIZACE = 'statistiky.pocetAktivnichVypujcek'"));
        add(2314, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérer le titre ou l´identificateur de la nouvelle notice' WHERE ID_LOKALIZACE = 'hledani.title.zadejteNazevNeboIdentifikator'"));
        add(2315, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Hors de la bibliothèque' WHERE ID_LOKALIZACE = 'statistiky.MIMO_KNIHOVNU'"));
        add(2316, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réservé' WHERE ID_LOKALIZACE = 'commons.rezervovany'"));
        add(2317, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de prolonger - titre est déjà réservé' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitTitulJeZarezervovan'"));
        add(2318, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur authentifié n´est pas enregistré dans le système' WHERE ID_LOKALIZACE = 'login.OverenyUzivatelNeniEvidovan'"));
        add(2319, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Les meilleurs prets' WHERE ID_LOKALIZACE = 'statistiky.NEJPUJCOVANEJSI'"));
        add(2320, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'non à la fois' WHERE ID_LOKALIZACE = 'hledani.aZarovenNeni'"));
        add(2321, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vous avez déjà preté/réservé cet exemplaire' WHERE ID_LOKALIZACE = 'loan.NaTentoExemplarJizMateVypujckuNeboRezervaci'"));
        add(2322, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre maximal de réservations a été dépassé. Vous ne pouvez pas réserver' WHERE ID_LOKALIZACE = 'loan.PrekrocenMaximalniPocetRezervaci'"));
        add(2323, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pour votre commande ou réservation du document cliquez sur le bouton \"Commande\" ou Choisir une réservation' WHERE ID_LOKALIZACE = 'loan.NejdriveZvolteZdaChceteKnihuObjednatNeboRezervovat'"));
        add(2324, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prénom et nom' WHERE ID_LOKALIZACE = 'commons.jmenoAPrijmeni'"));
        add(2325, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche' WHERE ID_LOKALIZACE = 'hledani.Vyhledavani'"));
        add(2326, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Non accessible - lecteur a les transactions bloquées' WHERE ID_LOKALIZACE = 'loan.CtenarMaBlokovanyTransakce'"));
        add(2327, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Commentaires' WHERE ID_LOKALIZACE = 'detail.komentare'"));
        add(2328, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Système d´information' WHERE ID_LOKALIZACE = 'util.systemoveInformace'"));
        add(2329, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document est accessible, vous pouvez le retirer dans les bâtiments' WHERE ID_LOKALIZACE = 'loan.DokumentJeKVyzvednutiNaBudovach'"));
        add(2330, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Type' WHERE ID_LOKALIZACE = 'commons.typ'"));
        add(2331, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vraiment effacer? L´exemplaire reste aux archives des numéros déterminés.' WHERE ID_LOKALIZACE = 'exemplar.OpravduSmazatJeVArchivuDodavanychCisel'"));
        add(2332, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vous avez déjà  envoyé une demande pour cet exemplaire' WHERE ID_LOKALIZACE = 'loan.NaTentoExemplarJsteOdeslalPozadavek'"));
        add(2333, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Emprunts' WHERE ID_LOKALIZACE = 'commons.vypujcky'"));
        add(2334, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Rangé par titre' WHERE ID_LOKALIZACE = 'vysledky.razenoPodleNazvu'"));
        add(2335, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réservation des documents a été supprimée' WHERE ID_LOKALIZACE = 'konto.rezervaceZrusena'"));
        add(2336, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Articles' WHERE ID_LOKALIZACE = 'exemplar.clanky'"));
        add(2337, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ind' WHERE ID_LOKALIZACE = 'commons.ind'"));
        add(2338, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Évaluation' WHERE ID_LOKALIZACE = 'detail.hodnoceni'"));
        add(2339, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Erreur au serveur' WHERE ID_LOKALIZACE = 'commons.NastalaChybaNaServeru'"));
        add(2340, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Sauvegarder le commentaire' WHERE ID_LOKALIZACE = 'detail.ulozitKomentar'"));
        add(2341, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date' WHERE ID_LOKALIZACE = 'commons.datum'"));
        add(2342, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Bâtiment' WHERE ID_LOKALIZACE = 'exemplar.building'"));
        add(2343, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fermez cette session' WHERE ID_LOKALIZACE = 'login.odhlasit'"));
        add(2344, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Clé' WHERE ID_LOKALIZACE = 'commons.klic'"));
        add(2345, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Critères' WHERE ID_LOKALIZACE = 'hledani.kriteria'"));
        add(2346, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Emprunté à {0}' WHERE ID_LOKALIZACE = 'commons.vypujcenyDoX'"));
        add(2347, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Détail' WHERE ID_LOKALIZACE = 'detail.detail'"));
        add(2348, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lettres initiales ou barre code du lecteur' WHERE ID_LOKALIZACE = 'ctenar.PocatecniPismenaCtenare'"));
        add(2349, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'E-mail incorrect' WHERE ID_LOKALIZACE = 'commons.ChybneZadanyEmail'"));
        add(2350, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = '{0}. en ordre' WHERE ID_LOKALIZACE = 'loan.XOnWaitingList'"));
        add(2351, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statut d´exemplaire ne permet pas de réservation' WHERE ID_LOKALIZACE = 'loan.StatusExemplareNeumoznujeRezervaci'"));
        add(2352, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pour réserver enregistrez voud' WHERE ID_LOKALIZACE = 'detail.proRezervaciSePrihlaste'"));
        add(2353, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Intervalle des numéros' WHERE ID_LOKALIZACE = 'commons.rozmeziCisel'"));
        add(2354, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche avancée' WHERE ID_LOKALIZACE = 'hledani.RozsireneVyhledavani'"));
        add(2355, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom du lecteur' WHERE ID_LOKALIZACE = 'registrace.prijmeniInputLabel'"));
        add(2356, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prolonger' WHERE ID_LOKALIZACE = 'konto.prodlouzit'"));
        add(2357, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Compte' WHERE ID_LOKALIZACE = 'statistiky.KONTO'"));
        add(2358, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Classe thématique' WHERE ID_LOKALIZACE = 'exemplar.thematicGroup'"));
        add(2359, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Remplissez tous les champs obligatoires!' WHERE ID_LOKALIZACE = 'commons.SomeFieldsNotFilled'"));
        add(2360, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Précédent' WHERE ID_LOKALIZACE = 'commons.Predchozi'"));
        add(2361, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Commander' WHERE ID_LOKALIZACE = 'commons.objednat'"));
        add(2362, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statistiques totales' WHERE ID_LOKALIZACE = 'statistiky.celkoveStatistiky'"));
        add(2363, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Sauvegarder' WHERE ID_LOKALIZACE = 'commons.Ulozit'"));
        add(2364, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Plus' WHERE ID_LOKALIZACE = 'commons.more'"));
        add(2365, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Auteur' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_AUTOR'"));
        add(2366, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mémoire Max.' WHERE ID_LOKALIZACE = 'util.maxPamet'"));
        add(2367, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Descripteur formel' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_DESKRIPTOR'"));
        add(2368, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement publique' WHERE ID_LOKALIZACE = 'login.prihlaseniVnitrnimSystemem'"));
        add(2369, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document est accessible, vous pouvez le retirer dans le bâtiment' WHERE ID_LOKALIZACE = 'loan.DokumentJeKVyzvednutiNaBudove'"));
        add(2370, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Question de recherche invalide' WHERE ID_LOKALIZACE = 'hledani.ChybneVyhledavaciQuery'"));
        add(2371, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Continuer' WHERE ID_LOKALIZACE = 'commons.continueButton'"));
        add(2372, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Je veux attendre au maximum  à' WHERE ID_LOKALIZACE = 'loan.IWantWaitUpTo'"));
        add(2373, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ajouter votre commentaire' WHERE ID_LOKALIZACE = 'detail.pridatVlastniKomentar'"));
        add(2374, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mensuel' WHERE ID_LOKALIZACE = 'commons.mesicni'"));
        add(2375, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document est réservé' WHERE ID_LOKALIZACE = 'detail.dokumentJeZarezervovan'"));
        add(2376, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Possibilité d´une réservation éventuelle' WHERE ID_LOKALIZACE = 'mvs.lzePripadneRezervovat'"));
        add(2377, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prix' WHERE ID_LOKALIZACE = 'commons.cena'"));
        add(2378, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement a été réussi' WHERE ID_LOKALIZACE = 'registrace.registraceProbehlaUspesne'"));
        add(2379, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de documents dans la bibliothèque' WHERE ID_LOKALIZACE = 'statistiky.pocetDokumentuVKnihovne'"));
        add(2380, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réservation' WHERE ID_LOKALIZACE = 'commons.rezervaces'"));
        add(2381, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche simple' WHERE ID_LOKALIZACE = 'statistiky.JEDNODUCHE_HLEDANI'"));
        add(2382, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre d´exemplaires' WHERE ID_LOKALIZACE = 'statistiky.pocetExemplaru'"));
        add(2383, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'MARC' WHERE ID_LOKALIZACE = 'detail.marc'"));
        add(2384, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Éditer' WHERE ID_LOKALIZACE = 'commons.edit'"));
        add(2385, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Désolés, la page demandée n´a pas été trouvée' WHERE ID_LOKALIZACE = 'commons.PozadovanaStrankaNebylaNalezena'"));
        add(2386, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accès interdit à cette page' WHERE ID_LOKALIZACE = 'commons.NaTutoStrankuNematePovolenPristup'"));
        add(2387, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document est accessible dans les bâtiments suivants. Choisissez le bâtiment qui vous convient pour le pret' WHERE ID_LOKALIZACE = 'loan.DokumentJeKObjednaniNaBudovach'"));
        add(2388, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveautés de' WHERE ID_LOKALIZACE = 'seznam.novinkyOd'"));
        add(2389, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Imprimer' WHERE ID_LOKALIZACE = 'commons.tisk'"));
        add(2390, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Éditer' WHERE ID_LOKALIZACE = 'commons.editovat'"));
        add(2391, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Compte' WHERE ID_LOKALIZACE = 'konto.konto'"));
        add(2392, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Compte de lecteur' WHERE ID_LOKALIZACE = 'konto.mojeKonto'"));
        add(2393, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Entrer par choix' WHERE ID_LOKALIZACE = 'hledani.zadavatVyberem'"));
        add(2394, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notice trouvée' WHERE ID_LOKALIZACE = 'vysledky.nalezenyZaznam'"));
        add(2395, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'De' WHERE ID_LOKALIZACE = 'seznam.od'"));
        add(2396, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statistiques du fond' WHERE ID_LOKALIZACE = 'statistiky.statistikyFondu'"));
        add(2397, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vraiment effacer? L´exemplaire reste aux archives du fond d´échanges' WHERE ID_LOKALIZACE = 'exemplar.oOravduSmazatJeVArchivuVymFondu'"));
        add(2398, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de lettres de rappel' WHERE ID_LOKALIZACE = 'konto.pocetUpominek'"));
        add(2399, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de publication' WHERE ID_LOKALIZACE = 'hledani.rokVydani'"));
        add(2400, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Indexes' WHERE ID_LOKALIZACE = 'rejstrik.Rejstriky'"));
        add(2401, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucune réservation dnas cette localisation' WHERE ID_LOKALIZACE = 'loan.VTetoLokaciNelzeRezervovat'"));
        add(2402, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ranger les résultats selon' WHERE ID_LOKALIZACE = 'hledani.seraditVysledkyPodle'"));
        add(2403, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de commande' WHERE ID_LOKALIZACE = 'konto.datumObjednani'"));
        add(2404, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document n´est pas encore évalué' WHERE ID_LOKALIZACE = 'detail.dokumentZatimNebylOhodnocen'"));
        add(2405, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucun pret selon la question formulée' WHERE ID_LOKALIZACE = 'loan.VypujckaNenalezena'"));
        add(2406, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mots clés' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_PREDMET'"));
        add(2407, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Additionner un exemplaire' WHERE ID_LOKALIZACE = 'commons.pridatExemplar'"));
        add(2408, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'PEB pas encore réglé, annuler?' WHERE ID_LOKALIZACE = 'record.deletion.JeVNevyrizeneMvsZrusit'"));
        add(2409, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de retour' WHERE ID_LOKALIZACE = 'konto.datumVraceni'"));
        add(2410, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Bâtiments dans lesquels se trouvent des exemplaires' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_BUDOVA'"));
        add(2411, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement' WHERE ID_LOKALIZACE = 'statistiky.REGISTRACE'"));
        add(2412, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche avancée' WHERE ID_LOKALIZACE = 'statistiky.KOMPLEXNI_HLEDANI'"));
        add(2413, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Avancée' WHERE ID_LOKALIZACE = 'statistiky.KOMPLEXNI'"));
        add(2414, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement par Openld' WHERE ID_LOKALIZACE = 'login.prihlaseniPomociOpenId'"));
        add(2415, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Faites votre enregistrement' WHERE ID_LOKALIZACE = 'login.loginButton'"));
        add(2416, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statistiques graphiques ne sont pas supportées dans votre navigateur. Pour les afficher installez un navigateur plus moderne' WHERE ID_LOKALIZACE = 'statistiky.funkceNeniProhlízecemPodporovana'"));
        add(2417, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Description' WHERE ID_LOKALIZACE = 'commons.popis'"));
        add(2418, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retirer' WHERE ID_LOKALIZACE = 'commons.odebrat'"));
        add(2419, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucun résultat a votre question de recherche. La recherche a été élargie aux mots similaires' WHERE ID_LOKALIZACE = 'hledani.prubeh.podobnostSlov'"));
        add(2420, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucuns exemplaires' WHERE ID_LOKALIZACE = 'loan.DokumentNemaExemplare'"));
        add(2421, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de la validité d´enregistrement' WHERE ID_LOKALIZACE = 'konto.datumVyprseniPlatnostiRegistrace'"));
        add(2422, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Procedé d´acquisition de ce document n´autorise pas sa réservation' WHERE ID_LOKALIZACE = 'loan.DokumentSTimtoZpNabNelzeRezervovat'"));
        add(2423, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pourcentages de recherches' WHERE ID_LOKALIZACE = 'statistiky.podilyHledani'"));
        add(2424, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Cartes de bibliothèques' WHERE ID_LOKALIZACE = 'commons.mapyKnihoven'"));
        add(2425, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Étagere a livres' WHERE ID_LOKALIZACE = 'statistiky.KNIHOVNICKA'"));
        add(2426, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret a été récemment prolongé' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitJizNedavnoProdlouzeno'"));
        add(2427, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Possibilité de prêt dans la salle d´étude' WHERE ID_LOKALIZACE = 'mvs.lzeVypujcitPrezencne'"));
        add(2428, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Contenu du champ' WHERE ID_LOKALIZACE = 'detail.obsahPole'"));
        add(2429, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Visité' WHERE ID_LOKALIZACE = 'commons.navstivene'"));
        add(2430, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur avec ce numéro et nom n´existe pas' WHERE ID_LOKALIZACE = 'ctenar.ReaderWithThisCisLegOrBarCodAndLastNameDoesNotExist'"));
        add(2431, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret rendu' WHERE ID_LOKALIZACE = 'loan.ReturnedLoan'"));
        add(2432, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Choisissez l´index' WHERE ID_LOKALIZACE = 'rejstrik.vyberteRejstrik'"));
        add(2433, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre d´exemplaires dans la bibliothèque' WHERE ID_LOKALIZACE = 'statistiky.pocetExemplaruVKnihovne'"));
        add(2434, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Garbage collector' WHERE ID_LOKALIZACE = 'util.garbageCollector'"));
        add(2435, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Choix de la collection' WHERE ID_LOKALIZACE = 'editace.vyberFond'"));
        add(2436, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Répéter le sous-champ' WHERE ID_LOKALIZACE = 'editace.opakovatPodpole'"));
        add(2437, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucunes notices trouvées' WHERE ID_LOKALIZACE = 'vysledky.nebylyNalezenyZadneZaznamy'"));
        add(2438, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucune réservation dans ce groupe thématique' WHERE ID_LOKALIZACE = 'loan.TutoTemSkupNelzeRezervovat'"));
        add(2439, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Juillet' WHERE ID_LOKALIZACE = 'commons.mesic.7'"));
        add(2440, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Août' WHERE ID_LOKALIZACE = 'commons.mesic.8'"));
        add(2441, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Septembre' WHERE ID_LOKALIZACE = 'commons.mesic.9'"));
        add(2442, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessible' WHERE ID_LOKALIZACE = 'commons.volny'"));
        add(2443, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Dans les bâtiments suivants le document est preté, choisissez les bâtiments dans lesquels vous voulez réserver ce document' WHERE ID_LOKALIZACE = 'loan.DokumentJeKZarezervovaniNaBudovach'"));
        add(2444, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Index des titres' WHERE ID_LOKALIZACE = 'rejstrik.RejstrikNazvuDokumentu'"));
        add(2445, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document est accessible, vous pouvez le réserver dans le bâtiment' WHERE ID_LOKALIZACE = 'loan.DokumentJeKObjednaniNaBudove'"));
        add(2446, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Janvier' WHERE ID_LOKALIZACE = 'commons.mesic.1'"));
        add(2447, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Février' WHERE ID_LOKALIZACE = 'commons.mesic.2'"));
        add(2448, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Connectés' WHERE ID_LOKALIZACE = 'statistiky.zTohoPrihlasenych'"));
        add(2449, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mars' WHERE ID_LOKALIZACE = 'commons.mesic.3'"));
        add(2450, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Maintenant vous pouvez écrire votre question de recherche. Pour supprimer cliquez le bouton \"Entrer par choix\"' WHERE ID_LOKALIZACE = 'hledani.nyniMuzetePsatRucne'"));
        add(2451, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Avril' WHERE ID_LOKALIZACE = 'commons.mesic.4'"));
        add(2452, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mai' WHERE ID_LOKALIZACE = 'commons.mesic.5'"));
        add(2453, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Juin' WHERE ID_LOKALIZACE = 'commons.mesic.6'"));
        add(2454, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réglé' WHERE ID_LOKALIZACE = 'loan.ProcessedOrder'"));
        add(2455, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de processeurs' WHERE ID_LOKALIZACE = 'util.pocetProcesoru'"));
        add(2456, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mot de passe actuel' WHERE ID_LOKALIZACE = 'konto.aktualniHeslo'"));
        add(2457, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Annulé' WHERE ID_LOKALIZACE = 'commons.stornovana'"));
        add(2458, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Code-barres' WHERE ID_LOKALIZACE = 'exemplar.barCode'"));
        add(2459, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Seulement pour la salle d´étude' WHERE ID_LOKALIZACE = 'loan.PouzePrezencne'"));
        add(2460, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement' WHERE ID_LOKALIZACE = 'registrace.registrace'"));
        add(2461, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche a duré {0} ms.' WHERE ID_LOKALIZACE = 'vysledky.hledaniTrvaloXms'"));
        add(2462, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par auteur' WHERE ID_LOKALIZACE = 'hledani.autora'"));
        add(2463, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Page demandée' WHERE ID_LOKALIZACE = 'mvs.pozadovanaStranka'"));
        add(2464, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Non accessible - lecteur a été éliminé ou effacé' WHERE ID_LOKALIZACE = 'loan.CtenarJeVyrazenNeboSmazan'"));
        add(2465, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fermer' WHERE ID_LOKALIZACE = 'commons.zavrit'"));
        add(2466, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notice a été envoyé a CASLIN, a effacer?' WHERE ID_LOKALIZACE = 'record.deletion.BylOdeslanDoCaslinZrusit'"));
        add(2467, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Voir' WHERE ID_LOKALIZACE = 'commons.viz'"));
        add(2468, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Index des titres' WHERE ID_LOKALIZACE = 'statistiky.REJSTRIK_NAZVU'"));
        add(2469, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document est {0}x en réservation' WHERE ID_LOKALIZACE = 'loan.DokumentJeXxZarezervovan'"));
        add(2470, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre' WHERE ID_LOKALIZACE = 'commons.pocet'"));
        add(2471, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Erreur dans la reconstitution de la notice' WHERE ID_LOKALIZACE = 'record.ErrorWhileFromMarcXmlReconstruction'"));
        add(2472, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom d´utilisateur' WHERE ID_LOKALIZACE = 'login.usernameLabel'"));
        add(2473, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document n´est pas réservé' WHERE ID_LOKALIZACE = 'loan.DokumentNeniZarezervovan'"));
        add(2474, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Résevation des titres/exemplaires libres est interdite' WHERE ID_LOKALIZACE = 'loan.RezervaceVolnychTituluNeboExemplaruNeniPovolena'"));
        add(2475, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'et les pages suivantes {0}' WHERE ID_LOKALIZACE = 'vysledky.aDalsichXStran'"));
        add(2476, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveau nom d´utilisateur' WHERE ID_LOKALIZACE = 'registrace.usernameLabel'"));
        add(2477, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vraiment effacer? L´exemplaire reste aux archives de prets.' WHERE ID_LOKALIZACE = 'exemplar.OpravduSmazatJeVArchivuVypujcek'"));
        add(2478, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Suivant' WHERE ID_LOKALIZACE = 'commons.dalsi'"));
        add(2479, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer - il existe des exemplaires' WHERE ID_LOKALIZACE = 'record.deletion.NelzeSmazatExistujiExemplare'"));
        add(2480, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pour ajouter un commentaire enregistrez vous' WHERE ID_LOKALIZACE = 'detail.proPridaniKomentareSePrihlaste'"));
        add(2481, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Par vedette d´autorité' WHERE ID_LOKALIZACE = 'statistiky.PODLE_AUTORITY'"));
        add(2482, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´éliminer, l´exemplaire est déjà éliminé ou effacé.' WHERE ID_LOKALIZACE = 'record.discardion.NelzeVyratitUzJeVyrazenNeboSmazan'"));
        add(2483, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro de notice erronné' WHERE ID_LOKALIZACE = 'loan.ZaznamSTimtoIdNeexistuje'"));
        add(2484, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Emprunt' WHERE ID_LOKALIZACE = 'konto.vypujceno'"));
        add(2485, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Commandé' WHERE ID_LOKALIZACE = 'commons.objednana'"));
        add(2486, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Choisir parmi les notices trouvées' WHERE ID_LOKALIZACE = 'hledani.vybratZVyhledanych'"));
        add(2487, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Auteur du document' WHERE ID_LOKALIZACE = 'hledani.autorDokumentu'"));
        add(2488, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveautés dans le catalogue' WHERE ID_LOKALIZACE = 'index.novinkyVKatalogu'"));
        add(2489, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom d´utilisateur est trop court (5 signes au minimum)' WHERE ID_LOKALIZACE = 'login.UsernameTooShort'"));
        add(2490, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire avec ID saisi n´existe pas' WHERE ID_LOKALIZACE = 'loan.ExemplarSTimtoIdNeexistuje'"));
        add(2491, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notices trouvées' WHERE ID_LOKALIZACE = 'vysledky.nalezeneZaznamy'"));
        add(2492, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Auteur' WHERE ID_LOKALIZACE = 'commons.autor'"));
        add(2493, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessibles {0} de {1}' WHERE ID_LOKALIZACE = 'commons.volnychXZY'"));
        add(2494, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Terme de retour des documents empruntés' WHERE ID_LOKALIZACE = 'konto.terminVraceni'"));
        add(2495, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Maintenant vous pouvez vous enregistrer' WHERE ID_LOKALIZACE = 'registrace.nyniSeMuzetePrihlasit'"));
        add(2496, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur est déjà enregistré sous ce numéro' WHERE ID_LOKALIZACE = 'ctenar.ReaderWithThisCisLegOrBarCodAlreadyHasRegistration'"));
        add(2497, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mots de passe ne sont pas identiques' WHERE ID_LOKALIZACE = 'login.PasswordsNotEqual'"));
        add(2498, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Supprimer toutes les indications' WHERE ID_LOKALIZACE = 'hledani.zrusitVsechnaOznaceni'"));
        add(2499, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'ID de l´exemplaire inséré est invalide.' WHERE ID_LOKALIZACE = 'loan.NeplatneIdExemplare'"));
        add(2500, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom d´utilisateur ou mot de passe invalide!' WHERE ID_LOKALIZACE = 'login.BadLoginCredentials'"));
        add(2501, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Corriger la notice' WHERE ID_LOKALIZACE = 'editace.opravitZáznam'"));
        add(2502, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Tous les champs' WHERE ID_LOKALIZACE = 'hledani.vsechnaPole'"));
        add(2503, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre d´utilisateurs online' WHERE ID_LOKALIZACE = 'commons.pocetUzivateluOnline'"));
        add(2504, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Langue' WHERE ID_LOKALIZACE = 'commons.jazyk'"));
        add(2505, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessible' WHERE ID_LOKALIZACE = 'commons.volnyDokument'"));
        add(2506, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Votre compte est bloqué!' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitCtenarBlokovan'"));
        add(2507, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérez cache' WHERE ID_LOKALIZACE = 'util.nactiCache'"));
        add(2508, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Oeuvre' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_UNINAZEV'"));
        add(2509, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Citation' WHERE ID_LOKALIZACE = 'detail.citace'"));
        add(2510, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche des vedettes d´autorité' WHERE ID_LOKALIZACE = 'hledani.vyhledavaniAutort'"));
        add(2511, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mémoire  utilisée (pleine)' WHERE ID_LOKALIZACE = 'util.vyuzitaPamet'"));
        add(2512, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Seulement salle d´étude' WHERE ID_LOKALIZACE = 'commons.prezencni'"));
        add(2513, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Champ' WHERE ID_LOKALIZACE = 'commons.pole'"));
        add(2514, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Entrées et enregistrements dans le système' WHERE ID_LOKALIZACE = 'statistiky.vstupyAPrihlaseniDoSystemu'"));
        add(2515, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Changement du mot de passe' WHERE ID_LOKALIZACE = 'konto.zmenaHesla'"));
        add(2516, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucuns crédits' WHERE ID_LOKALIZACE = 'konto.zadneKredity'"));
        add(2517, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statistiques annuelles' WHERE ID_LOKALIZACE = 'statistiky.rocniStatistiky'"));
        add(2518, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Localisation' WHERE ID_LOKALIZACE = 'commons.lokace'"));
        add(2519, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche des documents avec la vedette d´autorité' WHERE ID_LOKALIZACE = 'hledani.title.hledaniAutority'"));
        add(2520, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Les plus empruntés' WHERE ID_LOKALIZACE = 'seznam.Nejpujcovanejsi'"));
        add(2521, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Utilisateur' WHERE ID_LOKALIZACE = 'exemplar.owner'"));
        add(2522, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catégorie ne permet pas de réserver' WHERE ID_LOKALIZACE = 'loan.KategorieVypujckyNeumoznujeRezervaci'"));
        add(2523, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vous ne pouvez pas preter' WHERE ID_LOKALIZACE = 'loan.NemuzetePujcovat'"));
        add(2524, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notice est un périodique d´après ID, mais ID de l´exemplaire est zéro' WHERE ID_LOKALIZACE = 'loan.IdZazJePeriodikumAleIdExJeNula'"));
        add(2525, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Manière d´acquisition' WHERE ID_LOKALIZACE = 'exemplar.acquisitionWay'"));
        add(2526, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retourné' WHERE ID_LOKALIZACE = 'commons.vracena'"));
        add(2527, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Non accepté' WHERE ID_LOKALIZACE = 'commons.neprijata'"));
        add(2528, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retorunés' WHERE ID_LOKALIZACE = 'commons.vracene'"));
        add(2529, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'N´importe quel champ' WHERE ID_LOKALIZACE = 'hledani.jakekolivPole'"));
        add(2530, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Commande a été envoyée avec succès' WHERE ID_LOKALIZACE = 'detail.pozadavekBylUspesneOdeslan'"));
        add(2531, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prix' WHERE ID_LOKALIZACE = 'exemplar.price'"));
        add(2532, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'MDT' WHERE ID_LOKALIZACE = 'commons.mdt'"));
        add(2533, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire n´existe pas dans le bâtiment validé' WHERE ID_LOKALIZACE = 'loan.ExemplarNeniNaValidniBudove'"));
        add(2534, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre d´évaluations' WHERE ID_LOKALIZACE = 'detail.pocetHodnoceni'"));
        add(2535, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vraiment effacer? La notice comprend des volumes' WHERE ID_LOKALIZACE = 'record.deletion.OpravduSmazatZaznamMaRocniky'"));
        add(2536, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réservation' WHERE ID_LOKALIZACE = 'commons.rezervace'"));
        add(2537, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Système a été développé par' WHERE ID_LOKALIZACE = 'commons.systemBylVyvinutFirmou'"));
        add(2538, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret impossible, cet exemplaire est déjà preté/reservé' WHERE ID_LOKALIZACE = 'loan.NelzeVypujcitExemplarJeJizVypujcenNeboRezervovan'"));
        add(2539, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retour' WHERE ID_LOKALIZACE = 'commons.backButton'"));
        add(2540, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ou' WHERE ID_LOKALIZACE = 'hledani.nebo'"));
        add(2541, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherches' WHERE ID_LOKALIZACE = 'statistiky.HLEDANE_DOTAZY'"));
        add(2542, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessibilité' WHERE ID_LOKALIZACE = 'commons.dostupnost'"));
        add(2543, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Non réglé' WHERE ID_LOKALIZACE = 'loan.UnprocessedOrder'"));
        add(2544, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Indicateurs' WHERE ID_LOKALIZACE = 'commons.indikatory'"));
        add(2545, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Histoire de recherche' WHERE ID_LOKALIZACE = 'hledaneDotazy.historieHledani'"));
        add(2546, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´annuler PEB' WHERE ID_LOKALIZACE = 'loan.NelzeRusitMvs'"));
        add(2547, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer - vous n´avez pas d´autorisation suffisante' WHERE ID_LOKALIZACE = 'record.deletion.NelzeSmazatNedostatecnaPrava'"));
        add(2548, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer - cette notice comprend des champs protégés' WHERE ID_LOKALIZACE = 'record.deletion.NelzeSmazatZaznamObsahujeChranenaPole'"));
        add(2549, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Annuler' WHERE ID_LOKALIZACE = 'commons.zrusit'"));
        add(2550, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date d´emprunt du document' WHERE ID_LOKALIZACE = 'konto.datumVypujceni'"));
        add(2551, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'ISBN' WHERE ID_LOKALIZACE = 'commons.isbn'"));
        add(2552, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Contenu du document' WHERE ID_LOKALIZACE = 'hledani.obsahDokumentu'"));
        add(2553, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'BC' WHERE ID_LOKALIZACE = 'commons.barCod.zkratka'"));
        add(2554, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérez le texte du commentaire!' WHERE ID_LOKALIZACE = 'detail.NezadanTextKomentare'"));
        add(2555, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Documents' WHERE ID_LOKALIZACE = 'commons.dokumenty'"));
        add(2556, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mot de passe est trop court (min. 5 lettres)' WHERE ID_LOKALIZACE = 'login.PasswordTooShort'"));
        add(2557, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Quelques fonctions ne marchent pas bien. Pour l´affichage correct on recommande d´utiliser un de ces navigateurs' WHERE ID_LOKALIZACE = 'commons.vyuzijteNekteryZTechtoProhlizecu'"));
        add(2558, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aide' WHERE ID_LOKALIZACE = 'commons.napoveda'"));
        add(2559, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrer' WHERE ID_LOKALIZACE = 'registrace.registrovatButton'"));
        add(2560, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Créer une nouvelle notice' WHERE ID_LOKALIZACE = 'editace.vytvoritNovyZaznam'"));
        add(2561, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de retour du document' WHERE ID_LOKALIZACE = 'mvs.datumOdeslaniZpet'"));
        add(2562, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucun exemplaire trouvé' WHERE ID_LOKALIZACE = 'hledani.nenalezenZadnyExemplar'"));
        add(2563, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Livraison de document de l´étranger' WHERE ID_LOKALIZACE = 'mvs.lzeDorucitIZeZahranici'"));
        add(2564, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Emprunt a été prolongé à {0} jours' WHERE ID_LOKALIZACE = 'konto.vypujckaBylaProdlouzenaOXDni'"));
        add(2565, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche simple' WHERE ID_LOKALIZACE = 'hledani.JednoducheVyhledavani'"));
        add(2566, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catégorie' WHERE ID_LOKALIZACE = 'exemplar.loanCategory'"));
        add(2567, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer - liaison non vide' WHERE ID_LOKALIZACE = 'exemplar.NelzeSmazatNeprazdnaVazba'"));
        add(2568, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret impossible' WHERE ID_LOKALIZACE = 'loan.NelzeVypujcit'"));
        add(2569, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document sans exemplaires' WHERE ID_LOKALIZACE = 'detail.ZadneExemplareKDispozici'"));
        add(2570, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Rangé par importance' WHERE ID_LOKALIZACE = 'vysledky.razenoPodleRelevance'"));
        add(2571, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mémoire attribuée' WHERE ID_LOKALIZACE = 'util.alokovanaPamet'"));
        add(2572, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Voir aussi' WHERE ID_LOKALIZACE = 'commons.vizTez'"));
        add(2573, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Documents' WHERE ID_LOKALIZACE = 'commons.dokumentu'"));
        add(2574, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer - pas de statut 0' WHERE ID_LOKALIZACE = 'exemplar.NelzeSmazatStatusNeniNula'"));
        add(2575, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessible dans les bâtiments' WHERE ID_LOKALIZACE = 'loan.VolnyNaBudovach'"));
        add(2576, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´éliminer, il y a des exemplaires' WHERE ID_LOKALIZACE = 'record.discardion.NelzeVyraditExistujiNevyrazeneExemplare'"));
        add(2577, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de publication' WHERE ID_LOKALIZACE = 'hledani.rokuVydani'"));
        add(2578, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer  - fond d´échanges' WHERE ID_LOKALIZACE = 'exemplar.NelzeSmazatVymenneFondy'"));
        add(2579, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Langue' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_JAZYK'"));
        add(2580, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Effacer log' WHERE ID_LOKALIZACE = 'util.smazLog'"));
        add(2581, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Cote de rangement' WHERE ID_LOKALIZACE = 'exemplar.signature'"));
        add(2582, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vraiment effacer? Les commandes sont actives.' WHERE ID_LOKALIZACE = 'record.deletion.OpravduSmazatAktivniObjednavky'"));
        add(2583, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Erreur' WHERE ID_LOKALIZACE = 'commons.chyba'"));
        add(2584, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Articles' WHERE ID_LOKALIZACE = 'commons.clanky'"));
        add(2585, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Titre de document' WHERE ID_LOKALIZACE = 'commons.nazevDokumentu'"));
        add(2586, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'À l´intérieur de la bibliothèque' WHERE ID_LOKALIZACE = 'statistiky.UVNITR_KNIHOVNY'"));
        add(2587, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Partager' WHERE ID_LOKALIZACE = 'detail.sdilet'"));
        add(2588, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer - déjà éffacé' WHERE ID_LOKALIZACE = 'record.deletion.NelzeSmazatJizSmazano'"));
        add(2589, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retour' WHERE ID_LOKALIZACE = 'commons.zpet'"));
        add(2590, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mes favoris' WHERE ID_LOKALIZACE = 'oblibene.Oblibene'"));
        add(2591, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = '1 notice dans les préférés' WHERE ID_LOKALIZACE = 'oblibene.1ZaznamVOblibenych'"));
        add(2592, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = '{0}  notices dans mes préférés' WHERE ID_LOKALIZACE = 'oblibene.xZaznamyVOblibenych'"));
        add(2593, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = '{0} notices dans mes préférés' WHERE ID_LOKALIZACE = 'oblibene.xZaznamuVOblibenych'"));
        add(2594, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucunes notices' WHERE ID_LOKALIZACE = 'oblibene.vOblibenychNejsouZadneZaznamy'"));
        add(2595, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Effacer tout' WHERE ID_LOKALIZACE = 'oblibene.vyprazdnit'"));
        add(2596, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Afficher tout' WHERE ID_LOKALIZACE = 'commons.ZobrazitVse'"));
        add(2597, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ajouter notices recherchées' WHERE ID_LOKALIZACE = 'oblibene.pridatVseVyhledane'"));
        add(2598, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'De mes préférés' WHERE ID_LOKALIZACE = 'oblibene.zOblibenych'"));
        add(2599, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Dans les favoris' WHERE ID_LOKALIZACE = 'oblibene.doOblibenych'"));
        add(2600, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Il ne faut pas laisser la clé vide!' WHERE ID_LOKALIZACE = 'commons.KeyCannotBeEmpty'"));
        add(2601, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérer la couverture' WHERE ID_LOKALIZACE = 'detail.nahratObalku'"));
        add(2602, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de prolonger, la date du retour est dépassée' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitPrekrocenTerminVraceni'"));
        add(2603, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement par système central' WHERE ID_LOKALIZACE = 'login.prihlaseniLdapem'"));
        add(2604, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Note' WHERE ID_LOKALIZACE = 'exemplar.note'"));
        add(2605, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statut' WHERE ID_LOKALIZACE = 'exemplar.status'"));
        add(2606, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret impossible, l´exemplaire est localisé dans un bâtiment off-line' WHERE ID_LOKALIZACE = 'loan.NelzeVypujcitBudovaJeOffline'"));
        add(2607, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre maximal de prets par lecteur est dépassé, vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitPrekrocenMaxPocetVypCtenare'"));
        add(2608, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre maximal de prets par la catégorie est dépassé, vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitPrekrocenMaxPocetVypProTutoKategorii'"));
        add(2609, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document a déjà été preté par le lecteur (pour la dernière fois {0}), vraiment preter de nouveau?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitDokumentVypujcenVMinulostiNaposledyX'"));
        add(2610, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur a actuellement preté le livre, vraiment preter de nouveau?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitCtenarMaTitulAktualneVypujcen'"));
        add(2611, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur a des livres en rappel, vraiment prêter de nouveau?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitCtenarMaUpominky'"));
        add(2612, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur a des dettes, vraiment réaliser ce pret?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitCtenarMaDluhy'"));
        add(2613, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Attention - informations speciales sur la localisation du document: {0} Vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitZvlastniInfoKLokaci'"));
        add(2614, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Attention - informations speciales sur le statut document {0}. Vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitZvlastniInfoKeStatusu'"));
        add(2615, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Attention - informations speciales sur la catégorie du document: {0} Vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitZvlastniInfoKeKategorii'"));
        add(2616, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document a été catalogué par retro-catalogage, et doit etre tout d´abord catalogué. Voulez vous le preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitStatusJeZpetnaKatalogizace'"));
        add(2617, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement du lecteur n´est pas valable, vraiment prêter cet exemplaire?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitCtenarMaPropadlouRegistraci'"));
        add(2618, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'ATTENTION! Document a un supplément! Vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitDokumentMaPrilohu'"));
        add(2619, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'L´exemplaire est en réservation pas encore envoyée! Vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitNaExemplarJeNeodeslanaRezervace'"));
        add(2620, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'ATTENTION! Document appartient à un autre bâtiment! Vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitDokumentJeZJineBudovy'"));
        add(2621, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'ATTENTION! Document est d´un autre bàtiment! Vraiment retourner?' WHERE ID_LOKALIZACE = 'loan.OpravduVratitDokumentJeZJineBudovy'"));
        add(2622, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'OUI' WHERE ID_LOKALIZACE = 'commons.ANO'"));
        add(2623, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'ATTENTION! Document à une localisation differente! Vraiment retourner?' WHERE ID_LOKALIZACE = 'loan.OpravduVratitDokumentJeZJineLokace'"));
        add(2624, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'NON' WHERE ID_LOKALIZACE = 'commons.NE'"));
        add(2625, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document a un supplément! Vraiment retourner?' WHERE ID_LOKALIZACE = 'loan.OpravduVratitDokumentMaPrilohu'"));
        add(2626, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur a recu un rappel, vraiment retourner?' WHERE ID_LOKALIZACE = 'loan.OpravduVratitCtenarMaUpominky'"));
        add(2627, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Attention - informations spéciales à localisation du document:  {0}  Vraiment retourner?' WHERE ID_LOKALIZACE = 'loan.OpravduVratitZvlastniInfoKLokaci'"));
        add(2628, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Attention - information spéciale sur le statut du document: {0} Vraiment retourner?' WHERE ID_LOKALIZACE = 'loan.OpravduVratitZvlastniInfoKeStatusu'"));
        add(2629, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Attention - information spéciale sur la catégorie du document:{0} Vraiment retourner?' WHERE ID_LOKALIZACE = 'loan.OpravduVratitZvlastniInfoKeKategorii'"));
        add(2630, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document catalogué par retro-catalogage, il faut d´abord le cataloguer, voulez vous le retourner?' WHERE ID_LOKALIZACE = 'loan.OpravduVratitStatusJeZpetnaKatalogizace'"));
        add(2631, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réservation de cet exemplaire n´a pas été envoyée! Vraiment retourner??' WHERE ID_LOKALIZACE = 'loan.OpravduVratitNaExemplarJeNeodeslanaRezervace'"));
        add(2632, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'ATTENTION! Document a une autre localisation! Vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitDokumentJeZJineLokace'"));
        add(2633, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Edition du design' WHERE ID_LOKALIZACE = 'commons.CustomSoubory'"));
        add(2634, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pour cette collection la liste de champs affichés n´est pas créée. Vous êtes obligé de le créer dans \"ini\"' WHERE ID_LOKALIZACE = 'record.ProTentoFondNeniNastavenFiltrPoli'"));
        add(2635, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Affichage des lecteurs dans tous les bâtiments' WHERE ID_LOKALIZACE = 'ctenar.HledatNaVsechBudovach'"));
        add(2636, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statistiques d´emploi du module Z-client' WHERE ID_LOKALIZACE = 'statistiky.statistikyPouzivaniZKlienta'"));
        add(2637, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mois' WHERE ID_LOKALIZACE = 'commons.Mesic'"));
        add(2638, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Serveur DB' WHERE ID_LOKALIZACE = 'statistiky.zServerConnectionString'"));
        add(2639, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de recherches' WHERE ID_LOKALIZACE = 'statistiky.pocetHledani'"));
        add(2640, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de recherches réussies' WHERE ID_LOKALIZACE = 'statistiky.pocetUspesnychHledani'"));
        add(2641, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de notices utilisées' WHERE ID_LOKALIZACE = 'statistiky.pocetPouzitychZaznamu'"));
        add(2642, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro de carte de lecteur' WHERE ID_LOKALIZACE = 'ctenar.cardNumber'"));
        add(2643, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Code-barres' WHERE ID_LOKALIZACE = 'ctenar.barCode'"));
        add(2644, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prénom' WHERE ID_LOKALIZACE = 'ctenar.jmeno'"));
        add(2645, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom' WHERE ID_LOKALIZACE = 'ctenar.prijmeni'"));
        add(2646, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'E-mail' WHERE ID_LOKALIZACE = 'ctenar.email'"));
        add(2647, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Titre' WHERE ID_LOKALIZACE = 'ctenar.titul'"));
        add(2648, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date d´enregistrement' WHERE ID_LOKALIZACE = 'ctenar.registrationDate'"));
        add(2649, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date d´expiration de l´enregistrement' WHERE ID_LOKALIZACE = 'ctenar.expirationDate'"));
        add(2650, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de naissance' WHERE ID_LOKALIZACE = 'ctenar.birthDate'"));
        add(2651, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catégorie de lecteur' WHERE ID_LOKALIZACE = 'ctenar.readerCategory'"));
        add(2652, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Type de blocage' WHERE ID_LOKALIZACE = 'ctenar.blockingType'"));
        add(2653, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Adresse permanente - ville' WHERE ID_LOKALIZACE = 'ctenar.permanentCity'"));
        add(2654, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Adresse permanente - rue' WHERE ID_LOKALIZACE = 'ctenar.permanentStreet'"));
        add(2655, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Adresse permanente - code postal' WHERE ID_LOKALIZACE = 'ctenar.permanentPostalCode'"));
        add(2656, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Adresse temporaire - ville' WHERE ID_LOKALIZACE = 'ctenar.temporaryCity'"));
        add(2657, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Adresse temporaire - rue' WHERE ID_LOKALIZACE = 'ctenar.temporaryStreet'"));
        add(2658, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Adresse temporaire - code postal' WHERE ID_LOKALIZACE = 'ctenar.temporaryPostalCode'"));
        add(2659, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Adresse temporaire pour imprimerie' WHERE ID_LOKALIZACE = 'ctenar.zapnutaPrechodnaAdresaProTisk'"));
        add(2660, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Emploi' WHERE ID_LOKALIZACE = 'ctenar.zamestnani'"));
        add(2661, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Adresse professionnelle' WHERE ID_LOKALIZACE = 'ctenar.adresaZamestnani'"));
        add(2662, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Instruction' WHERE ID_LOKALIZACE = 'ctenar.vzdelani'"));
        add(2663, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Classe' WHERE ID_LOKALIZACE = 'ctenar.trida'"));
        add(2664, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro de carte d´identité' WHERE ID_LOKALIZACE = 'ctenar.cisloObcanskehoPrukazu'"));
        add(2665, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Note' WHERE ID_LOKALIZACE = 'ctenar.note'"));
        add(2666, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro de SMS' WHERE ID_LOKALIZACE = 'ctenar.smsPhoneNumber'"));
        add(2667, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro de téléphone' WHERE ID_LOKALIZACE = 'ctenar.phoneNumber'"));
        add(2668, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Salle de prêt' WHERE ID_LOKALIZACE = 'ctenar.rental'"));
        add(2669, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Bacheliers' WHERE ID_LOKALIZACE = 'ctenar.bakalari'"));
        add(2670, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Message' WHERE ID_LOKALIZACE = 'ctenar.vzkaz'"));
        add(2671, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Internet bloqué' WHERE ID_LOKALIZACE = 'ctenar.blokovanInternet'"));
        add(2672, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Type d´impression par réservation' WHERE ID_LOKALIZACE = 'ctenar.reservationsPrintType'"));
        add(2673, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Type d´impression des lettres de rappel' WHERE ID_LOKALIZACE = 'ctenar.overdueNoticesPrintType'"));
        add(2674, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'GU ID' WHERE ID_LOKALIZACE = 'ctenar.guId'"));
        add(2675, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'NET ID' WHERE ID_LOKALIZACE = 'ctenar.netId'"));
        add(2676, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lettres de premier rappel' WHERE ID_LOKALIZACE = 'ctenar.posilanyPredupominky'"));
        add(2677, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Self-check autorisé' WHERE ID_LOKALIZACE = 'ctenar.povolenSelfCheck'"));
        add(2678, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'RFID' WHERE ID_LOKALIZACE = 'ctenar.rfidUserId'"));
        add(2679, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur standard non bloqué' WHERE ID_LOKALIZACE = 'ctenar.neblokovanStdCtenar'"));
        add(2680, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur effacé' WHERE ID_LOKALIZACE = 'ctenar.smazanyCtenar'"));
        add(2681, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Bibliothèque' WHERE ID_LOKALIZACE = 'ctenar.knihovna'"));
        add(2682, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur standard bloqué' WHERE ID_LOKALIZACE = 'ctenar.blokovanStdCtenar'"));
        add(2683, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Créer un nouveau lecteur' WHERE ID_LOKALIZACE = 'ctenar.VytvoritNovehoCtenare'"));
        add(2684, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Par courriel - impression physique par imprimante' WHERE ID_LOKALIZACE = 'ctenar.printType.Postou'"));
        add(2685, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Par e-mail - en pièce jointe' WHERE ID_LOKALIZACE = 'ctenar.printType.Emailem'"));
        add(2686, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Par SMS' WHERE ID_LOKALIZACE = 'ctenar.printType.SmsZpravou'"));
        add(2687, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Cette valeur numérique est déjà occupée' WHERE ID_LOKALIZACE = 'commons.ThisValueIsAlreadyUsed'"));
        add(2688, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Demander via PEB' WHERE ID_LOKALIZACE = 'loan.VyzadatPresMvs'"));
        add(2689, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prolonger tout' WHERE ID_LOKALIZACE = 'loan.RenewAllAtOnce'"));
        add(2690, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Non prolongé' WHERE ID_LOKALIZACE = 'loan.NotRenewed'"));
        add(2691, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Liste d´acquisition' WHERE ID_LOKALIZACE = 'statistiky.PrirustkovySeznam'"));
        add(2692, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement expiré' WHERE ID_LOKALIZACE = 'ctenar.RegistrationExpired'"));
        add(2693, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mot de passe' WHERE ID_LOKALIZACE = 'ctenar.password'"));
        add(2694, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérez votre mot de passe de nouveau' WHERE ID_LOKALIZACE = 'commons.ConfirmPassword'"));
        add(2695, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire est accessible, vous pouvez le retirer dans le bâtiment' WHERE ID_LOKALIZACE = 'loan.ExemplarJeKVyzvednutiNaBudove'"));
        add(2696, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire est accessible, vous pouvez le réserver dans le bâtiment' WHERE ID_LOKALIZACE = 'loan.ExemplarJeKObjednaniNaBudove'"));
        add(2697, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro d´enregistrement' WHERE ID_LOKALIZACE = 'exemplar.issueEvidenceNumber'"));
        add(2698, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement de la bibliothèque' WHERE ID_LOKALIZACE = 'registrace.registraceKnihovny'"));
        add(2699, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Sigle de la bibliothèque' WHERE ID_LOKALIZACE = 'ctenar.sigla'"));
        add(2700, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom complet de la bibliothèque' WHERE ID_LOKALIZACE = 'ctenar.libraryFullName'"));
        add(2701, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom abrégé de la bibliothèque' WHERE ID_LOKALIZACE = 'ctenar.libraryShortName'"));
        add(2702, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Sommaires' WHERE ID_LOKALIZACE = 'statistiky.Souhrny'"));
        add(2703, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Journal de la bibliothèque publique' WHERE ID_LOKALIZACE = 'statistiky.OverallStats'"));
        add(2704, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Affichage de la couverture n´a pas été réussi' WHERE ID_LOKALIZACE = 'detail.CoverLoadProblem'"));
        add(2705, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Affichage de la couverture n´a pas été réussi - notice ne contient pas ISBN' WHERE ID_LOKALIZACE = 'detail.CoverLoadProblem.noIsbn'"));
        add(2706, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucune couverture dans aucun service' WHERE ID_LOKALIZACE = 'detail.CoverLoadProblem.noCover'"));
        add(2707, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Affichage de la couverture n´a pas été réussi - problème de connexion' WHERE ID_LOKALIZACE = 'detail.CoverLoadProblem.connectionProblem'"));
        add(2708, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Affichage de la couverture n´a pas été réussi' WHERE ID_LOKALIZACE = 'detail.CoverLoadProblem.temporaryBlocked'"));
        add(2709, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'À (inclus)' WHERE ID_LOKALIZACE = 'commons.ToInclusive'"));
        add(2710, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'De' WHERE ID_LOKALIZACE = 'commons.From'"));
        add(2711, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'À' WHERE ID_LOKALIZACE = 'commons.To'"));
        add(2712, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'De' WHERE ID_LOKALIZACE = 'commons.from'"));
        add(2713, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'À' WHERE ID_LOKALIZACE = 'commons.to'"));
        add(2714, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur a déjà réservé un autre exemplaire de ce titre! Vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitRezervaceJeNaJinyExemplar'"));
        add(2715, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ce titre que vous pretez est parmi les commandes pas encore réglées! Vraiment preter?' WHERE ID_LOKALIZACE = 'loan.OpravduVypujcitNaTitulJeNevyrizenaObjednavka'"));
        add(2716, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Il s´agit d´un PEB, vraiment rretourner?' WHERE ID_LOKALIZACE = 'loan.OpravduVratitMVS'"));
        add(2717, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de retourner - pas prete!' WHERE ID_LOKALIZACE = 'loan.NelzeVratitNevypujceno'"));
        add(2718, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de retourner, ixistence d´un pret etranger!' WHERE ID_LOKALIZACE = 'loan.NelzeVratitCiziVypujcka'"));
        add(2719, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de retourner dans un autre bâtiment!' WHERE ID_LOKALIZACE = 'loan.NelzeVratitNaCiziBudove'"));
        add(2720, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de retourner dans une autre salle de prêt' WHERE ID_LOKALIZACE = 'loan.NelzeVratitNaCiziPujcovne'"));
        add(2721, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de retourner par self-check' WHERE ID_LOKALIZACE = 'loan.NelzeVratitRFID'"));
        add(2722, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Identificateur' WHERE ID_LOKALIZACE = 'commons.Identifikator'"));
        add(2723, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Possibilités de prets' WHERE ID_LOKALIZACE = 'loan.MoznostiVypujceni'"));
        add(2724, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Preter autre document' WHERE ID_LOKALIZACE = 'loan.VypujcitDalsi'"));
        add(2725, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fond des documents' WHERE ID_LOKALIZACE = 'commons.FondyDokumentu'"));
        add(2726, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fond de vedettes d´autorité' WHERE ID_LOKALIZACE = 'commons.FondyAutorit'"));
        add(2727, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Salles de prêt' WHERE ID_LOKALIZACE = 'commons.Rentals'"));
        add(2728, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Localisation' WHERE ID_LOKALIZACE = 'commons.Locations'"));
        add(2729, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Périodicité' WHERE ID_LOKALIZACE = 'sdi.Periodicity'"));
        add(2730, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Titre est reservé, prolonger vraiment?' WHERE ID_LOKALIZACE = 'loan.OpravduProdlouzitTitulJeZarezervovan'"));
        add(2731, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Demandes PEB accessible sur {0}' WHERE ID_LOKALIZACE = 'konto.MvsZadankyNaX'"));
        add(2732, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prolonger de ...jours' WHERE ID_LOKALIZACE = 'loan.ProdlouzitOXDni'"));
        add(2733, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de création' WHERE ID_LOKALIZACE = 'commons.DatumVytvoreni'"));
        add(2734, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Possibilité de pret de livre electronique' WHERE ID_LOKALIZACE = 'loan.MoznostiVypujceniNeboEBook'"));
        add(2735, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document est réservé dans le bâtiment' WHERE ID_LOKALIZACE = 'loan.DokumentJeKZarezervovaniNaBudove'"));
        add(2736, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement d´utilisateur' WHERE ID_LOKALIZACE = 'konto.RegistrationAgreement'"));
        add(2737, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Confirmation de paiement' WHERE ID_LOKALIZACE = 'konto.ReceiptOfPayment'"));
        add(2738, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de' WHERE ID_LOKALIZACE = 'commons.DateFrom'"));
        add(2739, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date à' WHERE ID_LOKALIZACE = 'commons.DateTo'"));
        add(2740, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Paramètres d´impression' WHERE ID_LOKALIZACE = 'print.PrintSetting'"));
        add(2741, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Paiements' WHERE ID_LOKALIZACE = 'konto.Payments'"));
        add(2742, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Demander tout' WHERE ID_LOKALIZACE = 'loan.RequestAll'"));
        add(2743, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Type' WHERE ID_LOKALIZACE = 'exemplar.type'"));
        add(2744, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur a été effacé avec succès' WHERE ID_LOKALIZACE = 'editace.ctenarSmazan'"));
        add(2745, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer le lecteur, il a des dettes!' WHERE ID_LOKALIZACE = 'ctenar.NelzeSmazatCtenareMaDluhy'"));
        add(2746, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible d´effacer le lecteur - il a des prets' WHERE ID_LOKALIZACE = 'ctenar.NelzeSmazatCtenareMaAktivniVypujcky'"));
        add(2747, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vraiment effacer ce lecteur? Il a les dettes de taxe d´enregistrement' WHERE ID_LOKALIZACE = 'ctenar.OpravduSmazatCtenareMaDluhNaRegPopl'"));
        add(2748, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vraiment effacer le lecteur? Il a une réservation préparée' WHERE ID_LOKALIZACE = 'ctenar.OpravduSmazatCtenareMaPripraveneRezervace'"));
        add(2749, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vraiment effacer ce lecteur? Il a une réservation' WHERE ID_LOKALIZACE = 'ctenar.OpravduSmazatCtenareMaRezervace'"));
        add(2750, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Bâtiments' WHERE ID_LOKALIZACE = 'ctenar.buildings'"));
        add(2751, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Employé de la bibliothèque' WHERE ID_LOKALIZACE = 'login.jsemZamestnanecKnihovny'"));
        add(2752, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Informations sur le document {0}' WHERE ID_LOKALIZACE = 'mail.informaceODokumentuX'"));
        add(2753, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Titre du document: {0} Sous-titre: {1} Auteur: {2} Réference à: {3}' WHERE ID_LOKALIZACE = 'mail.nazevXPodtitulYAutorZLinkR'"));
        add(2754, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notices de la liste de documents préférés' WHERE ID_LOKALIZACE = 'mail.oblibeneMailSubject'"));
        add(2755, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom d´utilisateur' WHERE ID_LOKALIZACE = 'login.ldap.usernameLabel'"));
        add(2756, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mot de passe' WHERE ID_LOKALIZACE = 'login.ldap.passwordLabel'"));
        add(2757, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérez votre login' WHERE ID_LOKALIZACE = 'login.ldap.loginButton'"));
        add(2758, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'A qui' WHERE ID_LOKALIZACE = 'mail.ReceiverEmail'"));
        add(2759, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Sujet' WHERE ID_LOKALIZACE = 'mail.Subject'"));
        add(2760, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'De qui' WHERE ID_LOKALIZACE = 'mail.YourEmail'"));
        add(2761, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Votre nom' WHERE ID_LOKALIZACE = 'mail.SenderName'"));
        add(2762, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Message' WHERE ID_LOKALIZACE = 'mail.Body'"));
        add(2763, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérez l´adresse électronique à laquelle vous voulez envoyer e-mail' WHERE ID_LOKALIZACE = 'mail.EnterAddressForSendEmail'"));
        add(2764, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'E-mail a été envoyé avec succès' WHERE ID_LOKALIZACE = 'mail.MailWasSuccessfullySent'"));
        add(2765, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Envoyer e-mail' WHERE ID_LOKALIZACE = 'mail.Send'"));
        add(2766, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Envoyer e-mail avec la commande' WHERE ID_LOKALIZACE = 'mail.OdeslatEmailSObednavkou'"));
        add(2767, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro de revue' WHERE ID_LOKALIZACE = 'exemplar.typCisla.Issue'"));
        add(2768, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Reliure' WHERE ID_LOKALIZACE = 'exemplar.typCisla.Vazba'"));
        add(2769, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro de facture' WHERE ID_LOKALIZACE = 'exemplar.invoiceNumber'"));
        add(2770, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Diminution' WHERE ID_LOKALIZACE = 'exemplar.ubytek'"));
        add(2771, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Poste facture?' WHERE ID_LOKALIZACE = 'exemplar.invoiceItem'"));
        add(2772, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pièces jointes' WHERE ID_LOKALIZACE = 'exemplar.attachments'"));
        add(2773, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ordre' WHERE ID_LOKALIZACE = 'exemplar.order'"));
        add(2774, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fournisseur' WHERE ID_LOKALIZACE = 'exemplar.supplier'"));
        add(2775, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Type de numéro' WHERE ID_LOKALIZACE = 'exemplar.typCisla'"));
        add(2776, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre de pièces' WHERE ID_LOKALIZACE = 'exemplar.quantity'"));
        add(2777, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Volume' WHERE ID_LOKALIZACE = 'exemplar.bundledVolumeNumber'"));
        add(2778, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Année' WHERE ID_LOKALIZACE = 'exemplar.bundledVolumeYear'"));
        add(2779, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Intervalle de numéros' WHERE ID_LOKALIZACE = 'exemplar.bundledVolumeIssueRange'"));
        add(2780, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Temps du dernier changement' WHERE ID_LOKALIZACE = 'exemplar.lastModificationDate'"));
        add(2781, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Compte' WHERE ID_LOKALIZACE = 'exemplar.account'"));
        add(2782, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Manière de création' WHERE ID_LOKALIZACE = 'exemplar.creationWay'"));
        add(2783, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaires' WHERE ID_LOKALIZACE = 'detail.exemplare'"));
        add(2784, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveau document' WHERE ID_LOKALIZACE = 'editace.NovyDokument'"));
        add(2785, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouvelle vedette d´autorité' WHERE ID_LOKALIZACE = 'editace.NovaAutorita'"));
        add(2786, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogage' WHERE ID_LOKALIZACE = 'editace.Katalogizace'"));
        add(2787, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Générer' WHERE ID_LOKALIZACE = 'editace.Vygenerovat'"));
        add(2788, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom d´utilisateur' WHERE ID_LOKALIZACE = 'login.loginPage.usernameLabel'"));
        add(2789, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mot de passe' WHERE ID_LOKALIZACE = 'login.loginPage.passwordLabel'"));
        add(2790, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche dans le Z-serveur' WHERE ID_LOKALIZACE = 'hledani.title.zserverX'"));
        add(2791, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche dans le Z-serveur' WHERE ID_LOKALIZACE = 'hledani.title.zserver'"));
        add(2792, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de trouver la demande de reservation non réglée selon la recherche' WHERE ID_LOKALIZACE = 'loan.NelzeDohledatNevyrizenyPozadavekNaRezervaci'"));
        add(2793, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur a été bloqué ou effacé' WHERE ID_LOKALIZACE = 'loan.CtenarBlokovanNeboSmazan'"));
        add(2794, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de réserver - libre' WHERE ID_LOKALIZACE = 'loan.NelzeRezervovatVolne'"));
        add(2795, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Livre est actuellement preté ou réservé par lecteur' WHERE ID_LOKALIZACE = 'loan.CtenarMaTitulJizVypujcenNeboRezervovan'"));
        add(2796, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Position de facettes' WHERE ID_LOKALIZACE = 'util.EditaceRezu'"));
        add(2797, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ordre' WHERE ID_LOKALIZACE = 'commons.Poradi'"));
        add(2798, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Type de définition' WHERE ID_LOKALIZACE = 'util.TypDefinice'"));
        add(2799, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Liste de champs' WHERE ID_LOKALIZACE = 'util.TypDefinice.List'"));
        add(2800, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Script (seulement pour utilisateurs experts)' WHERE ID_LOKALIZACE = 'util.TypDefinice.Script'"));
        add(2801, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Définition' WHERE ID_LOKALIZACE = 'util.Definice'"));
        add(2802, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Facette d´exemplaire' WHERE ID_LOKALIZACE = 'util.ExemplarovyTyp'"));
        add(2803, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Démarré/Connecté' WHERE ID_LOKALIZACE = 'commons.Zapnuto'"));
        add(2804, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveau' WHERE ID_LOKALIZACE = 'commons.Novy'"));
        add(2805, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Emprunteur' WHERE ID_LOKALIZACE = 'exemplar.holder'"));
        add(2806, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'D´autorité' WHERE ID_LOKALIZACE = 'statistiky.AUTORITNI'"));
        add(2807, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mes préférés' WHERE ID_LOKALIZACE = 'statistiky.OBLIBENE'"));
        add(2808, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Envoyer e-mail' WHERE ID_LOKALIZACE = 'mail.SendEmail'"));
        add(2809, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Adresse du destinataire' WHERE ID_LOKALIZACE = 'mail.TargetAddress'"));
        add(2810, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Format' WHERE ID_LOKALIZACE = 'commons.Format'"));
        add(2811, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Facultatif' WHERE ID_LOKALIZACE = 'commons.optional'"));
        add(2812, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'En rapport  avec' WHERE ID_LOKALIZACE = 'detail.Souvisejici'"));
        add(2813, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Standard' WHERE ID_LOKALIZACE = 'templates.document-mail'"));
        add(2814, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Choisir tout' WHERE ID_LOKALIZACE = 'commons.VybratVse'"));
        add(2815, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Document est pour le moment en catalogage' WHERE ID_LOKALIZACE = 'detail.DokumentJeVeZpracovani'"));
        add(2816, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogage retrospectif' WHERE ID_LOKALIZACE = 'document.status.2'"));
        add(2817, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notice standarde' WHERE ID_LOKALIZACE = 'document.status.3'"));
        add(2818, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogage fini' WHERE ID_LOKALIZACE = 'document.status.4'"));
        add(2819, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Envoyé a CASLIN' WHERE ID_LOKALIZACE = 'document.status.5'"));
        add(2820, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accepté par le système CASLIN' WHERE ID_LOKALIZACE = 'document.status.6'"));
        add(2821, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Version Portaro' WHERE ID_LOKALIZACE = 'util.VerzePortara'"));
        add(2822, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Version du serveur d´application' WHERE ID_LOKALIZACE = 'util.VerzeAplikacnihoServeru'"));
        add(2823, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Standard' WHERE ID_LOKALIZACE = 'templates.favourites-mail'"));
        add(2824, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Démarquer tout' WHERE ID_LOKALIZACE = 'commons.OdznacitVse'"));
        add(2825, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Déjà impossible de prolonger' WHERE ID_LOKALIZACE = 'loan.NelzeProlongovatNeposunulUzBySeTerminVraceni'"));
        add(2826, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessible, mais ce groupe thématique ne permet aucunes commandes' WHERE ID_LOKALIZACE = 'availability.VolnyAleTutoTemSkupNelzeObjednavat'"));
        add(2827, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessible, mais la manière d´acquisition ne permet aucunes commandes' WHERE ID_LOKALIZACE = 'availability.VolnyAleTentoZpNabNelzeObjednavat'"));
        add(2828, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessible, mais dans cette localisation il est impossible de commander' WHERE ID_LOKALIZACE = 'availability.VolnyAleVTetoLokaciNelzeObjednavat'"));
        add(2829, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mot de passe a été changé avec succès' WHERE ID_LOKALIZACE = 'ctenar.HesloByloUspesneZmeneno'"));
        add(2830, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de prolonger l´exemplaire d´une bibliothèque étrangère' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitPasivniMvs'"));
        add(2831, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Accessible, mais le statut ne permet aucunes commandes' WHERE ID_LOKALIZACE = 'availability.VolnyAleStatusNeumoznujeObjednavat'"));
        add(2832, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Je commande les documents choisis' WHERE ID_LOKALIZACE = 'mail.ObjednavamVybraneDokumenty'"));
        add(2833, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Bâtiments' WHERE ID_LOKALIZACE = 'commons.Budovy'"));
        add(2834, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Afficher les suivants' WHERE ID_LOKALIZACE = 'vysledky.NacistDalsi'"));
        add(2835, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Transactions bloquées' WHERE ID_LOKALIZACE = 'ctenar.TransactionsBlocked'"));
        add(2836, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérez votre code de validation' WHERE ID_LOKALIZACE = 'login.loginPage.enterValidationCode'"));
        add(2837, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Code de validité n´est pas valable' WHERE ID_LOKALIZACE = 'commons.ValidationCodeIsNotValid'"));
        add(2838, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Code de validation' WHERE ID_LOKALIZACE = 'login.loginPage.validationCodeLabel'"));
        add(2839, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nom d´utilisateur' WHERE ID_LOKALIZACE = 'ctenar.username'"));
        add(2840, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Changement de mot de passe d´utilisateur pour entrer au catalogue de la bibliothèque' WHERE ID_LOKALIZACE = 'login.resetPassword.subject'"));
        add(2841, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pas trouvé' WHERE ID_LOKALIZACE = 'commons.NotFound'"));
        add(2842, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérez votre adresse d´e-mail' WHERE ID_LOKALIZACE = 'mail.EnterYourEmailAddress'"));
        add(2843, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Insérez le nouveau mot de passe' WHERE ID_LOKALIZACE = 'login.resetPassword.EnterNewPassword'"));
        add(2844, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Sauvegarder et faire enregistrement' WHERE ID_LOKALIZACE = 'login.resetPassword.SaveAndLogin'"));
        add(2845, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mot de passe oublié' WHERE ID_LOKALIZACE = 'login.resetPassword.ForgottenPassword'"));
        add(2846, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statut de l´exemplaire ne permet pas d´emprunts' WHERE ID_LOKALIZACE = 'availability.StatusExemplareNeumoznujeVypujcky'"));
        add(2847, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ce mail est utilisé par plus d''utilisateurs' WHERE ID_LOKALIZACE = 'login.resetPassword.MoreThanOneReaderWithThisEmailException'"));
        add(2848, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date de finition/résiliation' WHERE ID_LOKALIZACE = 'commons.DatumUkonceni'"));
        add(2849, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Liste de livres manquants' WHERE ID_LOKALIZACE = 'statistiky.UbytkovySeznam'"));
        add(2850, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Répertoire' WHERE ID_LOKALIZACE = 'commons.Tezaurus'"));
        add(2851, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Retouner vraiment? Un rappel sera etabli, generé' WHERE ID_LOKALIZACE = 'loan.OpravduVratitBudeVygenerovanaPokutaX'"));
        add(2852, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Note' WHERE ID_LOKALIZACE = 'loan.PoznamkaKVraceniX'"));
        add(2853, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Acces Web' WHERE ID_LOKALIZACE = 'registrace.WebovyPristup'"));
        add(2854, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statut des exemplaires' WHERE ID_LOKALIZACE = 'commons.ExemplaroveStatusy'"));
        add(2855, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Choisir si vous etes enregistré mais pas de mot d´acces au catalogue' WHERE ID_LOKALIZACE = 'registrace.ZvolteProRegistraciWebovehoPristupu'"));
        add(2856, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vide' WHERE ID_LOKALIZACE = 'commons.Prazdny'"));
        add(2857, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prolonger jusqu´a' WHERE ID_LOKALIZACE = 'loan.ProdlouzenoDoXYteProdlouzeni'"));
        add(2858, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catégories de pret' WHERE ID_LOKALIZACE = 'commons.LoanCategories'"));
        add(2859, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Etat de votre compte' WHERE ID_LOKALIZACE = 'konto.StavKonta'"));
        add(2860, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire est non localisable' WHERE ID_LOKALIZACE = 'availability.ExemplarNemaNastavenouBudovu'"));
        add(2861, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire n´est pas sur la liste de batiments' WHERE ID_LOKALIZACE = 'availability.ExemplarNeniMeziPlatnymiBudovamiCtenare'"));
        add(2862, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire est désherbé' WHERE ID_LOKALIZACE = 'availability.ExemplarJeVyrazen'"));
        add(2863, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire est disloqué' WHERE ID_LOKALIZACE = 'availability.ExemplarJeDislokovany'"));
        add(2864, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Liste interne' WHERE ID_LOKALIZACE = 'statistiky.MistniSeznam'"));
        add(2865, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Seuls les étudiants et employés de l´Université technique peuvent afficher le texte intégral apres enregistrement (FP et FUA ont le texte intégral libre d´acces sur dspace.tul.cz)' WHERE ID_LOKALIZACE = 'extres.ProStazeniJeVyzadovanoPrihlaseni'"));
        add(2866, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrement du lecteur est toujours valable et ne peut etre prologé,' WHERE ID_LOKALIZACE = 'ctenar.CannotExtendRegistrationWhichIsNotExpired'"));
        add(2867, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire a été désherbé' WHERE ID_LOKALIZACE = 'exemplar.ExeplarVyrazen'"));
        add(2868, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire a été supprimé' WHERE ID_LOKALIZACE = 'exemplar.ExemplarSmazan'"));
        add(2869, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro du document manquant' WHERE ID_LOKALIZACE = 'exemplar.ubytkoveCislo'"));
        add(2870, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Raison du document manquant' WHERE ID_LOKALIZACE = 'exemplar.duvodUbytku'"));
        add(2871, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Méthode de remplacement' WHERE ID_LOKALIZACE = 'exemplar.zpusobNahrazeniUbytku'"));
        add(2872, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Prix du document manquant' WHERE ID_LOKALIZACE = 'exemplar.cenaUbytku'"));
        add(2873, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro d´ordre?' WHERE ID_LOKALIZACE = 'exemplar.cisloPoradyUbytku'"));
        add(2874, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Quotidien' WHERE ID_LOKALIZACE = 'volume.periodicita.denik'"));
        add(2875, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Hebdomadaire' WHERE ID_LOKALIZACE = 'volume.periodicita.tydenik'"));
        add(2876, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mensuel' WHERE ID_LOKALIZACE = 'volume.periodicita.mesicnik'"));
        add(2877, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Annuel' WHERE ID_LOKALIZACE = 'volume.periodicita.rocnik'"));
        add(2878, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouvelle édition' WHERE ID_LOKALIZACE = 'volume.NovyRocnik'"));
        add(2879, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Numéro de volume' WHERE ID_LOKALIZACE = 'volume.volumeNumber'"));
        add(2880, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Année' WHERE ID_LOKALIZACE = 'volume.volumeYear'"));
        add(2881, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nombre d´exrmplaires' WHERE ID_LOKALIZACE = 'volume.pocetKusu'"));
        add(2882, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Note' WHERE ID_LOKALIZACE = 'volume.poznamka'"));
        add(2883, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Périodicité' WHERE ID_LOKALIZACE = 'volume.periodicita'"));
        add(2884, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Date d´acquisition' WHERE ID_LOKALIZACE = 'exemplar.creationDate'"));
        add(2885, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Total des rappels pour les prets actuels' WHERE ID_LOKALIZACE = 'konto.SoucetPokutZaAktualniVypujcky'"));
        add(2886, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Vous avez déja preté ce livre, vraiment commander?' WHERE ID_LOKALIZACE = 'loan.OpravduVyzadatJizVMinulostiPujceno'"));
        add(2887, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Categorie ne permet pas le pret' WHERE ID_LOKALIZACE = 'loan.VypKatNepovolujePujceni'"));
        add(2888, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret impossible dans un batiment étranger' WHERE ID_LOKALIZACE = 'loan.NelzeVypujcitNaCiziBudove'"));
        add(2889, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pré-enregistrement' WHERE ID_LOKALIZACE = 'registrace.Predregistrace'"));
        add(2890, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Choisir enregistrement si vous etes nouveau' WHERE ID_LOKALIZACE = 'registrace.ZvolteProNovouRegistraci'"));
        add(2891, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'PEB Bibliotheque' WHERE ID_LOKALIZACE = 'registrace.MvsKnihovna'"));
        add(2892, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Choisir si vous etes d´une autre bibliotheque et voulez le service PEB' WHERE ID_LOKALIZACE = 'registrace.ZvolteProRegistraciMvsKnihovny'"));
        add(2893, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Champ d´etudes' WHERE ID_LOKALIZACE = 'exemplar.bindingIssueRange'"));
        add(2894, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Quotidiennement' WHERE ID_LOKALIZACE = 'sdi.periodicity.Daily'"));
        add(2895, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Par semaine' WHERE ID_LOKALIZACE = 'sdi.periodicity.Weekly'"));
        add(2896, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mensuellement' WHERE ID_LOKALIZACE = 'sdi.periodicity.Monthly'"));
        add(2897, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Affichage de nouveaux résultats envoyés' WHERE ID_LOKALIZACE = 'sdi.NastaveniOdesilani'"));
        add(2898, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Envoyer de nouveautés de la recherche' WHERE ID_LOKALIZACE = 'sdi.PosilatNovinkyZTohotoHledani'"));
        add(2899, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Sauvegardé' WHERE ID_LOKALIZACE = 'commons.Ulozeno'"));
        add(2900, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Envoi de nouveautés de la recherche' WHERE ID_LOKALIZACE = 'sdi.SdiRequests'"));
        add(2901, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Afficher les resultats' WHERE ID_LOKALIZACE = 'sdi.ZobrazitVyhledaneZaznamy'"));
        add(2902, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Déconnecté' WHERE ID_LOKALIZACE = 'commons.Vypnuto'"));
        add(2903, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Déconnecter' WHERE ID_LOKALIZACE = 'commons.Vypnout'"));
        add(2904, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Connecter/Démarrer' WHERE ID_LOKALIZACE = 'commons.Zapnout'"));
        add(2905, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Envoi' WHERE ID_LOKALIZACE = 'sdi.ZasilaniXNaY'"));
        add(2906, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Cré' WHERE ID_LOKALIZACE = 'commons.Vytvoreno'"));
        add(2907, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pou/A' WHERE ID_LOKALIZACE = 'commons.do'"));
        add(2908, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Trouvé/Résultat de la recherche' WHERE ID_LOKALIZACE = 'commons.Vyhledano'"));
        add(2909, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveautés trouvées' WHERE ID_LOKALIZACE = 'sdi.NalezenoXNovinek'"));
        add(2910, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aucuns documents' WHERE ID_LOKALIZACE = 'commons.ZadnePolozky'"));
        add(2911, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret impossible, votre email n´est pas connu' WHERE ID_LOKALIZACE = 'extZdroje.CtenarNemaNastavenyEmail'"));
        add(2912, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de prolonger, le document avec ce numéro est réservé' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitCisloJeJizRezervovano'"));
        add(2913, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de prolonger, pret extérieur' WHERE ID_LOKALIZACE = 'loan.NelzeProdlouzitExterniVypujcku'"));
        add(2914, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impossible de rendre, existence d´un PEB' WHERE ID_LOKALIZACE = 'loan.NelzeVratitExterniVypujcku'"));
        add(2915, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveautés de' WHERE ID_LOKALIZACE = 'seznam.novinkyOdXDoY'"));
        add(2916, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Preter un livre numérique' WHERE ID_LOKALIZACE = 'loan.VypujcitEBook'"));
        add(2917, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret electronique/on-line' WHERE ID_LOKALIZACE = 'loan.eVypujcka'"));
        add(2918, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Demandes PEB de {0}' WHERE ID_LOKALIZACE = 'konto.MvsZadankyZX'"));
        add(2919, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Demandeur' WHERE ID_LOKALIZACE = 'commons.Zadatel'"));
        add(2920, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Bibliotheque expéditrice' WHERE ID_LOKALIZACE = 'mvs.executor'"));
        add(2921, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Selection' WHERE ID_LOKALIZACE = 'commons.Oznaceni'"));
        add(2922, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ce service alerte permet de vous envoyer périodiquement des nouveautés a partir de votre recherche par (mots clés), <br/> par exemple en cherchant le mot \"famille\" et ayant souscrit, un email vous sera envoyé si la bibliotheque achete des nouveaux livres avec cette thématique' WHERE ID_LOKALIZACE = 'sdi.PoznamkaFormuSEditaciSDI'"));
        add(2923, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'adresse' WHERE ID_LOKALIZACE = 'commons.Adresa'"));
        add(2924, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Téléphone' WHERE ID_LOKALIZACE = 'commons.Telefon'"));
        add(2925, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Installation usager/utilisateur' WHERE ID_LOKALIZACE = 'commons.UzivatelskeNastaveni'"));
        add(2926, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Tout' WHERE ID_LOKALIZACE = 'commons.Vse'"));
        add(2927, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire est réservé' WHERE ID_LOKALIZACE = 'availability.ExemplarJeVeStavuVyrizenaObjednavka'"));
        add(2928, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exemplaire est réservé' WHERE ID_LOKALIZACE = 'availability.ExemplarJeVeStavuNeodeslanaNeboOdeslanaRezervace'"));
        add(2929, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Afficher les précédents' WHERE ID_LOKALIZACE = 'vysledky.NacistPredchozichX'"));
        add(2930, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Supprimé' WHERE ID_LOKALIZACE = 'commons.Smazano'"));
        add(2931, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ne peut etre supprimé, élément utilisé' WHERE ID_LOKALIZACE = 'commons.NelzeSmazatPolozkaJePouzivana'"));
        add(2932, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret impossible - l´enregistrement est expiré' WHERE ID_LOKALIZACE = 'loan.NelzeVypujcitProslaRegistrace'"));
        add(2933, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Bibliotheque' WHERE ID_LOKALIZACE = 'hledani.facet.REZS_DB'"));
        add(2934, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Disponibles dans les bases de données' WHERE ID_LOKALIZACE = 'centralIndex.NajdeteVDatabazich'"));
        add(2935, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Correction' WHERE ID_LOKALIZACE = 'commons.Uprava'"));
        add(2936, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Création' WHERE ID_LOKALIZACE = 'commons.Vytvoreni'"));
        add(2937, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Créer un nouveau pret entre bibliotheques' WHERE ID_LOKALIZACE = 'ctenar.VytvoritNovouKnihovnu'"));
        add(2938, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Tous les tomes' WHERE ID_LOKALIZACE = 'detail.VsechnyDily'"));
        add(2939, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Selection de ligne' WHERE ID_LOKALIZACE = 'editace.VyberRady'"));
        add(2940, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Texte intégral' WHERE ID_LOKALIZACE = 'commons.PlnyText'"));
        add(2941, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Texte intégral' WHERE ID_LOKALIZACE = 'exemplar.fulltext'"));
        add(2942, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret dans le lecteur numérique via eReading.cz' WHERE ID_LOKALIZACE = 'loan.ereading.VypujcitDoCteckyPresEreading'"));
        add(2943, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Comment preter les livres numériques via eReading.cz?' WHERE ID_LOKALIZACE = 'loan.ereading.JakPujcovat'"));
        add(2944, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Cliquez sur \"preter\" ci-dessous' WHERE ID_LOKALIZACE = 'loan.ereading.KlikneteNaVypujcit'"));
        add(2945, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Enregistrez-vous sur {0} avec mel {1}' WHERE ID_LOKALIZACE = 'loan.ereading.ZaregistrujteNaXSeSEmailemY'"));
        add(2946, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Dans votre compte, il y a des livres numériques a télécharger' WHERE ID_LOKALIZACE = 'loan.ereading.VKonteXNajdeteVypujcku'"));
        add(2947, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Plus d´infos' WHERE ID_LOKALIZACE = 'commons.ViceInformaci'"));
        add(2948, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Pret electronique est accessible ici' WHERE ID_LOKALIZACE = 'loan.ext.EVypujckaJeDostupnaProTatoZarizeni'"));
        add(2949, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'appareil avec Android' WHERE ID_LOKALIZACE = 'loan.ext.ZarizeniSAndroidem'"));
        add(2950, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'A l´aide de l´application' WHERE ID_LOKALIZACE = 'loan.ext.pomociAplikace'"));
        add(2951, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'iOS appareil' WHERE ID_LOKALIZACE = 'loan.ext.ZarizeniSIOs'"));
        add(2952, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur numérique e-ink eReading.cz START 2, 3' WHERE ID_LOKALIZACE = 'loan.ereading.ZarizeniEreading'"));
        add(2953, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Afficher le livre numérique sur eReading.cz' WHERE ID_LOKALIZACE = 'loan.ereading.ZobrazitKnihuNaEreading'"));
        add(2954, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Aujourd´hui' WHERE ID_LOKALIZACE = 'commons.Dnes'"));
        add(2955, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Hier' WHERE ID_LOKALIZACE = 'commons.Vcera'"));
        add(2956, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Bases de données' WHERE ID_LOKALIZACE = 'commons.Databaze'"));
        add(2957, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Statistiques de bases de données' WHERE ID_LOKALIZACE = 'statistiky.StatistikyDatabazi'"));
        add(2958, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Volume' WHERE ID_LOKALIZACE = 'commons.RocnikX'"));
        add(2959, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Durée de pret est de 21 jours' WHERE ID_LOKALIZACE = 'loan.ereading.DodatecneInformace'"));
        add(2960, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Tenter une nouvelle recherche de la couverture' WHERE ID_LOKALIZACE = 'detail.ZnovuvyhledatObalku'"));
        add(2961, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Supprimer la couverture' WHERE ID_LOKALIZACE = 'detail.SmazatObalku'"));
        add(2962, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Documents favoris' WHERE ID_LOKALIZACE = 'oblibene.OblibeneDokumenty'"));
        add(2963, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Link vers la notice' WHERE ID_LOKALIZACE = 'record.OdkazNaZaznam'"));
        add(2964, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Mise a jour du programme' WHERE ID_LOKALIZACE = 'AKCE.0104.SETUP'"));
        add(2965, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Actualisation/Mise a jour via Z-klient' WHERE ID_LOKALIZACE = 'AKCE.2213.SETUP'"));
        add(2966, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Actualiser la transaction' WHERE ID_LOKALIZACE = 'AKCE.0105.SETUP'"));
        add(2967, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Actualise/Mets a jour via Z-klient' WHERE ID_LOKALIZACE = 'AKCE.2213.A3_ZKlient.HINT'"));
        add(2968, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Actualise/Mets a jour via Z-klient' WHERE ID_LOKALIZACE = 'AKCE.2213.A3_ZKlient'"));
        add(2969, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Acquisition' WHERE ID_LOKALIZACE = 'AKCE.3000.SETUP'"));
        add(2970, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Citation bibliographique' WHERE ID_LOKALIZACE = 'AKCE.1231.SETUP'"));
        add(2971, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Citation bibliographique' WHERE ID_LOKALIZACE = 'AKCE.2231.SETUP'"));
        add(2972, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Citation bibliographique' WHERE ID_LOKALIZACE = 'AKCE.2231.A3_TiskBibCit'"));
        add(2973, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Citation bibliographique' WHERE ID_LOKALIZACE = 'AKCE.1231.A4_TiskBibCit'"));
        add(2974, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Periodiques livrées/souscrites' WHERE ID_LOKALIZACE = 'AKCE.1237.SETUP'"));
        add(2975, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Export' WHERE ID_LOKALIZACE = 'AKCE.1212.A4_Export'"));
        add(2976, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exportation' WHERE ID_LOKALIZACE = 'AKCE.2212.A3_Export'"));
        add(2977, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exportation' WHERE ID_LOKALIZACE = 'AKCE.2212.SETUP'"));
        add(2978, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Exportez les notices dans les fichiers - MARC, XML, ISO' WHERE ID_LOKALIZACE = 'AKCE.2212.A3_Export.HINT'"));
        add(2979, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fichiers extérieurs' WHERE ID_LOKALIZACE = 'AKCE.2206.SETUP'"));
        add(2980, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Histoire et inventaire de la frequence des prets' WHERE ID_LOKALIZACE = 'AKCE.1216.A4_HistorieVyp'"));
        add(2981, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Histoire et inventaire de la frequence des prets' WHERE ID_LOKALIZACE = 'AKCE.2216.A3_HistorieVyp.HINT'"));
        add(2982, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Histoire et inventaire de la frequence des prets' WHERE ID_LOKALIZACE = 'AKCE.1216.A4_HistorieVyp.HINT'"));
        add(2983, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Histoire et inventaire de la frequence des prets' WHERE ID_LOKALIZACE = 'AKCE.2216.A3_HistorieVyp'"));
        add(2984, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Histoire de notices du fonds d´echange' WHERE ID_LOKALIZACE = 'AKCE.1218.A4_HistorieVF'"));
        add(2985, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Rechercher' WHERE ID_LOKALIZACE = 'AKCE.2001.A3_HledaniPole'"));
        add(2986, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche' WHERE ID_LOKALIZACE = 'AKCE.2001.SETUP'"));
        add(2987, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche' WHERE ID_LOKALIZACE = 'AKCE.1001.SETUP'"));
        add(2988, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par autorité' WHERE ID_LOKALIZACE = 'AKCE.2005.A3_HledAut'"));
        add(2989, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par des données de service' WHERE ID_LOKALIZACE = 'AKCE.2215.SETUP'"));
        add(2990, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par des données de service' WHERE ID_LOKALIZACE = 'AKCE.2215.A3_ServisHled'"));
        add(2991, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par données de service-ID, capacité, statut' WHERE ID_LOKALIZACE = 'AKCE.1215.A4_ServisHled.HINT'"));
        add(2992, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par données de service-ID, capacité, statut' WHERE ID_LOKALIZACE = 'AKCE.1215.A4_ServisHled'"));
        add(2993, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par données de volumes et de numéros' WHERE ID_LOKALIZACE = 'AKCE.2006.A3_HledExemp'"));
        add(2994, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par données de volumes et de numéros' WHERE ID_LOKALIZACE = 'AKCE.2006.A3_HledExemp.HINT'"));
        add(2995, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par données de volumes et de numéros' WHERE ID_LOKALIZACE = 'AKCE.1006.A4_HledExemp.HINT'"));
        add(2996, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par données de volumes et de numéros' WHERE ID_LOKALIZACE = 'AKCE.1006.A4_HledExemp'"));
        add(2997, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche de documents par autorités' WHERE ID_LOKALIZACE = 'AKCE.2005.SETUP'"));
        add(2998, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche du document par autorité' WHERE ID_LOKALIZACE = 'AKCE.1005.SETUP'"));
        add(2999, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche de documents par données contenues dans les volumes' WHERE ID_LOKALIZACE = 'AKCE.1006.SETUP'"));
        add(3000, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche de documents par données des volumes' WHERE ID_LOKALIZACE = 'AKCE.2006.SETUP'"));
        add(3001, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche de titres par autorités utilisées' WHERE ID_LOKALIZACE = 'AKCE.1005.A4_HledAut.HINT'"));
        add(3002, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche de titres par autorités utilisées' WHERE ID_LOKALIZACE = 'AKCE.2005.A3_HledAut.HINT'"));
        add(3003, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Importation' WHERE ID_LOKALIZACE = 'AKCE.2211.SETUP'"));
        add(3004, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Importer les notices' WHERE ID_LOKALIZACE = 'AKCE.2211.A3_Import'"));
        add(3005, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Importez les notices des fichiers externes - MARC, ISO, XML' WHERE ID_LOKALIZACE = 'AKCE.2211.A3_Import.HINT'"));
        add(3006, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue' WHERE ID_LOKALIZACE = 'AKCE.1100'"));
        add(3007, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue' WHERE ID_LOKALIZACE = 'AKCE.1100.SETUP'"));
        add(3008, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 1' WHERE ID_LOKALIZACE = 'AKCE.1101'"));
        add(3009, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 1' WHERE ID_LOKALIZACE = 'AKCE.1101.SETUP'"));
        add(3010, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 10' WHERE ID_LOKALIZACE = 'AKCE.1110.SETUP'"));
        add(3011, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 10' WHERE ID_LOKALIZACE = 'AKCE.1110'"));
        add(3012, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 11' WHERE ID_LOKALIZACE = 'AKCE.1111.SETUP'"));
        add(3013, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 11' WHERE ID_LOKALIZACE = 'AKCE.1111'"));
        add(3014, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 12' WHERE ID_LOKALIZACE = 'AKCE.1112.SETUP'"));
        add(3015, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 12' WHERE ID_LOKALIZACE = 'AKCE.1112'"));
        add(3016, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 13' WHERE ID_LOKALIZACE = 'AKCE.1113.SETUP'"));
        add(3017, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 13' WHERE ID_LOKALIZACE = 'AKCE.1113'"));
        add(3018, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 14' WHERE ID_LOKALIZACE = 'AKCE.1114.SETUP'"));
        add(3019, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 14' WHERE ID_LOKALIZACE = 'AKCE.1114'"));
        add(3020, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 15' WHERE ID_LOKALIZACE = 'AKCE.1115.SETUP'"));
        add(3021, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 15' WHERE ID_LOKALIZACE = 'AKCE.1115'"));
        add(3022, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 16' WHERE ID_LOKALIZACE = 'AKCE.1116'"));
        add(3023, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 16' WHERE ID_LOKALIZACE = 'AKCE.1116.SETUP'"));
        add(3024, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 17' WHERE ID_LOKALIZACE = 'AKCE.1117'"));
        add(3025, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 17' WHERE ID_LOKALIZACE = 'AKCE.1117.SETUP'"));
        add(3026, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 18' WHERE ID_LOKALIZACE = 'AKCE.1118.SETUP'"));
        add(3027, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 18' WHERE ID_LOKALIZACE = 'AKCE.1118'"));
        add(3028, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 19' WHERE ID_LOKALIZACE = 'AKCE.1119'"));
        add(3029, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 19' WHERE ID_LOKALIZACE = 'AKCE.1119.SETUP'"));
        add(3030, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 2' WHERE ID_LOKALIZACE = 'AKCE.1102'"));
        add(3031, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 2' WHERE ID_LOKALIZACE = 'AKCE.1102.SETUP'"));
        add(3032, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 20' WHERE ID_LOKALIZACE = 'AKCE.1120.SETUP'"));
        add(3033, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 20' WHERE ID_LOKALIZACE = 'AKCE.1120'"));
        add(3034, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 3' WHERE ID_LOKALIZACE = 'AKCE.1103.SETUP'"));
        add(3035, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 3' WHERE ID_LOKALIZACE = 'AKCE.1103'"));
        add(3036, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 4' WHERE ID_LOKALIZACE = 'AKCE.1104.SETUP'"));
        add(3037, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 4' WHERE ID_LOKALIZACE = 'AKCE.1104'"));
        add(3038, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 5' WHERE ID_LOKALIZACE = 'AKCE.1105'"));
        add(3039, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 5' WHERE ID_LOKALIZACE = 'AKCE.1105.SETUP'"));
        add(3040, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 6' WHERE ID_LOKALIZACE = 'AKCE.1106.SETUP'"));
        add(3041, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 6' WHERE ID_LOKALIZACE = 'AKCE.1106'"));
        add(3042, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 7' WHERE ID_LOKALIZACE = 'AKCE.1107.SETUP'"));
        add(3043, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 7' WHERE ID_LOKALIZACE = 'AKCE.1107'"));
        add(3044, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 8' WHERE ID_LOKALIZACE = 'AKCE.1108.SETUP'"));
        add(3045, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 8' WHERE ID_LOKALIZACE = 'AKCE.1108'"));
        add(3046, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 9' WHERE ID_LOKALIZACE = 'AKCE.1109.SETUP'"));
        add(3047, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 9' WHERE ID_LOKALIZACE = 'AKCE.1109'"));
        add(3048, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Toutes les collections' WHERE ID_LOKALIZACE = 'AKCE.1199'"));
        add(3049, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Toutes les collections' WHERE ID_LOKALIZACE = 'AKCE.1199.SETUP'"));
        add(3050, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogage' WHERE ID_LOKALIZACE = 'AKCE.2000.A0_OpenKatzFond'"));
        add(3051, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 1' WHERE ID_LOKALIZACE = 'AKCE.2101'"));
        add(3052, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 1' WHERE ID_LOKALIZACE = 'AKCE.2101.SETUP'"));
        add(3053, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 10' WHERE ID_LOKALIZACE = 'AKCE.2110.SETUP'"));
        add(3054, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 10' WHERE ID_LOKALIZACE = 'AKCE.2110'"));
        add(3055, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 11' WHERE ID_LOKALIZACE = 'AKCE.2111'"));
        add(3056, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 11' WHERE ID_LOKALIZACE = 'AKCE.2111.SETUP'"));
        add(3057, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 12' WHERE ID_LOKALIZACE = 'AKCE.2112.SETUP'"));
        add(3058, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 12' WHERE ID_LOKALIZACE = 'AKCE.2112'"));
        add(3059, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 13' WHERE ID_LOKALIZACE = 'AKCE.2113'"));
        add(3060, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 13' WHERE ID_LOKALIZACE = 'AKCE.2113.SETUP'"));
        add(3061, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 14' WHERE ID_LOKALIZACE = 'AKCE.2114.SETUP'"));
        add(3062, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 14' WHERE ID_LOKALIZACE = 'AKCE.2114'"));
        add(3063, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 15' WHERE ID_LOKALIZACE = 'AKCE.2115.SETUP'"));
        add(3064, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 15' WHERE ID_LOKALIZACE = 'AKCE.2115'"));
        add(3065, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 16' WHERE ID_LOKALIZACE = 'AKCE.2116'"));
        add(3066, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 16' WHERE ID_LOKALIZACE = 'AKCE.2116.SETUP'"));
        add(3067, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 17' WHERE ID_LOKALIZACE = 'AKCE.2117.SETUP'"));
        add(3068, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 17' WHERE ID_LOKALIZACE = 'AKCE.2117'"));
        add(3069, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 18' WHERE ID_LOKALIZACE = 'AKCE.2118'"));
        add(3070, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 18' WHERE ID_LOKALIZACE = 'AKCE.2118.SETUP'"));
        add(3071, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 19' WHERE ID_LOKALIZACE = 'AKCE.2119'"));
        add(3072, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 19' WHERE ID_LOKALIZACE = 'AKCE.2119.SETUP'"));
        add(3073, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 2' WHERE ID_LOKALIZACE = 'AKCE.2102'"));
        add(3074, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 2' WHERE ID_LOKALIZACE = 'AKCE.2102.SETUP'"));
        add(3075, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 20' WHERE ID_LOKALIZACE = 'AKCE.2120'"));
        add(3076, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 20' WHERE ID_LOKALIZACE = 'AKCE.2120.SETUP'"));
        add(3077, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 3' WHERE ID_LOKALIZACE = 'AKCE.2103'"));
        add(3078, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 4' WHERE ID_LOKALIZACE = 'AKCE.2104'"));
        add(3079, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 4' WHERE ID_LOKALIZACE = 'AKCE.2104.SETUP'"));
        add(3080, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 5' WHERE ID_LOKALIZACE = 'AKCE.2105'"));
        add(3081, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 5' WHERE ID_LOKALIZACE = 'AKCE.2105.SETUP'"));
        add(3082, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 6' WHERE ID_LOKALIZACE = 'AKCE.2106.SETUP'"));
        add(3083, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 6' WHERE ID_LOKALIZACE = 'AKCE.2106'"));
        add(3084, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 7' WHERE ID_LOKALIZACE = 'AKCE.2107'"));
        add(3085, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 7' WHERE ID_LOKALIZACE = 'AKCE.2107.SETUP'"));
        add(3086, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 8' WHERE ID_LOKALIZACE = 'AKCE.2108.SETUP'"));
        add(3087, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 8' WHERE ID_LOKALIZACE = 'AKCE.2108'"));
        add(3088, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 9' WHERE ID_LOKALIZACE = 'AKCE.2109'"));
        add(3089, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Catalogue - Fond n. 9' WHERE ID_LOKALIZACE = 'AKCE.2109.SETUP'"));
        add(3090, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notices cataloguées' WHERE ID_LOKALIZACE = 'AKCE.1236.SETUP'"));
        add(3091, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Copie de la notice' WHERE ID_LOKALIZACE = 'AKCE.2203.SETUP'"));
        add(3092, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Copier' WHERE ID_LOKALIZACE = 'AKCE.2203.A3_KatzKopie'"));
        add(3093, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Copier la notice' WHERE ID_LOKALIZACE = 'AKCE.2203.A3_KatzKopie.HINT'"));
        add(3094, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Menu \"Catalogue\"' WHERE ID_LOKALIZACE = 'AKCE.1000.SETUP'"));
        add(3095, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Menu  \"catalogage\"' WHERE ID_LOKALIZACE = 'AKCE.2000.SETUP'"));
        add(3096, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Menu \"System\"' WHERE ID_LOKALIZACE = 'AKCE.0100.SETUP'"));
        add(3097, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Registre local' WHERE ID_LOKALIZACE = 'AKCE.1233.SETUP'"));
        add(3098, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Registre local' WHERE ID_LOKALIZACE = 'AKCE.2233.SETUP'"));
        add(3099, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Installer la licence' WHERE ID_LOKALIZACE = 'AKCE.0103.SETUP'"));
        add(3100, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Choisir/Regler le statut du document' WHERE ID_LOKALIZACE = 'AKCE.2210.SETUP'"));
        add(3101, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveau' WHERE ID_LOKALIZACE = 'AKCE.2201.A3_KatzNovy'"));
        add(3102, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouveau titre' WHERE ID_LOKALIZACE = 'AKCE.3101.A2_1Novy'"));
        add(3103, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Nouvelle notice' WHERE ID_LOKALIZACE = 'AKCE.2201.SETUP'"));
        add(3104, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Edition de la notice' WHERE ID_LOKALIZACE = 'AKCE.2202.SETUP'"));
        add(3105, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Editer' WHERE ID_LOKALIZACE = 'AKCE.2202.A3_KatzOprav'"));
        add(3106, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Editer la notice actuelle' WHERE ID_LOKALIZACE = 'AKCE.2202.A3_KatzOprav.HINT'"));
        add(3107, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ouvrir le Catalogue' WHERE ID_LOKALIZACE = 'AKCE.1100.A0_OpenKatalog.HINT'"));
        add(3108, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ouvrir le fond pour catalogage' WHERE ID_LOKALIZACE = 'AKCE.2000.A0_OpenKatzFond.HINT'"));
        add(3109, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Ouvrir les volumes de documents' WHERE ID_LOKALIZACE = 'AKCE.2205.A3_Svazky.HINT'"));
        add(3110, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Selectionner la notice' WHERE ID_LOKALIZACE = 'AKCE.1008.SETUP'"));
        add(3111, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Selectionner les notices' WHERE ID_LOKALIZACE = 'AKCE.2008.SETUP'"));
        add(3112, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Selectionner/marquer..le titre' WHERE ID_LOKALIZACE = 'AKCE.1008.A4_Oznac'"));
        add(3113, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Selectionner le titre' WHERE ID_LOKALIZACE = 'AKCE.2008.A3_Oznac'"));
        add(3114, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Seelectionner le titre' WHERE ID_LOKALIZACE = 'AKCE.1008.A4_Oznac.HINT'"));
        add(3115, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Selectionner le titre (Num+)' WHERE ID_LOKALIZACE = 'AKCE.2008.A3_Oznac.HINT'"));
        add(3116, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Registre d´inventaire' WHERE ID_LOKALIZACE = 'AKCE.1232.A4_TiskPrirSezn.HINT'"));
        add(3117, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Registre d´inventaire' WHERE ID_LOKALIZACE = 'AKCE.2232.SETUP'"));
        add(3118, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Registre d´inventaire' WHERE ID_LOKALIZACE = 'AKCE.1232.A4_TiskPrirSezn'"));
        add(3119, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Registre d´inventaire' WHERE ID_LOKALIZACE = 'AKCE.2232.A3_TiskPrirSezn'"));
        add(3120, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Registre d´inventaire' WHERE ID_LOKALIZACE = 'AKCE.1232.SETUP'"));
        add(3121, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Registre d´inventaire' WHERE ID_LOKALIZACE = 'AKCE.2232.A3_TiskPrirSezn.HINT'"));
        add(3122, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Reservation' WHERE ID_LOKALIZACE = 'AKCE.2217.SETUP'"));
        add(3123, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Reserver le document' WHERE ID_LOKALIZACE = 'AKCE.1217.SETUP'"));
        add(3124, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réserver' WHERE ID_LOKALIZACE = 'AKCE.2217.A3_Rezervace'"));
        add(3125, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Reserver' WHERE ID_LOKALIZACE = 'AKCE.1217.A4_Rezervace'"));
        add(3126, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Reserver le document' WHERE ID_LOKALIZACE = 'AKCE.1217.A4_Rezervace.HINT'"));
        add(3127, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Réserver le document' WHERE ID_LOKALIZACE = 'AKCE.2217.A3_Rezervace.HINT'"));
        add(3128, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche de service' WHERE ID_LOKALIZACE = 'AKCE.1215.SETUP'"));
        add(3129, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Effacer' WHERE ID_LOKALIZACE = 'AKCE.2204.A3_KatzSmaz'"));
        add(3130, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Effacer les notices' WHERE ID_LOKALIZACE = 'AKCE.2204.A3_KatzSmaz.HINT'"));
        add(3131, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Effacer la notice' WHERE ID_LOKALIZACE = 'AKCE.2204.SETUP'"));
        add(3132, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fonction de liaison de fichiers extérnes aux notices' WHERE ID_LOKALIZACE = 'AKCE.2206.A3_PripojSoubor'"));
        add(3133, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Fonction de liaison de fichiers extérnes aux notices' WHERE ID_LOKALIZACE = 'AKCE.2206.A3_PripojSoubor.HINT'"));
        add(3134, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Volumes' WHERE ID_LOKALIZACE = 'AKCE.2205.SETUP'"));
        add(3135, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Volumes' WHERE ID_LOKALIZACE = 'AKCE.2205.A3_Svazky'"));
        add(3136, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression' WHERE ID_LOKALIZACE = 'AKCE.2230.SETUP'"));
        add(3137, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Presse' WHERE ID_LOKALIZACE = 'AKCE.1230.SETUP'"));
        add(3138, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression des periodiques livrées' WHERE ID_LOKALIZACE = 'AKCE.1237.A4_TiskDodPer.HINT'"));
        add(3139, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression des periodiques livrées' WHERE ID_LOKALIZACE = 'AKCE.1237.A4_TiskDodPer'"));
        add(3140, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression des notices cataloguées' WHERE ID_LOKALIZACE = 'AKCE.1235.A4_TiskKatZazn'"));
        add(3141, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression des notices cataloguées' WHERE ID_LOKALIZACE = 'AKCE.1235.A4_TiskKatZazn.HINT'"));
        add(3142, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression de la liste locale' WHERE ID_LOKALIZACE = 'AKCE.1234.A4_TiskMistSezn.HINT'"));
        add(3143, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression du registre local' WHERE ID_LOKALIZACE = 'AKCE.2234.A3_TiskMistSezn'"));
        add(3144, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression du registre local' WHERE ID_LOKALIZACE = 'AKCE.2234.A3_TiskMistSezn.HINT'"));
        add(3145, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression de la liste locale' WHERE ID_LOKALIZACE = 'AKCE.1234.A4_TiskMistSezn'"));
        add(3146, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression de notices sous forme de fichiers' WHERE ID_LOKALIZACE = 'AKCE.1238.A4_TiskZazSoub.HINT'"));
        add(3147, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression de notices sous forme de fichiers' WHERE ID_LOKALIZACE = 'AKCE.1238.A4_TiskZazSoub'"));
        add(3148, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression du registre de desherbage' WHERE ID_LOKALIZACE = 'AKCE.1233.A4_TiskUbytSezn.HINT'"));
        add(3149, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression du registre de desherbage' WHERE ID_LOKALIZACE = 'AKCE.2233.A3_TiskUbytSezn.HINT'"));
        add(3150, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression du registre de desherbage' WHERE ID_LOKALIZACE = 'AKCE.1233.A4_TiskUbytSezn'"));
        add(3151, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression du registre de desherbage' WHERE ID_LOKALIZACE = 'AKCE.2233.A3_TiskUbytSezn'"));
        add(3152, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression des codes-barres' WHERE ID_LOKALIZACE = 'AKCE.1236.A4_TiskBarCod'"));
        add(3153, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Impression des codes-barres' WHERE ID_LOKALIZACE = 'AKCE.1236.A4_TiskBarCod.HINT'"));
        add(3154, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche dans la notice selon les mots' WHERE ID_LOKALIZACE = 'AKCE.1001.A4_HledaniPole.HINT'"));
        add(3155, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Recherche par mots contenus dans les notices' WHERE ID_LOKALIZACE = 'AKCE.2001.A3_HledaniPole.HINT'"));
        add(3156, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Créer une nouvelle notice' WHERE ID_LOKALIZACE = 'AKCE.2201.A3_KatzNovy.HINT'"));
        add(3157, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Controle de l´acessibilté du document' WHERE ID_LOKALIZACE = 'AKCE.1003.SETUP'"));
        add(3158, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Changement de lecteur' WHERE ID_LOKALIZACE = 'AKCE.0101.SETUP'"));
        add(3159, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Changer le statut des notices' WHERE ID_LOKALIZACE = 'AKCE.2210.A3_ZmenStatus'"));
        add(3160, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Changer le statut des notices' WHERE ID_LOKALIZACE = 'AKCE.2210.A3_ZmenStatus.HINT'"));
        add(3161, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Affichage de la frequence et de l´histoire de prets des titres' WHERE ID_LOKALIZACE = 'AKCE.1216.SETUP'"));
        add(3162, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Afficher tous les titres accessibles' WHERE ID_LOKALIZACE = 'AKCE.1003.A4_Dostupnost.HINT'"));
        add(3163, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Supprimer les resultats de la recherche' WHERE ID_LOKALIZACE = 'AKCE.2002.SETUP'"));
        add(3164, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Suppression des resultats de la recherche' WHERE ID_LOKALIZACE = 'AKCE.1002.SETUP'"));
        add(3165, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Supprimer le choix' WHERE ID_LOKALIZACE = 'AKCE.2002.A3_HledaniZrus'"));
        add(3166, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Suppression de choix de notices recherchées' WHERE ID_LOKALIZACE = 'AKCE.1002.A4_HledaniZrus.HINT'"));
        add(3167, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Supprimer le choix de notices cherchées/trouvées' WHERE ID_LOKALIZACE = 'AKCE.2002.A3_HledaniZrus.HINT'"));
        add(3168, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Notices sous forme de fichiers' WHERE ID_LOKALIZACE = 'AKCE.1238.SETUP'"));
        add(3169, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Registre de desherbage' WHERE ID_LOKALIZACE = 'AKCE.2234.SETUP'"));
        add(3170, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Registre de desherbage' WHERE ID_LOKALIZACE = 'AKCE.1234.SETUP'"));
        add(3171, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Codes-barres' WHERE ID_LOKALIZACE = 'AKCE.1235.SETUP'"));
        add(3172, f.updateIniDefaultValue("OPAC", "SeznamLokalizaci", "cs; en; de; fr;"));
        add(3173, f.updateMessageKey("commons.objednat", "loan.Objednat"));
        add(3174, f.updateMessageKey("commons.vypujcit", "loan.Vypujcit"));
        add(3175, f.updateMessageKey("commons.vypujcitKnihu", "loan.VypujcitKnihu"));
        add(3176, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE LOKALIZACE ADD TEXT_SVK UTF_2000"},
                new String[]{})));
        add(3177, f.sql(String.format("INSERT INTO LOKALIZACE (ID_LOKALIZACE, JE_ZMENA, TEXT_CZE, TEXT_SVK, TEXT_ENG, TEXT_GER, TEXT_FRA) SELECT '%s', 0, TEXT_CZE, TEXT_SVK, TEXT_ENG, TEXT_GER, TEXT_FRA FROM LOKALIZACE WHERE ID_LOKALIZACE = '%s'", "loan.request.Vypujcit", "loan.Vypujcit")));
        add(3178, f.sql(String.format("INSERT INTO LOKALIZACE (ID_LOKALIZACE, JE_ZMENA, TEXT_CZE, TEXT_SVK, TEXT_ENG, TEXT_GER, TEXT_FRA) SELECT '%s', 0, TEXT_CZE, TEXT_SVK, TEXT_ENG, TEXT_GER, TEXT_FRA FROM LOKALIZACE WHERE ID_LOKALIZACE = '%s'", "loan.lending.Vypujcit", "loan.Vypujcit")));
        add(3179, f.updateMessageKey("commons.vratitKnihu", "loan.VratitKnihu"));
        add(3180, f.updateMessageKey("commons.rezervace", "loan.Rezervace"));
        add(3181, f.updateMessageKey("commons.rezervaces", "loan.Rezervaces"));
        add(3182, f.updateMessageKey("commons.vypujcky", "loan.Vypujcky"));
        add(3183, f.updateMessageKey("commons.objednavky", "loan.Objednavky"));
        add(3184, f.updateMessageKey("commons.vracene", "loan.Vracene"));
        add(3185, f.updateMessageKey("commons.mvs", "loan.MVS"));
        add(3186, f.updateMessageKey("konto.MvsZadankyNaX", "loan.MvsZadankyNaX"));
        add(3187, f.updateMessageKey("konto.MvsZadankyZX", "loan.MvsZadankyZX"));
        add(3188, f.updateMessageKey("commons.zobrazitHledaneDotazy", "hledani.ZobrazitHledaneDotazy"));
        add(3189, f.updateMessageKey("hledaneDotazy.datumVyhledani", "hledani.DatumVyhledani"));
        add(3190, f.updateMessageKey("hledaneDotazy.historieHledani", "hledani.HistorieHledani"));
        add(3191, f.updateMessageKey("hledaneDotazy.vyhledavanyDotaz", "hledani.VyhledavanyDotaz"));
        add(3192, f.updateMessageKey("hledaneDotazy.pocetVyhledanychZaznamu", "hledani.PocetVyhledanychZaznamu"));
        add(3193, f.deleteMessage("konto.konto"));
        add(3194, f.updateMessageKey("konto.terminVraceni", "loan.TerminVraceni"));
        add(3195, f.updateMessageKey("konto.datumVypujceni", "loan.DatumVypujceni"));
        add(3196, f.updateMessageKey("konto.datumVraceni", "loan.DatumVraceni"));
        add(3197, f.updateMessageKey("konto.typ", "loan.TypVypujcky"));
        add(3198, f.updateMessageKey("konto.vratit", "loan.Vratit"));
        add(3199, f.updateMessageKey("konto.prodlouzit", "loan.Prodlouzit"));
        add(3200, f.updateMessageKey("konto.vypujceno", "loan.Vypujceno"));
        add(3201, f.updateMessageKey("konto.rezervaceZrusena", "loan.RezervaceZrusena"));
        add(3202, f.deleteMessage("konto.vypujckaBylaZrusena"));
        add(3203, f.updateMessageKey("konto.datumRezervace", "loan.DatumRezervace"));
        add(3204, f.updateMessageKey("konto.datumObjednani", "loan.DatumObjednani"));
        add(3205, f.insertMessage("login.PrihlaseniProKnihovniky", "Přihlášení pro zaměstnance knihovny", "Librarians login"));
        add(3206, f.updateMessageKey("detail.pozadavekBylUspesneOdeslan", "loan.PozadavekBylUspesneOdeslan"));
        add(3207, f.sql(String.format("INSERT INTO LOKALIZACE (ID_LOKALIZACE, JE_ZMENA, TEXT_CZE, TEXT_SVK, TEXT_ENG, TEXT_GER, TEXT_FRA) SELECT '%s', 0, TEXT_CZE, TEXT_SVK, TEXT_ENG, TEXT_GER, TEXT_FRA FROM LOKALIZACE WHERE ID_LOKALIZACE = '%s'", "loan.mvs.PozadavekBylUspesneOdeslan", "loan.PozadavekBylUspesneOdeslan")));
        add(3208, f.insertIniKey("OPAC_USER", "DefaultByReaderFullRegistrationReaderCategory", 450, "Výchozí kategorie čtenáře při předregistraci čtenářem", null, null, "SCALAR", "CTEN_KAT"));
        add(3209, f.sql("UPDATE INI_SEKCE SET PORADI = 40 WHERE ID_SEKCE = 'OPAC'"));
        add(3210, f.sql("INSERT INTO INI_SEKCE (poradi, id_sekce, suffix_num, limit_od, limit_do) values (41, 'OPAC_RECORD', 0, null, null)"));
        add(3211, f.sql("UPDATE INI_SEKCE SET PORADI = 42 WHERE ID_SEKCE = 'OPAC_SEARCH'"));
        add(3212, f.sql("UPDATE INI_SEKCE SET PORADI = 43 WHERE ID_SEKCE = 'OPAC_USER'"));
        add(3213, f.sql("UPDATE INI_SEKCE SET PORADI = 44 WHERE ID_SEKCE = 'OPAC_LOAN'"));
        add(3214, f.deleteMessage("commons.VytvoritDalsi"));
        add(3215, f.insertMessage("editace.PridejDalsiPole", Map.of("text_cze", "Přidej další pole", "text_eng", "Add new field", "text_ger", "Neu erstellen")));
        add(3216, f.insertMessage("editace.PridejDalsiPodpole", Map.of("text_cze", "Přidej další podpole", "text_eng", "Add new subfield", "text_ger", "Neu erstellen")));
        add(3217, f.updateCustomFilesContent(customFolderPath, new String[] {"vm", "vtl"}, oldContent -> {
            String newContent = oldContent;
            newContent = newContent.replace(".getDatafields(", ".getFields(");
            newContent = newContent.replace(".getControlfields(", ".getFields(");
            newContent = newContent.replace(".datafields", ".fields");
            newContent = newContent.replace(".getPodpole(", ".getSubfield(");
            return newContent;
        }));
        add(3218, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_NADPISU",
                source -> source
                        .replace(".getDatafields(", ".getFields(")
                        .replace(".getControlfields(", ".getFields(")
                        .replace(".datafields", ".fields")
                        .replace(".getPodpole(", ".getSubfield(")));
        add(3218, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-VEDLE_OBALKY",
                source -> source
                        .replace(".getDatafields(", ".getFields(")
                        .replace(".getControlfields(", ".getFields(")
                        .replace(".datafields", ".fields")
                        .replace(".getPodpole(", ".getSubfield(")));
        add(3218, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-POD_OBALKOU",
                source -> source
                        .replace(".getDatafields(", ".getFields(")
                        .replace(".getControlfields(", ".getFields(")
                        .replace(".datafields", ".fields")
                        .replace(".getPodpole(", ".getSubfield(")));
        add(3218, f.updateIniCustomValue("OPAC", "DEFINICE_ODSTAVCE-AUTORITA",
                source -> source
                        .replace(".getDatafields(", ".getFields(")
                        .replace(".getControlfields(", ".getFields(")
                        .replace(".datafields", ".fields")
                        .replace(".getPodpole(", ".getSubfield(")));
        add(3218, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentPlne",
                source -> source
                        .replace(".getDatafields(", ".getFields(")
                        .replace(".getControlfields(", ".getFields(")
                        .replace(".datafields", ".fields")
                        .replace(".getPodpole(", ".getSubfield(")));
        add(3218, f.updateIniCustomValue("OPAC_SEARCH", "TextDefDokumentVice",
                source -> source
                        .replace(".getDatafields(", ".getFields(")
                        .replace(".getControlfields(", ".getFields(")
                        .replace(".datafields", ".fields")
                        .replace(".getPodpole(", ".getSubfield(")));
        add(3219, f.insertIniKey("OPAC_RECORD", "TemplateDocumentDetailRightPanel", 10, "Definice odstavce v zobrazení záznamu vpravo vedle nadpisu (nejčastěji je zde signatura nebo MDT)", null,
                (
                        """
                                <div class="panel-body">
                                  #sf(910 'b' '{detail.signatura}: ')
                                  #sf(80 'a' ' MDT: ')
                                </div>"""
                ).replace("'", "''"),
                "SCALAR", "VELOCITY_TEMPLATE"));
        add(3220, f.disableIniTriggers());
        add(3220, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_RECORD', FK_KLIC = 'TemplateDocumentDetailRightPanel' WHERE FK_KLIC = 'DEFINICE_ODSTAVCE-VEDLE_NADPISU'"));
        add(3221, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'DEFINICE_ODSTAVCE-VEDLE_NADPISU'"));
        add(3222, f.insertIniKey("OPAC_RECORD", "TemplateDocumentDetailUnderTitle", 20, "Definice odstavce v zobrazení záznamu pod nadpisem (pod názvem fondu a nad hodnocenim)", null,
                (
                        """
                                #fond()

                                <br/>

                                #sf(773 '' '{detail.PublishedIn} ' '<br/>')

                                #sf(250 '' '' '<br/>')

                                #sf(260 'ab')

                                #sf(260 'cdefg' '' ';')

                                #sf(264 '' '' ';')

                                #if($record.query('260').any)<br/>#end

                                #sf(300 '' '' '<br/>')

                                #sf(700 'a' '{detail.ostatniAutori}: ' '<br/>')\s

                                #sf(44)"""
                ).replace("'", "''"),
                "SCALAR", "VELOCITY_TEMPLATE"));
        add(3223, f.disableIniTriggers());
        add(3223, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_RECORD', FK_KLIC = 'TemplateDocumentDetailUnderTitle' WHERE FK_KLIC = 'DEFINICE_ODSTAVCE-VEDLE_OBALKY'"));
        add(3224, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'DEFINICE_ODSTAVCE-VEDLE_OBALKY'"));
        add(3225, f.insertIniKey("OPAC_RECORD", "TemplateDocumentDetailUnderCover", 30, "Definice odstavce v zobrazení záznamu pod obálkou (mezi obálkou a horizontální čarou)", null,
                (
                        """
                                #sf(650 'a' '' '<br/>')

                                #sf(653 'a' '' '<br/>')

                                #sf(520 '' '<br/>' '<br/>')

                                #sf(500 '' '<br/>' '<br/>')"""
                ).replace("'", "''"),
                "SCALAR", "VELOCITY_TEMPLATE"));
        add(3226, f.disableIniTriggers());
        add(3226, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_RECORD', FK_KLIC = 'TemplateDocumentDetailUnderCover' WHERE FK_KLIC = 'DEFINICE_ODSTAVCE-POD_OBALKOU'"));
        add(3227, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'DEFINICE_ODSTAVCE-POD_OBALKOU'"));
        add(3228, f.insertIniKey("OPAC_SEARCH", "TemplateDocumentSearchMain", 11, "Definice odstavce prvku výsledku hledání v plnohodnotném zobrazení", null,
                (
                        """
                                #fond()

                                <br/>

                                #sf(773 '' '{detail.PublishedIn} ' '<br/>')

                                #sf(260)

                                #sf(264)

                                #sf(910 'b' '{detail.signatura}: ')"""
                ).replace("'", "''"),
                "SCALAR", "VELOCITY_TEMPLATE"));
        add(3229, f.disableIniTriggers());
        add(3229, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'TemplateDocumentSearchMain' WHERE FK_KLIC = 'TextDefDokumentPlne'"));
        add(3230, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'TextDefDokumentPlne'"));
        add(3231, f.insertIniKey("OPAC_SEARCH", "TemplateDocumentSearchMore", 17, "Definice odstavce prvku výsledku hledání při kliknutí na tlačítko více.", null,
                null,
                "SCALAR", "VELOCITY_TEMPLATE"));
        add(3232, f.disableIniTriggers());
        add(3232, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_SEARCH', FK_KLIC = 'TemplateDocumentSearchMore' WHERE FK_KLIC = 'TextDefDokumentVice'"));
        add(3233, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'TextDefDokumentVice'"));
        add(3234, f.insertIniKey("OPAC_RECORD", "TemplateAuthorityDetailUnderTitle", 50, "Definice odstavce v zobrazeni autority", null,
                (
                        """
                                #sf(670 '' '' '<br/>'  '<br/>' )

                                #sf(678 '' '' '<br/>'  '<br/>')\s

                                #sf(856)"""
                ).replace("'", "''"),
                "SCALAR", "VELOCITY_TEMPLATE"));
        add(3235, f.disableIniTriggers());
        add(3235, f.sql("UPDATE INI_FILE SET FK_SEKCE = 'OPAC_RECORD', FK_KLIC = 'TemplateAuthorityDetailUnderTitle' WHERE FK_KLIC = 'DEFINICE_ODSTAVCE-AUTORITA'"));
        add(3236, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'DEFINICE_ODSTAVCE-AUTORITA'"));
        add(3237, f.insertMessage("export.DocxButton", "docx (Word)", "docx (Word)"));
        add(3238, f.insertMessage("export.DocButton", "Word (.doc)", "Word (.doc)"));
        add(3239, f.deleteMessage("export.DocxButton"));
        add(3240, f.insertMessage("loan.request.mail.Subject", Map.of("text_cze", "Objednávka", "text_eng", "Order", "text_ger", "Bestellung")));
        add(3241, f.updateMessageKey("mail.ObjednavamVybraneDokumenty", "loan.request.mail.Body"));
        add(3242, f.updateMessageKey("commons.objednavka", "loan.Objednavka"));
        add(3243, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Commande' WHERE ID_LOKALIZACE = 'loan.request.mail.Subject'"));
        add(3244, f.insertMessage("loan.Loan", "Výpůjčka", "Loan"));
        add(3245, f.updateMessageKey("ctenar.jmeno", "ctenar.firstName"));
        add(3246, f.updateMessageKey("ctenar.prijmeni", "ctenar.lastName"));
        add(3247, f.deleteMessage("commons.jmenoAPrijmeni"));
        add(3248, f.updateMessageKey("ctenar.zapnutaPrechodnaAdresaProTisk", "ctenar.temporaryAddressForPrintEnabled"));
        add(3249, f.updateMessageKey("ctenar.cisloObcanskehoPrukazu", "ctenar.identityCardNumber"));
        add(3250, f.updateMessageKey("ctenar.trida", "ctenar.schoolClass"));
        add(3251, f.updateMessageKey("ctenar.vzdelani", "ctenar.educationLevel"));
        add(3252, f.updateMessageKey("ctenar.vzkaz", "ctenar.message"));
        add(3253, f.updateMessageKey("ctenar.blokovanInternet", "ctenar.internetForbidden"));
        add(3254, f.updateMessageKey("ctenar.posilanyPredupominky", "ctenar.preoverdueNoticesSending"));
        add(3255, f.updateMessageKey("ctenar.povolenSelfCheck", "ctenar.selfcheckAllowed"));
        add(3256, f.updateMessageKey("ctenar.titul", "ctenar.degree"));
        add(3257, f.updateMessageKey("ctenar.zamestnani", "ctenar.job"));
        add(3258, f.updateMessageKey("ctenar.adresaZamestnani", "ctenar.jobAddress"));
        add(3259, f.insertMessage("commons.Value", Map.of("text_cze", "Hodnota", "text_eng", "Value", "text_ger", "Wert", "text_fra", "Valeur")));
        add(3260, f.insertMessage("commons.ObsahujePlnyText", Map.of("text_cze", "Obsahuje plný text", "text_eng", "Contains fulltext", "text_ger", "Es enthält Volltext", "text_fra", "Il contient le texte intégral")));
        add(3261, f.updateMessageKey("commons.zobrazitPlnyText", "commons.ZobrazitPlnyText"));
        add(3262, f.deleteMessage("commons.ObsahujePlnyText"));
        add(3263, f.insertMessage("commons.IsbnOrIssn", Map.of("text_cze", "ISBN/ISSN", "text_eng", "ISBN/ISSN", "text_ger", "ISBN/ISSN", "text_fra", "ISBN/ISSN")));
        add(3264, f.sql("UPDATE OPAC_MENU SET PERMISSION = 'UsersShow' WHERE PERMISSION = 'ReadersShow'"));
        add(3265, f.sql("UPDATE OPAC_MENU SET TARGET = '/users' WHERE TARGET = '/readers'"));
        add(3266, new ExceptionCatchingUpdate(f.grantSelect("DEF_REZ", "opac")));
        add(3267, f.insertMessage("ctenar.FullRegistrationFormHeader", Map.of("text_cze", "", "text_eng", "", "text_ger", "", "text_fra", "")));
        add(3268, f.insertMessage("ctenar.FullRegistrationFormFooter", Map.of("text_cze", "", "text_eng", "", "text_ger", "", "text_fra", "")));
        add(3269, f.insertIniValhod(34, 22, "DAT_NAR", "Datum narození"));
        add(3270, f.insertIniKey("OPAC_SEARCH", "AuthorityGlobalSearchTemplate", 58, "Vzor pro globální hledání v autoritách", null, "whole(P100:\"?\"^3) OR and(P100:?*^2 OR P110:?*^2 OR P111:?*^2 OR P119:?*^2 OR P130:?*^2 OR P150:?*^2 OR P151:?*^2 OR PALL:?*)", "SCALAR", "TEXT"));
        add(3271, f.insertIniKey("OPAC_SEARCH", "Sortings", 40, "Možnosti řazení vyhledaných záznamů. První položka je výchozí.", null, "relevance, PNAZEV, REZS_AUTOR, REZS_ROK, -REZS_ROK", "LIST", "TEXT"));
        add(3272, f.updateMessageKey("hledani.relevance", "hledani.razeni.podle.relevance"));
        add(3273, f.updateMessageKey("hledani.nazvu", "hledani.razeni.podle.PNAZEV"));
        add(3274, f.updateMessageKey("hledani.autora", "hledani.razeni.podle.REZS_AUTOR"));
        add(3275, f.updateMessageKey("hledani.rokuVydani", "hledani.razeni.podle.REZS_ROK"));
        add(3276, f.insertMessage("hledani.razeni.podle.-REZS_ROK", Map.of("text_cze", "roku vydání sestupně", "text_eng", "year of publication desc.", "text_ger", "jahr der Herausgabe absteigend", "text_fra", "date de publication en ordre décroissant")));
        add(3277, f.updateMessageKey("hledani.seraditVysledkyPodle", "hledani.razeni.SeraditVysledkyPodle"));
        add(3278, f.insertMessage("vysledky.razeni.RazenoPodle", Map.of("text_cze", "Řazeno podle", "text_eng", "Sorted by", "text_ger", "Sortierung nach", "text_fra", "Rangé par")));
        add(3279, f.insertIniKey("OPAC_SEARCH", "AuthoritySortings", 65, "Možnosti řazení vyhledaných autorit. První položka je výchozí.", null, "relevance", "LIST", "TEXT"));
        add(3280, f.disableIniTriggers());
        add(3280, f.sql("DELETE FROM INI_FILE WHERE FK_KLIC = 'DefaultRazeni'"));
        add(3281, f.sql("DELETE FROM INI_KEYS WHERE ID_KLIC = 'DefaultRazeni'"));
        add(3282, f.sql("DELETE FROM INI_VALHOD WHERE ID_VALHOD = 29"));
        add(3283, f.updateMessageKey("vysledky.razeni.RazenoPodle", "hledani.razeni.RazenoPodle"));
        add(3284, f.deleteMessage("vysledky.razenoPodleAutora"));
        add(3285, f.deleteMessage("vysledky.razenoPodleNazvu"));
        add(3286, f.deleteMessage("vysledky.razenoPodleRelevance"));
        add(3287, f.deleteMessage("vysledky.razenoPodleRokuVydani"));
        add(3288, f.insertIniKey("OPAC_SEARCH", "CombineDocumentAndAuthoritySearch", 200, "Zda kombinovat dokumenty a autority do jednoho vyhledávání", 1, "NE"));
        add(3289, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source
                .replace("#recordEditation", ".record-editation")
                .replace("seznamEditovanychPoli", "edited-fields-container")));
        add(3290, f.insertIniKey("EVERBIS", "AuthorityZServers", 60, "Seznam autoritních Z-serverů (ZSERVERAUT01; ZSERVERAUT02; ...), které má eVerbis používat", null, "ZSERVERAUT01"));
        add(3291, f.sql("UPDATE OPAC_MENU SET TARGET = '/lists/document-index?prefix=A&source=menu' WHERE TARGET = '/lists/document-index?source=menu'"));
        add(3292, f.sql("UPDATE OPAC_SEARCH_KEYS SET DATATYPE = 'PRIR_CISLO' WHERE DATATYPE = 'PRIRCISLO'"));
        add(3293, f.sql("UPDATE OPAC_SEARCH_KEYS SET NAZEV = 'PRIR_CISLO' WHERE NAZEV = 'PRIRCISLO'"));
        add(3294, f.sql("UPDATE OPAC_SEARCH_KEYS SET NAZEV = 'PRIR_CISLO' WHERE NAZEV = 'PRIRCISLO'"));
        add(3295, f.disableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(3296, f.sql("UPDATE DEF_HLEDRADEK SET VYCETPOLI = 'PRIR_CISLO' where VYCETPOLI = 'PRIRCISLO' and FK_HLED in (103,102)"));
        add(3297, f.enableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(3298, f.insertMessage("commons.Soubory", Map.of("text_cze", "Soubory", "text_eng", "Files", "text_ger", "Dateien", "text_fra", "Fichiers")));
        add(3299, f.insertMessage("export.XlsButton", "Excel (.xls)", "Excel (.xls)"));
        add(3300, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source
                .replace("dokumentListItem", "list-item-document")
                .replace("definovanyOdstavec", "paragraph-template")));
        add(3301, f.insertIniKey("OPAC_SEARCH", "TemplateAuthoritySearch", 58, "Definice odstavce autority v seznamu vyhledaných", null, "#sf(678)", "SCALAR", "VELOCITY_TEMPLATE"));
        add(3302, f.dbDependentSql(
                new String[] {"CREATE OR ALTER VIEW VIEW_OPAC_COVERS_AUT (FK_AUT, FK_FULLTEXT, PORADI) AS SELECT FK_AUT, FK_FULLTEXT, PORADI FROM FULLTEXT_AUTORITY JOIN FULLTEXT_SOUBORY ON (FK_FULLTEXT = ID_FULLTEXT) WHERE FK_TYP_FULLTEXT = 1 AND IS_INDEX_LUCENE != 2 AND IS_INDEX_LUCENE != 4"},
                new String[]{}));
        add(3304, f.grantSelect("VIEW_OPAC_COVERS_AUT", "opac"));
        add(3305, f.grantSelect("DEF_TYP_FULLTEXT", "opac"));
        add(3306, f.updateMessageKey("detail.prilohyDokumentu", "detail.PrilohyKeStazeni"));
        add(3307, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE POPS_POTVRZENI DROP CONSTRAINT FK_POPS_POTVRZENI_FULL"},
                new String[]{})));
        add(3308, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE POPS_POTVRZENI ADD CONSTRAINT FK_POPS_POTVRZENI_FULL FOREIGN KEY (FK_FULLTEXT) REFERENCES FULLTEXT_SOUBORY (ID_FULLTEXT) ON DELETE CASCADE"},
                new String[]{})));
        add(3309, f.updateMessageKey("registrace.registraceProbehlaUspesne", "registrace.credentials.RegistraceProbehlaUspesne"));
        add(3310, f.updateMessageKey("registrace.krok1", "registrace.credentials.Krok1"));
        add(3311, f.updateMessageKey("registrace.krok2", "registrace.credentials.Krok2"));
        add(3312, f.insertMessage("registrace.full.RegistraceProbehlaUspesne", Map.of("text_cze", "Delete Registrace proběhla úspěšně", "text_eng", "Delete Registration completed", "text_ger", "Delete Registrierung war erfolgreich", "text_fra", "Enregistrement a été réussi")));
        add(3317, f.insertMessage("registrace.full.MailSubject", Map.of("text_cze", "Registrace do katalogu", "text_eng", "Library catalog registration", "text_ger", "Die anmeldung für katalog", "text_fra", "Inscription pour le catalogue")));
        add(3318, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source
                .replace("#hledani", ".search")));
        add(3319, f.insertMessage("commons.Exemplare", Map.of("text_cze", "Exempláře", "text_eng", "Items", "text_ger", "Exemplare", "text_fra", "Exemplaires")));
        add(3320, f.updateMessageTranslation("commons.Exemplare", "text_svk", "Exempláre"));
        add(3321, f.sql("UPDATE OPAC_MENU SET PORADI = PORADI + 1 WHERE PORADI > 3 AND FK_OPAC_MENU IS NULL"));
        add(3322, () -> {
            Number maxId = jdbcTemplate.queryForObject("SELECT MAX(ID_OPAC_MENU) from OPAC_MENU", Integer.class);
            int nextId = maxId == null ? 1 : maxId.intValue() + 1;
            jdbcTemplate.update("INSERT INTO OPAC_MENU (ID_OPAC_MENU, FK_OPAC_MENU, PORADI, TEXT, TARGET, LOGIN_REQUIRED, PERMISSION, GEN) VALUES ("+nextId+", null, 4, 'commons.Exemplare', '/exemplars', 0, 'ExemplarsShow', NULL)");
        });
        add(3323, f.updateMessage("record.ProTentoFondNeniNastavenFiltrPoli", "Pracovní list (seznam editovaných polí - styl) pro tento je prázdný nebo není nastaven", "Style (edited field list) for this fond is empty or is not set"));
        add(3324, f.updateMessageKey("record.ProTentoFondNeniNastavenFiltrPoli", "record.PracovniListProTentoFondJePrazdny"));
        add(3325, f.dbDependentSql(
                new String[] {"CREATE OR ALTER VIEW VIEW_OPAC_COVERS (FK_ZAZ, FK_FULLTEXT, PORADI) AS SELECT FK_ZAZ, FK_FULLTEXT, PORADI FROM FULLTEXT_DOKUMENTY JOIN FULLTEXT_SOUBORY ON (FK_FULLTEXT = ID_FULLTEXT) WHERE FK_TYP_FULLTEXT = 1 AND IS_INDEX_LUCENE != 2 AND IS_INDEX_LUCENE != 4"},
                new String[]{}));
        add(3326, f.grantSelect("VIEW_OPAC_COVERS", "opac"));
        add(3327, f.insertMessage("record.ElektronickaForma", Map.of("text_cze", "Elektronická forma", "text_eng", "Electronic form", "text_ger", "Elektronischer form", "text_fra", "Sous forme électronique")));
        add(3328, f.insertMessage("ctenar.FullRegistrationTitle", Map.of("text_cze", "Předregistrace", "text_eng", "Pre-registration", "text_ger", "Vorregistrierung", "text_fra", "Preregistration")));
        add(3329, f.deleteIniCustomValue("OPAC", "OBECNE-LOGOVANI_AKCI_ZAP"));
        add(3330, f.deleteIniKey("OPAC", "OBECNE-LOGOVANI_AKCI_ZAP"));
        add(3331, f.deleteIniCustomValue("OPAC", "OBECNE-LOGOVANI_SESSION_ZAP"));
        add(3332, f.deleteIniKey("OPAC", "OBECNE-LOGOVANI_SESSION_ZAP"));
        add(3333, f.enableIniTriggers());
        add(3333, f.sql("INSERT INTO INI_SEKCE (poradi, id_sekce, suffix_num, limit_od, limit_do) values (100, 'OPAC_EXEMP', 0, null, null)"));
        add(3334, f.moveIniKeyV1("OPAC", "NOVINKY-POCET_NACITANYCH", "OPAC", "PresearchedNewsCount", 600, "Počet načítaných novinek na hlavní stránce. Má smysl pouze pokud je nastaveno preferování novinek s obalem. Pak se nejdříve načte tento počet novinek, ze kterých se pak vyberou jen ty s obalem. Zobrazí se jich tolik, kolik je definováno v počtu zobrazovaných. Pokud bude novinek s obalem méně, doplní se novinkami bez obalu.", null, "50", "SCALAR", "NUMBER"));
        add(3335, f.moveIniKeyV1("OPAC", "NOVINKY-POCET_ZOBRAZOVANYCH", "OPAC", "ShowedNewsCount", 610, "Počet novinek zobrazených na hlavní stránce", null, "15", "SCALAR", "NUMBER"));
        add(3336, f.moveIniKeyV1("OPAC", "NOVINKY-PREFEROVAT_S_OBALEM_ZAP", "OPAC", "PreferNewsWithCover", 620, "Na hlavní stránce preferovat novinky s obálkou", 1, "ANO", "SCALAR", "VALHOD"));
        add(3337, f.moveIniKeyV1("OPAC", "NOVINKY-POUZE_STATUS", "OPAC", "StatusesOfNews", 580, "Načítat jen novinky, které mají tento status", null, "7", "LIST", "STATUS_EX"));
        add(3338, f.moveIniKeyV1("OPAC", "ZAKAZANE_DOKUMENTY", "OPAC_RECORD", "ForbiddenDocuments", 100, "Seznam identifikátorů dokumentů, které nebudou v katalogu vyhledatelné", null, null, "LIST", "NUMBER"));
        add(3339, f.moveIniKeyV1("OPAC", "STATUSY_ZOBRAZOVANYCH_EXEMPLARU", "OPAC_EXEMP", "ExemplarStatuses", 200, "Seznam statusů exemplářů, které budou v katalogu zobrazovány", null, "7", "LIST", "STATUS_EX"));
        add(3340, f.moveIniKeyV1("OPAC", "OBECNE-DEFAULTNI_LOLOKALIZACE", "OPAC", "DefaultLocale", 235, "Výchozí lokalizace katalogu. Jedná se o jednoslovný kód skládající se z kódu jazyka, podtržítka a kódu území (cs_CZ, en_GB, en_US, apod.).", null, "cs_CZ", "SCALAR", "TEXT"));
        add(3341, f.moveIniKeyV1("OPAC", "StylKomentaru", "OPAC_RECORD", "CommentRestriction", 600, "Způsob, jakým budou na webu umožněny komentáře.", 26, "Everybody", "SCALAR", "VALHOD"));
        add(3342, f.moveIniKeyV1("OPAC", "SearchElsewhere", "OPAC_RECORD", "SearchElsewhere", 700, "Nastavení položek Hledat jinde", null,
                """
                        [\s
                          {
                            "name":"Google Books",\s
                            "template":"http://books.google.com/books?vid=ISBN{isbn}"
                          },\s
                          {
                            "name":"WorldCat",\s
                            "template":"http://www.worldcat.org/isbn/{isbn}"
                          },\s
                          {
                            "name":"Souborný katalog ČR",\s
                            "template":"http://aleph.nkp.cz/F/?func=find-c&amp;ccl_term=isn={isbn}&amp;local_base=SKC"
                          },\s
                          {
                            "name":"Jednotná informační brána",\s
                            "template":"http://www.jib.cz/V/?func=meta-1-check&amp;mode=advanced&amp;find_op_0=AND&amp;find_code_2=ISBN&amp;find_request_2={isbn}&amp;ckbox=CNL03210"
                          },\s
                          {
                            "name":"SFX Rozcestník",\s
                            "template":"http://sfx.jib.cz/sfxlcl3?url_ver=Z39.88-2004&amp;rft_val_fmt=info:ofi/fmt:kev:mtx:book&amp;rft.isbn={isbn}"
                          },\s
                          {
                            "name":"Amazon",\s
                            "template":"http://www.amazon.com/gp/search?index=books&amp;linkCode=qs&amp;keywords={name}"
                          }\s
                        ]""",
                "SCALAR", "TEXT"));
        add(3343, f.moveIniKeyV1("OPAC", "ExemplarDataInRecord", "OPAC_EXEMP", "ExemplarDataInRecord", 250, "Zda se mají do záznamu (do polí) načítat i exemplářové údaje. To může být užitečné při zobrazování těchto údajů v šablonách (velocity).", 1, "NE", "SCALAR", "VALHOD"));
        add(3344, f.moveIniKeyV1("OPAC", "zapnutoZobrazovaniMarc", "OPAC_RECORD", "MarcTab", 350, "Zda má být v detailu záznamu zobrazována záložka MARC", 1, "ANO", "SCALAR", "VALHOD"));
        add(3345, f.moveIniKeyV1("OPAC", "ZAZNAM-ZOBRAZOVAT_VSECHNA_POLE", "OPAC_RECORD", "DetailTabShowAllFields", 300, "Zda se mají zobrazovat všechna pole v detailu záznamu.", 1, "NE", "SCALAR", "VALHOD"));
        add(3346, f.moveIniKeyV1("OPAC", "ExemplarTabsByBuildings", "OPAC_EXEMP", "ExemplarTabsByBuildings", 260, "Zda mají být exempláře zobrazeny v záložkách podle budov nebo všechny v jedné tabulce (ano = rozdělení podle budov)", 1, "ANO", "SCALAR", "VALHOD"));
        add(3347, f.moveIniKeyV1("OPAC", "CitationDoctypes", "OPAC_RECORD", "CitationDoctypes", 520, "Seznam doctypů citací (typ dokumentu posílaný v každém požadavku na citace.com) podle fondů. Formát zápisu je fond(doctype) fond(doctype)...", null, "1(book) 2(contribution) 3(journal) 5(article) 8(ebook) 10(thesis) 11(article)", "SCALAR", "TEXT"));
        add(3348, f.moveIniKeyV1("OPAC", "CitationService", "OPAC_RECORD", "CitationService", 500, "Nastavení služby pro generování citací.", 31, "Off", "SCALAR", "VALHOD"));
        add(3349, f.moveIniKeyV1("OPAC", "ZAZNAM-ZOBRAZOVANA_POLE", "OPAC_RECORD", "DetailTabFields", 320, "Seznam polí a podpolí, která se budou zobrazovat v detailu záznamu. Má smysl, pouze pokud je vypnuto zobrazování všech polí", null, "100^acd; 245; 246; 700^ade; 20; 250; 260; 264; 650; 653; 773^atg; 300; 500; 520; 856; 80^a; 910^b;", "SCALAR", "TEXT"));
        add(3350, f.moveIniKeyV1("OPAC", "SeznamLokalizaci", "OPAC", "Locales", 230, "Seznam zobrazovaných jazykových mutací. Aktualně jsou podporovány čeština (cs), angličtina (en), němčina (de), francouzština (fr)", null, "cs; en; de; fr;", "LIST", "TEXT"));
        add(3351, f.moveIniKeyV1("OPAC", "ExemplarColumns", "OPAC_EXEMP", "ExemplarColumnsByFonds", 410, "Sloupce v seznamu exemplářů podle daných fondů. Pokud daný fond v seznamu není, použije se defaultní nastavení.", null, "3(PRIR_CISLO; SIGNATURA; ROCNIK; ROK; ROZMEZI_CISEL; LOKACE; KATEGORIE; DOSTUPNOST; FULLTEXT;)", "SCALAR", "TEXT"));
        add(3352, f.moveIniKeyV1("OPAC", "ZAZNAM-SLOUPCE_EXEMPLARE", "OPAC_EXEMP", "ExemplarColumns", 400,"Sloupce v seznamu exemplářů. Povolene hodnoty: PRIR_CISLO, BAR_COD, SIGNATURA, LOKACE, KATEGORIE, DOSTUPNOST, BUDOVA, TEM_SKUP, STATUS, POZNAMKA, CENA, UZIVATEL, ROK_PRIR, ZPUSOB_NABYTI, ZPUSOB_ZALOZENI, INDIVIDUALNI_HODNOTA, DATUM, UMISTENI, HOLDER", 35, "PRIR_CISLO; SIGNATURA; LOKACE; KATEGORIE; DOSTUPNOST; FULLTEXT;", "LIST", "VALHOD"));
        add(3353, f.moveIniKeyV1("OPAC", "ZAZNAM-SLOUPCE_CISLA", "OPAC_EXEMP", "IssueColumns", 420, "Seznam sloupců v seznamu cisel. Povolené hodnoty: PRIR_CISLO, BAR_COD, SIGNATURA, LOKACE, KATEGORIE, DOSTUPNOST, BUDOVA, TEM_SKUP, STATUS, POZNAMKA, CISLO, CLANKY", 35, "CISLO; BAR_COD; SIGNATURA; BUDOVA; LOKACE; KATEGORIE; DOSTUPNOST; FULLTEXT;", "LIST", "VALHOD"));
        add(3354, f.moveIniKeyV1("OPAC", "RazeniExemplaru", "OPAC_EXEMP", "ExemplarSorting", 600, "Sekvence vlastností exempláře, podle kterých se budou řadit exempláře", 38, "BUDOVA; LOKACE; PORADI; PRIR_CISLO", "LIST", "VALHOD"));
        add(3355, f.moveIniKeyV1("OPAC", "RazeniSvazku", "OPAC_EXEMP", "BindingSorting", 620, "Sekvence vlastností exempláře, podle kterých se budou řadit svazky periodik", 38, "BUDOVA; LOKACE; PORADI; PRIR_CISLO", "LIST", "VALHOD"));
        add(3356, f.moveIniKeyV1("OPAC", "RazeniCisel", "OPAC_EXEMP", "IssueSorting", 640, "Sekvence vlastností, podle kterých se budou řadit čísla", 38, "BUDOVA; LOKACE; -PORADI; -CISLO; PRIR_CISLO", "LIST", "VALHOD"));
        add(3357, f.moveIniKeyV1("OPAC", "ZobrazovanaPoleDokumentu", "OPAC_RECORD", "DetailTabFieldsByFonds", 323, "Seznam polí a podpolí podle čísel fondů, která se budou zobrazovat v detailu záznamu. Má smysl, pouze pokud je vypnuto zobrazování všech polí", null, null, "SCALAR", "TEXT"));
        add(3358, f.moveIniKeyV1("OPAC", "BudovyPasuNovinek", "OPAC", "BuildingsOfNewsSlider", 630, "Seznam budov, ze kterých se maji načítat záznamy do pásu s novinkami. Prázdný = všechny budovy", null, null, "LIST", "BUDOVA"));
        add(3359, f.sql("UPDATE INI_SEKCE SET PORADI = 39 WHERE ID_SEKCE = 'OPAC'"));
        add(3360, f.sql("UPDATE INI_SEKCE SET PORADI = 40 WHERE ID_SEKCE = 'OPAC_RECORD'"));
        add(3361, f.sql("UPDATE INI_SEKCE SET PORADI = 41 WHERE ID_SEKCE = 'OPAC_EXEMP'"));
        add(3362, f.moveIniKeyV1("OPAC", "VlastnostProMapy", "OPAC_EXEMP", "ExemplarPlacementInfoProperty", 800, "Exemplářová vlastnost (sloupec), podle které se budou načítat mapy regálů.", null, "LOKACE", "SCALAR", "TEXT"));
        add(3363, f.moveIniKeyV1("OPAC", "RootAuthority", "OPAC_RECORD", "AuthorityHierarchyRoot", 900, "ID autority nejvyšší úrovně, ze které se bude větvit autoritní thesaurus", null, null, "SCALAR", "NUMBER"));
        add(3364, f.moveIniKeyV1("OPAC", "OBECNE-ZAKAZANE_STATUSY_DOKUMENTU", "OPAC", "ForbiddenDocumentStatuses", 301, "Seznam statusů dokumentů, které nebudou v Portaru zobrazovány", null, "9;99", "LIST", "STATUS_DOK"));
        add(3365, f.moveIniKeyV1("OPAC", "ZAZNAM-MAXIMUM_PODOBNYCH", "OPAC_RECORD", "SimilarDocumentsCount", 800, "Maximální počet zobrazených podobných dokumentů", null, "5", "SCALAR", "NUMBER"));
        add(3366, f.insertMessage("commons.Cekejte", Map.of("text_cze", "Čekejte", "text_eng", "Wait", "text_ger", "Warten", "text_fra", "Attendre")));
        add(3367, f.insertMessage("loan.DokumentyJsouKVyzvednutiNaBudove", Map.of("text_cze", "Knihy jsou volné. Můžete si je vyzvednout na budově", "text_eng", "Documents are to pick up on building", "text_ger", "Buch ist verfügbar und kann in diesen Häusern abgeholt werden", "text_fra", "Document est accessible, vous pouvez le retirer dans les bâtiments")));
        add(3368, f.insertMessage("loan.DokumentyJsouKVyzvednutiNaBudovach", Map.of("text_cze", "Knihy jsou volné. Můžete si je vyzvednout na budovách", "text_eng", "Documents are to pick up on buildings", "text_ger", "Buch ist verfügbar und kann in diesen Häusern abgeholt werden", "text_fra", "Document est accessible, vous pouvez le retirer dans les bâtiments")));
        add(3369, f.insertMessage("loan.DokumentyJsouKZarezervovaniNaBudove", Map.of("text_cze", "Knihy jsou k zarezervování na budově", "text_eng", "Documents are checked out on building", "text_ger", "Dokuments steht im Haus zur Reservierung bereit", "text_fra", "Documents est réservé dans le bâtiment")));
        add(3370, f.insertMessage("loan.DokumentyJsouKZarezervovaniNaBudovach", Map.of("text_cze", "Knihy jsou k zarezervování na budovách", "text_eng", "Documents are checked out in following buildings. Select the buildings where you want to place a book on hold", "text_ger", "Buch ist in folgenden Häusern ausgeliehen. Wählen Sie das Haus aus, wo Sie das Buch reservieren wollen.", "text_fra", "Dans les bâtiments suivants le document est preté, choisissez les bâtiments dans lesquels vous voulez réserver ce document")));
        add(3371, f.insertMessage("loan.DokumentyJsouKObjednaniNaBudove", Map.of("text_cze", "Knihy jsou volné. Můžete si je vyzvednout na budově", "text_eng", "Documents are to pick up on building", "text_ger", "Buch ist verfügbar und kann in diesen Häusern abgeholt werden", "text_fra", "Document est accessible, vous pouvez le retirer dans les bâtiments")));
        add(3372, f.insertMessage("loan.DokumentyJsouKObjednaniNaBudovach", Map.of("text_cze", "Knihy jsou volné. Můžete si je vyzvednout na budovách", "text_eng", "Documents are to pick up on buildings", "text_ger", "Buch ist verfügbar und kann in diesen Häusern abgeholt werden", "text_fra", "Document est accessible, vous pouvez le retirer dans les bâtiments")));
        add(3373, f.insertMessage("loan.DokumentJePrezencneNaBudove", "Kniha je prezenčně (ke studiu na místě) na budově", "Document is to study in room on building"));
        add(3374, f.insertMessage("loan.DokumentJePrezencneNaBudovach", "Kniha je prezenčně (ke studiu na místě) na budovách", "Document is to study in room on buildings"));
        add(3375, f.insertMessage("loan.DokumentyJsouPrezencneNaBudove", "Knihy jsou prezenčně (ke studiu na místě) na budově", "Documents are to study in room on building"));
        add(3376, f.insertMessage("loan.DokumentyJsouPrezencneNaBudovach", "Knihy jsou prezenčně (ke studiu na místě) na budovách", "Documents are to study in room on buildings"));
        add(3377, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source
                .replace(".notOrderable", ".not-orderable")
                .replace(".notReservable", ".not-reservable")
                .replace(".notPickable", ".not-pickable")
                .replace(".notOnlyInPlace", ".not-visitable-only")
                .replace(".notMvsRequestable", ".not-mvs-requestable")
                .replace(".userCannotRequest", ".not-permitted")
                .replace(".onlyInPlace", ".visitable-only")
                .replace(".nelzeVypujcit", ".not-available")
                .replace(".externalOrdering", ".mail-requestable")
                .replace(".external-requesting", ".mail-requestable")
                .replace(".mvsRequestable", ".mvs-requestable")
                .replace(".externally-available", ".ereading-loanable")
                .replace(".loanRequestBtnBlock-document .", ".availability-button-document.") //elementy jiz nejsou v kontejneru
                .replace(".loanRequestBtnBlock-exemplar .", ".availability-button-exemplar.") //elementy jiz nejsou v kontejneru
                .replace(".loanRequestBtnBlock-document", ".availability-button-document")
                .replace(".loanRequestBtnBlock-exemplar", ".availability-button-exemplar")
                .replace(".blokSTlacitkemObjednat .", ".availability-button.") //elementy jiz nejsou v kontejneru
                .replace(".blokSTlacitkemObjednat", ".availability-button")));
        add(3378, f.insertIniKey("OPAC_USER", "CredentialsRegistrationIdentifierProps", 185, "Údaje, podle kterých se bude hledat čtenář při registraci webového přístupu", 39, "cardNumberOrBarCode; lastName;", "LIST", "VALHOD"));
        add(3379, f.insertIniValhod(39, 1, "cardNumberOrBarCode", "Čís. leg. nebo čár. kód"));
        add(3380, f.insertIniValhod(39, 2, "lastName", "Příjmení"));
        add(3381, f.insertIniValhod(39, 3, "email", "E-mail"));
        add(3382, f.insertMessage("registrace.credentials.Krok1.cardNumberOrBarCode", Map.of("text_cze", "Číslo legitimace nebo čárový kód", "text_eng", "Card number or bar code", "text_ger", "Ausweisnummer oder Kode", "text_fra", "Numéro de carte de lecteur ou Code-barres")));
        add(3383, f.insertMessage("registrace.credentials.Krok1.lastName", Map.of("text_cze", "Vaše příjmení", "text_eng", "Your last name", "text_ger", "Nachname", "text_fra", "Nom du lecteur")));
        add(3384, f.insertMessage("registrace.credentials.Krok1.email", Map.of("text_cze", "Váš e-mail", "text_eng", "Your e-mail", "text_ger", "Nachname", "text_fra", "Nom du lecteur")));
        add(3385, f.sql("INSERT INTO OPAC_USER_PREFS_KEYS (ID_KEY, NAME, DEFVAL, DATATYPE, STRUCTURE, FK_VALHOD, AUTO_GENERATED) values (2, 'LastOrderDesiredBuilding', '', 'BUDOVA', 'SCALAR', null, 1)"));
        add(3386, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE OPAC_USER_PREFS_KEYS ALTER COLUMN NAME TYPE STRING_50"},
                new String[]{}))); //zvetseni sloupce pro nazev
        add(3387, f.sql("INSERT INTO OPAC_USER_PREFS_KEYS (ID_KEY, NAME, DEFVAL, DATATYPE, STRUCTURE, FK_VALHOD, AUTO_GENERATED) values (3, 'LastReservationDesiredBuildings', '', 'BUDOVA', 'LIST', null, 1)"));
        add(3388, new ExceptionCatchingUpdate(f.sql("ALTER TABLE OPAC_HODNOCENI ADD CONSTRAINT PK_OPAC_HODNOCENI PRIMARY KEY (ID_OPAC_HODNOCENI)")));
        add(3389, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE OPAC_HODNOCENI ALTER COLUMN FK_ZAZ TYPE GENERATORY"},
                new String[]{}))); //pro vytvoreni ciziho klice museji byt sloupce stejne
        add(3390, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE OPAC_BOOKCASE ALTER COLUMN FK_ZAZ TYPE GENERATORY"},
                new String[]{}))); //pro vytvoreni ciziho klice museji byt sloupce stejne
        add(3391, new ExceptionCatchingUpdate(f.sql("ALTER TABLE OPAC_HODNOCENI ADD CONSTRAINT FK_OPAC_HODNOCENI_ZAZ FOREIGN KEY (FK_ZAZ) REFERENCES KAT1_4 (ID_ZAZ)")));
        add(3392, new ExceptionCatchingUpdate(f.sql("ALTER TABLE OPAC_BOOKCASE ADD CONSTRAINT PK_OPAC_BOOKCASE PRIMARY KEY (ID_OPAC_BOOKCASE)")));
        add(3393, new ExceptionCatchingUpdate(f.sql("ALTER TABLE OPAC_BOOKCASE ADD CONSTRAINT FK_OPAC_BOOKCASE_ZAZ FOREIGN KEY (FK_ZAZ) REFERENCES KAT1_4 (ID_ZAZ)")));
        add(3394, new ExceptionCatchingUpdate(f.sql("ALTER TABLE OPAC_BOOKCASE ADD CONSTRAINT FK_OPAC_BOOKCASE_CTEN FOREIGN KEY (FK_CTEN) REFERENCES CTENARI (ID_CTEN)")));
        add(3395, f.updateFilesContent(List.of(new File(path(tomcatFolderPath, "conf", "catalina.properties"))), source -> source.replace("log4j*.jar,", "")));
        add(3396, f.deleteIniCustomValue("OPAC", "HLEDANI-DEFINOVANA_NABIDKA_POLI")); //kvuli proveniu, ve kterem to z nejakeho duvodu jeste je
        add(3397, f.deleteIniKey("OPAC", "HLEDANI-DEFINOVANA_NABIDKA_POLI"));
        add(3398, f.sql("INSERT INTO INI_SEKCE (poradi, id_sekce, suffix_num, limit_od, limit_do) values (60, 'OPAC_PAYMENT', 0, null, null)"));
        add(3399, f.insertIniKey("OPAC_PAYMENT", "GopayApiUrl", 100, "Url na API endpoint GoPay platabní brány", null, "https://gate.gopay.cz/api", "SCALAR", "TEXT"));
        add(3400, f.insertIniKey("OPAC_PAYMENT", "GopayClientId", 110, "GoPay client ID", null, null, "SCALAR", "TEXT"));
        add(3411, f.insertIniKey("OPAC_PAYMENT", "GopayClientSecret", 120, "GoPay client secret", null, null, "SCALAR", "TEXT"));
        add(3412, f.insertIniKey("OPAC_PAYMENT", "GopayGoId", 130, "GoPay GoID", null, null, "SCALAR", "TEXT"));
        add(3413, f.insertIniValhod(31, 4, "Citace.com:single", "Jedna citace ze serveru citace.com"));
        add(3414, f.insertMessage("export.ExportVsechVyhledanych", Map.of("text_cze", "Export všech vyhledaných", "text_eng", "Export of all searched", "text_ger", "Exportieren Sie alle gesucht", "text_fra", "Exporter tous recherchés")));
        add(3415, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source.replace(".resetPassword", ".password-recreation")));
        add(3416, f.insertIniKey("OPAC_USER", "MojeIdTrustedProperties", 200, "Údaje z mojeID, které se mají používat jako důvěryhodná data o čtenáři (např. při předregistracích) a budou při každém přihlášení automaticky nastavována. Možné hodnoty jsou: JMENO, PRIJMENI, TRV_MI, TRV_UL, TRV_PSC, EMAIL, TELEFON", 34, "JMENO, PRIJMENI, TRV_MI, TRV_UL, TRV_PSC, EMAIL, TELEFON", "LIST", "VALHOD"));
        add(3417, f.insertMessage("commons.NejdrivePrihlasit", Map.of("text_cze", "Nejdříve přihlásit", "text_eng", "Login first", "text_ger", "Erste log", "text_fra", "Premier journal")));
        add(3418, f.insertMessage("loan.KnihuJeMozneStahnoutNaTechtoAdresach", Map.of("text_cze", "Knihu je možné stáhnout na následujících adresách", "text_eng", "Login first", "text_ger", "Das Buch kann unter folgenden Adressen heruntergeladen werden", "text_fra", "Le livre peut être téléchargé à l'adresse suivante")));
        add(3419, f.insertMessage("loan.Prezencne", Map.of("text_cze", "Prezenčně", "text_eng", "To study in room", "text_ger", "Präsenzausleihe möglich", "text_fra", "Pour étudier sur place")));
        add(3420, f.insertMessage("file.SouboryVlozitePresunutimSem", Map.of("text_cze", "Soubory vložíte přesunutím sem (drag & drop)", "text_eng", "Drag & drop files here", "text_ger", "Drag & drop dateien hier", "text_fra", "Faites glisser et déposer des fichiers ici")));
        add(3421, f.insertMessage("file.ProDragAndDropPouzijteNovejsiProhlizec", Map.of("text_cze", "Pro funkci drag & drop použijte novější prohlížeč", "text_eng", "For drag & drop use newer browser", "text_ger", "Für drag & drop verwenden neueren browser", "text_fra", "Pour utiliser le glisser-déposer plus récent navigateur")));
        add(3422, f.insertMessage("file.PripojeneSoubory", Map.of("text_cze", "Připojené soubory", "text_eng", "Attached files", "text_ger", "Angehängte dateien", "text_fra", "Fichiers joints")));
        add(3423, f.insertIniKey("EMAIL", "SMTP_USE_SENDER_AND_FROM", 100, "Zda se má při jiném odesilateli (např. pokud čtenář posílá přes Portaro mail knihovně) používat kombinace (tzn. fyzický odesilatel + autor emailu) hlaviček Sender a From misto kombinace From a ReplyTo", 1, "NE", "SCALAR", "VALHOD"));
        String[] stringsBotsUserAgents = {
                "bot.htm",
                "googlebot",
                "surveybot",
                "yahoo.com",
                "seznambot",
                "fulltext.sblog.cz",
                "baiduspider",
                "apache-httpclient",
                "check_http",
                "yandexbot",
                "java/",
                "ezooms",
                "exabot",
                "ahrefsbot",
                "yandex",
                "crawler",
                "python-openid",
                "python-urllib",
                "cliqz.com",
                "microsoft url control",
                "ia_archiver",
                "libwww-perl",
                "feedreader",
                "pycurl",
                "openstat",
                "xpymep.exe",
                "bot.php",
                "mj12bot",
                "wotbox",
                "pagesinventory",
                "ahrefsbot",
                "compspy.com",
                "windows-rss-platform",
                "bubing",
                "zmeu",
                "seznam screenshot-generator",
                "facebook.com",
                "masscan",
                "niki-bot",
                "www.checkprivacy",
                "thefuckie",
                "nutch",
                "www.feedly.com",
                "addthis.com",
                "proxy gear pro",
                "nikto",
                "dotbot",
                "mj12bot",
                "prtg network monitor",
                "spbot",
                "gigablastopensource",
                "metaspider",
                "publiclibraryarchive",
                "visionutils",
                "feedfetcher-google",
                "/usr/bin/"
        };
        List<String> likes = ListUtil.convert(List.of(stringsBotsUserAgents), source -> "'%" + source + "%'");
        String userAgentsCondition = "LOWER(USER_AGENT) like " + StringUtil.listToString(likes, " OR LOWER(USER_AGENT) like ");
        add(3424, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE " + userAgentsCondition)));
        add(3425, f.insertMessage("loan.MoznostiVypujceniNeboStazeni", "Možnosti vypůjčení / stažení", "Lend / download options"));
        add(3426, f.updateIniDefaultValue("OPAC_RECORD", "DetailTabFields", "100^acd; 245; 246; 700^ade; 20; 250; 260; 264; 650; 653; 773^atg; 300; 500; 520; 856; 80^a; 910^b; 911;"));
        add(3427, f.updateIniCustomValue("OPAC_RECORD", "DetailTabFields",
                source -> {
                    source = source.trim();
                    if (!source.endsWith(";")) {
                        source += ";";
                    }
                    if (!source.contains("911;")) {
                        source += " 911;";
                    }
                    return source;
                }
        ));
        add(3428, f.sql("UPDATE OAI_SETY SET SQL_COMMAND = 'SELECT FK_ZAZ, MAX(DATCAS) CAS FROM VYKONY_DOK WHERE FK_VYKON IN (1,2,3,4,5,7,8,9,10,11,12,15,17,18,51,52,53) AND DATCAS >= {dateFrom} AND DATCAS <= {dateTo} GROUP BY FK_ZAZ ORDER BY FK_ZAZ' WHERE SQL_COMMAND = 'SELECT FK_ZAZ, MAX(DATCAS) CAS FROM VYKONY_DOK WHERE FK_VYKON IN (1,2,3,4,5,7,8,9,10,11,12,15,17,18) AND DATCAS >= {dateFrom} AND DATCAS <= {dateTo} GROUP BY FK_ZAZ ORDER BY FK_ZAZ'"));
        add(3429, f.sql("UPDATE OAI_SETY SET SQL_COMMAND = 'SELECT FK_ZAZ, MAX(DATCAS) CAS FROM VYKONY_DOK WHERE FK_DOKFOND = 1 AND FK_VYKON IN (1,2,3,4,5,7,8,9,10,11,12,15,17,18,51,52,53) AND DATCAS >= {dateFrom} AND DATCAS <= {dateTo} GROUP BY FK_ZAZ ORDER BY FK_ZAZ' WHERE SQL_COMMAND = 'SELECT FK_ZAZ, MAX(DATCAS) CAS FROM VYKONY_DOK WHERE FK_DOKFOND = 1 AND FK_VYKON IN (1,2,3,4,5,7,8,9,10,11,12,15,17,18) AND DATCAS >= {dateFrom} AND DATCAS <= {dateTo} GROUP BY FK_ZAZ ORDER BY FK_ZAZ'"));
        add(3430, f.sql("UPDATE OAI_SETY SET SQL_COMMAND = 'SELECT FK_ZAZ, MAX(DATCAS) CAS FROM VYKONY_DOK JOIN DEF_DOKFOND ON (ID_DOKFOND = FK_DOKFOND) WHERE JE_PERIO = 1 AND FK_VYKON IN (1,2,3,4,5,7,8,9,10,11,12,15,17,18,51,52,53) AND DATCAS >= {dateFrom} AND DATCAS <= {dateTo} GROUP BY FK_ZAZ ORDER BY FK_ZAZ' WHERE SQL_COMMAND = 'SELECT FK_ZAZ, MAX(DATCAS) CAS FROM VYKONY_DOK JOIN DEF_DOKFOND ON (ID_DOKFOND = FK_DOKFOND) WHERE JE_PERIO = 1 AND FK_VYKON IN (1,2,3,4,5,7,8,9,10,11,12,15,17,18) AND DATCAS >= {dateFrom} AND DATCAS <= {dateTo} GROUP BY FK_ZAZ ORDER BY FK_ZAZ'"));
        add(3431, f.insertIniKey("OPAC", "BehindReverseProxy", 10, "Zda je katalog za reverzní proxy (např. apache nebo nginx) a tím pádem má používat speciální rozlišování přichozích ip adres atp.", 1, "NE", "SCALAR", "VALHOD"));
        add(3432, f.updateIniDefaultValue("EVERBIS", "EditovatelneVlastnostiExemplare", "PRIR_CISLO; SIGNATURA; BAR_COD; KATEGORIE; TEM_SKUP; ZPUSOB_NABYTI; ZPUSOB_ZALOZENI; BUDOVA; LOKACE; STATUS; CENA; UZIVATEL; ROK_PRIR; POZNAMKA; UCET; CISLO_FAKTURY; POCET_KUSU; PRILOHY"));
        add(3433, f.updateMessage("loan.NelzeVypujcitExemplarJeJizVypujcenNeboRezervovan", "Nelze vypůjčit, exemplář je již jednou vypůjčen/rezervován čtenářem {1}", "Cannot lend, exemplar is already lent or reserved by {1}"));
        add(3434, f.updateMessageKey("loan.VypujckaBylaVracena", "loan.VypujckaCtenareXBylaVracena"));
        add(3435, f.updateMessage("loan.VypujckaCtenareXBylaVracena", "Výpůjčka čtenáře {0} byla vrácena", "Hold was returned"));
        add(3436, f.insertMessage("loan.OpravduProdlouzitCtenarMaProslouRegistraci", "Čtenář má prošlou registraci, opravdu prodloužit?"));
        add(3437, f.insertMessage("loan.OpravduProdlouzitVypujckaJeUpominana", "Výpůjčka je upomínána, opravdu prodloužit?"));
        add(3438, f.insertMessage("loan.OpravduProdlouzitPrekrocenTerminVraceni", "Překročen termín vrácení, opravdu prodloužit?"));
        add(3439, f.insertMessage("loan.OpravduProdlouzitExemplarJeVCirkulaci", "Exemplář je v cirkulaci, opravdu prodloužit"));
        add(3440, f.updateMessageKey("loan.OpravduVypujcitCtenarMaDluhy", "loan.OpravduVypujcitCtenarMaDluhX"));
        add(3441, f.updateMessage("loan.OpravduVypujcitCtenarMaDluhX", "Čtenář má dluh {0}Kč, opravdu provést výpůjčku?", "Reader has debt {0}Kč, really return?"));
        add(3442, f.updateMessageKey("loan.Vypujceno", "loan.VypujcenoDoX"));
        add(3443, f.updateMessage("loan.VypujcenoDoX", "Vypůjčeno do {0}", "Lent to {0}"));
        add(3444, f.updateMessageKey("loan.VypujcenoDoX", "loan.Vypujceno"));
        add(3445, f.updateMessage("loan.Vypujceno", "Vypůjčeno", "Lent"));
        add(3446, f.updateMessage("exemplar.NelzeSmazatStatusNeniNula", "Nelze smazat - status exempláře není 0 (Ve zpracování)", "Cannot delete - status is not zero"));
        add(3447, f.insertMessage("loan.NelzeProdlouzitCtenarMaDluhy", "Nelze prodloužit, čtenář má dluhy", "Cannot renew, reader has debts"));
        add(3448, f.deleteMessage("loan.ExemplarJeKVyzvednutiNaBudove"));
        add(3449, f.deleteMessage("loan.ExemplarJeKObjednaniNaBudove"));
        add(3450, f.deleteMessage("loan.NejdriveZvolteZdaChceteKnihuObjednatNeboRezervovat"));
        add(3451, f.deleteMessage("loan.NemuzetePujcovat"));
        add(3452, f.insertMessage("loan.OpravduProdlouzitCtenarMaDluhy", "Čtenář má dluhy, opravdu prodloužit?", "Reader has debts, really renew?"));
        add(3453, f.insertMessage("loan.NelzeProdlouzitTitulovaBezMoznostiProdlouzeni", "Nelze prodloužit tuto balíkovou výpůjčku", "Cannot renew this bundle loan"));
        add(3454, f.insertMessage("loan.NelzeVypujcitNezadanPocetKusuTituloveVypujcky", "Nelze vypůjčit, nezadán počet kusů titulové výpůjčky"));
        add(3455, f.insertMessage("loan.NelzeVypujcitZaznamExemplareMaNeplatnyFond", "Nelze vypůjčit, neplatný fond záznamu"));
        add(3456, f.insertMessage("loan.Prezencni", "Prezenční", "On-site loan"));
        add(3457, f.insertMessage("loan.JednaSeOPrezencniVypujcku", "Jedná se o prezenční výpůjčku", "This is for in-place study only"));
        add(3458, f.insertMessage("loan.Absencni", "Absenční", "Off-site loan"));
        add(3459, f.deleteMessage("registrace.cisLegOrBarCodInputLabel"));
        add(3460, f.deleteMessage("loan.VypujcitDalsi"));
        add(3461, f.deleteMessage("loan.VratitDalsi"));
        add(3462, f.insertMessage("loan.OpravduVratitZadanVetsiPocetKusuNezJePujceno", "Zadán větši počet kusů než je půjčeno, vrátit všechny?"));
        add(3463, f.deleteMessage("util.smazLog"));
        add(3464, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE CTENARI ADD MOJEID_POSL_UPDATE_TIME DATETIME"},
                new String[]{})));
        add(3465, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE CTENARI ADD MOJEID_JE_VALID BOOLEAN DEFAULT 0"},
                new String[]{})));
        add(3467, new ExceptionCatchingUpdate(f.alterTableColumnType("CTENARI", "OPENID", "STRING_60", null)));
        add(3468, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source
                .replace(".novinkyPrev", ".btn-slider-prev")
                .replace(".novinkyNext", ".btn-slider-next")
                .replace("#novinkyPrev", "#btn-slider-prev")
                .replace("#novinkyNext", "#btn-slider-next")
                .replace(".novinkySlider", ".news-slider")
                .replace(".sliderButton", ".btn-slider")
                .replace(".sliderNovinkyItem", ".news-slider-item")
                .replace(".novinkyVKataloguHeader", ".news-slider-header")));
        add(3469, f.updateFilesContent(List.of(new File(path(customFolderPath, "html", "index.html"))), oldContent -> {
            String newContent = oldContent;

            if (newContent.contains("<div class=\"novinkyVKataloguDiv\"") && !newContent.contains("jp-news-slider")) {
                // nahrazeni <div class="novinkyVKataloguDiv" style="margin-top: 140px;"></div> za <div class="novinkyVKataloguDiv" style="margin-top: 140px;"> <jp-news-slider></jp-news-slider> </div>
                int indexOfDiv = newContent.indexOf("<div class=\"novinkyVKataloguDiv\"");
                int insertingIndex = newContent.indexOf(">", indexOfDiv) + 1;
                StringBuilder sb = new StringBuilder(newContent);
                sb.insert(insertingIndex, "\n        <jp-news-slider></jp-news-slider>\n    ");

                // smazani nasledneho javascriptu
                int scriptDeleteStart = sb.indexOf("<script", insertingIndex);
                int scriptDeleteEnd = sb.indexOf("</script>", insertingIndex) + 9;
                sb.replace(scriptDeleteStart, scriptDeleteEnd, "");
                newContent = sb.toString();
            }

            return newContent;
        }));
        add(3470, f.deleteMessage("commons.NaTutoStrankuNematePovolenPristup"));
        add(3471, f.updateIniDefaultValue("OPAC_SEARCH", "Rezy", "REZS_FOND; REZS_UNINAZEV; REZS_AUTOR; REZS_ROK; REZS_JAZYK; REZS_PREDMET; REZS_DESKRIPTOR; REZS_BUDOVA; REZS_LOKACE; REZS_VYPKAT")); //rok byl az na konci, dame ho vyse
        add(3472, f.updateIniDefaultValue("OPAC_SEARCH", "DefaultneRozbaleneRezy", "REZS_FOND; REZS_UNINAZEV; REZS_AUTOR; REZS_ROK; REZS_PREDMET; REZS_JAZYK"));
        add(3473, f.deleteMessage("statistiky.funkceNeniProhlízecemPodporovana"));
        add(3474, f.updateMessageKey("commons.rocni", "commons.granularity.ByYears"));
        add(3475, f.updateMessageKey("commons.mesicni", "commons.granularity.ByMonths"));
        add(3476, f.updateMessage("commons.granularity.ByYears", "Po rokách", "By years"));
        add(3477, f.updateMessage("commons.granularity.ByMonths", "Po měsících", "By months"));
        add(3478, f.insertMessage("commons.granularity.ByDays", "Po dnech", "By days"));
        add(3479, f.updateMessageKey("commons.granularity.ByYears", "commons.granularity.Year"));
        add(3480, f.updateMessageKey("commons.granularity.ByMonths", "commons.granularity.Month"));
        add(3481, f.updateMessageKey("commons.granularity.ByDays", "commons.granularity.Day"));
        add(3482, f.updateMessage("commons.granularity.Year", "Roky", "Years"));
        add(3483, f.updateMessage("commons.granularity.Month", "Měsíce", "Months"));
        add(3484, f.updateMessage("commons.granularity.Day", "Dny", "Days"));
        add(3485, MultipleUpdate.of(
                f.updateMessage("registrace.full.RegistraceProbehlaUspesne", "Registrace proběhla úspěšně", "Registration completed"),
                f.updateMessageTranslation("registrace.full.RegistraceProbehlaUspesne", "text_ger", "Registrierung war erfolgreich")
        ));
        add(3486, f.updateMessage("loan.KnihuJeMozneStahnoutNaTechtoAdresach", "Kniha je dostupná elektronicky na následujících adresách", "Document is available in electronic form on these links"));
        add(3487, f.updateMessageKey("loan.KnihuJeMozneStahnoutNaTechtoAdresach", "loan.KnihaJeDostupnaElektronickyNaTechtoAdresach"));
        add(3488, f.insertMessage("loan.ElektronickaVerze", "Elektronická verze", "Electronic form"));
        add(3489, f.insertIniKey("OPAC_USER", "PasswordRegex", 170, "Regulární výraz, který musí heslo (pokud se používá) splňovat. Validační hláška se nastavuje v lokalizaci pod ctenar.PasswordRegexMessage", null, "^.{4,}$", "SCALAR", "TEXT"));
        add(3490, f.insertMessage("ctenar.PasswordRegexMessage", "Heslo musí mít minimálně 4 znaky", "Password must have at least 4 characters"));
        add(3491, f.deleteMessage("loan.KnihuJeMozneStahnoutNaTechtoAdresach")); //kvuli huavu, ve kterem jsme se vratili o verzi zpet a je tam tento klic rucne opet pridan
        add(3492, f.deleteMessage("commons.do"));
        add(3493, f.insertMessage("statistiky.Seskupit", "Seskupit", "Group"));
        add(3494, f.updateMessageTranslation("commons.To", "text_eng", "To"));
        add(3495, f.updateMessageTranslation("commons.to", "text_eng", "to"));
        add(3496, f.insertIniKey("OPAC_USER", "MojeIdRequiredProperties", 195, "Údaje z mojeID, které budou povinně předávány. Možné hodnoty jsou: JMENO, PRIJMENI, TRV_MI, TRV_UL, TRV_PSC, EMAIL, TELEFON", 34, "JMENO, PRIJMENI, EMAIL", "LIST", "VALHOD"));
        add(3497, MultipleUpdate.of(f.disableIniTriggers(), new ExceptionCatchingUpdate(f.sql(String.format("UPDATE INI_KEYS SET FK_SEKCE = '%s', ID_KLIC = '%s' WHERE FK_SEKCE = '%s' AND ID_KLIC = '%s'", "EMAIL", "SMTP_REPLYTO_ADDRESS", "EMAIL", "SMTP_REPLAYTO_ADDRESS"))), f.enableIniTriggers()));
        add(3497, MultipleUpdate.of(f.disableIniTriggers(), new ExceptionCatchingUpdate(f.sql(String.format("UPDATE INI_FILE SET FK_SEKCE = '%s', FK_KLIC = '%s' WHERE FK_SEKCE = '%s' AND FK_KLIC = '%s'", "EMAIL", "SMTP_REPLYTO_ADDRESS", "EMAIL", "SMTP_REPLAYTO_ADDRESS"))), f.enableIniTriggers()));
        add(3498, new ExceptionCatchingUpdate(f.insertIniKey("EMAIL", "SMTP_REPLYTO_ADDRESS", 204, "Pokud je nutno nastavit jako adresu odchozí pošty jinou adresu než vaši, můžete nastavit adresu pro odpověď zde", null, "", "SCALAR", "EMAIL")));
        add(3499, f.updateMessageKey("loan.PozadavekBylUspesneOdeslan", "loan.standard.PozadavekBylUspesneOdeslan"));
        add(3500, f.copyMessage("loan.standard.PozadavekBylUspesneOdeslan", "loan.mail.PozadavekBylUspesneOdeslan"));
        add(3501, f.updateIniDefaultValue("OPAC_RECORD", "SearchElsewhere",
                """
                        [\s
                          {
                            "name":"Google Books",\s
                            "template":"http://books.google.com/books?vid=ISBN{isbn}"
                          },\s
                          {
                            "name":"WorldCat",\s
                            "template":"http://www.worldcat.org/isbn/{isbn}"
                          },\s
                          {
                            "name":"Souborný katalog ČR",\s
                            "template":"http://aleph.nkp.cz/F/?func=find-c&ccl_term=isn={isbn}&local_base=SKC"
                          },\s
                          {
                            "name":"Jednotná informační brána",\s
                            "template":"http://www.jib.cz/V/?func=meta-1-check&mode=advanced&find_op_0=AND&find_code_2=ISBN&find_request_2={isbn}&ckbox=CNL03210"
                          },\s
                          {
                            "name":"SFX Rozcestník",\s
                            "template":"http://sfx.jib.cz/sfxlcl3?url_ver=Z39.88-2004&rft_val_fmt=info:ofi/fmt:kev:mtx:book&rft.isbn={isbn}"
                          },\s
                          {
                            "name":"Amazon",\s
                            "template":"http://www.amazon.com/gp/search?index=books&linkCode=qs&keywords={name}"
                          }\s
                        ]"""));
        add(3502, f.insertIniKey("OPAC_SEARCH", "AuthorityIndexPrefixesByFonds", 600, "Nastavení prefixů hledání v autoritních rejstřících. Defaultně se rejstříky zobrazí s prefixem \"A\". Je-li třeba pro daný fond jiný, zde je třeba nastavit ve formátu \"fond(prefix) fond(prefix)\". Pro \"START\" je třeba nastavit prázdný text.", null, null, "SCALAR", "TEXT"));
        add(3503, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source.replace("search/complex-form", "search-form")));
        add(3504, f.sql("UPDATE OPAC_MENU SET TARGET = '/search-form' WHERE TARGET = '/search/simple-form'"));
        add(3505, f.insertIniKey("OPAC_USER", "ReaderRequiredProperties", 380, "Povinné vlastnosti čtenáře (při registraci i editaci), možné hodnoty jsou JMENO,PRIJMENI,USERNAME,PASSWORD,EMAIL,TELEFON,DAT_NAR,TRV_MI,TRV_UL,TRV_PSC", 34, "JMENO,PRIJMENI,USERNAME,PASSWORD,EMAIL,TELEFON,DAT_NAR,TRV_MI,TRV_UL,TRV_PSC", "LIST", "VALHOD"));
        add(3506, f.dbDependentSql(
                new String[] {"ALTER TABLE OPAC_MENU ADD JE_POVOL BOOLEAN DEFAULT 1"},
                new String[]{}));
        add(3507, f.sql("UPDATE OPAC_MENU SET JE_POVOL = 1"));
        add(3508, f.disableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(3509, f.dbDependentSql(
                new String[] {
                        "UPDATE DEF_HLEDRADEK as komplexni SET FK_HLED = 102, PORADI = (SELECT MAX(PORADI) FROM DEF_HLEDRADEK WHERE FK_HLED = 102) + 1 " +
                        "WHERE komplexni.FK_HLED = 103 AND NOT EXISTS (SELECT * FROM DEF_HLEDRADEK jednoduche WHERE jednoduche.FK_HLED = 102 and (jednoduche.VYCETPOLI = komplexni.VYCETPOLI or jednoduche.NAZEV = komplexni.NAZEV))"
                },
                new String[]{}));
        add(3510, f.enableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(3511, f.sql("UPDATE OPAC_MENU SET TARGET = '/search-form' WHERE TEXT = 'hledani.Vyhledavani'"));
        add(3512, f.sql("DELETE FROM OPAC_MENU WHERE TEXT = 'hledani.JednoducheVyhledavani'"));
        add(3513, f.sql("DELETE FROM OPAC_MENU WHERE TEXT = 'hledani.RozsireneVyhledavani'"));
        add(3514, f.insertMessage("registrace.PredregistracePomociMojeId", "Předregistrace s MojeID", "Pre-registration with MojeID"));
        add(3515, f.insertMessage("registrace.ZvolteProNovouRegistraciPomociMojeId", "Zvolte, pokud se chcete nově zaregistrovat a používáte MojeID", "Choose, when you are not registered in library yet and use MojeID"));
        add(3516, f.insertIniValhod(35, 30, "EV_CISLO", "Evidenční číslo"));
        add(3517, f.insertIniKey("OPAC", "ForceHttps", 15, "Zda má Portaro automaticky vynucovat https. Funguje pouze, pokud Portaro není za reverzní proxy a OPAC.URL začíná na https://.", 1, "NE", "SCALAR", "VALHOD"));
        add(3518, f.copyMessage("loan.VypujcitEBook", "loan.flexibooks.Vypujcit"));
        add(3519, f.copyMessage("loan.VypujcitEBook", "loan.ereading.Vypujcit"));
        add(3520, new ExceptionCatchingUpdate(f.addTableColumn("FULLTEXT_SKUPINY", "FK_NADR", "INT_NULL", null)));
        add(3521, new ExceptionCatchingUpdate(f.sql("ALTER TABLE FULLTEXT_SKUPINY ADD CONSTRAINT FK_FULLTEXT_SOUBORY_NADR FOREIGN KEY (FK_NADR) REFERENCES FULLTEXT_SKUPINY (ID_FULLTEXT_SKUPINY) ON DELETE CASCADE")));
        add(3522, f.createTable(
                new RichTableCreation("DEF_KATALOG")
                        .withColumn("ID_KATALOG")
                            .autoIncrementedPrimaryKey("PK_KATALOG", "SEQ_KATALOG", "TRG_KATALOG_BI0")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "GENERATORY NOT NULL")
                            .build()
                        .withColumn("NAZEV")
                            .unique()
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_121 NOT NULL")
                            .build()
                        .withGrantSelect("KATALOG")
                        .withGrantSelect("OPAC")
        ));
        add(3523, f.createTable(
                new RichTableCreation("OPAC_CUSTOM_DIRS")
                        .withColumn("FK_KATALOG")
                            .foreignKey("FK_CUSTOM_DIR_KATALOG", "DEF_KATALOG", "ID_KATALOG")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "GENERATORY")
                            .build()
                        .withColumn("FK_FULLTEXT_SKUPINA")
                            .foreignKey("FK_CUSTOM_DIR_SKUPINA", "FULLTEXT_SKUPINY", "ID_FULLTEXT_SKUPINY")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "INT_NULL")
                            .build()
                        .withGrantSelect("KATALOG")
                        .withGrantSelect("OPAC")
        ));
        add(3524, f.sql(new FirebirdUniqueKeyGenerator().convert(UniqueKeyCreation.ofMultipleColumns("UNQ_CUSTOM_DIRS_KAT_SKUP", "OPAC_CUSTOM_DIRS", List.of("FK_KATALOG", "FK_FULLTEXT_SKUPINA")))));
        add(3525, f.sql("INSERT INTO FULLTEXT_SKUPINY (NAZEV) VALUES ('portaro-custom-root')"));
        add(3526, () -> {
            int rootDirectoryId = jdbcTemplate.queryForObject("SELECT ID_FULLTEXT_SKUPINY FROM FULLTEXT_SKUPINY WHERE NAZEV = 'portaro-custom-root'", Integer.class);
            jdbcTemplate.update("INSERT INTO OPAC_CUSTOM_DIRS (FK_KATALOG, FK_FULLTEXT_SKUPINA) VALUES (NULL, "+rootDirectoryId+")");
        });
        add(2527, () -> {
            String[] filePaths = {
                    FileUtils.path(customFolderPath,"design","style.css"),
                    FileUtils.path(customFolderPath,"html","all.html"),
                    FileUtils.path(customFolderPath,"html","index.html"),
                    FileUtils.path(customFolderPath,"html","logo.html"),
                    FileUtils.path(customFolderPath,"html","logo-footer.html"),
                    FileUtils.path(customFolderPath,"html","sub-search.html")
            };
            for (String filePath : filePaths) {
                File file = new File(filePath);
                if (!file.exists()) {
                    log.warn("Custom soubor ({}) neexistuje, vytvarim ho.", filePath);
                    try {
                        file.getParentFile().mkdirs(); //vytvoreni adresarove struktury
                        file.createNewFile();
                    } catch (IOException ex) {
                        log.error("Chyba pri vytvareni souboru a jeho adresarove struktury", ex);
                    }
                }
            }
        });
        add(3528, f.updateCustomFilesContent(customFolderPath, FileUtils.TEXT_FILE_EXTENSIONS, source -> source.replace("#parse(", "#parseTemplate(")));
        add(3529, () -> {
            File dir = new File(customFolderPath);
            File[] files = dir.listFiles((dir1, name) -> !name.equals("config") && !name.equals("settings.properties"));
            Long rootDirectoryId = jdbcTemplate.queryForObject("SELECT ID_FULLTEXT_SKUPINY FROM FULLTEXT_SKUPINY WHERE NAZEV = 'portaro-custom-root'", Long.class);
            Assert.notNull(rootDirectoryId, "Custom root directory does not exist");

            Assert.notNull(rootDirectoryId, "Trying to clear all subdirectories of portaro-custom-root from FULLTEXT_SKUPINY");
            new ExceptionCatchingUpdate(f.sql("delete from fulltext_skupiny where fk_nadr = %s".formatted(rootDirectoryId))).run();

            ff.saveFilesRecursively(files, rootDirectoryId);
        });
        add(3530, f.sql("INSERT INTO DEF_KATALOG (ID_KATALOG, NAZEV) VALUES (NULL, 'Celá databáze')"));
        add(3531, f.sql("UPDATE OPAC_CUSTOM_DIRS SET FK_KATALOG = 1"));
        add(3532, f.updateMessageKey("exemplar.fulltext", "exemplar.fileDirectory"));
        add(3533, f.updateIniCustomValue("OPAC_RECORD", "TemplateDocumentDetailRightPanel", source -> source.trim().replace("#parse(", "#parseTemplate(")));
        add(3533, f.updateIniCustomValue("OPAC_RECORD", "TemplateDocumentDetailUnderTitle", source -> source.trim().replace("#parse(", "#parseTemplate(")));
        add(3533, f.updateIniCustomValue("OPAC_RECORD", "TemplateDocumentDetailUnderCover", source -> source.trim().replace("#parse(", "#parseTemplate(")));
        add(3533, f.updateIniCustomValue("OPAC_RECORD", "TemplateAuthorityDetailUnderTitle", source -> source.trim().replace("#parse(", "#parseTemplate(")));
        add(3533, f.updateIniCustomValue("OPAC_SEARCH", "TemplateDocumentSearchMain", source -> source.trim().replace("#parse(", "#parseTemplate(")));
        add(3533, f.updateIniCustomValue("OPAC_SEARCH", "TemplateDocumentSearchMore", source -> source.trim().replace("#parse(", "#parseTemplate(")));
        add(3534, () -> {
            File srcFile = new File(FileUtils.path(customFolderPath,"config"), "nastaveni.properties");
            if (srcFile.exists()) {
                File destFile = new File(customFolderPath, "settings.properties");
                try {
                    org.apache.commons.io.FileUtils.moveFile(srcFile, destFile);
                } catch (IOException e) {
                    throw new RuntimeException("Cannot move file " + srcFile + " to " + destFile, e);
                }
                log.warn("Soubor {} presunut do {}", srcFile, destFile);
            } else {
                log.warn("Soubor {} neexistuje, neni co presouvat", srcFile);
            }
        });
        add(3535, () -> {
            Collection<File> allFiles = org.apache.commons.io.FileUtils.listFiles(new File(customFolderPath), null, true);

            log.warn("Nalezeno {} souboru k zazalohovani: {}", allFiles.size(), allFiles);

            File zipFile = new File(customFolderPath, "custom-backup.zip");
            FileOutputStream zipFileWrite;
            try {
                zipFileWrite = new FileOutputStream(zipFile);
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            }

            new FileZipperImpl().zipFiles(allFiles, zipFileWrite);

            log.warn("Custom adresar zazalohovan do {}", zipFile);
        });
        add(3536, () -> {
            File dir = new File(customFolderPath);

            File[] files = dir.listFiles((dir12, name) -> !name.equals("settings.properties") && !name.equals("custom-backup.zip"));

            for (File fileOrDirectory : files) {
                try {
                    org.apache.commons.io.FileUtils.forceDelete(fileOrDirectory);
                } catch (IOException ex) {
                    log.error("Chyba pri mazani souboru {}", fileOrDirectory, ex);
                }
            }

            log.warn("Custom soubory smazany");
        });
        add(3537, f.updateCustomFilesContent(customFolderPath, new String[] {"properties"}, source -> source
                .replace("appserverUrl=", "appserver.url=")
                .replace("databaseUsername=", "database.username=")
                .replace("databasePassword=", "database.password=")
                .replace("databaseHost=", "database.host=")
                .replace("databasePort=", "database.port=")
                .replace("databaseFile=", "database.file=")
                .replace("httpProxyUrl=", "http.proxy.url=")
                .replace("httpProxyAuth=", "http.proxy.auth=")
                .replace("httpProxyUsername=", "http.proxy.username=")
                .replace("httpProxyPassword=", "http.proxy.password=")));
        add(3538, f.insertIniKey("OPAC_LOAN", "SentReservationsCancelling", 110, "Zda čtenáři mohou rušit i odeslané rezervace", 1, "NE", "SCALAR", "VALHOD"));
        add(3539, f.updateMessage("loan.KnihaJeDostupnaElektronickyNaTechtoAdresach", "Kniha je dostupná elektronicky pod následujícími odkazy", "Document is available in electronic form on these links"));
        add(3540, f.insertIniKey("OPAC_SEARCH", "BasicModeSearchFieldsCount", 80, "Počet zobrazených polí v základním režimu formuláře pro vyhledávání", null, "10", "SCALAR", "NUMBER"));
        add(3541, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE fulltext_soubory ALTER COLUMN filename TYPE UTF_121"},
                new String[]{})));
        add(3542, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE fulltext_soubory ALTER COLUMN popis TYPE UTF_121"},
                new String[]{})));
        add(3543, f.updateMessageKey("sdi.ZasilaniXNaY", "sdi.ZasilaniX"));
        add(3544, f.updateMessage("sdi.ZasilaniX", "Zasílání <strong>{0}</strong>", "Sending <strong>{0}</strong>"));
        add(3545, f.addTableColumn("DB_STRUCT", "VERSION_OPAC_1_0", "INT_NONULL", null));
        add(3546, f.addTableColumn("DB_STRUCT", "VERSION_OPAC_2_0", "INT_NONULL", null));
        add(3547, f.deleteMessage("statistiky.rocniStatistiky"));
        add(3548, f.deleteMessage("statistiky.mesicniStatistiky"));
        add(3549, f.deleteMessage("hledani.JednoducheVyhledavani"));
        add(3550, f.deleteMessage("hledani.vyhledavanyVyraz"));
        add(3551, f.deleteMessage("hledani.zadavatVyberem"));
        add(3552, f.deleteMessage("commons.RocnikX"));
        add(3553, f.deleteMessage("commons.CisloPerX"));
        add(3554, f.deleteMessage("commons.NapovedaKeKatalogu"));
        add(3555, f.deleteIniKeyIncludingCustomValue("OPAC", "ZapnutoHttps"));
        add(3556, f.insertIniKey("OPAC_LOAN", "RenewalOfDebtorsLoans", 95, "Zda čtenáři mohou prodlužovat výpůjčky i když mají dluhy", 1, "ANO", "SCALAR", "VALHOD"));
        add(3557, ff.updateCustomDbFiles("html", "sub-search.html", source -> source.replace("REZS_TYP_FULLTEXT%3A&quot;DEF_TYP_FULLTEXT.POPIS.6&quot;", "REZS_TYP_FULLTEXT%3A%22DEF_TYP_FULLTEXT.POPIS.6%22")));
        add(3558, ff.updateCustomDbFiles("html", "sub-search.html", source -> source.replace("Naskenované%20dokumenty", "Naskenovan%C3%A9%20dokumenty")));
        add(3559, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE fulltext_soubory ALTER COLUMN filename TYPE UTF_121"},
                new String[]{})));
        add(3560, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE fulltext_soubory ALTER COLUMN popis TYPE UTF_121"},
                new String[]{})));
        add(3561, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE fulltext_soubory ALTER COLUMN zdroj TYPE UTF_512"},
                new String[]{})));
        add(3562, new ExceptionCatchingUpdate(f.dbDependentSql(
                new String[] {"ALTER TABLE fulltext_soubory ALTER COLUMN cesta TYPE UTF_512"},
                new String[]{})));
        add(3563, f.grantSelect("OBALKYKNIH", "opac"));
        add(3564, f.insertIniKey("OPAC", "Services", 140, "Zapnuté služby, které má Portaro automaticky spouštět", null, "ObalkyKnih,GoogleBooks,OpenLibrary", "SCALAR", "TEXT"));
        add(3565, f.disableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(3566, f.disableTrigger("TRG_ZAL_DEF_HLED_AIUD9"));
        add(3567, f.sql("DELETE FROM DEF_HLEDRADEK WHERE FK_HLED = 103"));
        add(3568, f.sql("DELETE FROM DEF_HLED WHERE ID_HLED = 103"));
        add(3569, f.enableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(3570, f.enableTrigger("TRG_ZAL_DEF_HLED_AIUD9"));
        add(3571, f.dropForeignKey("OPAC_CUSTOM_DIRS", "FK_CUSTOM_DIR_KATALOG"));
        add(3572, f.dropUniqueConstraint("OPAC_CUSTOM_DIRS", "UNQ_CUSTOM_DIRS_KAT_SKUP"));
        add(3573, f.dropColumn("OPAC_CUSTOM_DIRS", "FK_KATALOG"));
        add(3574, f.dropTrigger("TRG_KATALOG_BI0", "unsupported"));
        add(3575, f.dropTable("DEF_KATALOG"));
        add(3576, f.dropSequence("SEQ_KATALOG"));
        add(3577, f.addTableColumn("OPAC_FILTERS", "POZNAMKA", "UTF_512", null));
        add(3578, f.sql("UPDATE INI_KEYS SET PORADI = 5, DATOVY_TYP = 'TEXT' WHERE FK_SEKCE = 'OPAC' AND ID_KLIC = 'URL'"));
        add(3579, f.insertMessage("payment.VybertePoplatkyKZaplaceni", "Vyberte prosím poplatky, které chcete zaplatit", "Please, choose fees to pay"));
        add(3580, f.insertMessage("payment.Zaplatit", "Zaplatit", "Pay"));
        add(3581, f.sql(FirebirdSequenceIdTriggerGenerator.createWithoutNullCondition().convert(new IdGeneratorCreation("TRG_FULLTEXT_SKUPINY_BI0", "FULLTEXT_SKUPINY", "id_fulltext_skupiny", "SEQ_FULLTEXT_SKUPINY"))));
        add(3582, f.addTableColumn("DB_STRUCT", "VERSION_MIG_2_0", "INT_NONULL", null));
        add(3583, f.sql("UPDATE DB_STRUCT SET VERSION_MIG_2_0 = 0"));
        add(3584, f.sql("UPDATE DB_STRUCT SET VERSION_OPAC_2_0 = 0"));
        add(3585, f.updateMessage("hledani.facet.REZS_DESKRIPTOR", "Žánr (druh dokumentu)", "Genre"));
        add(3586, f.insertMessage("loan.NelzeProdlouzitDosloByKeZkraceni", "Nelze prodloužit, výpůjčka je aktuálně delší než by bylo prodloužení", "Cannot renew, loan is longer"));
        add(3587, f.createTable(
                new RichTableCreation("FILTER_ROW")
                        .withColumn("ID_FILTER_ROW")
                            .primaryKey("PK_FILTER_ROW")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "GENERATORY NOT NULL")
                            .build()
                        .withColumn("FK_FILTER_SUBJ")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "GENERATORY NOT NULL")
                            .build()
                        .withColumn("PORADI")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "GENERATORY NOT NULL")
                            .build()
                        .withColumn("ALLOW")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "SMALL_NOTNULL")
                            .build()
                        .withColumn("DENY_MESSAGE")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_121")
                            .build()
                        .withColumn("POPIS")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_512")
                            .build()
                        .withGrantAll("KATALOG")
                        .withGrantAll("OPAC")
        ));
        add(3588, f.createTable(
                new RichTableCreation("FILTER_MATCHER")
                        .withColumn("ID_FILTER_MATCHER")
                            .primaryKey("PK_FILTER_MATCHER")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "GENERATORY NOT NULL")
                            .build()
                        .withColumn("FK_FILTER_ROW")
                            .foreignKey("FK_FILTER_MATCHER_ROW", "FILTER_ROW", "ID_FILTER_ROW")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "GENERATORY NOT NULL")
                            .build()
                        .withColumn("TYP")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_121 NOT NULL")
                            .build()
                        .withColumn("NEGATED")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "BOOLEAN DEFAULT 0")
                            .build()
                        .withColumn("VAL")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_250")
                            .build()
                        .withColumn("POPIS")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_512")
                            .build()
                        .withGrantAll("KATALOG")
                        .withGrantAll("OPAC")
        ));
        add(3589, f.sql("update opac_filters set typ = 'AlwaysMatch' where typ = 'Static'"));
        add(3590, f.sql("INSERT INTO FILTER_ROW (ID_FILTER_ROW, FK_FILTER_SUBJ, PORADI, ALLOW, DENY_MESSAGE, POPIS) VALUES (1, 1, 1, 2, null, 'Povoleni zobrazeni souboru')"));
        add(3591, f.sql("INSERT INTO FILTER_ROW (ID_FILTER_ROW, FK_FILTER_SUBJ, PORADI, ALLOW, DENY_MESSAGE, POPIS) VALUES (2, 2, 1, 2, null, 'Povoleni tisku souboru')"));
        add(3592, f.sql("INSERT INTO FILTER_MATCHER (ID_FILTER_MATCHER, FK_FILTER_ROW, TYP, NEGATED, VAL, POPIS) VALUES (1, 1, 'AlwaysMatch', 0, null, 'Zachyceni vsech souboru pro zobrazeni')"));
        add(3593, f.sql("INSERT INTO FILTER_MATCHER (ID_FILTER_MATCHER, FK_FILTER_ROW, TYP, NEGATED, VAL, POPIS) VALUES (2, 2, 'AlwaysMatch', 0, null, 'Zachyceni vsech souboru pro tisk')"));
        add(3594, f.sql("update opac_filters set typ = 'FileCategory' where typ = 'FileType'"));
        add(3595, f.grantSelect("DEF_FULLTEXT_PRISTUP", "opac"));
        add(3596, ff.updateCustomDbFiles("html", "index.html", source -> source.replaceAll("\n\\s*<script type=\"text/javascript\">[\\S\\s]*zaregistrujNaseptavac[\\S\\s]*</script>", "")));
        add(3600, ff.updateCustomDbFiles("html", "index.html", source -> source.replaceAll("<input type=\"text\" class=\"form-control index-search-input\" name=\"q\" value=\"\" placeholder=\"#loc\\('hledani.hledatKnihyAutoryTemata'\\)\" id=\"searchStringInputVIndexu\"/>", "<jp-global-search-input class=\"form-control index-search-input\" name=\"q\" placeholder=\"#loc('hledani.hledatKnihyAutoryTemata')\" id=\"searchStringInputVIndexu\"></jp-global-search-input>")));
        add(3601, new ExceptionCatchingUpdate(f.sql(
                """
                        CREATE OR ALTER VIEW VIEW_HLEDANI_DOK(
                            ID_HLED,
                            CITAC,
                            ID_ZAZ,
                            FK_ZAZ,
                            NAZEV,
                            AUTOR,
                            NAKL,
                            ISBN,
                            ROK_OD,
                            ROK_DO,
                            STATUS4,
                            FK_DOKFOND,
                            TRIDNAZ,
                            FLATNAZ,
                            FK_UZIV,
                            DATCAS)
                        AS
                        select
                        ID_HLED,
                        CITAC,
                        ID_ZAZ,
                        cis_ZAZ,
                        kat1_4.NAZEV,
                        kat1_4.AUTOR,
                        kat1_4.NAKL,
                        kat1_4.ISBN,
                        kat1_4.ROK_OD,
                        kat1_4.ROK_DO,
                        kat1_4.STATUS4,
                        kat1_4.FK_DOKFOND,
                        kat1_4.TRIDNAZ,\s
                        kat1_4.flatnaz,
                        kat1_4.FK_UZIV,
                        kat1_4.DATCAS
                          from hledani
                                left join kat1_4 on cis_zaz = kat1_4.fk_zaz
                        where id_zaz is not null"""
        )));
        add(3602, f.sql("insert into opac_search_keys (nazev, klic, datatype) values ('FOND', 'REZS_FOND', 'FOND')"));
        add(3604, f.disableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(3606, f.disableTrigger("TRG_ZAL_DEF_HLED_AIUD9"));
        add(3608, f.sql("update def_hledradek set vycetpoli = 'content_lang' where vycetpoli = 'P41' and fk_hled = 102"));
        add(3609, f.sql("insert into opac_search_keys (nazev, klic, datatype) values ('content_lang', 'REZS_JAZYK', 'VAL63K')"));
        add(3610, f.sql("update def_hledradek set vycetpoli = 'origin_country' where vycetpoli = 'P44' and fk_hled = 102"));
        add(3611, f.sql("insert into opac_search_keys (nazev, klic, datatype) values ('origin_country', 'P44', 'VAL44A')"));
        add(3616, f.enableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(3618, f.enableTrigger("TRG_ZAL_DEF_HLED_AIUD9"));
        add(3620, f.insertMessage("login.PrihlasitSamlem", "Přihlášení pomocí centrálního systému", "Login with central system"));
        add(3622, f.insertIniKey("OPAC", "ListenIp", 12, "IP adresa, na ktere ma katalog poslouchat, vychozi jsou vsechny (jen pro v2.0+)", null, null, "SCALAR", "TEXT"));
        add(3630, f.createTable(
                new RichTableCreation("MATCHER")
                        .withColumn("ID_MATCHER")
                            .primaryKey("PK_MATCHER")
                            .autoIncrementedPrimaryKey("PK_MATCHER", "SEQ_MATCHER", "TRG_MATCHER_BI0")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "GENERATORY NOT NULL")
                            .build()
                        .withColumn("FK_MATCHER_TYPE")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_121 NOT NULL")
                            .build()
                        .withColumn("NEGATED")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "BOOLEAN DEFAULT 0")
                            .build()
                        .withColumn("VAL")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_250")
                            .build()
                        .withColumn("NOTE")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_512")
                            .build()
                        .withGrantAll("KATALOG")
                        .withGrantAll("OPAC")
        ));
        add(3634, f.addTableColumn("matcher", "temp_fk_filter_matcher", "generatory", null));
        add(3636, f.sql(
                "insert into matcher (fk_matcher_type, negated, val, note, temp_fk_filter_matcher) \n" +
                        "select typ, negated, val, popis, id_filter_matcher from filter_matcher"));
        add(3638, f.addTableColumn("filter_matcher", "fk_matcher", "generatory not null", null));
        add(3640, f.sql("update filter_matcher curr set fk_matcher = (select id_matcher from matcher m where m.temp_fk_filter_matcher = curr.id_filter_matcher)"));
        add(3642, f.sql(new StandardForeignKeyGenerator().convert(new ForeignKeyCreation("fk_filter_matcher_matcher", "filter_matcher", "fk_matcher", "matcher", "id_matcher", ForeignKeyCreation.OnDelete.CASCADE))));
        add(3644, f.dropColumn("matcher", "temp_fk_filter_matcher"));
        add(3646, f.dropColumn("filter_matcher", "typ"));
        add(3648, f.dropColumn("filter_matcher", "negated"));
        add(3650, f.dropColumn("filter_matcher", "val"));
        add(3652, f.dropColumn("filter_matcher", "popis"));
        add(3656, f.sql("delete from opac_search_keys where nazev = 'FOND'"));
        add(3660, f.sql("insert into opac_search_keys (nazev, klic, datatype) values ('FOND', 'REZS_FOND', 'FOND')"));
        add(3664, f.insertIniKey("OPAC_LOAN", "ProcessedOrdersCancelling", 108, "Zda čtenáři mohou rušit i vyřízené objednávky", 1, "NE", "SCALAR", "VALHOD"));
        add(3668, f.sql("update matcher set fk_matcher_type = 'Always' where fk_matcher_type = 'AlwaysMatch'"));
        add(3670, f.sql("update matcher set fk_matcher_type = 'UserReaderCategory' where fk_matcher_type = 'ReaderCategory'"));
        add(3675, f.updateIniDefaultValue("OPAC_SEARCH", "VzorGlobalniHledaniPokus2", "and(P245:?~^5 OR P245:?~^4 OR P650:?~^2 OR P653:?~^2 OR PALL:?~ OR PFULLTEXT:?~)"));
        add(3680, f.insertMessage("loan.type.Standard", "Std.výpůjčka", "Std.loan"));
        add(3682, f.insertMessage("loan.type.Title", "Množstevní výpůjčka", "Bundle loan"));
        add(3684, f.insertMessage("loan.type.Mvs", "MVS výpůjčka", "ILL loan"));
        add(3686, f.insertMessage("loan.type.Extenal", "Externí výpůjčka", "External loan"));
        add(3688, f.updateMessage("loan.NelzeProdlouzitTitulovaBezMoznostiProdlouzeni", "Nelze prodloužit tuto množstevní výpůjčku", "Cannot renew this bundle loan"));
        add(3700, f.createTable(
                new RichTableCreation("OAI_SET")
                        .withColumn("ID_OAI_SET")
                            .primaryKey("PK_OAI_SET")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_121 NOT NULL")
                            .build()
                        .withColumn("NAZEV")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_121 NOT NULL")
                            .build()
                        .withColumn("SQL_COMMAND")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_4000 NOT NULL")
                            .build()
                        .withGrantSelect("KATALOG")
                        .withGrantAll("OPAC")
        ));
        add(3720, f.sql("insert into oai_set (id_oai_set, nazev, sql_command) select id_oai_set || '', nazev, sql_command from oai_sety"));
        add(3730, f.dropTable("oai_sety"));
        add(3740, f.sql("insert into oai_set (id_oai_set, nazev, sql_command) select lower(nazev), nazev, sql_command from oai_set where id_oai_set = '0'"));
        add(3750, f.sql("insert into oai_set (id_oai_set, nazev, sql_command) select lower(nazev), nazev, sql_command from oai_set where id_oai_set = '1'"));
        add(3760, f.sql("insert into oai_set (id_oai_set, nazev, sql_command) select lower(nazev), nazev, sql_command from oai_set where id_oai_set = '2'"));
        add(3770, f.sql("insert into oai_set (id_oai_set, nazev, sql_command) select lower(nazev), nazev, sql_command from oai_set where id_oai_set = '3'"));
        add(3780, f.sql("update oai_set set nazev = 'Default - zastarale, pouzijte set default' where id_oai_set = '0'"));
        add(3790, f.sql("update oai_set set nazev = 'CASLIN - zastarale, pouzijte set caslin' where id_oai_set = '1'"));
        add(3800, f.sql("update oai_set set nazev = 'Monografie - zastarale, pouzijte set monografie' where id_oai_set = '2'"));
        add(3810, f.sql("update oai_set set nazev = 'Periodika - zastarale, pouzijte set periodika' where id_oai_set = '3'"));
        add(3820, new ExceptionCatchingUpdate(f.dropSequence("SEQ_ID_OPAC_ULOZENE_DOTAZY")));
        add(3830, f.insertIniKey("OPAC_PAYMENT", "GpwebpayUrl", 200, "GP webpay order url", null, "https://3dsecure.gpwebpay.com/pgw/order.do", "SCALAR", "TEXT"));
        add(3840, f.insertIniKey("OPAC_PAYMENT", "GpwebpayMerchantNumber", 210, "GP webpay merchant number", null, null, "SCALAR", "TEXT"));
        add(3850, f.createTable(
                new RichTableCreation("PLATBY_PLACENI_GPWEBPAY")
                        .withColumn("FK_PLATBY_PLACENI")
                            .primaryKey("PK_PLATBY_PLACENI_GPWEBPAY")
                            .foreignKey("FK_PLATBY_PLACENI_GPWP_PLAC", "PLATBY_PLACENI", "ID_PLATBY_PLACENI")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "GENERATORY NOT NULL")
                            .build()
                        .withColumn("GPWEBPAY_ORDER_NUMBER")
                            .datatype(DatabaseConnectionSettings.DBTYPE_FIREBIRD, "UTF_121 NOT NULL")
                            .build()
                        .withGrantSelect("KATALOG")
                        .withGrantAll("OPAC")
        ));
        add(3860, f.insertIniKey("OPAC_PAYMENT", "GpwebpayEnabled", 190, "Zapíná platební bránu GP webpay", 1, "NE", "SCALAR", "VALHOD"));
        add(3870, f.insertIniKey("OPAC_PAYMENT", "GopayEnabled", 90, "Zapíná platební bránu GoPay", 1, "NE", "SCALAR", "VALHOD"));
        add(3880, f.addTableColumn("PLATBY_PLACENI_GPWEBPAY", "GATEWAY_URL", "UTF_2000", null));
        add(3890, f.addTableColumn("PLATBY_PLACENI_GPWEBPAY", "RESULT_PRCODE", "SMALL_NULL", null));
        add(3910, f.addTableColumn("PLATBY_PLACENI_GPWEBPAY", "RESULT_SRCODE", "SMALL_NULL", null));
        add(3920, f.addTableColumn("PLATBY_PLACENI_GPWEBPAY", "RESULT_TEXT", "UTF_250", null));
        add(3940, f.insertIniKey("OPAC", "AcmeChallengeKey", 400, "Klic pro overeni domeny v ACME (lets encrypt)", null, null, "SCALAR", "TEXT"));
        add(3950, f.insertMessage("hledani.ExpertniRezim", "Expertní vyhledávání", "Expert mode"));
        add(3960, f.grantSelect("DEF_AUTINDIK", "opac"));
        add(3970, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE lower(USER_AGENT) like 'wget%'")));
        add(3980, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'Mozilla/5.0+(compatible; UptimeRobot/2.0; http://www.uptimerobot.com/)'")));
        add(3990, f.addTableColumn("PLATBY_PLACENI_GPWEBPAY", "SOAP_RESULT_STATE", "SMALL_NULL", null));
        add(4000, f.addTableColumn("PLATBY_PLACENI_GPWEBPAY", "SOAP_RESULT_STATUS", "UTF_121", null));
        add(4010, f.addTableColumn("PLATBY_PLACENI_GPWEBPAY", "SOAP_RESULT_SUBSTATUS", "UTF_121", null));
        add(4020, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE lower(USER_AGENT) like '%qwantify%'")));
        add(4030, f.sql("insert into oai_set (id_oai_set, nazev, sql_command) select 'cpk', 'CPK', sql_command from oai_set where id_oai_set = 'monografie'"));
        add(4040, f.insertMessage("user.prefs.key.1", "Prohledávaná oddělení", "Departments to search in"));
        add(4050, f.insertMessage("user.prefs.key.4", "Mazání historie výpůjček po", "Delete loan history after"));
        add(4060, f.insertMessage("user.prefs.key.6", "Posílání hromadných emailů", "Bulk emails sending"));
        add(4070, f.insertMessage("commons.Change", "Změnit", "Change"));
        add(4080, f.insertMessage("commons.SetDefault", "Nastavit výchozí", "Set default"));
        add(4090, f.insertMessage("commons.NotSelected", "Nevybráno", "Not selected"));
        add(4100, f.insertMessage("user.EditUserData", "Upravit uživ. údaje", "Edit user data"));
        add(4110, f.insertMessage("user.UserData", "Uživatelské údaje", "User data"));
        add(4120, f.insertMessage("user.PersonalData", "Osobní údaje", "Personal data"));
        add(4130, f.insertMessage("user.InstitutionData", "Údaje o instituci", "Institution data"));
        add(4140, f.insertMessage("user.LibraryData", "Údaje o knihovně", "Library data"));
        add(4150, f.insertMessage("user.ReaderData", "Čtenářské údaje", "Reader data"));
        add(4160, f.insertMessage("user.ReaderAccount", "Čtenářský účet", "Reader account"));
        add(4170, f.insertMessage("user.AddReaderAccount", "Přidat čtenářský účet", "Přidat čtenářský účet"));
        add(4180, f.insertMessage("user.LibrarianAccount", "Knihovnický účet", "Librarian account"));
        add(4190, f.insertMessage("user.AddLibrarianAccount", "Přidat knihovnický účet", "Add librarian account"));
        add(4200, f.insertMessage("commons.Pridat", "Přidat", "Add"));
        add(4210, f.insertMessage("commons.Contacts", "Kontakty", "Contacts"));
        add(4220, f.insertMessage("commons.address.permanent", "trvalá", "permanent"));
        add(4230, f.insertMessage("commons.address.postal", "poštovní", "postal"));
        add(4240, f.insertMessage("user.Reader", "Čtenář", "Reader"));
        add(4250, f.insertMessage("user.Librarian", "Knihovník", "Librarian"));
        add(4260, f.insertMessage("mediaviewer.mail.Subject", "Digitální dokumenty", "Digital documents"));
        add(4270, f.updateMessage("loan.ereading.DodatecneInformace", "Délka výpůjčky je 31 dní.", "Loan period is 31 days"));
        add(4280, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Durée de pret est de 23 jours' WHERE ID_LOKALIZACE = 'loan.ereading.DodatecneInformace'"));
        add(4290, f.updateMessage("loan.ereading.ZarizeniEreading", "e-ink čteček eReading.cz eReading.cz 4 Touch Light, C-TECH Lexis (EBR-61), Energy eReader Pro HD, inkBOOK Classic 2, inkBOOK Prime", "e-ink reader eReading.cz eReading.cz 4 Touch Light, C-TECH Lexis (EBR-61), Energy eReader Pro HD, inkBOOK Classic 2, inkBOOK Prime"));
        add(4300, f.sql("UPDATE LOKALIZACE SET TEXT_FRA = 'Lecteur numérique e-ink eReading.cz eReading.cz 4 Touch Light, C-TECH Lexis (EBR-61), Energy eReader Pro HD, inkBOOK Classic 2, inkBOOK Prime' WHERE ID_LOKALIZACE = 'loan.ereading.ZarizeniEreading'"));
        add(4310, new ExceptionCatchingUpdate(f.sql("DELETE FROM OPAC_LOG_SESSIONS WHERE USER_AGENT = 'ReactorNetty/0.7.8.RELEASE'")));
        add(4320, f.insertMessage("hledani.FillToForm", "Vyplnit do formuláře", "Fill to form"));
        add(4330, f.insertMessage("statistiky.media-viewer", "Digitální knihovna", "Digital library"));
        add(4340, f.insertMessage("statistiky.STATISTIKY", "Statistiky", "Stats"));
        add(4350, f.insertMessage("statistiky.user-preferences", "Uživatelská nastavení", "User preferences"));
        add(4360, new ExceptionCatchingUpdate(f.copyMessage("statistiky.KOMPLEXNI_HLEDANI", "statistiky.SEARCH_FORM")));
        add(4370, f.updateIniDefaultValue("OPAC_RECORD", "SearchElsewhere",
                """
                        [\s
                          {
                            "name":"Google Books",\s
                            "template":"http://books.google.com/books?vid=ISBN{isbn}"
                          },\s
                          {
                            "name":"WorldCat",\s
                            "template":"http://www.worldcat.org/isbn/{isbn}"
                          },\s
                          {
                            "name":"Souborný katalog ČR",\s
                            "template":"http://aleph.nkp.cz/F/?func=find-c&ccl_term=isn={isbn}&local_base=SKC"
                          },\s
                          {
                            "name":"SFX Rozcestník",\s
                            "template":"http://sfx.jib.cz/sfxlcl3?url_ver=Z39.88-2004&rft_val_fmt=info:ofi/fmt:kev:mtx:book&rft.isbn={isbn}"
                          },\s
                          {
                            "name":"Amazon",\s
                            "template":"http://www.amazon.com/gp/search?index=books&linkCode=qs&keywords={name}"
                          }\s
                        ]"""));
        add(4380, f.updateIniDefaultValue("OPAC", "VnitrniIPAdresy", "127.0.0.1,0:0:0:0:0:0:0:1"));
        add(4400, f.disableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
        add(4410, f.sql("update def_hledradek set vycetpoli = 'isbn' where fk_hled = 102 and vycetpoli = 'documentIsbn'"));
        add(4420, f.sql("update def_hledradek set vycetpoli = 'issn' where fk_hled = 102 and vycetpoli = 'documentIssn'"));
        add(4430, f.sql("update def_hledradek set vycetpoli = 'isbnOrIssn' where fk_hled = 102 and vycetpoli = 'documentIsbnOrIssn'"));
        add(4440, f.enableTrigger("TRG_ZAL_DEF_HLEDRADEK_AIUD9"));
    }

}
