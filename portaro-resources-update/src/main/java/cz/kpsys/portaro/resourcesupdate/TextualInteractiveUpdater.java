package cz.kpsys.portaro.resourcesupdate;

import cz.kpsys.portaro.commons.object.Settable;
import org.beryx.textio.TextIO;
import org.beryx.textio.TextTerminal;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

public class TextualInteractiveUpdater {

    private final ResourcesUpdater updater;
    private final Settable<Integer> currentVersionSaver;
    private final Consumer<String> repairRunner;
    private final TextIO textIO;
    private final List<Question> questions;
    private final Map<Class<? extends Exception>, Question> exceptionQuestions;

    public TextualInteractiveUpdater(TextTerminal<?> textTerminal, ResourcesUpdater updater, Settable<Integer> currentVersionSaver, Consumer<String> repairRunner) {
        this.updater = updater;
        this.currentVersionSaver = currentVersionSaver;
        this.repairRunner = repairRunner;
        this.textIO = new TextIO(textTerminal);
        this.questions = new ArrayList<>();
        this.exceptionQuestions = new HashMap<>();
    }


    public TextualInteractiveUpdater withQuestion(String question, Consumer<String> afterAnswerFn) {
        this.questions.add(new SimpleQuestion(question, afterAnswerFn));
        return this;
    }


    public TextualInteractiveUpdater withExceptionQuestion(Class<? extends Exception> exceptionType, String question, Consumer<String> afterAnswerFn) {
        this.exceptionQuestions.put(exceptionType, new SimpleQuestion(question, afterAnswerFn));
        return this;
    }


    public void update() {
        writeLn("======== STARTING UPDATE ========\n\n");

        boolean successfull = doUpdate();

        writeLn("\n\n" + (successfull ? "======== UPDATE SUCCESSFULLY FINISHED ========" : "======== UPDATE UNSUCCESSFULLY FINISHED ========"));
    }


    private boolean doUpdate() {
        try {
            updater.update();
        } catch (Exception e) {
            StringWriter stringWriter = new StringWriter();
            PrintWriter printWriter = new PrintWriter(stringWriter);
            e.printStackTrace(printWriter);

            writeLn(stringWriter.toString());
            writeLn("\n======== ERROR ========");
            writeLn(e.getMessage());
            writeLn("=======================\n\n");

            Integer exceptionedUpdateVersion = null;
            Throwable realException = e;

            if (e instanceof VersionedUpdateException) {
                exceptionedUpdateVersion = ((VersionedUpdateException) e).getUpdateVersion();
                realException = e.getCause();
            }

            for (Map.Entry<Class<? extends Exception>, Question> entry : exceptionQuestions.entrySet()) {
                if (entry.getKey().isInstance(realException)) {
                    askQuestion(entry.getValue());
                    return doUpdate();
                }
            }

            String answer = exceptionedUpdateVersion == null ?
                    question("Do you wanna\n - try it again (type 'Y'),\n - terminate update (type 'N')\n - run repairing sql (type 'R')?", "Y", "N", "R") :
                    question("Do you wanna\n - try it again (type 'Y'),\n - terminate update (type 'N')\n - run repairing sql (type 'R')\n - continue to next upgrade (DANGEROUS!, type 'C')?", "Y", "N", "R", "C");
            switch (answer) {
                case "Y":
                    return doUpdate();
                case "N":
                    return false;
                case "R":
                    repair();
                    return doUpdate();
                case "C":
                    currentVersionSaver.set(exceptionedUpdateVersion);
                    return doUpdate();
            }
        }

        questions.forEach(this::askQuestion);

        return true;
    }


    private void writeLn(String line) {
        textIO.getTextTerminal().println(line);
    }


    private String question(String question, String...acceptableAnswers) {
        while (true) {
            String input = textIO.newStringInputReader()
                    .read(question);
            if (Arrays.asList(acceptableAnswers).contains(input)) {
                return input;
            }
        }
    }


    private void repair() {
        askQuestion(new SimpleQuestion("Write repairing one-line sql:", repairRunner));
    }


    private void askQuestion(Question q) {
        String answer = textIO.newStringInputReader()
                .read(q.getQuestion());
        q.afterAnswer(answer);
    }


    public interface Question {

        String getQuestion();

        void afterAnswer(String answer);

    }


    private static class SimpleQuestion implements Question {

        private final String question;
        private final Consumer<String> afterAnswerFn;

        public SimpleQuestion(String question, Consumer<String> afterAnswerFn) {
            this.question = question;
            this.afterAnswerFn = afterAnswerFn;
        }

        @Override
        public String getQuestion() {
            return question;
        }

        @Override
        public void afterAnswer(String answer) {
            afterAnswerFn.accept(answer);
        }
    }
}
