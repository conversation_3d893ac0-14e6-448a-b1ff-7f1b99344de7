package cz.kpsys.portaro.resourcesupdate.migrationto20;

import java.util.Stack;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DefTisksestMigrationHelper {

    public static String convertKeyword(String content, String oldKeyword, String newKeyword) {
        Pattern p = Pattern.compile("(?:Text=|Fields=|Variables=).*?((?<!\")(?<![a-zA-Z_])" + oldKeyword + ")");
        boolean tryNext = true;
        while (tryNext) {
            String replaced = replaceTextOfMatchGroup(content, p, 1, s -> newKeyword);
            if (replaced.equals(content)) {
                tryNext = false;
            } else {
                content = replaced;
            }
        }
        return content;
    }

    public static String replaceTextOfMatchGroup(String sourceString, Pattern pattern, int groupToReplace, Function<String, String> replaceStrategy) {
        Stack<Integer> startPositions = new Stack<>();
        Stack<Integer> endPositions = new Stack<>();
        Matcher matcher = pattern.matcher(sourceString);

        while (matcher.find()) {
            startPositions.push(matcher.start(groupToReplace));
            endPositions.push(matcher.end(groupToReplace));
        }
        StringBuilder sb = new StringBuilder(sourceString);
        while (!startPositions.isEmpty()) {
            int start = startPositions.pop();
            int end = endPositions.pop();
            if (start >= 0 && end >= 0) {
                sb.replace(start, end, replaceStrategy.apply(sourceString.substring(start, end)));
            }
        }
        return sb.toString();
    }

}
