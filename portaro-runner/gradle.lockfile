# This is a Gradle generated file for dependency locking.
# Manual edits can break the build and are not advised.
# This file is expected to be part of source control.
asm:asm:3.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
ch.qos.logback:logback-classic:1.5.18=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
ch.qos.logback:logback-core:1.5.18=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.adobe.xmp:xmpcore:6.1.11=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.auth0:java-jwt:4.5.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.beust:jcommander:1.82=__$$asciidoctorj$$___r
com.clickhouse:clickhouse-client:0.9.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.clickhouse:clickhouse-data:0.9.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.clickhouse:clickhouse-http-client:0.9.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.clickhouse:clickhouse-jdbc:0.9.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.clickhouse:client-v2:0.9.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.clickhouse:jdbc-v2:0.9.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.drewnoakes:metadata-extractor:2.19.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.eatthepath:fast-uuid:0.2.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.eatthepath:pushy:0.15.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.core:jackson-annotations:2.20=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.core:jackson-core:2.20.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.core:jackson-databind:2.20.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-csv:2.20.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.20.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.20.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.20.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.20.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.20.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.20.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.20.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson.module:jackson-module-parameter-names:2.20.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson:jackson-bom:2.20.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.uuid:java-uuid-generator:5.1.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.fasterxml.woodstox:woodstox-core:7.1.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml:classmate:1.5.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.github.ben-manes.caffeine:caffeine:3.2.2=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.github.cliftonlabs:json-simple:3.0.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.github.kagkarlsson:db-scheduler-spring-boot-starter:16.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.github.kagkarlsson:db-scheduler:16.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.github.stephenc.jcip:jcip-annotations:1.0-1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.github.ulisesbocchio:jasypt-spring-boot-starter:3.0.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.github.ulisesbocchio:jasypt-spring-boot:3.0.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.github.vladimir-bukhtoyarov:bucket4j-core:6.4.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.github.waffle:waffle-jna:3.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.google.code.findbugs:jsr305:3.0.2=checkstyle,productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.google.errorprone:error_prone_annotations:2.28.0=checkstyle
com.google.errorprone:error_prone_annotations:2.40.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.google.guava:failureaccess:1.0.2=checkstyle
com.google.guava:failureaccess:1.0.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.google.guava:guava:33.3.1-jre=checkstyle
com.google.guava:guava:33.4.8-jre=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava=checkstyle,compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.google.inject:guice:3.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.google.j2objc:j2objc-annotations:3.0.0=checkstyle,compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.googlecode.libphonenumber:libphonenumber:8.13.55=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.h2database:h2:2.3.232=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.itextpdf:barcodes:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:bouncy-castle-connector:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:commons:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:font-asian:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:forms:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:hyph:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:io:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:itext-core:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:kernel:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:layout:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:pdfa:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:pdfua:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:sign:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:styled-xml-parser:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.itextpdf:svg:9.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.nimbusds:content-type:2.3=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.nimbusds:lang-tag:1.7=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.nimbusds:nimbus-jose-jwt:10.4.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.nimbusds:nimbus-jose-jwt:9.37.3=compileClasspath,testCompileClasspath
com.nimbusds:oauth2-oidc-sdk:11.28=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.opencsv:opencsv:5.12.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.puppycrawl.tools:checkstyle:10.19.0=checkstyle
com.sun.istack:istack-commons-runtime:4.1.2=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.twelvemonkeys.common:common-image:3.11.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.common:common-io:3.11.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.common:common-lang:3.11.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.imageio:imageio-core:3.11.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.imageio:imageio-metadata:3.11.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.imageio:imageio-webp:3.11.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.servlet:servlet:3.11.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.zaxxer:HikariCP:6.3.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.zaxxer:SparseBitSet:1.3=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
commons-beanutils:commons-beanutils:1.11.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
commons-beanutils:commons-beanutils:1.9.4=checkstyle
commons-codec:commons-codec:1.15=checkstyle
commons-codec:commons-codec:1.16.1=compileClasspath,testCompileClasspath
commons-codec:commons-codec:1.18.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
commons-collections:commons-collections:3.2.2=checkstyle,productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
commons-fileupload:commons-fileupload:1.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
commons-io:commons-io:2.16.1=compileClasspath,testCompileClasspath
commons-io:commons-io:2.20.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
commons-logging:commons-logging:1.3.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
commons-net:commons-net:3.12.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
cz.gopay:gp-java-api-v3-apache-http-client:3.8.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
cz.gopay:gp-java-api-v3-common:3.8.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
de.brendamour:jpasskit:0.4.2=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
de.codecentric:spring-boot-admin-client:3.5.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
de.codecentric:spring-boot-admin-starter-client:3.5.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
info.picocli:picocli:4.7.6=checkstyle
io.dropwizard.metrics:metrics-core:4.1.4=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
io.github.openfeign:feign-core:13.6=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.github.openfeign:feign-form-spring:13.6=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.github.openfeign:feign-form:13.6=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.github.openfeign:feign-jackson:13.6=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
io.github.openfeign:feign-slf4j:13.6=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.micrometer:micrometer-commons:1.15.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.micrometer:micrometer-core:1.15.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.micrometer:micrometer-jakarta9:1.15.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.micrometer:micrometer-observation:1.15.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.netty:netty-buffer:4.1.104.Final=compileClasspath,testCompileClasspath
io.netty:netty-buffer:4.2.6.Final=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
io.netty:netty-codec-base:4.2.6.Final=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
io.netty:netty-codec-dns:4.1.104.Final=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.netty:netty-codec-http2:4.1.104.Final=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.netty:netty-codec-http:4.1.104.Final=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.netty:netty-codec-socks:4.1.104.Final=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.netty:netty-codec:4.1.104.Final=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.netty:netty-common:4.1.104.Final=compileClasspath,testCompileClasspath
io.netty:netty-common:4.2.6.Final=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
io.netty:netty-handler-proxy:4.1.104.Final=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.netty:netty-handler:4.1.104.Final=compileClasspath,testCompileClasspath
io.netty:netty-handler:4.2.6.Final=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
io.netty:netty-resolver-dns:4.1.104.Final=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.netty:netty-resolver:4.1.104.Final=compileClasspath,testCompileClasspath
io.netty:netty-resolver:4.2.6.Final=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
io.netty:netty-transport-native-unix-common:4.1.104.Final=compileClasspath,testCompileClasspath
io.netty:netty-transport-native-unix-common:4.2.6.Final=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
io.netty:netty-transport:4.1.104.Final=compileClasspath,testCompileClasspath
io.netty:netty-transport:4.2.6.Final=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
io.projectreactor:reactor-core:3.7.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.smallrye:jandex:3.2.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
io.swagger.core.v3:swagger-annotations-jakarta:2.2.36=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.swagger.core.v3:swagger-core-jakarta:2.2.36=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.swagger.core.v3:swagger-models-jakarta:2.2.36=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.activation:jakarta.activation-api:2.1.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.annotation:jakarta.annotation-api:2.1.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.authentication:jakarta.authentication-api:3.0.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.authorization:jakarta.authorization-api:2.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.batch:jakarta.batch-api:2.1.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.ejb:jakarta.ejb-api:4.0.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.el:jakarta.el-api:5.0.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.enterprise:jakarta.enterprise.cdi-api:4.0.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.enterprise:jakarta.enterprise.lang-model:4.0.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.faces:jakarta.faces-api:4.0.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.inject:jakarta.inject-api:2.0.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.interceptor:jakarta.interceptor-api:2.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.jms:jakarta.jms-api:3.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.json.bind:jakarta.json.bind-api:3.0.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.json:jakarta.json-api:2.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.mail:jakarta.mail-api:2.1.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.persistence:jakarta.persistence-api:3.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.platform:jakarta.jakartaee-api:10.0.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.platform:jakarta.jakartaee-web-api:10.0.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.resource:jakarta.resource-api:2.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.security.enterprise:jakarta.security.enterprise-api:3.0.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.servlet.jsp.jstl:jakarta.servlet.jsp.jstl-api:3.0.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.servlet.jsp:jakarta.servlet.jsp-api:3.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.servlet:jakarta.servlet-api:6.0.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.transaction:jakarta.transaction-api:2.0.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.validation:jakarta.validation-api:3.1.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.websocket:jakarta.websocket-api:2.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.websocket:jakarta.websocket-client-api:2.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.ws.rs:jakarta.ws.rs-api:3.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.xml.bind:jakarta.xml.bind-api:4.0.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
javax.activation:javax.activation-api:1.2.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
javax.annotation:javax.annotation-api:1.3.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
javax.cache:cache-api:1.1.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
javax.ws.rs:javax.ws.rs-api:2.1.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
javax.xml.bind:jaxb-api:2.3.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
jline:jline:2.14.6=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
joda-time:joda-time:2.14.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
net.bytebuddy:byte-buddy-agent:1.17.6=testCompileClasspath,testRuntimeClasspath
net.bytebuddy:byte-buddy:1.15.11=productionRuntimeClasspath,runtimeClasspath
net.bytebuddy:byte-buddy:1.17.6=testCompileClasspath,testRuntimeClasspath
net.i2p.crypto:eddsa:0.3.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
net.java.dev.jna:jna-platform:5.13.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
net.java.dev.jna:jna:5.13.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
net.jcip:jcip-annotations:1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
net.minidev:accessors-smart:2.5.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
net.minidev:json-smart:2.5.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
net.sf.saxon:Saxon-HE:12.5=checkstyle
net.shibboleth.utilities:java-support:8.0.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
net.sourceforge.nekohtml:nekohtml:1.9.22=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
no.bekk.db-scheduler-ui:db-scheduler-ui-starter:4.5.6=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
no.bekk.db-scheduler-ui:db-scheduler-ui:4.5.6=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.antlr:antlr4-runtime:4.13.0=compileClasspath,testCompileClasspath
org.antlr:antlr4-runtime:4.13.2=checkstyle,productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-collections4:4.5.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-compress:1.27.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-digester3:3.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-imaging:1.0.0-alpha6=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-lang3:3.18.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.commons:commons-lang3:3.8.1=checkstyle
org.apache.commons:commons-math3:3.6.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-text:1.13.0=compileClasspath,testCompileClasspath
org.apache.commons:commons-text:1.14.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-text:1.3=checkstyle
org.apache.httpcomponents.client5:httpclient5:5.1.3=checkstyle
org.apache.httpcomponents.client5:httpclient5:5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.httpcomponents.core5:httpcore5-h2:5.1.3=checkstyle
org.apache.httpcomponents.core5:httpcore5-h2:5.3.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.httpcomponents.core5:httpcore5:5.1.3=checkstyle
org.apache.httpcomponents.core5:httpcore5:5.3.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.httpcomponents:fluent-hc:4.5.13=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.httpcomponents:httpclient:4.5.13=checkstyle
org.apache.httpcomponents:httpclient:4.5.14=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.httpcomponents:httpcore:4.4.14=checkstyle
org.apache.httpcomponents:httpcore:4.4.16=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.logging.log4j:log4j-api:2.24.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.logging.log4j:log4j-core:2.17.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.logging.log4j:log4j-to-slf4j:2.24.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.maven.doxia:doxia-core:1.12.0=checkstyle
org.apache.maven.doxia:doxia-logging-api:1.12.0=checkstyle
org.apache.maven.doxia:doxia-module-xdoc:1.12.0=checkstyle
org.apache.maven.doxia:doxia-sink-api:1.12.0=checkstyle
org.apache.pdfbox:fontbox:3.0.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.pdfbox:pdfbox-io:3.0.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.pdfbox:pdfbox:3.0.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.poi:poi:5.4.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.santuario:xmlsec:3.0.6=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.sshd:sshd-common:2.15.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.sshd:sshd-core:2.15.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.sshd:sshd-sftp:2.15.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.tomcat.embed:tomcat-embed-core:10.1.44=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.tomcat.embed:tomcat-embed-el:10.1.44=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.tomcat.embed:tomcat-embed-websocket:10.1.44=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.velocity.tools:velocity-tools-generic:3.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.apache.velocity:velocity-engine-core:2.4.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.xbean:xbean-reflect:3.7=checkstyle
org.apereo.cas.client:cas-client-core:4.0.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apiguardian:apiguardian-api:1.1.2=testCompileClasspath
org.asciidoctor:asciidoctorj-api:2.5.7=__$$asciidoctorj$$___r
org.asciidoctor:asciidoctorj:2.5.7=__$$asciidoctorj$$___r
org.aspectj:aspectjweaver:1.9.22.1=compileClasspath,testCompileClasspath
org.aspectj:aspectjweaver:1.9.24=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.attoparser:attoparser:2.0.7.RELEASE=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.beryx:awt-color-factory:1.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.beryx:text-io:3.4.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.bitbucket.b_c:jose4j:0.9.3=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.bouncycastle:bcpkix-jdk18on:1.72=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.bouncycastle:bcprov-jdk18on:1.72=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.bouncycastle:bcutil-jdk18on:1.72=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.checkerframework:checker-qual:3.48.1=checkstyle
org.checkerframework:checker-qual:3.49.3=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.codehaus.plexus:plexus-classworlds:2.6.0=checkstyle
org.codehaus.plexus:plexus-component-annotations:2.1.0=checkstyle
org.codehaus.plexus:plexus-container-default:2.1.0=checkstyle
org.codehaus.plexus:plexus-utils:3.3.0=checkstyle
org.codehaus.woodstox:stax2-api:4.2.2=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.cryptacular:cryptacular:1.2.4=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.eclipse.angus:angus-activation:2.0.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.eclipse.angus:jakarta.mail:1.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.firebirdsql.***********************************,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.freemarker:freemarker:2.3.34=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.glassfish.jaxb:jaxb-core:4.0.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.glassfish.jaxb:jaxb-runtime:4.0.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.glassfish.jaxb:txw2:4.0.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.hdrhistogram:HdrHistogram:2.2.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.hibernate.common:hibernate-commons-annotations:7.0.3.Final=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.hibernate.javax.persistence:hibernate-jpa-2.1-api:1.0.2.Final=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.hibernate.orm:hibernate-community-dialects:6.5.3.Final=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.hibernate.orm:hibernate-core:6.6.26.Final=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.hibernate.validator:hibernate-validator:8.0.2.Final=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.imgscalr:imgscalr-lib:4.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.jasig.cas.client:cas-client-core:3.6.4=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.jasypt:jasypt:1.9.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.javassist:javassist:3.28.0-GA=checkstyle
org.jboss.logging:jboss-logging:3.4.3.Final=compileClasspath,testCompileClasspath
org.jboss.logging:jboss-logging:3.5.0.Final=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.jcommander:jcommander:2.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.jdom:jdom2:2.0.6.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.jetbrains.kotlin:kotlin-stdlib:2.2.10=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.jetbrains:annotations:13.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.jruby:jruby-complete:9.3.8.0=__$$asciidoctorj$$___r
org.jspecify:jspecify:1.0.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.junit.jupiter:junit-jupiter-api:6.0.0-RC3=testCompileClasspath,testRuntimeClasspath
org.junit.jupiter:junit-jupiter-engine:6.0.0-RC3=testRuntimeClasspath
org.junit.jupiter:junit-jupiter-params:6.0.0-RC3=testCompileClasspath,testRuntimeClasspath
org.junit.jupiter:junit-jupiter:6.0.0-RC3=testCompileClasspath,testRuntimeClasspath
org.junit.platform:junit-platform-commons:6.0.0-RC3=testCompileClasspath,testRuntimeClasspath
org.junit.platform:junit-platform-engine:6.0.0-RC3=testRuntimeClasspath
org.junit.platform:junit-platform-launcher:6.0.0-RC3=testRuntimeClasspath
org.junit:junit-bom:6.0.0-RC3=testCompileClasspath,testRuntimeClasspath
org.latencyutils:LatencyUtils:2.0.3=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.lz4:lz4-java:1.8.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.mapstruct:mapstruct-processor:1.6.3=annotationProcessor
org.mapstruct:mapstruct:1.6.3=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.mariadb.jdbc:mariadb-java-client:3.4.2=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.mockito:mockito-core:5.19.0=testCompileClasspath,testRuntimeClasspath
org.objenesis:objenesis:3.3=testRuntimeClasspath
org.openid4java:openid4java-nodeps:0.9.6=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.opensaml:opensaml-core:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-messaging-api:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-profile-api:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-saml-api:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-saml-impl:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-security-api:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-security-impl:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-soap-api:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-soap-impl:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-storage-api:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-xmlsec-api:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opensaml:opensaml-xmlsec-impl:4.0.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.opentest4j:opentest4j:1.3.0=testCompileClasspath,testRuntimeClasspath
org.ow2.asm:asm:9.7.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.postgresql:postgresql:42.7.7=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.projectlombok:lombok:1.18.40=annotationProcessor,compileClasspath
org.reactivestreams:reactive-streams:1.0.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.reflections:reflections:0.10.2=checkstyle
org.roaringbitmap:RoaringBitmap:0.9.47=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.roaringbitmap:shims:0.9.47=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.shredzone.acme4j:acme4j-client:2.16=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.shredzone.acme4j:acme4j-utils:2.16=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.slf4j:jcl-over-slf4j:2.0.7=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.slf4j:jul-to-slf4j:2.0.17=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.slf4j:log4j-over-slf4j:2.1.0-alpha1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.slf4j:slf4j-api:2.0.17=compileClasspath,testCompileClasspath
org.slf4j:slf4j-api:2.1.0-alpha1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.sonatype.sisu.inject:cglib:2.2.1-v20090111=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springdoc:springdoc-openapi-starter-common:2.8.10=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springdoc:springdoc-openapi-starter-webmvc-api:2.8.10=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.10=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-actuator-autoconfigure:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-actuator:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-autoconfigure:3.5.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-actuator:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-aop:3.5.5=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-data-jpa:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-jdbc:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-json:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-logging:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-thymeleaf:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-tomcat:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-validation:3.5.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-web:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter:3.5.5=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot:3.5.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-commons:4.3.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-context:4.3.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-openfeign-core:4.3.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-starter-openfeign:4.3.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-starter:4.3.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.data:spring-data-commons:3.5.3=compileClasspath,testCompileClasspath
org.springframework.data:spring-data-commons:3.5.4=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.data:spring-data-jpa:3.5.3=compileClasspath,testCompileClasspath
org.springframework.data:spring-data-jpa:3.5.4=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.integration:spring-integration-core:6.5.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.integration:spring-integration-file:6.5.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.integration:spring-integration-sftp:6.5.1=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.ldap:spring-ldap-core:3.3.3=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.retry:spring-retry:2.0.12=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.security:spring-security-cas:6.5.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.security:spring-security-config:6.5.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.security:spring-security-core:6.5.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.security:spring-security-crypto:6.5.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.security:spring-security-oauth2-client:6.5.4=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.security:spring-security-oauth2-core:6.5.4=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.security:spring-security-oauth2-jose:6.5.4=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.security:spring-security-openid:5.8.16=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.security:spring-security-saml2-service-provider:6.5.4=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework.security:spring-security-web:6.5.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-aop:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-aspects:6.2.10=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-beans:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-context-support:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-context:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-core:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-expression:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-jcl:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-jdbc:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-messaging:6.2.9=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework:spring-orm:6.2.10=compileClasspath,testCompileClasspath
org.springframework:spring-orm:6.2.11=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
org.springframework:spring-tx:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-web:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-webflux:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-webmvc:6.2.11=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.thymeleaf:thymeleaf-spring6:3.1.3.RELEASE=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.thymeleaf:thymeleaf:3.1.3.RELEASE=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.unbescape:unbescape:1.1.6.RELEASE=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.webjars:swagger-ui:5.27.1=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.webjars:webjars-locator-lite:1.1.0=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.xmlresolver:xmlresolver:5.2.2=checkstyle
org.yaml:snakeyaml:2.4=compileClasspath,productionRuntimeClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
xerces:xercesImpl:2.11.0=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
xml-apis:xml-apis:1.4.01=productionRuntimeClasspath,runtimeClasspath,testRuntimeClasspath
empty=developmentOnly,testAndDevelopmentOnly,testAnnotationProcessor
