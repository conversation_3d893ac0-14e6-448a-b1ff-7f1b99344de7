package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.app.PortaroUrls;
import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.appserver.dml.TableWriteGenerator;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.barcode.BarCodeValidatorContextualProvider;
import cz.kpsys.portaro.commons.barcode.BarCodeType;
import cz.kpsys.portaro.commons.barcode.BarCodeValidator;
import cz.kpsys.portaro.commons.barcode.BarCodeValidatorByBarCodeTypeProvider;
import cz.kpsys.portaro.commons.barcode.ValidLengthBarCodeValidator;
import cz.kpsys.portaro.commons.cache.CacheDeleter;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.cache.DynamicCache;
import cz.kpsys.portaro.commons.contextual.*;
import cz.kpsys.portaro.commons.convert.ListToModifiedListConverter;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.ProviderByIdProvider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.StaticLink;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.database.IntegerValueDatabaseLoader;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.databasestructure.ExemplarDb;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.exemplar.*;
import cz.kpsys.portaro.exemplar.accessnumber.AccessNumberSequenceItem;
import cz.kpsys.portaro.exemplar.accessnumber.AccessNumberSequenceLoader;
import cz.kpsys.portaro.exemplar.accessnumber.SpringDbAccessNumberSequenceLoader;
import cz.kpsys.portaro.exemplar.account.Account;
import cz.kpsys.portaro.exemplar.acquisitionway.AcquisitionWayLoader;
import cz.kpsys.portaro.exemplar.customvalue.CustomValue;
import cz.kpsys.portaro.exemplar.delete.ExemplarDeleter;
import cz.kpsys.portaro.exemplar.delete.ExemplarDeleterAppserver;
import cz.kpsys.portaro.exemplar.delete.SecuredExemplarDeleter;
import cz.kpsys.portaro.exemplar.discard.*;
import cz.kpsys.portaro.exemplar.edit.*;
import cz.kpsys.portaro.exemplar.exemplarstatus.ExemplarStatus;
import cz.kpsys.portaro.exemplar.exemplarstatus.ExemplarStatusLoader;
import cz.kpsys.portaro.exemplar.export.ExportedExemplarsLoader;
import cz.kpsys.portaro.exemplar.loancategory.LoanCategoryLoader;
import cz.kpsys.portaro.exemplar.regal.*;
import cz.kpsys.portaro.exemplar.replacementway.ReplacementWay;
import cz.kpsys.portaro.exemplar.restore.ExemplarDiscardionRestorer;
import cz.kpsys.portaro.exemplar.restore.ExemplarDiscardionRestorerAppserver;
import cz.kpsys.portaro.exemplar.signature.SignatureSequenceFactory;
import cz.kpsys.portaro.exemplar.signature.SignatureSequenceItem;
import cz.kpsys.portaro.exemplar.signature.SignatureSequenceLoader;
import cz.kpsys.portaro.exemplar.signature.SpringDbSignatureSequenceLoader;
import cz.kpsys.portaro.exemplar.thematicgroup.ThematicGroup;
import cz.kpsys.portaro.exemplar.view.ExemplarToExemplarWithDocumentAndDiscardionViewableItemsConverter;
import cz.kpsys.portaro.exemplar.volume.*;
import cz.kpsys.portaro.exemplar.volumenumber.SpringDbVolumeNumberSequenceItemLoader;
import cz.kpsys.portaro.exemplar.volumenumber.VolumeNumberSequenceItemLoader;
import cz.kpsys.portaro.export.*;
import cz.kpsys.portaro.finance.Currency;
import cz.kpsys.portaro.grid.TableGenerator;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.licence.FeatureManager;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordSaver;
import cz.kpsys.portaro.record.deletion.RecordDeletionCommand;
import cz.kpsys.portaro.record.export.ExportedDepartmentSearchHelper;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.operation.RecordOperation;
import cz.kpsys.portaro.record.operation.RecordOperationType;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.field.StaticSearchFields;
import cz.kpsys.portaro.security.*;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.sequence.SequenceItem;
import cz.kpsys.portaro.sequence.SequenceLoader;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.template.Templates;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.role.editor.LibrarianPrivileges;
import cz.kpsys.portaro.view.web.rest.exemplar.VolumeCheckingExemplarsDeleter;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.core.support.RepositoryFactorySupport;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;

import static cz.kpsys.portaro.commons.localization.Texts.ofMessageCoded;
import static cz.kpsys.portaro.commons.localization.Texts.ofNative;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.KAT1_5.KAT1_5_ID_EX_SEQ_FB;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.KAT1_5.KAT1_5_ID_EX_SEQ_PG;
import static cz.kpsys.portaro.security.PermissionResolver.adaptingSubject;
import static cz.kpsys.portaro.security.PermissionResolver.withoutSubject;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ExemplarConfig {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull TransactionTemplateFactory readonlyTransactionTemplateFactory;
    @NonNull CacheService cacheService;
    @NonNull ByIdLoadable<Location, Integer> locationLoader;
    @NonNull ExemplarStatusLoader exemplarStatusLoader;
    @NonNull ByIdLoadable<Account, String> accountLoader;
    @NonNull LoanCategoryLoader loanCategoryLoader;
    @NonNull AcquisitionWayLoader acquisitionWayLoader;
    @NonNull Codebook<CustomValue, String> customValueLoader;
    @NonNull Codebook<ReplacementWay, String> replacementWayLoader;
    @NonNull ByIdLoadable<ThematicGroup, String> thematicGroupLoader;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull DmlAppserverService dmlAppserverService;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull SecurityManager securityManager;
    @NonNull PermissionFactory permissionFactory;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull Provider<@NonNull Fond> defaultDocumentFondProvider;
    @NonNull SettingLoader settingLoader;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull TemplateEngine templateEngine;
    @NonNull Translator<Department> translator;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull Provider<Department> rootDepartmentProvider;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull ExportedDepartmentSearchHelper exportedDepartmentSearchHelper;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull AllByIdsLoadable<Record, UUID> nonDetailedDocumentLoader;
    @NonNull RecordSaver recordSaver;
    @NonNull DynamicCache<Record> recordCache;
    @NonNull ByIdLoadable<RecordOperationType, Integer> recordOperationTypeLoader;
    @NonNull TableWriteGenerator<RecordOperation> recordOperationTableWriteGenerator;
    @NonNull FeatureManager featureManager;
    @NonNull ExporterResolver exporterResolver;
    @NonNull StaticExportDescriptorLoader staticExportDescriptorLoader;
    @NonNull ModelBeanBuilder modelBeanBuilder;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull ObjectMapper appserverXmlMapper;
    @NonNull BarCodeValidatorByBarCodeTypeProvider barCodeValidatorByBarCodeTypeProvider;
    @NonNull Provider<Department> realRootDepartmentProvider;
    @NonNull ByIdLoadable<Currency, String> currencyLoader;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull EntityManager entityManager;
    @NonNull Runnable saveTransactionAuthenticator;
    @NonNull Saver<RecordOperation, RecordOperation> recordOperationSaver;
    @NonNull DatabaseProperties databaseProperties;
    @NonNull RepositoryFactorySupport jpaRepositoryFactory;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;

    @Bean
    public VolumeLoader volumeLoader() {
        return new SpringDbVolumeLoader(jdbcTemplate, queryFactory);
    }

    @Bean
    public Saver<Volume, VolumeEntity> volumeSaver() {
        var saver = new GenericHookableSaver<>(new PreConvertingSaver<>(
                new VolumeToEntityConverter(),
                new FlushingJpaSaver<>(new SimpleJpaRepository<>(VolumeEntity.class, entityManager))
        ));
        saver.addPreHook(saveTransactionAuthenticator);
        return saver;
    }

    @Bean
    public Deleter<Volume> volumeDeleter() {
        return new VolumeCheckingExemplarsDeleter(
                new SoftDeleterBySaver<>(volumeSaver()),
                exemplarLoader()
        );
    }

    @Bean
    public VolumeCreator volumeCreator() {
        return new VolumeCreator(
                ContextIgnoringContextualProvider.of(IntegerValueDatabaseLoader.ofSequenceValue(ExemplarDb.VOLUME.SEQ_ID_VOLUME, notAutoCommittingJdbcTemplate, queryFactory)),
                volumeSaver(),
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public VolumeUpdater volumeUpdater() {
        return new VolumeUpdater(volumeSaver(), defaultTransactionTemplateFactory.get());
    }

    @Bean
    public RowMapper<Exemplar> exemplarRowMapper() {
        return new ExemplarRowMapper(
                locationLoader,
                departmentLoader,
                rootDepartmentProvider,
                thematicGroupLoader,
                loanCategoryLoader,
                exemplarStatusLoader,
                acquisitionWayLoader,
                accountLoader,
                fondLoader,
                customValueLoader,
                currencyLoader);
    }

    @Bean
    public IdAndIdsLoadable<Exemplar, Integer> exemplarAllByIdsLoader() {
        SpringDbExemplarAllByIdsLoader exemplarAllByIdsLoader = new SpringDbExemplarAllByIdsLoader(notAutoCommittingJdbcTemplate, queryFactory, exemplarRowMapper());
        ChunkingAllByIdsLoader<Exemplar, Integer, Integer> chunkingAllByIdsLoader = ChunkingAllByIdsLoader.ofIdentified(exemplarAllByIdsLoader);

        return new TransactionalIdAndIdsLoadable<>(
                new ByIdLoadableByAllByIdsLoadable<>(chunkingAllByIdsLoader, Exemplar.class),
                readonlyTransactionTemplateFactory.get()
        );
    }

    @Bean
    public ExemplarLoader exemplarLoader() {
        SpringDbExemplarLoader bean = new SpringDbExemplarLoader(jdbcTemplate, queryFactory, exemplarAllByIdsLoader(), exemplarRowMapper());
        return new SortingExemplarLoader(bean, exemplarListSorter());
    }

    @Bean
    public ByIdLoadable<? extends ExemplarDescriptor, Integer> exemplarDescriptorLoader() {
        return exemplarLoader();
    }

    @Bean
    public ExemplarListSorter exemplarListSorter() {
        Comparator<Exemplar> exemplarComparator = new MultiPropertyExemplarComparator<>(settingLoader.getOnRootProvidedList(SettingKeys.EXEMPLAR_SORT_PROPS));
        Comparator<Exemplar> bindingComparator = new MultiPropertyExemplarComparator<>(settingLoader.getOnRootProvidedList(SettingKeys.BINDING_SORT_PROPS));
        Comparator<Exemplar> issueComparator = new MultiPropertyExemplarComparator<>(settingLoader.getOnRootProvidedList(SettingKeys.ISSUE_SORT_PROPS));
        return new ExemplarListSorter(exemplarComparator, bindingComparator, issueComparator);
    }

    @Bean
    public ExportedExemplarsLoader exportedExemplarsLoader() {
        return new ExportedExemplarsLoader(
                exemplarSearchLoader(),
                exemplarListSorter(),
                exportedDepartmentSearchHelper,
                settingLoader.getDepartmentedProvider(ExemplarSettingKeys.EXEMPLAR_STATUSES_EXPORT).throwingWhenNull().invalidWhen(Collection::isEmpty),
                exemplarStatusLoader,
                securityManager
        );
    }

    @Bean
    public Codebook<RegalMap, String> regalMapLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(new SpringDbRegalMapLoader(jdbcTemplate, queryFactory))
                .staticCached(RegalMap.class.getSimpleName())
                .build();
    }

    @Bean
    public RegalMapSaver regalMapSaver() {
        return new SpringDbRegalMapSaver(jdbcTemplate, queryFactory, List.of(cacheService.createCleanerFor(RegalMap.class.getSimpleName())));
    }

    @Bean
    public RegalService regalService() {
        return new RegalService(
                regalMapLoader(),
                settingLoader.getOnRootProvider(SettingKeys.REGAL_MAP_PROPERTY)
        );
    }

    @Bean
    public ExemplarDeleter exemplarDeleter() {
        Provider<ExemplarStatus> deletableExemplarStatusProvider = ProviderByIdProvider.ofStaticId(exemplarStatusLoader, ExemplarStatus.PROCESSING_STATUS_ID);
        ExemplarDeleterAppserver bean = new ExemplarDeleterAppserver(mappingAppserver, deletableExemplarStatusProvider, exemplarSaver(), appserverXmlMapper);
        return new SecuredExemplarDeleter(bean, securityManager);
    }

    @Bean
    public ExemplarDiscarder exemplarDiscarder() {
        Provider<ExemplarStatus> discardedExemplarStatusProvider = ProviderByIdProvider.ofStaticId(exemplarStatusLoader, ExemplarStatus.DISCARDED_STATUS_ID);
        return new AppserverExemplarDiscarder(
                dmlAppserverService,
                exemplarLoader(),
                discardedExemplarStatusProvider,
                recordOperationTypeLoader,
                recordOperationTableWriteGenerator,
                recordLoader,
                fromSequenceDiscardNumberSequenceItemLoader());
    }

    @Bean
    public ExemplarDiscardionRestorer exemplarDiscardionRestorer() {
        Provider<ExemplarStatus> restoredExemplarStatusProvider = ProviderByIdProvider.ofStaticId(exemplarStatusLoader, ExemplarStatus.OK);
        return new ExemplarDiscardionRestorerAppserver(
                dmlAppserverService,
                exemplarLoader(),
                restoredExemplarStatusProvider,
                recordOperationTypeLoader,
                recordOperationTableWriteGenerator,
                recordLoader);
    }

    @Bean
    public Saver<EditedExemplar, Exemplar> exemplarSaver() {
        ExemplarSaverFondChangingProxy exemplarSaverFondChangingProxy = new ExemplarSaverFondChangingProxy(
                new UniqueIdentifiersCheckingExemplarSaver(pureExemplarSaver(), exemplarValidator()),
                CatalogConstants.FONDS_FOR_CONVERT_TO_DEFAULT_AFTER_FIRST_EXEMPLAR_CREATION,
                defaultDocumentFondProvider,
                recordLoader,
                recordSaver
        );
        BarCodeTrimmingExemplarSaverProxy barCodeTrimmingExemplarSaverProxy = new BarCodeTrimmingExemplarSaverProxy(
                exemplarSaverFondChangingProxy,
                exemplarBarCodeValidatorProvider()
        );
        return new SecuredExemplarSaver(
                barCodeTrimmingExemplarSaverProxy,
                securityManager
        );
    }

    @Bean
    public Saver<EditedExemplar, Exemplar> jpaExemplarSaver() {
        ExemplarSaverFondChangingProxy exemplarSaverFondChangingProxy = new ExemplarSaverFondChangingProxy(
                new UniqueIdentifiersCheckingExemplarSaver(basicJpaExemplarSaver(), exemplarValidator()),
                CatalogConstants.FONDS_FOR_CONVERT_TO_DEFAULT_AFTER_FIRST_EXEMPLAR_CREATION,
                defaultDocumentFondProvider,
                recordLoader,
                recordSaver
        );
        BarCodeTrimmingExemplarSaverProxy barCodeTrimmingExemplarSaverProxy = new BarCodeTrimmingExemplarSaverProxy(
                exemplarSaverFondChangingProxy,
                exemplarBarCodeValidatorProvider()
        );
        return new SecuredExemplarSaver(
                barCodeTrimmingExemplarSaverProxy,
                securityManager
        );
    }

    @Bean
    public AppserverExemplarSaver pureExemplarSaver() {
        return new AppserverExemplarSaver(
                dmlAppserverService,
                accessNumberSequenceItemLoader(),
                signatureSequenceItemLoader(),
                recordOperationTypeLoader,
                recordOperationTableWriteGenerator,
                recordLoader
        );
    }

    @Bean
    public ContextualProvider<UUID, Integer> exemplarNextOrderProvider() {
        return new ExemplarNextOrderByRecordIdProvider(exemplarEntityRepository());
    }

    @Bean
    public JpaExemplarSaver basicJpaExemplarSaver() {
        Saver<ExemplarEntity, ExemplarEntity> exemplarEntitySaver = new FlushingJpaSaver<>(exemplarEntityRepository());
        Saver<IssueEntity, IssueEntity> issueEntityleSaver = new FlushingJpaSaver<>(new SimpleJpaRepository<>(IssueEntity.class, entityManager));
        Saver<BindingEntity, BindingEntity> bindingEntityleSaver = new FlushingJpaSaver<>(new SimpleJpaRepository<>(BindingEntity.class, entityManager));

        return new JpaExemplarSaver(
                exemplarNextOrderProvider(),
                ContextIgnoringContextualProvider.of(IntegerValueDatabaseLoader.ofSequenceValueDbDependent(KAT1_5_ID_EX_SEQ_FB, KAT1_5_ID_EX_SEQ_PG, notAutoCommittingJdbcTemplate, queryFactory, databaseProperties)),
                exemplarEntitySaver, issueEntityleSaver, bindingEntityleSaver, jdbcTemplate, queryFactory,
                accessNumberSequenceItemLoader(),
                signatureSequenceItemLoader(),
                recordOperationTypeLoader,
                recordOperationSaver,
                recordLoader);
    }

    private @NonNull ExemplarEntityRepository exemplarEntityRepository() {
        return jpaRepositoryFactory.getRepository(ExemplarEntityRepository.class);
    }

    @Bean
    public ExemplarValidator exemplarValidator() {
        return new ExemplarValidator(
                exemplarSearchLoader(),
                settingLoader.getDepartmentedProvider(SettingKeys.BUILDING_SCOPE_ACCESS_NUMBERS_UNIQUENESS_ENABLED),
                settingLoader.getDepartmentedProvider(SettingKeys.TITLE_SIGNATURES_ENABLED),
                departmentAccessor);
    }

    @Bean
    public AccessNumberSequenceLoader accessNumberSequenceLoader() {
        return new SpringDbAccessNumberSequenceLoader(jdbcTemplate, queryFactory, locationLoader);
    }

    @Bean
    public ExemplarSequenceItemLoader<AccessNumberSequenceItem> accessNumberSequenceItemLoader() {
        return new AccessNumberSequenceItemLoaderDelegating(
                exemplarSearchLoader(),
                accessNumberSequenceLoader(),
                settingLoader.getOnRootProvider(SettingKeys.BUILDING_SCOPE_ACCESS_NUMBERS_UNIQUENESS_ENABLED),
                departmentAccessor);
    }

    @Bean
    public SignatureSequenceLoader signatureSequenceLoader() {
        SignatureSequenceFactory factory = new SignatureSequenceFactory(settingLoader.getOnRootProvider(SettingKeys.TITLE_SIGNATURES_ENABLED));
        return new SpringDbSignatureSequenceLoader(jdbcTemplate, queryFactory, factory);
    }

    @Bean
    public ExemplarSequenceItemLoader<SignatureSequenceItem> signatureSequenceItemLoader() {
        return new SignatureSequenceItemLoaderDelegating(
                signatureSequenceLoader(),
                exemplarLoader(),
                exemplarSearchLoader(),
                settingLoader.getOnRootProvider(SettingKeys.TITLE_SIGNATURES_ENABLED));
    }

    @Bean
    public SequenceLoader<DiscardNumberSequence> discardNumberSequenceLoader() {
        return new SpringDbDiscardNumberSequenceLoader(jdbcTemplate, queryFactory);
    }

    @Bean
    public DiscardNumberSequenceItemLoader<DiscardNumberSequenceItem> fromSequenceDiscardNumberSequenceItemLoader() {
        return new FromSequenceDiscardNumberSequenceItemLoader(discardNumberSequenceLoader());
    }

    @Bean
    public DiscardNumberSequenceItemLoader<SequenceItem> fromDiscardionsDiscardNumberSequenceItemLoader() {
        return new FromDiscardionsDiscardNumberSequenceItemLoader(jdbcTemplate, queryFactory, settingLoader.getOnRootProvider(SettingKeys.DISCARD_NUMBERS_WITHIN_BUILDING_ENABLED));
    }

    @Bean
    public VolumeNumberSequenceItemLoader volumeNumberSequenceItemLoader() {
        return new SpringDbVolumeNumberSequenceItemLoader(jdbcTemplate, queryFactory);
    }

    @Bean
    public IdAndIdsLoadable<Discardion, Long> discardionLoader() {
        var entitiesToModelsConverter = new ListToModifiedListConverter<>(new EntityToDiscardionConverter(replacementWayLoader));
        return modelBeanBuilder.allByIdsLoader(DiscardionEntity.class, entitiesToModelsConverter).build();
    }

    @Bean
    public PageSearchLoader<MapBackedParams, Integer, RangePaging> exemplarIdSearchLoader() {
        SpringDbExemplarIdSearchLoader pureSearchLoader = new SpringDbExemplarIdSearchLoader(
                jdbcTemplate,
                queryFactory,
                departmentAccessor,
                accessNumberSequenceLoader(),
                signatureSequenceLoader(),
                new StaticContextualDelegatingProvider<>(exemplarBarCodeValidatorProvider(), realRootDepartmentProvider)
        );

        return new ParametersConvertingPageSearchLoader<>(pureSearchLoader)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Exemplar> exemplarSearchLoader() {
        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                ResultConvertingPageSearchLoader.createConvertingFromIds(
                        exemplarIdSearchLoader(),
                        exemplarAllByIdsLoader()
                ));
    }

    @Bean
    public CacheDeleter<Exemplar> exemplarCacheDeletable() {
        return exemplar -> recordCache.deleteFromCacheById(exemplar.getRecordId());
    }

    @Bean
    public ContextualFunction<Record, Department, Boolean> departmentedRecordCanHaveExemplarsPredicate() {
        return new ContextualRecordCanHaveExemplarsPredicate<>();
    }

    @Bean
    public ContextualFunction<Record, Department, Boolean> subtreeDepartmentedRecordHasExemplarsPredicate() {
        return new ContextualCountPredicateGtZeroResolver<>(subtreeDepartmentedRecordExemplarsCountResolver());
    }

    @Bean
    public ContextualFunction<Record, Department, Boolean> familyDepartmentedRecordHasExemplarsPredicate() {
        return new ContextualCountPredicateGtZeroResolver<>(familyDepartmentedRecordExemplarsCountResolver());
    }

    @Bean
    public ContextualFunction<Record, Department, Boolean> singleNodeDepartmentedRecordHasExemplarsPredicate() {
        return new ContextualCountPredicateGtZeroResolver<>(singleNodeDepartmentedRecordExemplarsCountResolver());
    }

    @Bean
    public ItemCustomizableContextualSearchFunction<Record, Department, Integer> subtreeDepartmentedRecordExemplarsCountResolver() {
        return new DepartmentedRecordExemplarsCountResolver(
                departmentedRecordCanHaveExemplarsPredicate(),
                exemplarIdSearchLoader(),
                departmentAccessor,
                HierarchyLoadScope.SUBTREE
        );
    }

    @Bean
    public ItemCustomizableContextualSearchFunction<Record, Department, Integer> familyDepartmentedRecordExemplarsCountResolver() {
        return new DepartmentedRecordExemplarsCountResolver(
                departmentedRecordCanHaveExemplarsPredicate(),
                exemplarIdSearchLoader(),
                departmentAccessor,
                HierarchyLoadScope.FAMILY
        );
    }

    @Bean
    public ItemCustomizableContextualSearchFunction<Record, Department, Integer> singleNodeDepartmentedRecordExemplarsCountResolver() {
        return new DepartmentedRecordExemplarsCountResolver(
                departmentedRecordCanHaveExemplarsPredicate(),
                exemplarIdSearchLoader(),
                departmentAccessor,
                HierarchyLoadScope.NODE
        );
    }

    @Bean
    public ItemCustomizableContextualSearchFunction<Record, Department, List<Exemplar>> subtreeDepartmentedRecordExemplarsLoader() {
        return new SearchingDepartmentedRecordExemplarsLoader(
                exemplarSearchLoader(),
                departmentAccessor,
                HierarchyLoadScope.SUBTREE
        );
    }

    @Bean
    public ItemCustomizableContextualSearchFunction<Record, Department, List<Exemplar>> familyDepartmentedRecordExemplarsLoader() {
        return new SearchingDepartmentedRecordExemplarsLoader(
                exemplarSearchLoader(),
                departmentAccessor,
                HierarchyLoadScope.FAMILY
        );
    }

    @Bean
    public ContextualConsumer<RecordDeletionCommand, Department> subtreeDepartmentedRecordExemplarsDeleter() {
        return new DepartmentedRecordExemplarsDeleter(
                subtreeDepartmentedRecordExemplarsLoader(),
                exemplarDeleter(),
                authenticationHolder
        );
    }

    @Bean
    public ContextualProvider<Department, BarCodeValidator> exemplarBarCodeValidatorProvider() {
        return new BarCodeValidatorContextualProvider<>(
                barCodeValidatorByBarCodeTypeProvider.withAdditionalValidator(BarCodeType.CUSTOM, new ValidLengthBarCodeValidator(settingLoader.getOnRootProvider(SettingKeys.EXEMPLAR_BAR_CODE_MAX_LENGTH))),
                settingLoader.getDepartmentedProvider(SettingKeys.EXEMPLAR_BARCODE_TYPES));
    }



    @EventListener(ApplicationReadyEvent.class)
    public void registerExports() {
        if (featureManager.isEnabled(FeatureManager.FEATURE_EXPORTS)) {
            AbstractToTextualFileExporter<List<Exemplar>> exemplarsCsvExporter = new CsvFileExporter<>("exemplars.csv", Templates.TEMPLATE_EXEMPLARS_CSV, Exemplar.class, "exemplars", templateEngine);
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.EXEMPLARS_CSV, exemplarsCsvExporter);

            var searchedExemplarsCsvExporter = SearchedItemsExporter.ofUnlimited(exemplarsCsvExporter).withCacheDeleter(exemplarCacheDeletable());
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.SEARCHED_EXEMPLARS_CSV, searchedExemplarsCsvExporter);

            TableGenerator<ExemplarWithDocumentAndDiscardion> tableGenerator = new TableGenerator<ExemplarWithDocumentAndDiscardion>()
                    .withColumn(ofMessageCoded("exemplar.AccessNumber.abbr"), ExemplarWithDocumentAndDiscardion::getAccessNumber)
                    .withColumn(ofMessageCoded("exemplar.Signature.abbr"), ExemplarWithDocumentAndDiscardion::getSignature)
                    .withColumn(ofMessageCoded("exemplar.BarCode.abbr"), ExemplarWithDocumentAndDiscardion::getBarCode)
                    .withColumn(ofNative("ISBN"), ExemplarWithDocumentAndDiscardion::getDocumentIsbns)
                    .withColumn(ofMessageCoded("commons.nazevDokumentu"), ExemplarWithDocumentAndDiscardion::getRecord)
                    .withColumn(ofNative("Autor"), ExemplarWithDocumentAndDiscardion::getDocumentMainAuthors)
                    .withColumn(ofMessageCoded("department.Department.abbr"), ExemplarWithDocumentAndDiscardion::getDepartment)
                    .withColumn(ofMessageCoded("exemplar.location"), ExemplarWithDocumentAndDiscardion::getLocation)
                    .withColumn(ofMessageCoded("commons.fond"), ExemplarWithDocumentAndDiscardion::getFond)
                    .withColumn(ofNative("Dat.vytv."), ExemplarWithDocumentAndDiscardion::getCreationDate)
                    .withColumn(ofNative("Dat.změny"), ExemplarWithDocumentAndDiscardion::getLastModificationDate)
                    .withColumn(ofNative("Úbyt.č."), ExemplarWithDocumentAndDiscardion::getDiscardionDiscardNumber)
                    .withColumn(ofNative("Cena"), exemplar -> exemplar.getPrice().amount())
                    .withColumn(ofNative("Měna"), exemplar -> exemplar.getPrice().currency())
                    .withColumn(ofNative("Kusů"), ExemplarWithDocumentAndDiscardion::getQuantity)
                    .withColumn(ofNative("URL"), exemplar -> StaticLink.ofWithoutBase(PortaroUrls.getRecordPath(exemplar.getRecordId())));
            Exporter<List<ExemplarWithDocumentAndDiscardion>> exemplarWithDocumentAndDiscardionListExporter = new TableGeneratorXlsFileExporter<>("exemplars.xls", translator, serverUrlProvider.throwingWhenNull(), tableGenerator, ExemplarWithDocumentAndDiscardion.class).withHeader();
            SubjectConvertingExporter<List<Exemplar>, List<ExemplarWithDocumentAndDiscardion>> exemplarListExporter = SubjectConvertingExporter.create(
                    exemplarWithDocumentAndDiscardionListExporter,
                    ExemplarToExemplarWithDocumentAndDiscardionViewableItemsConverter.createChunkingAndCacheDeleting(nonDetailedDocumentLoader, recordCache, discardionLoader()),
                    TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Exemplar.class))
            );
            var searchedExemplarsXlsExporter = SearchedItemsExporter.ofUnlimited(exemplarListExporter).withCacheDeleter(exemplarCacheDeletable());
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.SEARCHED_EXEMPLARS_XLS, searchedExemplarsXlsExporter);
        }

        staticExportDescriptorLoader
                .with(List.of(CatalogConstants.Export.Tag.SEARCHED_EXEMPLARS, ExportConstants.Tag.CSV, ExportConstants.Tag.DOWNLOAD), CatalogConstants.Export.Exporters.SEARCHED_EXEMPLARS_CSV, ofMessageCoded("export.CsvButton"))
                .with(List.of(CatalogConstants.Export.Tag.SEARCHED_EXEMPLARS, ExportConstants.Tag.XLS, ExportConstants.Tag.DOWNLOAD), CatalogConstants.Export.Exporters.SEARCHED_EXEMPLARS_XLS, ofMessageCoded("export.XlsButton"));
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        StaticSearchFields.CODEBOOK
                .add(ExemplarConstants.SearchFields.LOCATION)
                .add(ExemplarConstants.SearchFields.DOCUMENT_SIGNATURE)
                .add(ExemplarConstants.SearchFields.DOCUMENT_ACCESS_NUMBER);

        converterRegisterer
                .registerForIntegerId(Exemplar.class, exemplarLoader())
                .registerForIntegerId(ExemplarDescriptor.class, exemplarDescriptorLoader())
                .registerForStringId(RegalMap.class, regalMapLoader())
                .registerForIntegerId(Volume.class, volumeLoader());

        final ContextualProvider<Department, @NonNull List<Integer>> exemplarStatuses = settingLoader.getDepartmentedProvider(SettingKeys.EXEMPLAR_STATUSES);
        final ContextualProvider<Department, @NonNull List<Integer>> exportableExemplarStatuses = settingLoader.getDepartmentedProvider(ExemplarSettingKeys.EXEMPLAR_STATUSES_EXPORT);

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLARS_PAGE_SHOW, permissionFactory.edit());

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLARS_SHOW_OF_DOCUMENT, PermissionResolver.or(
                (auth, ctx, document) -> document.isExemplarable() ? PermissionResult.allow() : PermissionResult.pointless(Texts.ofNative("Document is not exemplarable")),
                PermissionResolver.and(
                        (auth, ctx, document) -> CatalogConstants.FONDS_FOR_CONVERT_TO_DEFAULT_AFTER_FIRST_EXEMPLAR_CREATION.contains(document.getFond().getId()) ? PermissionResult.allow() : PermissionResult.forbid(),
                        withoutSubject(permissionRegistry.getLazy(ExemplarSecurityActions.EXEMPLAR_CREATE_ANY))
                )
        ));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_EXPORT, PermissionResolver.or(
                (auth, ctx, exemplar) -> exportableExemplarStatuses.getOn(ctx).contains(exemplar.getStatus().getId()) ? PermissionResult.allow() : PermissionResult.forbid(Texts.ofNative("Exemplar status is not exportable")),
                permissionFactory.edit()
        ));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_SHOW, PermissionResolver.or(
                (auth, ctx, exemplar) -> exemplarStatuses.getOn(ctx).contains(exemplar.getStatus().getId()) ? PermissionResult.allow() : PermissionResult.disabledFeature(Texts.ofNative("Exemplar status is not showable")),
                permissionFactory.edit()
        ));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_CREATE_ANY, permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(LibrarianPrivileges.ACTION_EXEMPLARS_CREATE));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_CREATE_WITH_RECORD_AND_DEPARTMENT, PermissionResolver.and(
                PermissionResolver.or(
                        adaptingSubject(ExemplarSecurityActions.RecordAndDepartment::record, (auth, ctx, record) -> record.isExemplarable() ? PermissionResult.allow() : PermissionResult.pointless(Texts.ofNative("Document is not exemplarable"))),
                        adaptingSubject(ExemplarSecurityActions.RecordAndDepartment::record, (auth, ctx, record) -> CatalogConstants.FONDS_FOR_CONVERT_TO_DEFAULT_AFTER_FIRST_EXEMPLAR_CREATION.contains(record.getFond().getId()) ? PermissionResult.allow() : PermissionResult.forbid())
                ),
                adaptingSubject(ExemplarSecurityActions.RecordAndDepartment::department, (auth, ctx, department) -> department.getExemplarable() ? PermissionResult.allow() : PermissionResult.pointless(Texts.ofNative("Department is not exemplarable"))),
                withoutSubject(permissionRegistry.getLazy(ExemplarSecurityActions.EXEMPLAR_CREATE_ANY))
        ));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_EDIT_ANY, permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(LibrarianPrivileges.ACTION_EXEMPLARS_EDIT));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_EDIT, PermissionResolver.and(
                withoutSubject(permissionRegistry.getLazy(ExemplarSecurityActions.EXEMPLAR_EDIT_ANY)),
                adaptingSubject(Exemplar::getDepartment, permissionFactory.editSubjectWithDepartment())
        ));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_DISCARD, PermissionResolver.and(
                permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(LibrarianPrivileges.ACTION_EXEMPLARS_DISCARD),
                adaptingSubject(Exemplar::getDepartment, permissionFactory.editSubjectWithDepartment())
        ));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_DELETE, PermissionResolver.and(
                permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(LibrarianPrivileges.ACTION_EXEMPLARS_DELETE),
                adaptingSubject(Exemplar::getDepartment, permissionFactory.editSubjectWithDepartment())
        ));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_FIELD_GENERATE, permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(LibrarianPrivileges.ACTION_EXEMPLARS_GENERATE_FIELDS));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_ALL_FIELDS_SHOW, permissionFactory.edit());

        permissionRegistry.add(ExemplarSecurityActions.LOAN_CATEGORIES_SHOW, permissionFactory.editAction(LibrarianPrivileges.ACTION_LOAN_CATEGORY_SHOW));

        permissionRegistry.add(ExemplarSecurityActions.LOAN_CATEGORY_CREATE, permissionFactory.editAction(LibrarianPrivileges.ACTION_LOAN_CATEGORY_CREATE));

        permissionRegistry.add(ExemplarSecurityActions.LOAN_CATEGORY_EDIT, permissionFactory.editAction(LibrarianPrivileges.ACTION_LOAN_CATEGORY_EDIT));

        permissionRegistry.add(ExemplarSecurityActions.LOAN_CATEGORY_DELETE, permissionFactory.editAction(LibrarianPrivileges.ACTION_LOAN_CATEGORY_DELETE));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_RESTORE, permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(LibrarianPrivileges.ACTION_EXEMPLARS_DISCARDION_RESTORE));
    }

}
