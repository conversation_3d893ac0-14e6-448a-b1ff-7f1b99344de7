package cz.kpsys.portaro.config;

import cz.kpsys.portaro.acquisition.RecordDemandEntity;
import cz.kpsys.portaro.appserver.dml.MetadataExtractorIntegrator;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.department.DepartmentEntity;
import cz.kpsys.portaro.event.EventEntity;
import cz.kpsys.portaro.exemplar.account.AccountEntity;
import cz.kpsys.portaro.exemplar.acquisitionway.AcquisitionWayEntity;
import cz.kpsys.portaro.exemplar.discard.DiscardionEntity;
import cz.kpsys.portaro.exemplar.edit.BindingEntity;
import cz.kpsys.portaro.exemplar.edit.ExemplarEntity;
import cz.kpsys.portaro.exemplar.edit.IssueEntity;
import cz.kpsys.portaro.exemplar.exchangeset.ExchangeExemplarSetEntity;
import cz.kpsys.portaro.exemplar.thematicgroup.ThematicGroupEntity;
import cz.kpsys.portaro.exemplar.volume.VolumeEntity;
import cz.kpsys.portaro.ext.obalkyknih.db.ObalkyknihEntity;
import cz.kpsys.portaro.file.FileAccessTypeEntity;
import cz.kpsys.portaro.file.custom.RootCustomDirectoryEntity;
import cz.kpsys.portaro.file.directory.ParentableDirectoryEntity;
import cz.kpsys.portaro.finance.AmountTypeEntity;
import cz.kpsys.portaro.finance.CurrencyEntity;
import cz.kpsys.portaro.inventory.capture.CaptureEntity;
import cz.kpsys.portaro.inventory.inventory.InventoryEntity;
import cz.kpsys.portaro.inventory.match.MatchEntity;
import cz.kpsys.portaro.loan.LoanEntity;
import cz.kpsys.portaro.loan.ill.persist.MvsEntity;
import cz.kpsys.portaro.loan.reminder.LoanReminderEntity;
import cz.kpsys.portaro.loan.resolving.LoanRuleEntity;
import cz.kpsys.portaro.localization.LocalizationEntity;
import cz.kpsys.portaro.location.LocationEntity;
import cz.kpsys.portaro.messages.db.entity.MessageEntity;
import cz.kpsys.portaro.messages.participants.ThreadParticipantEntity;
import cz.kpsys.portaro.object.PhraseEntity;
import cz.kpsys.portaro.payment.TransactionEntity;
import cz.kpsys.portaro.pops.TenderEntity;
import cz.kpsys.portaro.pops.agreement.AgreementEntity;
import cz.kpsys.portaro.record.RecordEntity;
import cz.kpsys.portaro.record.collection.recordcollection.RecordCollectionEntity;
import cz.kpsys.portaro.record.collection.recordcollectioncategory.RecordCollectionCategory;
import cz.kpsys.portaro.record.collection.recordcollectionitem.RecordCollectionItemEntity;
import cz.kpsys.portaro.record.collection.userrecordcollection.UserRecordCollectionEntity;
import cz.kpsys.portaro.record.comment.CommentEntity;
import cz.kpsys.portaro.record.detail.data.FieldTypeEntity;
import cz.kpsys.portaro.record.detail.data.ViewableFieldEntity;
import cz.kpsys.portaro.record.edit.field007.Field007CodeEntity;
import cz.kpsys.portaro.record.edit.field007.Field007DocumentCategoryEntity;
import cz.kpsys.portaro.record.edit.field007.Field007LabelEntity;
import cz.kpsys.portaro.record.edit.field008.Field008CodeEntity;
import cz.kpsys.portaro.record.export.ExportEntity;
import cz.kpsys.portaro.record.export.RecordExportFilterEntity;
import cz.kpsys.portaro.record.export.batch.TransferredBatchEntity;
import cz.kpsys.portaro.record.fond.FondEntity;
import cz.kpsys.portaro.record.holding.RecordHoldingEntity;
import cz.kpsys.portaro.record.operation.RecordOperationEntity;
import cz.kpsys.portaro.record.operation.RecordOperationTypeEntity;
import cz.kpsys.portaro.search.facet.FacetTypeEntity;
import cz.kpsys.portaro.search.field.CustomSearchFieldEntity;
import cz.kpsys.portaro.security.permission.DefaultPermissionEntity;
import cz.kpsys.portaro.security.permission.UserPermissionEntity;
import cz.kpsys.portaro.setting.CustomSettingEntity;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.cardnumber.CardNumberSequenceEntity;
import cz.kpsys.portaro.user.contact.AddressEntity;
import cz.kpsys.portaro.user.payment.provider.csobgw.CsobGwPaymentEntity;
import cz.kpsys.portaro.user.payment.provider.gopay.GopayPaymentEntity;
import cz.kpsys.portaro.user.payment.provider.gpwebpay.GpwebpayPaymentEntity;
import cz.kpsys.portaro.user.prop.UserServicePropertyEntity;
import cz.kpsys.portaro.user.relation.UserRelationEntity;
import cz.kpsys.portaro.user.role.reader.ReaderCategoryDepartmentRelationEntity;
import cz.kpsys.portaro.user.role.reader.ReaderCategoryEntity;
import cz.kpsys.portaro.user.sdi.SdiRequestEntity;
import cz.kpsys.portaro.userpreferences.UserPreferenceEntity;
import cz.kpsys.portaro.util.logging.ClientSessionEntity;
import cz.kpsys.portaro.verbisbox.station.BoxStationEntity;
import cz.kpsys.portaro.view.domain.menu.MenuItemEntity;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl;
import org.hibernate.jpa.boot.spi.IntegratorProvider;
import org.hibernate.resource.jdbc.spi.PhysicalConnectionHandlingMode;
import org.hibernate.type.SqlTypes;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.jpa.repository.support.JpaRepositoryFactory;
import org.springframework.data.repository.core.support.RepositoryFactorySupport;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.SharedEntityManagerCreator;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.hibernate.cfg.AvailableSettings.*;

@Configuration
@EnableJpaRepositories
@EnableTransactionManagement
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class JpaConfig {

    private static final String HBM2DDL_VALIDATE = "validate";
    private static final String HBM2DDL_NONE = "";

    @NonNull DataSource notAutocommitDataSource;
    @NonNull DatabaseProperties databaseProperties;
    @NonNull BeanFactory applicationContext;
    @NonNull Provider<@NonNull ZoneId> databaseColumnsTimeZoneProvider;

    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory() {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(notAutocommitDataSource);
        em.setPackagesToScan(
                "cz.kpsys.portaro.matcher",
                "cz.kpsys.portaro.filter",
                AccountEntity.class.getPackage().getName(),
                AcquisitionWayEntity.class.getPackage().getName(),
                AddressEntity.class.getPackage().getName(),
                AmountTypeEntity.class.getPackage().getName(),
                AgreementEntity.class.getPackage().getName(),
                BoxStationEntity.class.getPackage().getName(),
                BindingEntity.class.getPackage().getName(),
                CaptureEntity.class.getPackage().getName(),
                CardNumberSequenceEntity.class.getPackage().getName(),
                ClientSessionEntity.class.getPackage().getName(),
                CommentEntity.class.getPackage().getName(),
                CsobGwPaymentEntity.class.getPackage().getName(),
                CustomSearchFieldEntity.class.getPackage().getName(),
                CustomSettingEntity.class.getPackage().getName(),
                CurrencyEntity.class.getPackage().getName(),
                DiscardionEntity.class.getPackage().getName(),
                DefaultPermissionEntity.class.getPackage().getName(),
                EventEntity.class.getPackage().getName(),
                ExchangeExemplarSetEntity.class.getPackage().getName(),
                ExportEntity.class.getPackage().getName(),
                ExemplarEntity.class.getPackage().getName(),
                FacetTypeEntity.class.getPackage().getName(),
                Field007DocumentCategoryEntity.class.getPackage().getName(),
                Field007CodeEntity.class.getPackage().getName(),
                Field007LabelEntity.class.getPackage().getName(),
                Field008CodeEntity.class.getPackage().getName(),
                FieldTypeEntity.class.getPackage().getName(),
                FileAccessTypeEntity.class.getPackage().getName(),
                FondEntity.class.getPackage().getName(),
                GopayPaymentEntity.class.getPackage().getName(),
                GpwebpayPaymentEntity.class.getPackage().getName(),
                InventoryEntity.class.getPackage().getName(),
                IssueEntity.class.getPackage().getName(),
                DepartmentEntity.class.getPackage().getName(),
                TransactionEntity.class.getPackage().getName(),
                VolumeEntity.class.getPackage().getName(),
                LoanRuleEntity.class.getPackage().getName(),
                LoanEntity.class.getPackage().getName(),
                LoanReminderEntity.class.getPackage().getName(),
                LocationEntity.class.getPackage().getName(),
                LocalizationEntity.class.getPackage().getName(),
                MatchEntity.class.getPackage().getName(),
                MenuItemEntity.class.getPackage().getName(),
                MessageEntity.class.getPackage().getName(),
                MvsEntity.class.getPackage().getName(),
                ObalkyknihEntity.class.getPackage().getName(),
                ParentableDirectoryEntity.class.getPackage().getName(),
                PhraseEntity.class.getPackage().getName(),
                ReaderCategoryDepartmentRelationEntity.class.getPackage().getName(),
                ReaderCategoryEntity.class.getPackage().getName(),
                RecordCollectionCategory.class.getPackage().getName(),
                RecordCollectionEntity.class.getPackage().getName(),
                RecordCollectionItemEntity.class.getPackage().getName(),
                RecordHoldingEntity.class.getPackage().getName(),
                RecordEntity.class.getPackage().getName(),
                RecordExportFilterEntity.class.getPackage().getName(),
                RecordOperationEntity.class.getPackage().getName(),
                RecordOperationTypeEntity.class.getPackage().getName(),
                RecordDemandEntity.class.getPackage().getName(),
                RootCustomDirectoryEntity.class.getPackage().getName(),
                SdiRequestEntity.class.getPackage().getName(),
                ThematicGroupEntity.class.getPackage().getName(),
                ThreadParticipantEntity.class.getPackage().getName(),
                TenderEntity.class.getPackage().getName(),
                TransferredBatchEntity.class.getPackage().getName(),
                UserPermissionEntity.class.getPackage().getName(),
                UserPreferenceEntity.class.getPackage().getName(),
                UserServicePropertyEntity.class.getPackage().getName(),
                UserRecordCollectionEntity.class.getPackage().getName(),
                UserRelationEntity.class.getPackage().getName(),
                ViewableFieldEntity.class.getPackage().getName()
        );

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);

        Map<String, Object> jpaProps = new HashMap<>();
        jpaProps.put(CONNECTION_HANDLING, PhysicalConnectionHandlingMode.DELAYED_ACQUISITION_AND_RELEASE_AFTER_TRANSACTION);
        jpaProps.put(HBM2DDL_AUTO, HBM2DDL_NONE);
        jpaProps.put(DIALECT, databaseProperties.getHibernateDialect());
        jpaProps.put(SHOW_SQL, "false");
        jpaProps.put(FORMAT_SQL, "false");
        jpaProps.put(JDBC_TIME_ZONE, databaseColumnsTimeZoneProvider.get());
        jpaProps.put(PREFERRED_INSTANT_JDBC_TYPE, SqlTypes.TIMESTAMP);
        jpaProps.put(PREFERRED_UUID_JDBC_TYPE, databaseProperties.getHibernatePreferredUuidType());
        jpaProps.put(EntityManagerFactoryBuilderImpl.INTEGRATOR_PROVIDER, (IntegratorProvider) () -> List.of(MetadataExtractorIntegrator.INSTANCE));
        em.setJpaPropertyMap(jpaProps);

        return em;
    }

    @Bean
    public PlatformTransactionManager transactionManager() {
        JpaTransactionManager bean = new JpaTransactionManager();
        bean.setEntityManagerFactory(entityManagerFactory().getObject());
        return bean;
    }

    @Bean
    public TransactionTemplateFactory defaultTransactionTemplateFactory() {
        return TransactionTemplateFactory.ofDefaultPropagationNotReadonly(transactionManager());
    }

    @Bean
    public TransactionTemplateFactory readonlyTransactionTemplateFactory() {
        return defaultTransactionTemplateFactory().withReadonly();
    }

    @Bean
    public RepositoryFactorySupport jpaRepositoryFactory() {
        // create spring managed shared EntityManager proxy (it manages entity manager creation across multiple threads)
        // if jpaRepositoryFactory uses native EntityManager instance, the repositories created with the factory will get out of sync with DB
        // (loaded entities will not reflect actual DB state, and they can not be updated, flushed or evicted from memory)
        EntityManager entityManager = SharedEntityManagerCreator.createSharedEntityManager(Objects.requireNonNull(entityManagerFactory().getObject()));
        RepositoryFactorySupport jpaRepositoryFactory = new JpaRepositoryFactory(entityManager);
        jpaRepositoryFactory.setBeanFactory(applicationContext);
        return jpaRepositoryFactory;
    }

}
