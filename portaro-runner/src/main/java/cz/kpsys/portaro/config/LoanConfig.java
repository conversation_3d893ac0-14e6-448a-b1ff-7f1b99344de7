package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.action.ActionSaver;
import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.auth.department.AuthContextualDepartmentsLoader;
import cz.kpsys.portaro.auth.department.CurrentAuthDepartmentsLoader;
import cz.kpsys.portaro.auth.password.PasswordChecker;
import cz.kpsys.portaro.business.command.CommandInterceptor;
import cz.kpsys.portaro.business.command.SequentialInterceptor;
import cz.kpsys.portaro.commons.cache.CacheDeleter;
import cz.kpsys.portaro.commons.cache.CompositeCacheDeleter;
import cz.kpsys.portaro.commons.cache.DynamicCache;
import cz.kpsys.portaro.commons.cache.GuavaTimedDynamicCache;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.convert.*;
import cz.kpsys.portaro.commons.date.DatetimeRangeToBase64StringConverter;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.io.FileZipperImpl;
import cz.kpsys.portaro.commons.json.FromStringConvertingJsonDeserializer;
import cz.kpsys.portaro.commons.json.ToStringConvertingJsonSerializer;
import cz.kpsys.portaro.commons.localization.ContextualLocaleLocalizer;
import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.contextual.ContextualVisibleDepartmentsLoader;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.database.IdAndIdsLoadableJpa;
import cz.kpsys.portaro.database.IntegerValueDatabaseLoader;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.databasestructure.LoanDb;
import cz.kpsys.portaro.datatype.DatatypedAcceptableValuesRegistry;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.exemplar.edit.AppserverExemplarSaver;
import cz.kpsys.portaro.exemplar.edit.JpaExemplarSaver;
import cz.kpsys.portaro.exemplar.loancategory.LoanCategory;
import cz.kpsys.portaro.export.*;
import cz.kpsys.portaro.file.directory.DirectoryInsightLoader;
import cz.kpsys.portaro.file.export.DataZippingFilesExporter;
import cz.kpsys.portaro.file.filecategory.FileCategoryBySystemTypeLoader;
import cz.kpsys.portaro.finance.AmountTypeLoader;
import cz.kpsys.portaro.finance.Currency;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.id.IDdFile;
import cz.kpsys.portaro.loan.*;
import cz.kpsys.portaro.loan.availability.*;
import cz.kpsys.portaro.loan.availability.timeslot.RecordBasedSlotAvailabilityResolver;
import cz.kpsys.portaro.loan.cancellation.AppserverCancellationService;
import cz.kpsys.portaro.loan.cancellation.CancelabilityService;
import cz.kpsys.portaro.loan.cancellation.CancelabilityServiceImpl;
import cz.kpsys.portaro.loan.cancellation.CancellationService;
import cz.kpsys.portaro.loan.export.LoanExportDto;
import cz.kpsys.portaro.loan.export.LoanToLoanExportDtoConverter;
import cz.kpsys.portaro.loan.export.UserActiveLoansExporterDelegatingToLoansExporter;
import cz.kpsys.portaro.loan.ill.IllSettingKeys;
import cz.kpsys.portaro.loan.lending.*;
import cz.kpsys.portaro.loan.lending.check.*;
import cz.kpsys.portaro.loan.lending.command.InternalLendingCommand;
import cz.kpsys.portaro.loan.lending.command.LendingCommand;
import cz.kpsys.portaro.loan.lending.extern.*;
import cz.kpsys.portaro.loan.lending.internal.InternalErrorHandlersProvider;
import cz.kpsys.portaro.loan.lending.webapi.LendingApiController;
import cz.kpsys.portaro.loan.lending.webapi.LendingRequest;
import cz.kpsys.portaro.loan.migration.LoanMigrationCommand;
import cz.kpsys.portaro.loan.migration.LoanMigrator;
import cz.kpsys.portaro.loan.notice.*;
import cz.kpsys.portaro.loan.penalty.AppserverDelayedReturnPenaltyService;
import cz.kpsys.portaro.loan.penalty.DelayedReturnPenaltyService;
import cz.kpsys.portaro.loan.quantum.LoanQuantityChangeLoader;
import cz.kpsys.portaro.loan.quantum.LoanQuantityChangesSummingLoanQuantityLoader;
import cz.kpsys.portaro.loan.quantum.SpringDbLoanQuantityChangeLoader;
import cz.kpsys.portaro.loan.reminder.*;
import cz.kpsys.portaro.loan.renewal.*;
import cz.kpsys.portaro.loan.request.AppserverStandardLoanRequestService;
import cz.kpsys.portaro.loan.request.NotAnonymousLoanRequest;
import cz.kpsys.portaro.loan.request.StandardLoanRequestService;
import cz.kpsys.portaro.loan.request.form.BuildingPreferenceSavingStandardLoanRequestServiceProxy;
import cz.kpsys.portaro.loan.request.form.LoanRequestFormFactory;
import cz.kpsys.portaro.loan.resolving.*;
import cz.kpsys.portaro.loan.returning.*;
import cz.kpsys.portaro.loan.returning.webapi.ReturningApiController;
import cz.kpsys.portaro.loan.returning.webapi.ReturningResultToResponseConverter;
import cz.kpsys.portaro.loan.timeslot.TimeslotOccupationLoader;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.mail.MailService;
import cz.kpsys.portaro.messages.sms.SmsSender;
import cz.kpsys.portaro.payment.DepartmentedTypedUserAmount;
import cz.kpsys.portaro.payment.TransactionCreator;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordSettingKeys;
import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.record.document.CompositeDownloadLinkContextualResolver;
import cz.kpsys.portaro.record.document.ContextIgnoringDownloadLinkContextualResolver;
import cz.kpsys.portaro.record.view.downloadlink.DownloadLinkContextualResolver;
import cz.kpsys.portaro.record.view.downloadlink.DownloadLinkResolverBy856Fields;
import cz.kpsys.portaro.record.view.downloadlink.DownloadLinkResolverByDirectoryInsight;
import cz.kpsys.portaro.record.view.downloadlink.RecordMediaViewerUrlGenerator;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.field.StaticSearchFields;
import cz.kpsys.portaro.security.*;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.security.export.SecuredExporter;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sip2.server.impl.handler.DebtAmountLendingPermissionResolver;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.template.TemplateLoadingExportDescriptorLoader;
import cz.kpsys.portaro.template.Templates;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.Library;
import cz.kpsys.portaro.user.UserLoader;
import cz.kpsys.portaro.user.locale.UserLocaleResolver;
import cz.kpsys.portaro.user.role.editor.LibrarianPrivileges;
import cz.kpsys.portaro.user.role.reader.PrintType;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import cz.kpsys.portaro.user.sec.SecurityActions;
import cz.kpsys.portaro.userpreferences.UserPrefAccessor;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import cz.kpsys.portaro.view.web.page.LoanConfirmationPrintPageController;
import cz.kpsys.portaro.view.web.rest.loan.*;
import cz.kpsys.portaro.view.web.rest.loan.migration.LoanMigrationApiController;
import cz.kpsys.portaro.view.web.rest.user.DelayedReturnPenaltyApiController;
import cz.kpsys.portaro.web.page.ModelAndPageViewFactory;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.val;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.format.FormatterRegistry;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ExecutorService;

import static cz.kpsys.portaro.app.CatalogConstants.Export.Exporters;
import static cz.kpsys.portaro.commons.localization.Texts.*;
import static cz.kpsys.portaro.security.PermissionResolver.*;
import static cz.kpsys.portaro.security.PermissionResult.allow;
import static cz.kpsys.portaro.user.BasicUser.ROLE_LIBRARIAN;
import static cz.kpsys.portaro.user.BasicUser.ROLE_READER;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LoanConfig {

    public static final int SEARCHED_LOANS_LIMIT_ITEMS = 400;

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull TransactionTemplateFactory readonlyTransactionTemplateFactory;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull EntityManager entityManager;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull Runnable saveTransactionAuthenticator;
    @NonNull ModelBeanBuilder modelBeanBuilder;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull DmlAppserverService dmlAppserverService;
    @NonNull ObjectMapper appserverXmlMapper;
    @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedDocumentLoader;
    @NonNull AllByIdsLoadable<Record, UUID> detailedDocumentLoader;
    @NonNull DynamicCache<Record> recordCache;
    @NonNull UserLoader userLoader;
    @NonNull IdAndIdsLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull SettingLoader settingLoader;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull ContextualProvider<Department, Converter<UUID, @NonNull String>> recordIdToRecordDetailUrlConverterProvider;
    @NonNull Translator<Department> translator;
    @NonNull UserLocaleResolver userLocaleResolver;
    @NonNull UserPrefAccessor userPrefAccessor;
    @NonNull ContextualLocaleLocalizer<Department> localizer;
    @NonNull TemplateEngine templateEngine;
    @NonNull MailService mailService;
    @NonNull SmsSender smsSender;
    @NonNull SecurityManager securityManager;
    @NonNull ObjectMapper objectMapper;
    @NonNull AllByIdsLoadable<Exemplar, Integer> exemplarLoader;
    @NonNull PageSearchLoader<MapBackedParams, Integer, RangePaging> exemplarIdSearchLoader;
    @NonNull ByIdLoadable<Location, Integer> locationLoader;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull ByIdLoadable<LoanCategory, String> loanCategoryLoader;
    @NonNull ByIdLoadable<ReaderCategory, String> readerCategoryLoader;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull AmountTypeLoader amountTypeLoader;
    @NonNull ExecutorService executorService;
    @NonNull DirectoryInsightLoader directoryInsightLoader;
    @NonNull SecurityAccessor securityAccessor;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull PermissionFactory permissionFactory;
    @NonNull CurrentAuthDepartmentsLoader currentAuthEditableDepartmentsLoader;
    @NonNull ExporterResolver exporterResolver;
    @NonNull TemplateLoadingExportDescriptorLoader templateLoadingExportDescriptorLoader;
    @NonNull StaticExportDescriptorLoader staticExportDescriptorLoader;
    @NonNull FormatterRegistry conversionService;
    @NonNull SimpleModule objectMapperModule;
    @NonNull ActionSaver actionSaver;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull AppserverExemplarSaver pureExemplarSaver;
    @NonNull JpaExemplarSaver basicJpaExemplarSaver;
    @NonNull ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordParagraphItemsConverter;
    @NonNull TimeslotOccupationLoader timeslotOccupationLoader;
    @NonNull PasswordChecker passwordChecker;
    @NonNull FileDataStreamer securedFileThumbnailDataStreamer;
    @NonNull RecordBasedSlotAvailabilityResolver recordBasedSlotAvailabilityResolver;
    @NonNull AuthContextualDepartmentsLoader loanAuthContextualDepartmentsLoader;
    @NonNull ModelAndPageViewFactory modelAndPageViewFactory;
    @NonNull DatatypedAcceptableValuesRegistry allowedDatatypeToAllValuesProviderMap;
    @NonNull DatatypedAcceptableValuesRegistry allDatatypeToAllValuesProviderMap;
    @NonNull Eventer eventer;
    @NonNull Provider<@NonNull Currency> defaultCurrencyProvider;
    @NonNull AuthenticatedContextualProvider<Department, List<Department>> editableDepartmentsAuthenticatedContextualProvider;
    @NonNull ParameterizedSearchLoader<MapBackedParams, DepartmentedTypedUserAmount> typedUserBilanceSearchLoader;
    @NonNull DatabaseProperties databaseProperties;
    @NonNull TransactionCreator transactionCreator;
    @NonNull FileCategoryBySystemTypeLoader fileCategoryBySystemTypeLoader;

    @Bean
    public LoanApiController loanApiController() {
        return new LoanApiController(
                loanSearchLoader(),
                securedLoansProviderService(),
                departmentAccessor,
                loanService(),
                loanReadyProcessor(),
                loanReadyNoticeSender(),
                userLoader);
    }

    @Bean
    public AvailabilityApiController availabilityController() {
        return new AvailabilityApiController(
                documentAvailabilityService(),
                exemplarAvailabilityService());
    }

    @Bean
    public LoanRequestApiController loanRequestApiController() {
        return new LoanRequestApiController(
                loanService(),
                loanRequestFormFactory(),
                standardLoanRequestService(),
                documentAvailabilityService()
        );
    }

    @Bean
    public CancellationApiController cancellationApiController() {
        return new CancellationApiController(loanService());
    }

    @Bean
    public LendingApiController lendingApiController() {
        return new LendingApiController(lendingService(), departmentAccessor, securityAccessor);
    }

    @Bean
    public DelayedReturnPenaltyApiController delayedReturnPenaltyApiController() {
        return new DelayedReturnPenaltyApiController(delayedReturnPenaltyService(), securityManager);
    }

    @Bean
    public RenewalApiController renewalApiController() {
        return new RenewalApiController(actionSaver, renewalService(), loanSearchLoader());
    }

    @Bean
    public ReturningApiController returningApiController() {
        return new ReturningApiController(
                returningService(),
                departmentAccessor,
                securityAccessor,
                returningResultToResponseConverter()
        );
    }

    @Bean
    public ContextualFunction<@NonNull ReturningResult, @NonNull Department, @NonNull ActionResponse> returningResultToResponseConverter() {
        return new ReturningResultToResponseConverter(userLoader);
    }

    @Bean
    public LoanConfirmationPrintPageController loanConfirmationPrintPageController() {
        return new LoanConfirmationPrintPageController(
                templateEngine,
                modelAndPageViewFactory,
                securedLoansProviderService(),
                new ListToModifiedListConverter<>(new LoanToLoanExportDtoConverter())
        );
    }

    @Bean
    public LoanMigrationApiController loanMigrationApiController() {
        return new LoanMigrationApiController(loanMigrator());
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<LendingRequest> lendingRequestPreValidationModifier() {
        return new LendingRequest.LendingRequestPreValidationModifier();
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<LendingRequest> lendingRequestDefaulter() {
        return new LendingRequest.LendingRequestDefaulter();
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<LoanReturnRequest> loanReturnRequestPreValidationModifier() {
        return new LoanReturnRequest.LoanReturnRequestPreValidationModifier();
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<LoanReturnRequest> loanReturnRequestDefaulter() {
        return new LoanReturnRequest.LoanReturnRequestDefaulter(loanQuantityLoader());
    }

    @Bean
    public ContextualProvider<Department, @NonNull LoanSetting> loanSettingProvider() {
        return department -> new EnablableStaticLoanSetting(
                settingLoader.getOn(LoanSettingKeys.LOANS_ENABLED, department),
                settingLoader.getOn(LoanSettingKeys.ORDERING_TYPE, department),
                settingLoader.getOn(LoanSettingKeys.RESERVING_TYPE, department)
        );
    }

    /**
     * povoleni pujcovani ctenarum s proslou registraci
     */
    @Bean
    public ContextualProvider<Department, @NonNull Boolean> lendingToExpiredReaderAllowed() {
        return ContextIgnoringContextualProvider.of(false);
    }

    @Bean
    public LoanService loanService() {
        return new LoanService(
                securityManager,
                externalLendingService(),
                cancellationService(),
                loanQuantityLoader(),
                renewabilityService(),
                cancelabilityService(),
                timeslotOccupationLoader
        );
    }

    @Bean
    public LoansProviderService loansProviderService() {
        return new SearchingLoansProviderService(
                loanSettingProvider(),
                loanSearchLoader(),
                loanAuthContextualDepartmentsLoader(),
                settingLoader.getOnRootProvidedList(RecordSettingKeys.FORBIDDEN_RECORDS)
        );
    }

    @Bean
    public AuthContextualDepartmentsLoader loanAuthContextualDepartmentsLoader() {
        return new LoanAuthContextualDepartmentsLoader(
                currentAuthEditableDepartmentsLoader,
                departmentAccessor,
                settingLoader.getDepartmentedProvider(LoanSettingKeys.EDITABLE_SUBDEPARTMENTS_ONLY_LOANS_SHOW_ENABLED),
                ContextualVisibleDepartmentsLoader.ofSettingsKey(departmentAccessor, settingLoader, LoanSettingKeys.LOANS_PROVIDED_TO_DEPARTMENTS)
        );
    }

    @Bean
    public LoansProviderService securedLoansProviderService() {
        return new SecuredLoansProviderService(loansProviderService(), securityManager);
    }

    @Bean
    public LoanRequestFormFactory loanRequestFormFactory() {
        return new LoanRequestFormFactory(
                nonDetailedDocumentLoader,
                settingLoader.getDepartmentedProvider(LoanSettingKeys.DEFAULT_LOAN_REQUEST_INTEREST_DAYS),
                translator,
                documentAvailabilityService(),
                exemplarAvailabilityService(),
                userLocaleResolver,
                userPrefAccessor,
                settingLoader.getDepartmentedProvider(LoanSettingKeys.RESERVATION_DATE_TO_REQUIRED)
        );
    }

    @Bean
    public Codebook<LoanRule<LoanRuleGroupedValue>, Integer> sortedLoanCombinationValueEntityLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedByJpa(LoanRuleEntity.class)
                .converted(new EntitiesToLoanRulesConverter(
                        readerCategoryLoader, loanCategoryLoader, defaultCurrencyProvider))
                .sorted(LoanRule.priorityComparator())
                .staticCached(LoanRuleEntity.class.getSimpleName())
                .build();
    }

    @Bean
    public LoanRuleValueResolver<Boolean> loanLendableResolver() {
        return new FirstRuleMatchingLoanRuleValueResolver<>(sortedLoanCombinationValueEntityLoader(), LoanRuleGroupedValue::lendable);
    }

    @Bean
    public LoanRuleValueResolver<Integer> loanPeriodDaysResolver() {
        return new FirstRuleMatchingLoanRuleValueResolver<>(sortedLoanCombinationValueEntityLoader(), LoanRuleGroupedValue::loanPeriodDays);
    }

    @Bean
    public LoanRulePriceResolver loanPriceResolver() {
        LoanRuleValueResolver<Price> delegate = new FirstRuleMatchingLoanRuleValueResolver<>(
                sortedLoanCombinationValueEntityLoader(), LoanRuleGroupedValue::loanPrice);
        return new LoanRulePriceResolver(delegate, defaultCurrencyProvider);
    }

    @Bean
    public LendingService lendingService() {
        AppserverLendingService pureService = new AppserverLendingService(
                mappingAppserver,
                circulatingAffectedExemplarCacheDeleter(),
                appserverXmlMapper,
                nonDetailedLoanLoader(),
                externalLoanRecordLoader(),
                externalErrorHandlersProvider(),
                directErrorHandlersProvider()
        );
        LendingServiceVerifyingLenderProxy lenderVerifying = new LendingServiceVerifyingLenderProxy(
                pureService,
                settingLoader.getDepartmentedProvider(LoanSettingKeys.LENDER_READER_VERIFYING_ENABLED),
                userLoader,
                passwordChecker
        );
        InterceptingLendingService intercepting = new InterceptingLendingService(
                lenderVerifying,
                lendingInterceptor()
        );
        return new SecuredLendingService(intercepting, securityManager);
    }

    @Bean
    public LendingService lendingServiceWithoutVerification() {
        AppserverLendingService pureService = new AppserverLendingService(
                mappingAppserver,
                circulatingAffectedExemplarCacheDeleter(),
                appserverXmlMapper,
                nonDetailedLoanLoader(),
                externalLoanRecordLoader(),
                externalErrorHandlersProvider(),
                directErrorHandlersProvider()

        );
        return new InterceptingLendingService(
                pureService,
                lendingInterceptor()
        );
    }

    @Bean
    public LendingService directLendingService() {
        InternalLendingService pureService = new InternalLendingService(
                new CommandToLendingDataConverter(
                        userLoader,
                        loanSearchLoader()
                ),
                loanIdGenerator(),
                loanLoader(),
                loanEntityLoader(),
                loanEntitySaver(),
                loanLendableResolver(),
                loanPeriodDaysResolver(),
                lendingService()
        );
        LendingServiceVerifyingLenderProxy lenderVerifying = new LendingServiceVerifyingLenderProxy(
                pureService,
                settingLoader.getDepartmentedProvider(LoanSettingKeys.LENDER_READER_VERIFYING_ENABLED),
                userLoader,
                passwordChecker
        );
        InterceptingLendingService intercepting = new InterceptingLendingService(
                lenderVerifying,
                lendingInterceptor()
        );
        FallbackingLendingService fallback = new FallbackingLendingService(
                settingLoader.getDepartmentedProvider(IllSettingKeys.ILL_PORTARO_PROCESS_ENABLED),
                intercepting,
                lendingService()
        );
        return new SecuredLendingService(fallback, securityManager);
    }

    private CommandInterceptor<LendingCommand> lendingInterceptor() {
        return new SequentialInterceptor<>(List.of(
                new InternalOnlyPassingLendingCommandInterceptor(CheckExemplarIsFree.forLoan(exemplarHolderService(), InternalLendingCommand::lender, InternalLendingCommand::exemplar)),
                new CheckDebtsOfExternalLendingLender(settingLoader.getDepartmentedProvider(LoanSettingKeys.EXT_LOANS_CHECK_DEBTS), debtAmountLendingPermissionResolver()),
                new CheckOnlyOneExemplarOfRecord(userLoader, loanSearchLoader(), loanAuthContextualDepartmentsLoader,
                        settingLoader.getDepartmentedProvider(LoanSettingKeys.LENDING_MAX_ONE_PHYSICAL_EXEMPLAR_OF_ONE_RECORD_BY_NON_LIBRARIAN),
                        settingLoader.getDepartmentedProvider(LoanSettingKeys.LENDING_WARN_MAX_ONE_EXEMPLAR_OF_SAME_RECORD)),
                new FillLendingOnDifferentDepartment(settingLoader.getDepartmentedProvider(LoanSettingKeys.LENDING_CHECK_DIFFERENT_DEPARTMENT)),
                new FillLendingOnDifferentLocation(settingLoader.getDepartmentedProvider(LoanSettingKeys.LENDING_CHECK_DIFFERENT_LOCATION)),
                new FillLendingUserHasDebt(settingLoader.getDepartmentedProvider(LoanSettingKeys.LENDING_CHECK_USER_HAS_DEBT))
        ));
    }


    @Bean
    public ReturningService returningService() {
        AppserverReturningService appserverService = new AppserverReturningService(
                appserverXmlMapper,
                mappingAppserver,
                circulatingAffectedExemplarCacheDeleter(),
                loanLoader(),
                defaultCurrencyProvider
        );
        StandardReturningService standardService = new StandardReturningService(
                loanEntityLoader(),
                loanEntitySaver(),
                circulatingAffectedExemplarCacheDeleter()
        );
        FallbackingReturningService fallbackService = new FallbackingReturningService(
                ContextIgnoringContextualProvider.of(false),
                standardService,
                appserverService
        );
        InterceptingReturningService intercepting = new InterceptingReturningService(
                fallbackService,
                returningInterceptor()
        );
        return new SecuredReturningService(intercepting, securityManager);
    }

    public CommandInterceptor<LoanReturnCommand> returningInterceptor() {
        return new SequentialInterceptor<>(List.of(
                new FillReturningOnDifferentDepartment(settingLoader.getDepartmentedProvider(LoanSettingKeys.RETURNING_CHECK_DIFFERENT_DEPARTMENT)),
                new FillReturningDifferentLocation(settingLoader.getDepartmentedProvider(LoanSettingKeys.RETURNING_CHECK_DIFFERENT_LOCATION))
        ));
    }


    @Bean
    public CacheDeleter<Exemplar> circulatingAffectedExemplarCacheDeleter() {
        return CompositeCacheDeleter.of(
                exemplar -> documentAvailabilityCache().deleteFromCacheById(exemplar.getRecordId()),
                exemplar -> exemplarAvailabilityCache().deleteFromCacheById(AppserverDataBackedExemplarAvailability.createId(exemplar.getId()))
        );
    }


    @Bean
    public ExemplarAwareLoanStateChanger stateChanger() {
        return new ExemplarAwareLoanStateChanger(dmlAppserverService);
    }


    @Bean
    public LoanReadyProcessor loanReadyProcessor() {
        //vytvarec dluhu za rezervaci / objednavku
        LoanReadyNoticeSendDebtCreator debtCreator = new LoanReadyNoticeSendDebtCreator(
                defaultTransactionTemplateFactory.get(),
                readerCatLoanCatSettingLoader(),
                amountTypeLoader,
                departmentAccessor,
                editableDepartmentsAuthenticatedContextualProvider,
                defaultCurrencyProvider,
                transactionCreator
        );

        var loanProcessor = new NoticeSendingDebtCreatingStateChangingLoanProcessor(loanReadyNoticeSender(), stateChanger(), debtCreator);

        var interceptor = new SequentialInterceptor<>(List.of(
                CheckExemplarIsFree.forNoticingLender(exemplarHolderService(), LoanReadyNotice::waitingLoan)
        ));

        return new InterceptingLoanReadyProcessor(loanProcessor, interceptor);
    }

    @Bean
    public ExternalErrorHandlersProvider externalErrorHandlersProvider() {
        return new ExternalErrorHandlersProvider();
    }

    @Bean
    public InternalErrorHandlersProvider directErrorHandlersProvider() {
        return new InternalErrorHandlersProvider(basicUserLoader);
    }

    @Bean
    public LoanReadyNoticeSender loanReadyNoticeSender() {
        LoanReadyNoticeMailSender mail = new LoanReadyNoticeMailSender(
                templateEngine,
                mailService,
                settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE),
                recordsToViewableRecordParagraphItemsConverter
        );
        LoanReadyNoticeSmsSender sms = new LoanReadyNoticeSmsSender(
                templateEngine,
                smsSender,
                settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE),
                recordsToViewableRecordParagraphItemsConverter
        );
        LoanReadyNoticeFileGenerator post = new LoanReadyNoticeFileGenerator(serverUrlProvider.throwingWhenNull());

        Map<PrintType, LoanReadyNoticeSender> map = Map.of(
                PrintType.EMAIL, mail,
                PrintType.SMS, sms,
                PrintType.POST, post,
                PrintType.EMAIL_AND_SMS, new LoanReadyNoticeMultipleSender(List.of(mail, sms))
        );

        return new LoanReadyNoticeSenderByPrintTypeDispatcher(map);
    }


    @Bean
    public ExemplarHolderService exemplarHolderService() {
        return new ExemplarHolderService(loanSearchLoader());
    }


    @Bean
    public StandardLoanRequestService standardLoanRequestService() {
        return new BuildingPreferenceSavingStandardLoanRequestServiceProxy(
                new AppserverStandardLoanRequestService(mappingAppserver, new DatetimeRangeToBase64StringConverter(objectMapper), appserverXmlMapper),
                userPrefAccessor
        );
    }


    @Bean
    public LendingService externalLendingService() {
        AppserverLendingService pureService = new AppserverLendingService(
                mappingAppserver,
                circulatingAffectedExemplarCacheDeleter(),
                appserverXmlMapper,
                nonDetailedLoanLoader(),
                externalLoanRecordLoader(),
                externalErrorHandlersProvider(),
                directErrorHandlersProvider()
        );

        MailSendingExternalLendingService mailSendingService = new MailSendingExternalLendingService(
                pureService,
                mailService,
                templateEngine,
                userLocaleResolver,
                externalLoanRecordLoader(),
                localizer
        );

        InterceptingLendingService interceptor = new InterceptingLendingService(
                mailSendingService,
                lendingInterceptor()
        );

        return new SecuredLendingService(interceptor, securityManager);
    }

    @Bean
    public @NonNull ByIdLoadable<ExternalLoanRecord, Integer> externalLoanRecordLoader() {
        return new SpringDbExternalLoanRecordLoader(jdbcTemplate, queryFactory);
    }

    @Bean
    public RenewalService renewalService() {
        AppserverRenewalService bean = new AppserverRenewalService(mappingAppserver, appserverXmlMapper, renewalAppserverRequestCreator());
        bean.setEnabledProvider(settingLoader.getOnRootProvider(LoanSettingKeys.RENEWAL_ENABLED));
        return bean;
    }


    @Bean
    public DelayedReturnPenaltyService delayedReturnPenaltyService() {
        return new AppserverDelayedReturnPenaltyService(appserverXmlMapper, mappingAppserver);
    }


    @Bean
    public RenewabilityService renewabilityService() {
        AppserverRenewabilityService bean = new AppserverRenewabilityService(mappingAppserver, appserverXmlMapper, renewalAppserverRequestCreator());
        bean.setEnabledProvider(settingLoader.getDepartmentedProvider(LoanSettingKeys.RENEWAL_ENABLED));
        return bean;
    }


    @Bean
    public RenewalAppserverRequestCreator renewalAppserverRequestCreator() {
        RenewalAppserverRequestCreator creator = new RenewalAppserverRequestCreator();
        creator.setRenewalByReaderOfDelayedLoansAllowed(settingLoader.getDepartmentedProvider(LoanSettingKeys.DELAYED_LOANS_RENEWAL_ENABLED));
        creator.setRenewalByReaderWithDebtsAllowed(settingLoader.getDepartmentedProvider(LoanSettingKeys.DEBTOR_LOANS_RENEWAL_BY_READER_ENABLED));
        creator.setRenewalByExpiredReaderAllowed(settingLoader.getDepartmentedProvider(LoanSettingKeys.EXPIRED_READER_LOANS_RENEWAL_BY_READER_ENABLED));
        return creator;
    }


    @Bean
    public DynamicCache<AppserverDataBackedExemplarAvailability> exemplarAvailabilityCache() {
        return GuavaTimedDynamicCache.ofIdentified(Duration.ofSeconds(5), true);
    }


    @Bean
    public DynamicCache<DocumentAvailability> documentAvailabilityCache() {
        return GuavaTimedDynamicCache.ofIdentified(Duration.ofSeconds(5), true);
    }

    @Bean
    public CancelabilityService cancelabilityService() {
        ContextualProvider<Department, @NonNull Boolean> unprocessedOrdersCancellingEnabled = ContextIgnoringContextualProvider.of(true);
        ContextualProvider<Department, @NonNull Boolean> processedOrdersCancellingEnabled = settingLoader.getDepartmentedProvider(LoanSettingKeys.PROCESSED_ORDERS_CANCELLING_ENABLED);
        ContextualProvider<Department, @NonNull Boolean> unsentReservationsCancellingEnabled = ContextIgnoringContextualProvider.of(true);
        ContextualProvider<Department, @NonNull Boolean> sentReservationsCancellingEnabled = settingLoader.getDepartmentedProvider(LoanSettingKeys.SENT_RESERVATIONS_CANCELLING_ENABLED);
        ContextualProvider<Department, @NonNull Boolean> pickupReadySeekedIllCancellingEnabled = ContextIgnoringContextualProvider.of(false);
        return new CancelabilityServiceImpl(
                unprocessedOrdersCancellingEnabled,
                processedOrdersCancellingEnabled,
                unsentReservationsCancellingEnabled,
                sentReservationsCancellingEnabled,
                pickupReadySeekedIllCancellingEnabled
        );
    }

    @Bean
    public AllByIdsLoadable<LoanReminder, Integer> allByIdsLoanReminderLoader() {
        LoanReminderFromEntityConverter loanReminderFromEntityConverter = new LoanReminderFromEntityConverter(loanLoader());

        return ChunkingAllByIdsLoader.ofIdentified(
                modelBeanBuilder.allByIdsLoader(LoanReminderEntity.class, loanReminderFromEntityConverter).build()
        );
    }

    @Bean
    public PageSearchLoader<MapBackedParams, LoanReminder, RangePaging> loanReminderSearchLoader() {
        SpringDbLoanReminderIdSearchLoader idSearchLoader = new SpringDbLoanReminderIdSearchLoader(jdbcTemplate, queryFactory);
        return ResultConvertingPageSearchLoader.createConvertingFromIds(idSearchLoader, allByIdsLoanReminderLoader());
    }

    @Bean
    public IdAndIdsLoadable<Loan, LoanId> loanLoader() {
        return createLoanLoader(detailedDocumentLoader); //dokumenty u vypujcek nacitame i s detailem
    }

    @Bean
    public IdAndIdsLoadable<Loan, LoanId> nonDetailedLoanLoader() {
        return createLoanLoader(nonDetailedDocumentLoader); //dokumenty u vypujcek nacitame bez detailu
    }

    @NonNull
    private ByIdLoadableByAllByIdsLoadable<Loan, LoanId> createLoanLoader(AllByIdsLoadable<Record, UUID> documentLoader) {
        AllByIdsLoadable<ActiveLoanEntity, LoanId> entityLoader = new SpringDbAllByIdsLoanLoader(notAutoCommittingJdbcTemplate, queryFactory, databaseProperties);
        Converter<List<? extends ActiveLoanEntity>, List<Loan>> entitiesToLoansConverter = new EntitiesToLoansConverter(
                locationLoader,
                documentLoader,
                exemplarLoader,
                basicUserLoader,
                departmentAccessor,
                loanCategoryLoader,
                LoanType.CODEBOOK
        );
        IdAndIdsLoadable<Loan, LoanId> convertingLoader = new IdAndIdsLoadableConverting<>(
                entityLoader,
                EToEConverter.create(),
                entitiesToLoansConverter
        );
        TransactionalIdAndIdsLoadable<Loan, LoanId> transactionalLoader = new TransactionalIdAndIdsLoadable<>(
                convertingLoader,
                readonlyTransactionTemplateFactory.get()
        );
        AllByIdsLoadable<Loan, LoanId> allByIdsLoanLoader = ChunkingAllByIdsLoader.ofIdentified(transactionalLoader);

        return new ByIdLoadableByAllByIdsLoadable<>(allByIdsLoanLoader, Loan.class);
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Loan> loanSearchLoader() {
        val springDbLoanIdSearchLoader = new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                ResultConvertingPageSearchLoader.createConvertingFromIds(
                        loanIdSearchLoader(),
                        loanLoader()
                ));

        PageSearchLoader<MapBackedParams, Loan, RangePaging> pageSearchLoader = new PageSearchLoader<>() {

            @Override
            public Chunk<Loan, RangePaging> getPage(RangePaging paging, SortingItem customSorting, MapBackedParams params) {
                if (params.hasNotNull(LoanConstants.SearchParams.EXEMPLAR_Q)) {
                    return byExemplarQLoanSearchLoader().getPage(paging, customSorting, params);
                }
                return springDbLoanIdSearchLoader.getPage(paging, customSorting, params);
            }

            @Override
            public int getTotalElements(MapBackedParams params) {
                if (params.hasNotNull(LoanConstants.SearchParams.EXEMPLAR_Q)) {
                    return byExemplarQLoanSearchLoader().getTotalElements(params);
                }
                return springDbLoanIdSearchLoader.getTotalElements(params);
            }
        };

        return new ParameterizedSearchLoaderImpl<>(MapBackedParams::createEmpty, pageSearchLoader);
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, LoanId> loanIdSearchLoader() {
        TransactionalPageSearchLoader<MapBackedParams, LoanId, RangePaging> transactional = new TransactionalPageSearchLoader<>(
                new SpringDbLoanIdSearchLoader(notAutoCommittingJdbcTemplate, queryFactory, departmentAccessor, databaseProperties),
                readonlyTransactionTemplateFactory.get()
        );
        return new ParameterizedSearchLoaderImpl<>(MapBackedParams::createEmpty, transactional);
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Loan> byExemplarQLoanSearchLoader() {
        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                new ByExemplarQLoanPageSearchLoader(loanSearchLoader(), exemplarIdSearchLoader)
        );
    }

    @Bean
    public ContextualProvider<Department, @NonNull Integer> loanIdGenerator() {
        return ContextIgnoringContextualProvider.of(IntegerValueDatabaseLoader.ofSequenceValueDbDependent(LoanDb.VYPUC.SEQ_ID_VYPUC_FB, LoanDb.VYPUC.SEQ_ID_VYPUC_PG, jdbcTemplate, queryFactory, databaseProperties));
    }

    @Bean
    public JpaRepository<LoanEntity, Integer> loanEntityJpaRepository() {
        return new SimpleJpaRepository<>(LoanEntity.class, entityManager);
    }

    @Bean
    public IdAndIdsLoadableJpa<LoanEntity, Integer> loanEntityLoader() {
        return new IdAndIdsLoadableJpa<>(loanEntityJpaRepository());
    }

    @Bean
    public Saver<LoanEntity, LoanEntity> loanEntitySaver() {
        var saver = new GenericHookableSaver<>(new FlushingJpaSaver<>(loanEntityJpaRepository()));
        saver.addPreHook(saveTransactionAuthenticator);
        return new TransactionalSaver<>(saver, defaultTransactionTemplateFactory.get());
    }

    @Bean
    public LoanQuantityChangeLoader loanQuantityChangeLoader() {
        return new SpringDbLoanQuantityChangeLoader(jdbcTemplate, queryFactory);
    }


    @Bean
    public LoanQuantityLoader loanQuantityLoader() {
        return new LoanQuantityChangesSummingLoanQuantityLoader(loanQuantityChangeLoader());
    }


    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Loan> nonDetailedLoanSearchLoader() {
        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                ResultConvertingPageSearchLoader.createConvertingFromIds(
                        loanIdSearchLoader(),
                        nonDetailedLoanLoader()));
    }


    @Bean
    public DocumentAvailabilityService documentAvailabilityService() {
        DownloadLinkContextualResolver<Department> downloadLinkResolver = new CompositeDownloadLinkContextualResolver<>(List.of(
                new DownloadLinkResolverByDirectoryInsight<>(
                        directoryInsightLoader,
                        new RecordMediaViewerUrlGenerator<>(
                                serverUrlProvider.throwingWhenNull(),
                                recordIdToRecordDetailUrlConverterProvider
                        ),
                        fileCategoryBySystemTypeLoader
                ),
                new ContextIgnoringDownloadLinkContextualResolver<>(new DownloadLinkResolverBy856Fields())
        ));
        DocumentAvailabilityService bean = new DocumentAvailabilityServiceAppserver(
                mappingAppserver,
                documentAvailabilityCache(),
                securityManager,
                StringToIntegerToAnyConverter.nullConvertingToNull(new IdToObjectConverter<>(departmentLoader)),
                StringToAnyListConverter.create(StringToIntegerToAnyConverter.nullConvertingToNull(new IdToObjectConverter<>(departmentLoader))),
                lastReturnDateLoader(),
                lastUserLoanDateLoader(),
                new SpringDbReservationsCountLoader(jdbcTemplate, queryFactory),
                preparedOrReadyToPrepareLoansCountProvider(),
                loanSettingProvider(),
                executorService,
                authenticationHolder,
                downloadLinkResolver,
                new ProvidedFondIdComparingExternalLoanRecordFondResolver(settingLoader.getOnRootProvider(SettingKeys.EXTERNAL_RESOURCES_FOND)),
                appserverXmlMapper,
                StaticProvider.of(CoreConstants.CZECH_TIME_ZONE_ID)
        );
        bean = new ExternalRecordSupportingDocumentAvailabilityService(bean);
        return new SlotTestingDocumentAvailabilityService(false, bean, recordBasedSlotAvailabilityResolver);
    }


    @Bean
    public ContextualFunction<Record, Department, Integer> preparedOrReadyToPrepareLoansCountProvider() {
        return (recordId, ctx) -> {
            MapBackedParams customParams = MapBackedParams.build(StaticParamsModifier.of(
                    LoanConstants.SearchParams.LOAN_STATE, LoanState.processedOrReadyToProcess()
            ));
            return familyDepartmentedRecordLoansCountLoader().getOn(recordId, customParams, ctx);
        };
    }

    @Bean
    public ItemCustomizableContextualSearchFunction<Record, Department, Integer> familyDepartmentedRecordLoansCountLoader() {
        return new SearchingDepartmentedRecordLoansCountLoader(
                loanIdSearchLoader(),
                departmentAccessor,
                HierarchyLoadScope.FAMILY
        );
    }


    @Bean
    public ExemplarAvailabilityService exemplarAvailabilityService() {
        return new AppserverExemplarAvailabilityService(
                mappingAppserver,
                exemplarAvailabilityCache(),
                securityManager,
                loanSettingProvider(),
                appserverXmlMapper,
                StringToAnyListConverter.create(StringToIntegerToAnyConverter.nullConvertingToNull(new IdToObjectConverter<>(departmentLoader)))
        );
    }

    @Bean
    public ContextualFunction<Exemplar, Department, @NonNull Boolean> exemplarFreeResolver() {
        return new ServiceDelegatingExemplarFreeResolver(exemplarAvailabilityService());
    }


    @Bean
    public LastReturnDateLoader lastReturnDateLoader() {
        return new SpringDbLastReturnDateLoader(jdbcTemplate, queryFactory);
    }


    @Bean
    public LastUserLoanDateLoader lastUserLoanDateLoader() {
        return new SpringDbLastUserLoanDateLoader(jdbcTemplate, queryFactory);
    }


    @Bean
    public CancellationService cancellationService() {
        return new AppserverCancellationService(
                appserverXmlMapper,
                recordCache,
                mappingAppserver,
                cancelabilityService());
    }


    @Bean
    public ReaderCatLoanCatSettingLoader readerCatLoanCatSettingLoader() {
        return new ReaderCatLoanCatSettingLoaderAppserver(appserverXmlMapper, mappingAppserver);
    }


    @Bean
    public EnabledExternalLoanServicesProvider enabledExternalLoadServicesProvider() {
        return new EnabledExternalLoanServicesProvider(
                settingLoader.getOnRootProvider(LoanSettingKeys.PALMKNIHY_ENABLED),
                settingLoader.getOnRootProvider(LoanSettingKeys.PALMKNIHY_PDF_ENABLED),
                settingLoader.getOnRootProvider(LoanSettingKeys.PALMKNIHY_AUDIO_ENABLED),
                settingLoader.getOnRootProvider(LoanSettingKeys.FLEXIBOOKS_ENABLED),
                settingLoader.getOnRootProvider(LoanSettingKeys.LEVNAKNIHOVNA_ENABLED)
        );
    }

    @Bean
    public LoanMigrator loanMigrator() {
        return new LoanMigrator(delayedReturnPenaltyService(), loanIdSearchLoader(), notAutoCommittingJdbcTemplate,
                queryFactory, eventer, securityManager);
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerExports() {
        //LOANS
        SubjectConvertingExporter<List<Loan>, List<LoanExportDto>> loansCsvExporter = SubjectConvertingExporter.createForListItemConverting(new CsvFileExporter<>("loans.csv", Templates.TEMPLATE_LOANS_CSV, LoanExportDto.class, "loans", templateEngine), new LoanToLoanExportDtoConverter(), Loan.class);
        exporterResolver.addStatic(Exporters.LOANS_CSV, loansCsvExporter);

        Exporter<List<Loan>> loansXlsExporter = new XlsFileExporterByCsvExporter<>("loans.xls", loansCsvExporter);
        exporterResolver.addStatic(Exporters.LOANS_XLS, loansXlsExporter);

        SubjectConvertingExporter<List<Loan>, List<IDdFile>> loansCoversExporter = SubjectConvertingExporter.createForList(
                new DataZippingFilesExporter("covers.zip", securedFileThumbnailDataStreamer, new FileZipperImpl()),
                new ListToModifiedListConverter<Loan, IDdFile>(loan -> loan.getDocument().getCover()).allowNullTargetValues(),
                Loan.class
        );
        exporterResolver.addStatic(Exporters.LOANS_COVERS_ZIP, loansCoversExporter);


        //SEARCHED LOANS
        Exporter<?> searchedLoansCsvExporter = SearchedItemsExporter.of(loansCsvExporter, SEARCHED_LOANS_LIMIT_ITEMS);
        exporterResolver.addStatic(Exporters.SEARCHED_LOANS_CSV, searchedLoansCsvExporter);

        Exporter<?> searchedLoansXlsExporter = SearchedItemsExporter.of(new XlsFileExporterByCsvExporter<>("loans.xls", loansCsvExporter), SEARCHED_LOANS_LIMIT_ITEMS);
        exporterResolver.addStatic(Exporters.SEARCHED_LOANS_XLS, searchedLoansXlsExporter);


        //USER ACTIVE LOANS
        Exporter<BasicUser> userActiveLoansCsvExporter = new UserActiveLoansExporterDelegatingToLoansExporter(loansCsvExporter, securedLoansProviderService(), userLoader);
        exporterResolver.addStatic(Exporters.USER_ACTIVE_LOANS_CSV, userActiveLoansCsvExporter);

        Exporter<BasicUser> userActiveLoansXlsExporter = new UserActiveLoansExporterDelegatingToLoansExporter(loansXlsExporter, securedLoansProviderService(), userLoader);
        exporterResolver.addStatic(Exporters.USER_ACTIVE_LOANS_XLS, userActiveLoansXlsExporter);

        Exporter<BasicUser> userActiveLoansCoversExporter = new UserActiveLoansExporterDelegatingToLoansExporter(loansCoversExporter, securedLoansProviderService(), userLoader);
        exporterResolver.addStatic(Exporters.USER_ACTIVE_LOANS_COVERS_ZIP, userActiveLoansCoversExporter);


        //GDPR export of eviedence of personal data
        Exporter<BasicUser> personalDataEvidenceExporter = new ObjectToJsonFileExporter<>("data.json", BasicUser.class, objectMapper)
                .withData("userData", userLoader::getUser)
                .withData("loanData", user -> nonDetailedLoanSearchLoader().getContent(RangePaging.forAll(), p -> p.set(LoanConstants.SearchParams.LENDER, List.of(user))));
        Exporter<BasicUser> securedPersonalDataEvidenceExporter = new SecuredExporter<>(personalDataEvidenceExporter, securityManager, SecurityActions.USER_PERSONAL_DATA_EVIDENCE_EXPORT);
        exporterResolver.addStatic(Exporters.PERSONAL_DATA_EVIDENCE_JSON, securedPersonalDataEvidenceExporter);


        templateLoadingExportDescriptorLoader
                .registerTemplated(List.of(CatalogConstants.Export.Tag.USER, ExportConstants.Tag.CSV, ExportConstants.Tag.DOWNLOAD), Exporters.USER_ACTIVE_LOANS_CSV, Templates.TEMPLATE_LOANS_CSV.getType(), MultiText.ofTexts(ofMessageCoded("user.export.ActiveLoans"), ofMessageCoded("export.CsvButton")).withDelimiter(" - "))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.USER, ExportConstants.Tag.XLS, ExportConstants.Tag.DOWNLOAD), Exporters.USER_ACTIVE_LOANS_XLS, Templates.TEMPLATE_LOANS_CSV.getType(), MultiText.ofTexts(ofMessageCoded("user.export.ActiveLoans"), ofMessageCoded("export.XlsButton")).withDelimiter(" - "))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.LOANS, ExportConstants.Tag.CSV, ExportConstants.Tag.DOWNLOAD), Exporters.LOANS_CSV, Templates.TEMPLATE_LOANS_CSV.getType(), ofMessageCoded("export.CsvButton"))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.LOANS, ExportConstants.Tag.XLS, ExportConstants.Tag.DOWNLOAD), Exporters.LOANS_XLS, Templates.TEMPLATE_LOANS_CSV.getType(), ofMessageCoded("export.XlsButton"))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.SEARCHED_LOANS, ExportConstants.Tag.CSV, ExportConstants.Tag.DOWNLOAD), Exporters.SEARCHED_LOANS_CSV, Templates.TEMPLATE_LOANS_CSV.getType(), ofMessageCoded("export.CsvButton"), ofArgumentedMessageCoded("export.LimitXRecords", SEARCHED_LOANS_LIMIT_ITEMS))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.SEARCHED_LOANS, ExportConstants.Tag.XLS, ExportConstants.Tag.DOWNLOAD), Exporters.SEARCHED_LOANS_XLS, Templates.TEMPLATE_LOANS_CSV.getType(), ofMessageCoded("export.XlsButton"), ofArgumentedMessageCoded("export.LimitXRecords", SEARCHED_LOANS_LIMIT_ITEMS));

        staticExportDescriptorLoader
                .with(List.of(CatalogConstants.Export.Tag.USER, ExportConstants.Tag.ZIP, ExportConstants.Tag.DOWNLOAD), Exporters.USER_ACTIVE_LOANS_COVERS_ZIP, ofNative("Aktivní výpůjčky - obálky"))
                .with(List.of(CatalogConstants.Export.Tag.USER, ExportConstants.Tag.JSON, ExportConstants.Tag.DOWNLOAD), Exporters.PERSONAL_DATA_EVIDENCE_JSON, ofMessageCoded("gdpr.ExportOsobnichDat"));
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        StaticSearchFields.CODEBOOK
                .add(LoanConstants.SearchFields.RECORD_LOAN_SOURCE);

        pureExemplarSaver.withCacheDeletable(circulatingAffectedExemplarCacheDeleter());
        basicJpaExemplarSaver.withCacheDeletable(circulatingAffectedExemplarCacheDeleter());

        allowedDatatypeToAllValuesProviderMap.registerEquals(LoanConstants.Datatype.EXTERNAL_LOAN_SERVICE, enabledExternalLoadServicesProvider());
        allDatatypeToAllValuesProviderMap.registerEquals(LoanConstants.Datatype.EXTERNAL_LOAN_SERVICE, ExternalLoanService.CODEBOOK);

        Converter<String, Loan> stringToLoanConverter = new ChainingConverter<>(LoanId::fromString, new IdToObjectConverter<>(loanLoader()));
        conversionService.addConverter(String.class, Loan.class, stringToLoanConverter);
        objectMapperModule.addDeserializer(Loan.class, new FromStringConvertingJsonDeserializer<>(Loan.class, stringToLoanConverter));

        objectMapperModule.addSerializer(LoanId.class, new ToStringConvertingJsonSerializer<>(LoanId.class, new ObjectToStringByToStringConverter<>()));
        converterRegisterer.registerForStringId(ExternalLoanService.class, ExternalLoanService.CODEBOOK);
        converterRegisterer.registerForIntegerId(LoanReminderType.class, LoanReminderType.CODEBOOK);
    }


    // TODO: Přežitek zmigrovaný ze SIP2 půjčování. Chtělo by to SIP upravit a tohle předětal na interceptor
    @Bean
    public PermissionResolver<BasicUser> debtAmountLendingPermissionResolver() {
        return new DebtAmountLendingPermissionResolver(
                settingLoader.getDepartmentedProvider(LoanSettingKeys.LENDING_RELAXED_DEBT_LIMIT_VALUE).andThen(BigDecimal::valueOf),
                settingLoader.getDepartmentedProvider(LoanSettingKeys.LENDING_RELAXED_DEBT_LIMIT_AMOUNT_TYPES),
                typedUserBilanceSearchLoader
        );
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerPermissions() {
        permissionRegistry.add(LoanSecurityActions.LOAN_SEARCH, permissionFactory.edit());

        permissionRegistry.add(LoanSecurityActions.LOANS_SHOW_USER_LOANS, or(
                permissionFactory.edit(),
                permissionFactory.currentEvidedAuthenticActiveIsSubjectUser()
        ));

        permissionRegistry.add(LoanSecurityActions.LOANS_SHOW_OF_DOCUMENT, permissionFactory.edit());

        permissionRegistry.add(LoanSecurityActions.LOAN_LEND, adaptingSubject(userLoader::getUser, and(
                permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(lendingToExpiredReaderAllowed()),
                permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_LEND)
        )));

        permissionRegistry.add(LoanSecurityActions.LOAN_LEND_EXTERNAL, adaptingSubject(userLoader::getUser,
                permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(
                        ContextIgnoringContextualProvider.of(false))
        ));

        permissionRegistry.add(LoanSecurityActions.LOAN_RETURN_ANY, permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_RETURN));

        permissionRegistry.add(LoanSecurityActions.LOAN_RETURN, and(
                withoutSubject(permissionRegistry.getLazy(LoanSecurityActions.LOAN_RETURN_ANY)),
                adaptingSubject(loan -> loan.getExemplar().getDepartment(), permissionFactory.editSubjectWithDepartment())
        ));

        permissionRegistry.add(LoanSecurityActions.LOAN_CANCEL, (auth, ctx, loan) -> {
            //ctenar muze rusit pouze sve vypujcky
            if (loan != null && !auth.getRole().contains(ROLE_LIBRARIAN) && !auth.getActiveUser().equals(loan.getUser())) {
                return PermissionResult.forbid();
            }
            return or(
                    permissionFactory.currentEvidedAuthenticActiveWithAnyOfRoles(ROLE_READER),
                    permissionFactory.currentEvidedAuthenticEditWithLoanLicence()
            ).can(auth, ctx, null);
        });

        permissionRegistry.add(LoanSecurityActions.LOAN_RENEW, adaptingSubject(
                userLoader::getUser, and(
                        permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowed(),
                        or(
                                permissionFactory.currentEvidedAuthenticActiveIsSubjectUser(),
                                permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_RENEW_SINGLE_LOAN)
                        )
                )
        ));

        permissionRegistry.add(LoanSecurityActions.LOAN_REQUEST_STANDARD, adaptingSubject(
                NotAnonymousLoanRequest::getRequester,
                and(
                        (auth, ctx, requester) -> !(requester instanceof Library) ? allow() : PermissionResult.forbid(), //#12607 objednavky nechceme zobrazovat knihovnam - toto budeme chtit nejak resit, abychom pouze povolovali podle roli
                        permissionFactory.currentEvidedAuthenticActive(),
                        permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowed(),
                        or(
                                permissionFactory.currentEvidedAuthenticActiveIsSubjectUser(),
                                permissionFactory.currentEvidedAuthenticEditWithLoanLicence()
                        )
                )
        ));

        permissionRegistry.add(LoanSecurityActions.LOAN_MIGRATE_FROM_USER_TO_ANY, and(
                permissionFactory.enabled(settingLoader.getDepartmentedProvider(LoanSettingKeys.LOAN_MIGRATION_ENABLED),
                        Texts.ofNative("Loan migration is not enabled")),
                permissionFactory.currentEvidedAuthenticEditWithLoanLicence(),
                permissionFactory.editOnReadableDepartmentsOfUser()
        ));

        permissionRegistry.add(LoanSecurityActions.LOAN_MIGRATE, and(
                adaptingSubject(
                        LoanMigrationCommand::source,
                        permissionRegistry.getLazy(LoanSecurityActions.LOAN_MIGRATE_FROM_USER_TO_ANY)
                ),
                adaptingSubject(
                        LoanMigrationCommand::target,
                        and(
                                permissionFactory.currentEvidedAuthenticEditWithLoanLicence(),
                                permissionFactory.editOnReadableDepartmentsOfUser()
                        )
                )
        ));

    }

}
