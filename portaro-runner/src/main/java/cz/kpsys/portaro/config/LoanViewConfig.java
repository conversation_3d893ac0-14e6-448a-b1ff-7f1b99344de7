package cz.kpsys.portaro.config;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.department.AuthContextualDepartmentsLoader;
import cz.kpsys.portaro.auth.department.CurrentAuthDepartmentsLoader;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.form.Form;
import cz.kpsys.portaro.form.valueeditor.acceptableroot.AcceptableRootValueEditor;
import cz.kpsys.portaro.form.valueeditor.bool.BooleanValueEditor;
import cz.kpsys.portaro.form.valueeditor.date.DateValueEditor;
import cz.kpsys.portaro.form.valueeditor.multipleacceptable.MultipleAcceptableValueEditor;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanConstants;
import cz.kpsys.portaro.loan.LoanState;
import cz.kpsys.portaro.loan.reminder.LoanReminder;
import cz.kpsys.portaro.loan.reminder.LoanReminderConstants;
import cz.kpsys.portaro.loan.reminder.LoanReminderType;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.factory.SearchFactory;
import cz.kpsys.portaro.search.factory.SearchFactoryMatching;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.factory.SearchFactoryResolverMatcher;
import cz.kpsys.portaro.search.view.CompositeSearchFormFactory;
import cz.kpsys.portaro.search.view.SearchFormFactory;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import cz.kpsys.portaro.security.SecurityAccessor;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import cz.kpsys.portaro.view.ViewableItemsConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static cz.kpsys.portaro.form.editedproperty.EditedProperty.createWithProperty;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Configuration
public class LoanViewConfig {

    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull AuthContextualDepartmentsLoader familyModeAuthContextualDepartmentsLoader;
    @NonNull PageSearchLoader<MapBackedParams, Loan, RangePaging> loanSearchLoader;
    @NonNull PageSearchLoader<MapBackedParams, LoanReminder, RangePaging> loanReminderSearchLoader;
    @NonNull AllValuesProvider<ReaderCategory> readerCategoryLoader;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull SecurityAccessor securityAccessor;
    @NonNull CurrentAuthDepartmentsLoader currentAuthEditableDepartmentsLoader;
    @NonNull SearchFactoryResolver searchFactoryResolver;
    @NonNull ViewableItemsConverter viewableItemsConverter;
    @NonNull CompositeSearchFormFactory searchFormFactory;



    @Bean
    public SearchFactory loanSearchFactory() {
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherBySingleKind(BasicMapSearchParams.KIND_LOAN)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Loan, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_LOAN));
                    p.set(LoanConstants.SearchParams.LOAN_STATE, LoanState.allNonRelic());
                });

                return new PageSearchLoaderSearch<>(loanSearchLoader)
                        .withDefaultSorting(SortingItem.ofSimpleDesc(Loan.REQUEST_DATE))
                        .withIntersectingConstraintParam(CoreSearchParams.DEPARTMENT, familyModeAuthContextualDepartmentsLoader.getAll(authenticationHolder.getCurrentAuth(), ctx))
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }

    @Bean
    public SearchFactory reminderSearchFactory() {
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherBySingleKind(BasicMapSearchParams.KIND_LOAN_REMINDER)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, LoanReminder, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(StaticParamsModifier.of(
                        CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_LOAN_REMINDER),
                        CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY),
                        LoanReminderConstants.SearchParams.SENT_INCLUDED, false
                ));

                return new PageSearchLoaderSearch<>(loanReminderSearchLoader)
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }


    @Bean
    public SearchFormFactory loanSearchTypeSearchFormFactory() {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                List<Form> forms = new ArrayList<>();

                Form form = new Form("loan-search", Texts.ofMessageCoded("hledani.kriteria"));
                // form.addField(createWithProperty(LoanConstants.SearchParams.LOAN_STATE, MultipleAcceptableValueEditor.getEmptyEditor(LoanState.allNonRelic())));
                form.addField(createWithProperty(CoreSearchParams.DEPARTMENT,
                        AcceptableRootValueEditor.getEmptyEditor(securityAccessor.getShowedEditingRootHierarchicalDepartment(currentAuth, ctx))
                                .withEnabledItems(currentAuthEditableDepartmentsLoader.getFamiliesByAuth(currentAuth))));
                form.addField(createWithProperty(LoanConstants.SearchParams.LEND_FROM_DATE, DateValueEditor.getEmptyEditor().withPastValidation()));
                form.addField(createWithProperty(LoanConstants.SearchParams.LEND_TO_DATE, DateValueEditor.getEmptyEditor()));
                forms.add(form);

                return forms;
            }
        };
    }


    @Bean
    public SearchFormFactory loanReminderSearchTypeSearchFormFactory() {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                List<Form> forms = new ArrayList<>();

                Form form = new Form("loan-reminder-search", Texts.ofMessageCoded("hledani.kriteria"));
                form.addField(createWithProperty(CoreSearchParams.DEPARTMENT,
                        AcceptableRootValueEditor.getEmptyEditor(securityAccessor.getShowedEditingRootHierarchicalDepartment(currentAuth, ctx))
                                .withEnabledItems(currentAuthEditableDepartmentsLoader.getFamiliesByAuth(currentAuth))));
                form.addField(createWithProperty(LoanReminderConstants.SearchParams.LOAN_REMINDER_TYPE,
                        MultipleAcceptableValueEditor.getEmptyEditor(LoanReminderType.CODEBOOK.getAll())));
                form.addField(createWithProperty(LoanReminderConstants.SearchParams.SENT_INCLUDED,
                        BooleanValueEditor.getEmptyEditor()));
                form.addField(createWithProperty(UserConstants.SearchParams.READER_CATEGORY, MultipleAcceptableValueEditor.getEmptyEditor(readerCategoryLoader)));
                forms.add(form);

                return forms;
            }
        };
    }

    @Bean
    public CommandLineRunner loanSearchRegistrar() {
        return args -> {
            searchFactoryResolver
                    .withStandardOrder(loanSearchFactory())
                    .withStandardOrder(reminderSearchFactory());

            viewableItemsConverter.registerStandardNoop(Loan.class);
            viewableItemsConverter.registerStandardNoop(LoanReminder.class);

            searchFormFactory
                    .add(SearchViewConstants.TYPE_LOAN_SEARCH, loanSearchTypeSearchFormFactory())
                    .add(SearchViewConstants.TYPE_LOAN_REMINDER_SEARCH, loanReminderSearchTypeSearchFormFactory());
        };
    }

}
