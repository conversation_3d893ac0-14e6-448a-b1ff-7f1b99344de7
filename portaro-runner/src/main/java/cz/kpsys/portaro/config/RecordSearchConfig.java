package cz.kpsys.portaro.config;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.contextual.CompositeAllValuesContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.contextual.FilteredAndSortedAllValuesContextualProvider;
import cz.kpsys.portaro.commons.date.StringToInstantConverter;
import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.*;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.datatype.DatatypedAcceptableValuesRegistry;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.erp.employee.workattendance.RecordDayLoader;
import cz.kpsys.portaro.erp.employee.workattendance.SearchLoadingRecordDayIdLoader;
import cz.kpsys.portaro.exemplar.ExemplarConstants;
import cz.kpsys.portaro.exemplar.exemplarstatus.ExemplarStatus;
import cz.kpsys.portaro.form.form.Form;
import cz.kpsys.portaro.form.valueeditor.acceptableroot.AcceptableRootValueEditor;
import cz.kpsys.portaro.form.valueeditor.date.DateValueEditor;
import cz.kpsys.portaro.form.valueeditor.multipleacceptable.MultipleAcceptableValueEditor;
import cz.kpsys.portaro.form.valueeditor.scannabletext.ScannableTextValueEditor;
import cz.kpsys.portaro.form.valueeditor.searchoredit.SearchOrEditValueEditor;
import cz.kpsys.portaro.form.valueeditor.searchoredit.StaticSearchParams;
import cz.kpsys.portaro.form.valueeditor.singleacceptable.SingleAcceptableValueEditor;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditor;
import cz.kpsys.portaro.form.valueeditor.year.YearValueEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorAliasType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.date.DateValueEditorGranularity;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.loan.LoanConstants;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.datasource.Datasource;
import cz.kpsys.portaro.record.datasource.LocalDatasource;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeLoader;
import cz.kpsys.portaro.record.detail.constraints.LinkConstraintsResolver;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import cz.kpsys.portaro.record.isbn.IsbnEditorFactory;
import cz.kpsys.portaro.record.load.RecordDayIdLoader;
import cz.kpsys.portaro.record.newestdocuments.NewestDocumentsService;
import cz.kpsys.portaro.record.operation.RecordOperation;
import cz.kpsys.portaro.record.operation.RecordOperationType;
import cz.kpsys.portaro.record.search.*;
import cz.kpsys.portaro.record.search.restriction.*;
import cz.kpsys.portaro.record.search.restriction.lucene.FieldTypeSearchFieldToLuceneSearchFieldFactory;
import cz.kpsys.portaro.record.search.restriction.lucene.FieldTypeSearchFieldToLuceneSortFieldFactory;
import cz.kpsys.portaro.record.sec.CurrentAuthFondsLoader;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.factory.SearchFactory;
import cz.kpsys.portaro.search.factory.SearchFactoryMatching;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.factory.SearchFactoryResolverMatcher;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.field.StaticSearchFields;
import cz.kpsys.portaro.search.impl.SpringDbMostLentDocumentsLoader;
import cz.kpsys.portaro.search.impl.SpringDbMostWatchedDocumentsLoader;
import cz.kpsys.portaro.search.impl.SpringDbNewestDocumentsLoader;
import cz.kpsys.portaro.search.impl.SpringDbTopRatedLoader;
import cz.kpsys.portaro.search.params.ParamsModifier;
import cz.kpsys.portaro.search.restriction.serialize.lucene.DispatchingLuceneQueryFieldLoader;
import cz.kpsys.portaro.search.restriction.serialize.lucene.DispatchingLuceneSortFieldLoader;
import cz.kpsys.portaro.search.view.CompositeSearchFormFactory;
import cz.kpsys.portaro.search.view.SearchFormFactory;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import cz.kpsys.portaro.security.PermissionFactory;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.view.web.CatalogWebSettingsKeys;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Stream;

import static cz.kpsys.portaro.commons.util.ObjectUtil.castedGetOrNull;
import static cz.kpsys.portaro.form.editedproperty.EditedProperty.createWithProperty;
import static cz.kpsys.portaro.form.formfield.FormField.createWithPropertyAndHint;
import static cz.kpsys.portaro.record.search.restriction.FieldTypedSearchFieldParsing.DYNAMIC_FIELD_PREFIX;
import static cz.kpsys.portaro.search.view.SearchViewConstants.*;
import static cz.kpsys.portaro.security.PermissionResolver.or;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Configuration
@Slf4j
public class RecordSearchConfig {

    @NonNull QueryFactory queryFactory;
    @NonNull NamedParameterJdbcOperations notCriticalJdbcTemplate;
    @NonNull CacheService cacheService;
    @NonNull SettingLoader settingLoader;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull Function<String, List<Fond>> subkindToEnabledFondsExpander;
    @NonNull ContextualProvider<Department, List<Fond>> showableFondsDepartmentedProvider;
    @NonNull CurrentAuthFondsLoader currentAuthShowableFondsLoader;
    @NonNull AllValuesProvider<RecordOperationType> recordOperationTypeLoader;
    @NonNull RecordSearchParamsStringQueryBuilder<MapBackedParams> luceneSearchQueryBuilder;
    @NonNull ByIdLoadable<RecordStatus, Integer> recordStatusLoader;
    @NonNull AllByIdsLoadable<RecordStatus, Integer> recordStatusesByIdsLoader;
    @NonNull Converter<List<UUID>, List<? extends Record>> idsToRecordsConverter;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchSqlLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> notCriticalDetailedAuthoritySearchSqlLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, UUID> documentIdSearchSqlLoader;
    @NonNull Converter<List<String>, List<Record>> stringIdsToRecordsConverter;
    @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;
    @NonNull IdAndIdsLoadable<Record, UUID> richRecordLoader;
    @NonNull ByIdLoadable<ExemplarStatus, Integer> exemplarStatusLoader;
    @NonNull PageSearchLoader<MapBackedParams, RecordOperation, RangePaging> recordOperationSearchLoader;
    @NonNull Provider<SortingItem> defaultRecordSearchSortingProvider;
    @NonNull Provider<SortingItem> defaultAuthoritySearchSortingProvider;
    @NonNull CompositeAddableAllValuesProvider<Datasource> allDatasetsProvider;
    @NonNull CompositeAllValuesContextualProvider<Department, Datasource> allowedDatasetsProvider;
    @NonNull SearchFactoryResolver searchFactoryResolver;
    @NonNull CompositeSearchFormFactory searchFormFactory;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull PermissionFactory permissionFactory;
    @NonNull DatatypedAcceptableValuesRegistry allDatatypeToAllValuesProviderMap;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> relatedRecordsSearchLoader;
    @NonNull ParamsModifier<DefaultGettableAndSettableSearchParams> fondRelatedParamsExpander;
    @NonNull Provider<Department> rootDepartmentProvider;
    @NonNull StringToInstantConverter recordFieldStringToInstantConverter;
    @NonNull MappableSearchParamsRegistry mappableSearchParamsRegistry;
    @NonNull DatatypableStringConverter datatypableStringConverter;
    @NonNull FieldTypeLoader fieldTypeLoader;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;
    @NonNull DispatchingLuceneQueryFieldLoader luceneQueryFieldLoader;
    @NonNull DispatchingLuceneSortFieldLoader luceneSortFieldLoader;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull CompositeByIdLoader<SearchField, String> searchFieldLoader;
    @NonNull CompositeAllValuesProvider<SearchField> searchFieldsProvider;
    @NonNull CompositeByIdLoader<SortingItem, String> searchSortingLoader;
    @NonNull Provider<@NonNull Fond> dayFondProvider;




    @Bean
    public AppserverSearchEngine appserverSearchEngine() {
        return new AppserverSearchEngine(
                mappingAppserver,
                settingLoader.getDepartmentedProvider(SettingKeys.FACETS),
                settingLoader.getDepartmentedProvider(SettingKeys.FACET_KEYS_MAX_COUNT),
                departmentAccessor,
                luceneSortFieldLoader
        );
    }

    @Bean
    public SearchService<InternalSearchResult<String, MapBackedParams, RangePaging>, MapBackedParams> recordSearchService() {
        AppserverLuceneSearchService pure = new AppserverLuceneSearchService(
                appserverSearchEngine(),
                new SearchResponseToSearchResultConverter(
                        datatypableStringConverter,
                        recordFieldStringToInstantConverter
                ),
                luceneSearchQueryBuilder
        );

        var paramsConvertingService = new ParametersConvertingSearchService<>(pure)
                .withExpandingParam(CoreSearchParams.SUBKIND, RecordConstants.SearchParams.FOND, subkindToEnabledFondsExpander)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);

        CachingSearchService<RangePaging> cached = new CachingSearchService<>(paramsConvertingService);
        cacheService.registerSpringCacheCleaner(PagedSearchResult.class.getSimpleName(), CachingSearchService.LONG_TERM_CACHE_NAME);
        cacheService.registerCleaner(PagedSearchResult.class.getSimpleName(), cached);
        return cached;
    }

    @Bean
    public Datasource localCurrentDepartmentDataset() {
        return new LocalDatasource(Datasource.DATASOURCE_LOCAL_ID, Texts.ofMessageCoded("dataset.local"), Set.of(BasicMapSearchParams.SUBKIND_DOCUMENT, BasicMapSearchParams.SUBKIND_AUTHORITY), enabledFondsProvider);
    }

    @Bean
    public Datasource localRootDepartmentDataset() {
        return new LocalDatasource(Datasource.DATASOURCE_LOCAL_ALL_DEPARTMENTS_ID, Texts.ofMessageCoded("dataset.local-root"), Set.of(BasicMapSearchParams.SUBKIND_DOCUMENT, BasicMapSearchParams.SUBKIND_AUTHORITY), enabledFondsProvider);
    }

    @Bean
    public AllValuesProvider<Datasource> allLocalDatasetsProvider() {
        return StaticAllValuesProvider.of(localCurrentDepartmentDataset(), localRootDepartmentDataset());
    }

    @Bean
    public ContextualProvider<Department, @NonNull List<Datasource>> allowedLocalDatasetsProvider() {
        return new FilteredAndSortedAllValuesContextualProvider<>(
                allLocalDatasetsProvider(),
                settingLoader.getDepartmentedProvider(SettingKeys.ENABLED_LOCAL_DATASETS)
        );
    }

    @Bean
    public SearchFactory globalSearchFactory() {
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherAlwaysMatching()) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                String type = customParams.get(CoreSearchParams.TYPE);
                Datasource datasource = customParams.get(RecordConstants.SearchParams.DATASOURCE);

                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
                    p.set(CoreSearchParams.FACETS_ENABLED, true);
                    p.set(CoreSearchParams.INCLUDE_DRAFT, false);
                    p.set(CoreSearchParams.INCLUDE_DELETED, false);
                    p.set(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx));
                    if (settingLoader.getDepartmentedProvider(SettingKeys.ONLY_USED_AUTHORITIES_SHOW_ENABLED).getOn(ctx)) {
                        p.set(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx));
                    }
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS).getOn(ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES).andThen(recordStatusesByIdsLoader::getAllByIds).getOn(ctx)
                            .stream()
                            .filter(recordStatus -> !RecordStatus.DEPRECATED_STATUSES.contains(recordStatus))
                            .toList());

                    // excluded depends on search base,
                    if (Objects.equals(datasource, localRootDepartmentDataset())) {
                        p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.ALL));
                        p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, true);
                    } else {
                        p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY));
                        p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
                    }
                });

                //default sorting depends on current kind or type
                SortingItem defaultSorting = defaultRecordSearchSortingProvider.get();
                DefaultGettableAndSettableSearchParams expandedFondsParams = fondRelatedParamsExpander.modify(customParams);
                if (expandedFondsParams.hasNotNull(RecordConstants.SearchParams.FOND) && !expandedFondsParams.get(RecordConstants.SearchParams.FOND).isEmpty() && expandedFondsParams.get(RecordConstants.SearchParams.FOND).stream().allMatch(FondTypeResolver::isAuthorityFond)) {
                    defaultSorting = defaultAuthoritySearchSortingProvider.get();
                } else if (Objects.equals(type, TYPE_BY_AUTHORITY)) {
                    defaultSorting = CatalogConstants.Search.Lucene.SORT_BY_NAME;
                }

                return ConvertingStandardSearch.of(new SearchServiceBackedSearch<>(recordSearchService()), stringIdsToRecordsConverter)
                        .pageSizeChangeable(true)
                        .randomPageAccessible(true)
                        .sortable(true)
                        .facetable(true)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams)
                        .withDefaultSorting(defaultSorting);
            }
        };
    }


    @Bean
    public SearchFactory randomSearchFactory() {
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(TYPE_RANDOM)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                Datasource datasource = castedGetOrNull(customParams, MapBackedParams.class, p -> p.get(RecordConstants.SearchParams.DATASOURCE));

                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
                    p.set(CoreSearchParams.FACETS_ENABLED, false);
                    p.set(CoreSearchParams.INCLUDE_DRAFT, false);
                    p.set(CoreSearchParams.INCLUDE_DELETED, false);
                    p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
                    p.set(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES).andThen(recordStatusesByIdsLoader::getAllByIds).getOn(ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS).getOn(ctx));

                    if (Objects.equals(datasource, localRootDepartmentDataset())) {
                        p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.ALL));
                        p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, true);
                    } else {
                        p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY));
                        p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
                    }
                });

                return ConvertingStandardSearch.of(new PseudoRandomSearch<>(new SearchServiceBackedSearch<>(recordSearchService())), stringIdsToRecordsConverter)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }

    @Bean
    public SearchFactory documentIndexSearchFactory() {
        //Preparement for index via appserver
        new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(TYPE_DOCUMENT_INDEX)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
                    p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY));
                    p.set(CoreSearchParams.INCLUDE_DRAFT, false);
                    p.set(CoreSearchParams.INCLUDE_DELETED, false);
                    p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
                    p.set(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllDocumentByAuth(currentAuth, ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES).andThen(recordStatusesByIdsLoader::getAllByIds).getOn(ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS).getOn(ctx));
                });

                return ConvertingStandardSearch.of(new SearchServiceBackedSearch<>(recordSearchService()), stringIdsToRecordsConverter)
                        .pageSizeChangeable(true)
                        .randomPageAccessible(true)
                        .alphabetic(true)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams)
                        .withDefaultSorting(CatalogConstants.Search.Lucene.SORT_BY_NAME);
            }
        };

        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(TYPE_DOCUMENT_INDEX)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
                    p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY));
                    p.set(CoreSearchParams.INCLUDE_DRAFT, false);
                    p.set(CoreSearchParams.INCLUDE_DELETED, false);
                    p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
                    p.set(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllDocumentByAuth(currentAuth, ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES).andThen(recordStatusesByIdsLoader::getAllByIds).getOn(ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS).getOn(ctx));
                });

                return new PageSearchLoaderSearch<>(detailedRecordSearchSqlLoader)
                        .pageSizeChangeable(true)
                        .randomPageAccessible(true)
                        .alphabetic(true)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }


    @Bean
    public SearchFactory topRatedSearchFactory() {
        PageSearchLoader<MapBackedParams, UUID, RangePaging> pureSearchLoader = new SpringDbTopRatedLoader(notCriticalJdbcTemplate, queryFactory);

        ParametersConvertingPageSearchLoader<MapBackedParams, UUID, RangePaging> paramsConvertingSearchLoader = new ParametersConvertingPageSearchLoader<>(pureSearchLoader)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);

        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(TYPE_TOP_RATED)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
                    p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY));
                    p.set(CoreSearchParams.INCLUDE_DRAFT, false);
                    p.set(CoreSearchParams.INCLUDE_DELETED, false);
                    p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
                    p.set(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllDocumentByAuth(currentAuth, ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES).andThen(recordStatusesByIdsLoader::getAllByIds).getOn(ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS).getOn(ctx));
                });

                return new PageSearchLoaderSearch<>(new ResultConvertingPageSearchLoader<>(paramsConvertingSearchLoader, idsToRecordsConverter))
                        .pageSizeChangeable(true)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }


    @Bean
    public SearchFactory newestSearchFactory() {
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(TYPE_NEWEST)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
                    p.set(CoreSearchParams.INCLUDE_DRAFT, false);
                    p.set(CoreSearchParams.INCLUDE_DELETED, false);
                    p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
                    p.set(ExemplarConstants.SearchParams.EXEMPLAR_STATUS, settingLoader.getDepartmentedProvider(RecordSettingKeys.NEWS_EXEMPLAR_STATUSES).andThen(new AllByIdsLoadableByIdLoaderAdapter<>(exemplarStatusLoader)::getAllByIds).getOn(ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES).andThen(recordStatusesByIdsLoader::getAllByIds).getOn(ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS).getOn(ctx));
                    p.set(CoreSearchParams.FROM_DATE, Instant.now().minus(Duration.ofDays(settingLoader.getDepartmentedProvider(RecordSettingKeys.NEWS_SEARCH_MAX_AGE).getOn(ctx))));
                    p.set(CoreSearchParams.TO_DATE, Instant.now());
                });

                return new PageSearchLoaderSearch<>(new ResultConvertingPageSearchLoader<>(newestDocumentsLoader(), idsToRecordsConverter))
                        .pageSizeChangeable(true)
                        .withIntersectingConstraintParam(CoreSearchParams.DEPARTMENT, Department.filterOnlineDepartments(departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY)))
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }


    @Bean
    public SearchFactory authorityIndexSearchFactory() {
        //Preparement for index via appserver
        SearchFactory authorityIndexSearchByAppserverSearchFactory = new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(TYPE_AUTHORITY_INDEX)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
                    p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY));
                    p.set(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllAuthorityByAuth(currentAuth, ctx));
                    if (settingLoader.getDepartmentedProvider(SettingKeys.ONLY_USED_AUTHORITIES_SHOW_ENABLED).getOn(ctx)) {
                        p.set(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx));
                    }
                });

                return ConvertingStandardSearch.of(new SearchServiceBackedSearch<>(recordSearchService()), stringIdsToRecordsConverter)
                        .pageSizeChangeable(true)
                        .randomPageAccessible(true)
                        .alphabetic(true)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams)
                        .withDefaultSorting(CatalogConstants.Search.Lucene.SORT_BY_NAME);
            }
        };

        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(TYPE_AUTHORITY_INDEX)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
                    p.set(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllAuthorityByAuth(currentAuth, ctx));
                    if (settingLoader.getDepartmentedProvider(SettingKeys.ONLY_USED_AUTHORITIES_SHOW_ENABLED).getOn(ctx)) {
                        p.set(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx));
                    }
                });

                return new PageSearchLoaderSearch<>(notCriticalDetailedAuthoritySearchSqlLoader)
                        .randomPageAccessible(true)
                        .alphabetic(true)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams)
                        .withDefaultSorting(CatalogConstants.Search.Lucene.SORT_BY_NAME);
            }
        };
    }


    @Bean
    public SearchFactory mostLentSearchFactory() {
        PageSearchLoader<MapBackedParams, UUID, RangePaging> pureSearchLoader = new SpringDbMostLentDocumentsLoader(
                notCriticalJdbcTemplate,
                queryFactory,
                100,
                departmentAccessor
        );

        ParametersConvertingPageSearchLoader<MapBackedParams, UUID, RangePaging> paramsConvertingSearchLoader = new ParametersConvertingPageSearchLoader<>(pureSearchLoader)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);

        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(TYPE_MOST_LENT)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
                    p.set(CoreSearchParams.INCLUDE_DRAFT, false);
                    p.set(CoreSearchParams.INCLUDE_DELETED, false);
                    p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
                    p.set(ExemplarConstants.SearchParams.FORBIDDEN_EXEMPLAR, settingLoader.getDepartmentedProvider(SettingKeys.FORBIDDEN_EXEMPLARS).getOn(ctx));
                    p.set(RecordConstants.SearchParams.ROOT_FOND, ListUtil.filter(currentAuthShowableFondsLoader.getAllDocumentByAuth(currentAuth, ctx), fond -> settingLoader.getDepartmentedProvider(SettingKeys.MOST_LENT_DOCUMENTS_WITH_PERIODICALS_ENABLED).getOn(ctx) || !fond.isPeriodical()));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES).andThen(recordStatusesByIdsLoader::getAllByIds).getOn(ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS).getOn(ctx));
                    p.set(CoreSearchParams.FROM_DATE, Instant.now().minus(Duration.ofDays(150)));
                    p.set(CoreSearchParams.TO_DATE, Instant.now());
                });

                return new PageSearchLoaderSearch<>(new ResultConvertingPageSearchLoader<>(paramsConvertingSearchLoader, idsToRecordsConverter))
                        .pageSizeChangeable(true)
                        .withIntersectingConstraintParam(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY))
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }


    @Bean
    public SearchFactory mostWatchedSearchFactory() {
        PageSearchLoader<MapBackedParams, UUID, RangePaging> pureSearchLoader = new SpringDbMostWatchedDocumentsLoader(notCriticalJdbcTemplate, queryFactory);

        ParametersConvertingPageSearchLoader<MapBackedParams, UUID, RangePaging> paramsConvertingSearchLoader = new ParametersConvertingPageSearchLoader<>(pureSearchLoader)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);

        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(TYPE_MOST_WATCHED)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
                    p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY));
                    p.set(CoreSearchParams.INCLUDE_DRAFT, false);
                    p.set(CoreSearchParams.INCLUDE_DELETED, false);
                    p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
                    p.set(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllDocumentByAuth(currentAuth, ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES).andThen(recordStatusesByIdsLoader::getAllByIds).getOn(ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS).getOn(ctx));
                });

                return new PageSearchLoaderSearch<>(new ResultConvertingPageSearchLoader<>(paramsConvertingSearchLoader, idsToRecordsConverter))
                        .pageSizeChangeable(true)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }


    @Bean
    public SearchFactory recordOperationSearchFactory() {
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherBySingleKind(BasicMapSearchParams.KIND_RECORD_OPERATION)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, RecordOperation, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(StaticParamsModifier.of(
                        CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY),
                        CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD_OPERATION)
                ));
                return new PageSearchLoaderSearch<>(recordOperationSearchLoader)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.OPERATED_ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }

    @Bean
    public SearchFactory relatedRecordsSearchFactory() { // TODO: if appserver relatedRecordsSearch is OK, you can delete this
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherConjunction(
                new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(TYPE_TABLE),
                new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherBySingleProperty<>(RecordConstants.SearchParams.RECORD_RELATED_RECORD)
        )) {

            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                return new PageSearchLoaderSearch<>(relatedRecordsSearchLoader)
                        .pageSizeChangeable(true)
                        .sortable(true)
                        .facetable(true);
            }
        };
    }

    @Bean
    public PageSearchLoader<MapBackedParams, UUID, RangePaging> newestDocumentsLoader() {
        SpringDbNewestDocumentsLoader pureSearchLoader = new SpringDbNewestDocumentsLoader(
                notCriticalJdbcTemplate,
                queryFactory,
                settingLoader.getOnRootProvider(RecordSettingKeys.NEWEST_SEARCH_VIEW_NAME)
        );

        return new ParametersConvertingPageSearchLoader<>(pureSearchLoader)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);
    }

    @Bean
    public NewestDocumentsService newestDocumentsService() {
        NewestDocumentsService bean = new NewestDocumentsService(
                richRecordLoader,
                newestDocumentsLoader(),
                settingLoader.getDepartmentedProvider(RecordSettingKeys.NEWS_WITH_COVER_PREFERING_ENABLED),
                settingLoader.getDepartmentedProvider(RecordSettingKeys.NEWS_SLIDER_PRESEARCHED_COUNT),
                settingLoader.getDepartmentedProvider(RecordSettingKeys.NEWS_SLIDER_SHOWED_COUNT),
                settingLoader.getDepartmentedProvider(RecordSettingKeys.NEWS_INCLUDE_PERIODICAL_FONDS_ENABLED),
                showableFondsDepartmentedProvider,
                settingLoader.getOnRootProvidedList(RecordSettingKeys.FORBIDDEN_RECORDS),
                settingLoader.getOnRootProvidedList(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES),
                settingLoader.getOnRootProvidedList(RecordSettingKeys.NEWS_EXEMPLAR_STATUSES),
                recordStatusLoader,
                exemplarStatusLoader,
                settingLoader.getDepartmentedProvider(RecordSettingKeys.NEWS_SLIDER_BUILDINGS),
                departmentLoader,
                departmentAccessor,
                departmentAccessor
        );
        cacheService.registerCleaner(bean);
        return bean;
    }

    @Bean
    public ContextualProvider<Department, Boolean> forceDisableSearchInRootDataset() {
        return settingLoader.getDepartmentedProvider(SettingKeys.FORCE_DISABLE_SEARCH_IN_ROOT_DATASET);
    }

    @Bean
    public SearchFormFactory fieldsTypeSearchFormFactory() {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                log.warn("Search with searchType=fields !! we thought that we already have not been using it");

                List<Form> forms = new ArrayList<>();
                boolean singleSearchType = subkinds.size() == 1;

                Form form = new Form("fields-record-search", Texts.ofDefaulted(Texts.ofMessageCoded("hledani.kriteria.%s".formatted(searchType)), Texts.ofMessageCoded("hledani.kriteria")));
                form.addField(createWithProperty(CoreSearchParams.NAME, TextValueEditor.getEmptyEditor().withMaxLength(40)));
                if (singleSearchType && subkinds.contains(BasicMapSearchParams.SUBKIND_DOCUMENT)) {
                    form.addField(createWithProperty(RecordConstants.SearchParams.ISBN_OR_ISSN, TextValueEditor.getEmptyEditor().withMaxLength(20)));
                    form.addField(createWithProperty(RecordConstants.SearchParams.AUTHOR, TextValueEditor.getEmptyEditor().withMaxLength(40)));
                    form.addField(createWithProperty(RecordConstants.SearchParams.PUBLICATION_YEAR, YearValueEditor.getEmptyEditor()));
                }
                forms.add(form);

                return forms;
            }
        };
    }

    @Bean
    public SearchFormFactory searchSelectionTypeSearchFormFactory() {
        Provider<@NonNull Boolean> alwaysSearchByQOnly = settingLoader.getOnRootProvider(CatalogWebSettingsKeys.SUTOR_SUTIN_LAYOUT_ENABLED);
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                DefaultGettableAndSettableSearchParams expandedFondsParams = fondRelatedParamsExpander.modify(customParams);
                List<Fond> fonds = expandedFondsParams.get(RecordConstants.SearchParams.FOND);

                if (kinds.contains(BasicMapSearchParams.KIND_EXEMPLAR)) {
                    return List.of(createExemplarSearchSelectionForm());
                }
                if (kinds.contains(BasicMapSearchParams.KIND_LOAN)) {
                    return List.of(createLoanSearchSelectionForm());
                }
                if (!alwaysSearchByQOnly.get() && !fonds.isEmpty() && fonds.stream().allMatch(FondTypeResolver::isDocumentFond)) {
                    return List.of(createDocumentSearchSelectionForm(fonds, customParams, ctx));
                }
                if (!fonds.isEmpty()) {
                    return List.of(createGenericRecordSearchSelectionForm(fonds, customParams, ctx));
                }
                return List.of(createGenericSearchSelectionForm(kinds, ctx));
            }
        };
    }

    private Form createExemplarSearchSelectionForm() {
        Form form = new Form("exemplar-selection-search", Texts.ofMessageCoded("hledani.vybratZVyhledanych"));

        form.addField(createWithProperty(CoreSearchParams.Q, Texts.ofMessageCoded("commons.Identifikator"),
                ScannableTextValueEditor.getEmptyEditor()
                        .withMaxLength(20)
                        .withPlaceholder(Texts.ofMessageCoded("exemplar.ExemplarIdentifier"))
                        .withAllowedAutomaticStart(true)
                        .withRequired(true)));
        return form;
    }

    private Form createLoanSearchSelectionForm() {
        Form form = new Form("loan-selection-search", Texts.ofMessageCoded("hledani.vybratZVyhledanych"));

        form.addField(createWithProperty(LoanConstants.SearchParams.EXEMPLAR_Q, Texts.ofMessageCoded("commons.Identifikator"),
                ScannableTextValueEditor.getEmptyEditor()
                        .withMaxLength(20)
                        .withPlaceholder(Texts.ofMessageCoded("exemplar.ExemplarIdentifier"))
                        .withAllowedAutomaticStart(true)
                        .withRequired(true)));
        return form;
    }

    private <PARAMS extends MapBackedParams> Form createGenericRecordSearchSelectionForm(@NonNull List<Fond> fonds, PARAMS customParams, Department ctx) {
        Form form = new Form("record-selection-search", Texts.ofMessageCoded("hledani.vybratZVyhledanych"));

        form.addField(createWithPropertyAndHint(CoreSearchParams.Q, Texts.ofMessageCoded("commons.nazev"), Texts.ofMessageCoded("search.recordSearchSelectionForm.nazev.hint"),
                TextValueEditor.getEmptyEditor()
                        .withMinLength(3)
                        .withMaxLength(100)
                        .withPlaceholder(Texts.ofMessageCoded("commons.EnterInitialLetters"))));

        List<LabeledId<String>> acceptableDatasources = getDatasourceEditorAcceptableValues(fonds, customParams, ctx);
        if (!acceptableDatasources.isEmpty()) {
            form.addField(createWithProperty(RecordConstants.SearchParams.DATASOURCE, SingleAcceptableValueEditor.getEmptyEditor(acceptableDatasources)));
        }

        return form;
    }

    private Form createGenericSearchSelectionForm(List<String> kinds, Department ctx) {
        Form form = new Form("generic-selection-search", Texts.ofMessageCoded("hledani.vybratZVyhledanych"));

        form.addField(createWithPropertyAndHint(CoreSearchParams.Q, Texts.ofMessageCoded("commons.nazev"), Texts.ofMessageCoded("search.recordSearchSelectionForm.nazev.hint"),
                TextValueEditor.getEmptyEditor()
                        .withMinLength(3)
                        .withMaxLength(100)
                        .withPlaceholder(Texts.ofMessageCoded("commons.EnterInitialLetters"))));

        return form;
    }

    private <PARAMS extends MapBackedParams> Form createDocumentSearchSelectionForm(@NonNull List<Fond> fonds, PARAMS customParams, Department ctx) {
        Form form = new Form("document-selection-search", Texts.ofMessageCoded("hledani.vybratZVyhledanych"));

        form.addField(createWithPropertyAndHint(RecordConstants.SearchParams.ISBN_OR_ISSN, Texts.ofMessageCoded("commons.IsbnOrIssn"), Texts.ofMessageCoded("search.recordSearchSelectionForm.isbnOrIssn.hint"),
                IsbnEditorFactory.createDefault()
        ));

        form.addField(createWithPropertyAndHint(CoreSearchParams.NAME, Texts.ofMessageCoded("commons.nazev"), Texts.ofMessageCoded("search.recordSearchSelectionForm.nazev.hint"),
                TextValueEditor.getEmptyEditor()
                        .withMinLength(3)
                        .withMaxLength(100)));

        form.addField(createWithPropertyAndHint(RecordConstants.SearchParams.AUTHOR, Texts.ofMessageCoded("commons.autor"), Texts.ofMessageCoded("search.recordSearchSelectionForm.autor.hint"),
                TextValueEditor.getEmptyEditor()
                        .withMinLength(3)
                        .withMaxLength(100)));

        form.addField(createWithPropertyAndHint(RecordConstants.SearchParams.PUBLICATION_YEAR, Texts.ofMessageCoded("commons.rokVydani"), Texts.ofMessageCoded("search.recordSearchSelectionForm.rokVydani.hint"),
                YearValueEditor.getEmptyEditor()));

        List<LabeledId<String>> acceptableDatasources = getDatasourceEditorAcceptableValues(fonds, customParams, ctx);
        if (!acceptableDatasources.isEmpty()) {
            form.addField(createWithProperty(RecordConstants.SearchParams.DATASOURCE, SingleAcceptableValueEditor.getEmptyEditor(acceptableDatasources)));
        }

        return form;
    }

    private <PARAMS extends MapBackedParams> List<LabeledId<String>> getDatasourceEditorAcceptableValues(@NonNull List<Fond> fonds, PARAMS p, Department ctx) {
        Stream<Datasource> allPossibleDatasourceStream = allDatasetsProvider.getAll().stream()
                .filter(datasource -> {
                    boolean isConcreteDatasourceInParams = p.hasNotNull(RecordConstants.SearchParams.DATASOURCE) && p.get(RecordConstants.SearchParams.DATASOURCE).equals(datasource);
                    if (isConcreteDatasourceInParams) {
                        return true;
                    }
                    boolean isInAllowedDatasources = allowedDatasetsProvider.getOn(ctx).contains(datasource);
                    if (isInAllowedDatasources) {
                        return true;
                    }
                    return false;
                });
        return allPossibleDatasourceStream
                .filter(datasource -> !p.hasNotNull(RecordConstants.SearchParams.DATASOURCE_GROUP) || datasource.isOfGroup(p.get(RecordConstants.SearchParams.DATASOURCE_GROUP)))
                .filter(datasource -> !isSearchForSourceDocument(fonds) || datasource.supportsSubkind(BasicMapSearchParams.SUBKIND_DOCUMENT)) // if searching for source document, allow only document datasets
                .filter(datasource -> datasource.supportsAnyOfFonds(fonds))
                .filter(datasource -> isLocalRootSearchable(datasource, ctx))
                .filter(datasource -> isForceDisableSearchInRootDataset(datasource, ctx))
                .map(datasource -> {
                    Text text = datasource.text();
                    if (datasource.equals(localCurrentDepartmentDataset())) {
                        text = MultiText.ofTexts(datasource.text(), ctx.getText()).withSpaceDelimiter();
                    }
                    return new LabeledId<>(datasource.id(), text);
                })
                .toList();
    }

    private boolean isLocalRootSearchable(Datasource datasource, Department ctx) {
        return !(localRootDepartmentDataset().equals(datasource) && rootDepartmentProvider.get().equals(ctx));
    }

    private boolean isForceDisableSearchInRootDataset(Datasource datasource, Department ctx) {
        return !(forceDisableSearchInRootDataset().getOn(ctx) && localRootDepartmentDataset().equals(datasource));
    }

    private static <PARAMS extends MapBackedParams> boolean isSearchForSourceDocument(@NonNull List<Fond> fonds) {
        List<Fond> authorityFonds = Fond.filterAuthorityFonds(fonds);
        return authorityFonds.size() == 1 && authorityFonds.getFirst().isForSourceDocument();
    }

    @Bean
    public SearchFormFactory newestTypeSearchFormFactory() {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                List<Form> forms = new ArrayList<>();

                Form formDate = new Form("newest-from-date-facet", Texts.ofMessageCoded("seznam.novinkyOd"));
                formDate.addField(createWithProperty(CoreSearchParams.FROM_DATE, Texts.ofEmpty(), DateValueEditor.getEmptyEditor().withPastValidation()));
                forms.add(formDate);
                Form form = new Form("newest-other-facets", Texts.ofMessageCoded("hledani.vybratZVyhledanych"));
                form.addField(createWithProperty(RecordConstants.SearchParams.ROOT_FOND, MultipleAcceptableValueEditor.getEmptyEditor(currentAuthShowableFondsLoader.getAllDocumentByAuth(currentAuth, ctx))));
                form.addField(createWithProperty(CoreSearchParams.DEPARTMENT, AcceptableRootValueEditor.getEmptyEditor(departmentAccessor.getHierarchical(ctx).filterOnline())));
                forms.add(form);

                return forms;
            }
        };
    }

    private SearchFormFactory selectableFondSearchFormFactory(final String formId) {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                List<Form> forms = new ArrayList<>();

                Form form = new Form(formId, Texts.ofMessageCoded("hledani.vybratZVyhledanych"));
                form.addField(createWithProperty(RecordConstants.SearchParams.ROOT_FOND,
                        MultipleAcceptableValueEditor.getEmptyEditor(currentAuthShowableFondsLoader.getAllDocumentByAuth(currentAuth, ctx)).withForcedCheckboxes()));
                forms.add(form);

                return forms;
            }
        };
    }

    @Bean
    public SearchFormFactory mostWatchedTypeSearchFormFactory() {
        return selectableFondSearchFormFactory("most-watched-search");
    }

    @Bean
    public SearchFormFactory topRatedTypeSearchFormFactory() {
        return selectableFondSearchFormFactory("top-rated-search");
    }

    @Bean
    public SearchFormFactory mostLentTypeSearchFormFactory() {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                List<Form> forms = new ArrayList<>();

                Form form = new Form("most-lent-search", Texts.ofMessageCoded("hledani.vybratZVyhledanych"));
                form.addField(createWithProperty(RecordConstants.SearchParams.ROOT_FOND,
                        MultipleAcceptableValueEditor.getEmptyEditor(currentAuthShowableFondsLoader.getAllDocumentByAuth(currentAuth, ctx)).withForcedCheckboxes()));
                form.addField(createWithProperty(CoreSearchParams.DEPARTMENT, AcceptableRootValueEditor.getEmptyEditor(departmentAccessor.getHierarchical(ctx))));
                forms.add(form);

                return forms;
            }
        };
    }

    @Bean
    public SearchFormFactory documentIndexTypeSearchFormFactory() {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                List<Form> forms = new ArrayList<>();

                Form form = new Form("document-index-prefix-facet", Texts.ofMessageCoded("rejstrik.zadejtePocatecniPismena"));
                form.addField(createWithProperty(RecordConstants.SearchParams.PREFIX,
                        TextValueEditor.getEmptyEditor()
                                .withMaxLength(40)));
                forms.add(form);
                Form formSelectFond = new Form("document-index-other-facets", Texts.ofMessageCoded("hledani.vybratZVyhledanych"));
                formSelectFond.addField(createWithProperty(RecordConstants.SearchParams.ROOT_FOND,
                        MultipleAcceptableValueEditor.getEmptyEditor(currentAuthShowableFondsLoader.getAllDocumentByAuth(currentAuth, ctx))));
                forms.add(formSelectFond);

                return forms;
            }
        };
    }

    @Bean
    public SearchFormFactory authorityIndexTypeSearchFormFactory() {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                List<Form> forms = new ArrayList<>();

                Form form = new Form("authority-index-prefix-facet", Texts.ofMessageCoded("rejstrik.zadejtePocatecniPismena"));
                form.addField(createWithProperty(RecordConstants.SearchParams.PREFIX, TextValueEditor.getEmptyEditor().withMaxLength(40)));
                forms.add(form);
                Form formSelectFond = new Form("authority-index-other-facets", Texts.ofMessageCoded("rejstrik.vyberteRejstrik"));
                formSelectFond.addField(createWithProperty(RecordConstants.SearchParams.ROOT_FOND, SingleAcceptableValueEditor.getEmptyEditor(currentAuthShowableFondsLoader.getAllAuthorityByAuth(currentAuth, ctx)).withModelAsArray(true).withRequired(true)));
                forms.add(formSelectFond);

                return forms;
            }
        };
    }

    @Bean
    public SearchFormFactory recordOperationTypeSearchFormFactory() {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                List<Form> forms = new ArrayList<>();

                Form form = new Form("record-operation-search", Texts.ofMessageCoded("hledani.kriteria"));
                form.addField(createWithProperty(RecordConstants.SearchParams.OPERATION_TYPE, MultipleAcceptableValueEditor.getEmptyEditor(recordOperationTypeLoader)));
                form.addField(createWithProperty(CoreSearchParams.FROM_DATE, DateValueEditor.getEmptyEditor()
                        .withMaximumGranularity(DateValueEditorGranularity.DATE_AND_TIME)
                        .withViewFormat("d.L.y H:mm")
                        .withPastValidation()));
                form.addField(createWithProperty(CoreSearchParams.TO_DATE, DateValueEditor.getEmptyEditor()
                        .withMaximumGranularity(DateValueEditorGranularity.DATE_AND_TIME)
                        .withViewFormat("d.L.y H:mm")
                        .withPastValidation()));
                form.addField(createWithProperty(RecordConstants.SearchParams.OPERATED_ROOT_FOND, MultipleAcceptableValueEditor.getEmptyEditor(currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))));
                form.addField(createWithProperty(CoreSearchParams.INITIATOR, SearchOrEditValueEditor.getEmptyEditor()
                        .withType(ValueEditorAliasType.USER)
                        .withStaticSearchParams(StaticSearchParams.createOfKindAndType(BasicMapSearchParams.KIND_USER, SearchViewConstants.TYPE_USER_SEARCH))
                        .withAllowToDeleteValue(true)));
                forms.add(form);

                return forms;
            }
        };
    }

    @Bean
    public Codebook<FieldTypeSearchField, String> fieldTypeSearchFieldLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(fieldTypeLoader)
                .converted(new RecursiveFieldTypesToSearchFieldsConverter())
                .staticCached(FieldType.class.getSimpleName())
                .build();
    }

    @Bean
    public Codebook<FieldTypeSortItem, String> fieldTypeSortItemLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(fieldTypeLoader)
                .converted(new RecursiveFieldTypesToSortFieldsConverter())
                .staticCached(FieldType.class.getSimpleName())
                .build();
    }

    @Bean
    public FieldTypeSearchFieldToLuceneSearchFieldFactory fieldTypeSearchFieldFieldToLuceneSearchFieldFactory() {
        return new FieldTypeSearchFieldToLuceneSearchFieldFactory(fieldTypeSearchFieldLoader());
    }

    @Bean
    public FieldTypeSearchFieldToLuceneSortFieldFactory fieldTypeSearchFieldFieldToLuceneSortFieldFactory() {
        return new FieldTypeSearchFieldToLuceneSortFieldFactory();
    }

    @Bean
    public LinkConstraintsResolver linkConstraintsResolver() {
        return new LinkConstraintsResolver(
                nonDetailedRichRecordLoader,
                richRecordLoader,
                detailedRecordSearchSqlLoader,
                fieldTypesByFondLoader,
                departmentAccessor,
                fieldTypeSearchFieldLoader()
        );
    }

    @Bean
    public RecordDayIdLoader recordDayIdLoader() {
        return new SearchLoadingRecordDayIdLoader(
                documentIdSearchSqlLoader,
                dayFondProvider,
                fieldTypeSearchFieldLoader()
        );
    }

    @Bean
    public RecordDayLoader recordDayLoader() {
        return new RecordDayLoader(
                recordDayIdLoader(),
                idsToRecordsConverter,
                StaticProvider.of(CoreConstants.CZECH_TIME_ZONE_ID) // TODO: temporary workaround what zone should be here?
        );
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        StaticSearchFields.CODEBOOK
                .add(RecordConstants.SearchFields.FOND)
                .add(RecordConstants.SearchFields.DOCUMENT_FOND)
                .add(RecordConstants.SearchFields.AUTHORITY_FOND)
                .add(RecordConstants.SearchFields.RECORD_CREATION_DATE)
                .add(RecordConstants.SearchFields.AUTHORITY_ALL)
                .add(RecordConstants.SearchFields.AUTHORITY_NAME)
                .add(RecordConstants.SearchFields.CNA)
                .add(RecordConstants.SearchFields.DOCUMENT_ALL)
                .add(RecordConstants.SearchFields.RECORD_STATUS)
                .add(RecordConstants.SearchFields.DOCUMENT_ISBN)
                .add(RecordConstants.SearchFields.DOCUMENT_ISSN)
                .add(RecordConstants.SearchFields.DOCUMENT_ISBN_OR_ISSN)
                .add(RecordConstants.SearchFields.DOCUMENT_NAME)
                .add(RecordConstants.SearchFields.AUTHOR)
                .add(RecordConstants.SearchFields.DOCUMENT_YEAR)
                .add(RecordConstants.SearchFields.PUBLISHER)
                .add(RecordConstants.SearchFields.RECORD_ID)
                .add(RecordConstants.SearchFields.RECORD_RELATED_RECORD)
                .add(RecordConstants.SearchFields.RECORD_FILE_CATEGORY)
                .add(RecordConstants.SearchFields.RECORD_SOURCE_RECORD)
                .add(RecordConstants.SearchFields.TABLE_OF_CONTENT);

        searchFieldsProvider.add(fieldTypeSearchFieldLoader());
        searchFieldLoader.add(fieldTypeSearchFieldLoader());
        searchSortingLoader.add(fieldTypeSortItemLoader());

        mappableSearchParamsRegistry.registerPrefix(DYNAMIC_FIELD_PREFIX, new PrefixedFieldSetter(fieldTypeSearchFieldLoader(), datatypableStringConverter));
        luceneQueryFieldLoader.setDynamicFieldToLuceneSearchFieldFactory(fieldTypeSearchFieldFieldToLuceneSearchFieldFactory());
        luceneSortFieldLoader.setDynamicFieldToLuceneSearchFieldFactory(fieldTypeSearchFieldFieldToLuceneSortFieldFactory());

        allDatatypeToAllValuesProviderMap.registerEquals(CoreConstants.Datatype.LOCAL_DATASET, allLocalDatasetsProvider());

        searchFactoryResolver
                .withStandardOrder(newestSearchFactory())
                .withStandardOrder(mostWatchedSearchFactory())
                .withStandardOrder(topRatedSearchFactory())
                .withStandardOrder(mostLentSearchFactory())
                .withStandardOrder(documentIndexSearchFactory())
                .withStandardOrder(authorityIndexSearchFactory())
                .withStandardOrder(recordOperationSearchFactory())
                .withStandardOrder(randomSearchFactory())
                .withLast(globalSearchFactory());

        searchFormFactory
                .add(SearchViewConstants.TYPE_FIELDS, fieldsTypeSearchFormFactory())
                .add(SearchViewConstants.TYPE_SEARCH_SELECTION, searchSelectionTypeSearchFormFactory())
                .add(SearchViewConstants.TYPE_NEWEST, newestTypeSearchFormFactory())
                .add(SearchViewConstants.TYPE_MOST_WATCHED, mostWatchedTypeSearchFormFactory())
                .add(SearchViewConstants.TYPE_TOP_RATED, topRatedTypeSearchFormFactory())
                .add(SearchViewConstants.TYPE_MOST_LENT, mostLentTypeSearchFormFactory())
                .add(SearchViewConstants.TYPE_DOCUMENT_INDEX, documentIndexTypeSearchFormFactory())
                .add(SearchViewConstants.TYPE_AUTHORITY_INDEX, authorityIndexTypeSearchFormFactory())
                .add(SearchViewConstants.TYPE_RECORD_OPERATION_SEARCH, recordOperationTypeSearchFormFactory());

        allDatasetsProvider.withProvider(allLocalDatasetsProvider());
        allowedDatasetsProvider.add(allowedLocalDatasetsProvider());

        permissionRegistry.add(RecordSecurityActions.RECORD_SEARCH_IN_DATASOURCE, or(
                (auth, ctx, datasource) -> PermissionResult.ifCan(datasource.equals(localCurrentDepartmentDataset()), auth),
                permissionFactory.edit()
        ));

        luceneSearchQueryBuilder.withRestrictionModifierFor(MapBackedParams.class, new CommonRecordSearchParamsRestrictionModifier(linkConstraintsResolver(), enabledLoadableFondsExpander, fieldTypeSearchFieldLoader()));
    }

}
