package cz.kpsys.portaro.config;

import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.SideThreadAuthenticationIsolator;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualFunction;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.auth.current.CurrentAuthWebResolver;
import cz.kpsys.portaro.auth.department.CurrentAuthDepartmentsLoader;
import cz.kpsys.portaro.commons.CommonsConstants;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualBiFunction;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.convert.ChainingConverter;
import cz.kpsys.portaro.commons.convert.PrefixRemovingStringToEConverter;
import cz.kpsys.portaro.commons.convert.SingleItemConverterByListConverter;
import cz.kpsys.portaro.commons.date.DateRangeToStringConverter;
import cz.kpsys.portaro.commons.date.DatetimeRangeToTimezonedStringConverter;
import cz.kpsys.portaro.commons.date.InstantToStringConverter;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.IdentifiedValue;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.filter.ConjunctionObjectFilter;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.contextual.ContextualVisibleDepartmentsLoader;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.exemplar.ExemplarListSorter;
import cz.kpsys.portaro.export.*;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.directory.DirectoryLoader;
import cz.kpsys.portaro.formconfig.valueeditor.AcceptableValuesResolver;
import cz.kpsys.portaro.formconfig.valueeditor.AuthenticatedAcceptableValuesResolver;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.licence.FeatureManager;
import cz.kpsys.portaro.mail.MailService;
import cz.kpsys.portaro.marcxml.MarcConstants;
import cz.kpsys.portaro.marcxml.convert.JacksonRecordMarcDtoToStringConverter;
import cz.kpsys.portaro.marcxml.model.StrictRecordMarcDto;
import cz.kpsys.portaro.prop.ObjectPropertiesGenerator;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.authority.RecordHierarchyLoader;
import cz.kpsys.portaro.record.bulkedit.RecordBulkeditor;
import cz.kpsys.portaro.record.citation.*;
import cz.kpsys.portaro.record.copy.TransactionalRecordCopier;
import cz.kpsys.portaro.record.creation.RecordsFromFilesCreatorService;
import cz.kpsys.portaro.record.deletion.RecordDeleter;
import cz.kpsys.portaro.record.deletion.RecordHoldingDeletionCommand;
import cz.kpsys.portaro.record.deletion.RecordHoldingsSubtreeDeleter;
import cz.kpsys.portaro.record.deletion.RecordHoldingsSubtreeDeletionCommand;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.discardation.RecordHoldingDiscarder;
import cz.kpsys.portaro.record.document.*;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.record.edit.RecordFieldEditor;
import cz.kpsys.portaro.record.edit.authorityfield008.AuthorityField008DefinitionsLoader;
import cz.kpsys.portaro.record.edit.field007.Field007DefinitionsLoader;
import cz.kpsys.portaro.record.edit.field008.Field008DefinitionsLoader;
import cz.kpsys.portaro.record.edit.fieldshierarchy.FieldValueCommandResolver;
import cz.kpsys.portaro.record.edit.view.UserInteractionRecordEditation;
import cz.kpsys.portaro.record.edit.view.UserInteractionRecordEditationFactory;
import cz.kpsys.portaro.record.evaluation.RatingLoader;
import cz.kpsys.portaro.record.evaluation.RatingSaver;
import cz.kpsys.portaro.record.export.csv.CsvFileRecordsExporter;
import cz.kpsys.portaro.record.export.html.HtmlFileDocumentsExporter;
import cz.kpsys.portaro.record.export.marc.BasicRecordsToMarcDtosConverter;
import cz.kpsys.portaro.record.export.marc.RecordsToMarcDtosConverter;
import cz.kpsys.portaro.record.export.ris.RisFileDocumentExporter;
import cz.kpsys.portaro.record.file.RecordWithAttachmentSaveCommand;
import cz.kpsys.portaro.record.file.cover.CoverService;
import cz.kpsys.portaro.record.holding.RecordHolding;
import cz.kpsys.portaro.record.holding.RecordHoldingLoader;
import cz.kpsys.portaro.record.holding.RecordHoldingUpserter;
import cz.kpsys.portaro.record.lastvisiteddocuments.LastVisitedDocumentsList;
import cz.kpsys.portaro.record.lastvisiteddocuments.LimitedSizeLastVisitedDocumentsList;
import cz.kpsys.portaro.record.link.RecordLinkLoader;
import cz.kpsys.portaro.record.merge.RecordMerger;
import cz.kpsys.portaro.record.newestdocuments.NewestDocumentsService;
import cz.kpsys.portaro.record.operation.CachingRecordEditLevelProvider;
import cz.kpsys.portaro.record.operation.RecordOperation;
import cz.kpsys.portaro.record.revision.RecordRevisionApiController;
import cz.kpsys.portaro.record.revision.RecordRevisionLoader;
import cz.kpsys.portaro.record.sec.CurrentAuthFondsLoader;
import cz.kpsys.portaro.record.view.*;
import cz.kpsys.portaro.record.view.show.CompositeRecordShowListener;
import cz.kpsys.portaro.record.view.show.RecordShowListener;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.view.RecordToViewableRecordsConverter;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.template.*;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import cz.kpsys.portaro.user.contact.ContactManager;
import cz.kpsys.portaro.user.contact.Email;
import cz.kpsys.portaro.user.relation.RepresentableUserLoader;
import cz.kpsys.portaro.user.sec.SecurityActions;
import cz.kpsys.portaro.view.ViewableItemsConverter;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import cz.kpsys.portaro.view.domain.CurrentPage;
import cz.kpsys.portaro.view.domain.DocumentExemplarsViewFactory;
import cz.kpsys.portaro.view.domain.LoginView;
import cz.kpsys.portaro.view.domain.menu.MenuItem;
import cz.kpsys.portaro.view.domain.paragraph.RecordParagraph;
import cz.kpsys.portaro.view.domain.paragraph.StringTemplateParagraph;
import cz.kpsys.portaro.view.domain.paragraph.TemplateDescriptorParagraph;
import cz.kpsys.portaro.view.web.CatalogWebSettingsKeys;
import cz.kpsys.portaro.view.web.page.AuthorityPageController;
import cz.kpsys.portaro.view.web.page.DocumentPageController;
import cz.kpsys.portaro.view.web.page.RecordPageController;
import cz.kpsys.portaro.view.web.rest.NewestDocumentsApiController;
import cz.kpsys.portaro.view.web.rest.page.CurrentPageController;
import cz.kpsys.portaro.view.web.rest.page.PageDataApiController;
import cz.kpsys.portaro.view.web.rest.record.*;
import cz.kpsys.portaro.web.bot.BotFinder;
import cz.kpsys.portaro.web.page.CurrentPageFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.web.client.RestOperations;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.servlet.LocaleResolver;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;

import static cz.kpsys.portaro.commons.localization.Texts.ofArgumentedMessageCoded;
import static cz.kpsys.portaro.commons.localization.Texts.ofMessageCoded;
import static cz.kpsys.portaro.template.Templates.TEMPLATE_AUTHORITY_EXTERNAL_SEARCH;
import static cz.kpsys.portaro.template.Templates.TEMPLATE_DOCUMENT_EXTERNAL_SEARCH;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordViewConfig {

    public static final String FEATURE_NAME = "Rating";
    private static final int SEARCHED_RECORDS_LIMIT_ITEMS = 400;

    @NonNull NamedParameterJdbcOperations notCriticalJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull SettingLoader settingLoader;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull ConversionService conversionService;
    @NonNull RestOperations rest;
    @NonNull IdAndIdsLoadable<Record, UUID> recordLoader;
    @NonNull ByIdLoadable<@NonNull Record, String> recordStringIdPrefixDispatchingLoader;
    @NonNull AllByIdsLoadable<RecordStatus, Integer> recordStatusesByIdsLoader;
    @NonNull RecordHierarchyLoader recordHierarchyLoader;
    @NonNull RecordRevisionLoader recordRevisionLoader;
    @NonNull ByIdLoadable<Record, Integer> authorityByKindedIdLoader;
    @NonNull IdAndIdsLoadable<Record, Integer> nonDetailedDocumentByKindedIdLoader;
    @NonNull IdAndIdsLoadable<Record, UUID> richRecordLoader;
    @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;
    @NonNull ByIdLoadable<@NonNull UUID, String> recordIdByField1ValueLoader;
    @NonNull ObjectPropertiesGenerator<Record> recordPropertiesGenerator;
    @NonNull AuthoritySourceDocumentProvider authoritySourceDocumentProvider;
    @NonNull RecordShowListener recordShowCountSaver;
    @NonNull CurrentAuthDepartmentsLoader currentAuthEditableDepartmentsLoader;
    @NonNull CurrentAuthFondsLoader currentAuthEditableFondsLoader;
    @NonNull FeatureManager featureManager;
    @NonNull ExporterResolver exporterResolver;
    @NonNull SearchService<InternalSearchResult<String, MapBackedParams, RangePaging>, MapBackedParams> recordSearchService;
    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull CurrentAuthFondsLoader currentAuthShowableFondsLoader;
    @NonNull RatingSaver ratingSaver;
    @NonNull RatingLoader ratingLoader;
    @NonNull CoverService coverService;
    @NonNull BotFinder botFinder;
    @NonNull Translator<Department> translator;
    @NonNull TemplateEngine templateEngine;
    @NonNull ExportDescriptorLoader exportDescriptorLoader;
    @NonNull RecordLinkLoader externalDetailRecordLinkLoader;
    @NonNull RecordLinkLoader searchElsewhereRecordLinkLoader;
    @NonNull PageSearchLoader<MapBackedParams, Exemplar, RangePaging> exemplarSearchLoader;
    @NonNull ExemplarListSorter exemplarListSorter;
    @NonNull SecurityManager securityManager;
    @NonNull SearchFactoryResolver searchFactoryResolver;
    @NonNull MailService mailService;
    @NonNull ContactManager contactManager;
    @NonNull TemplateDescriptorLoader templateDescriptorLoader;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull Repository<UserInteractionRecordEditation, String> recordEditationRepository;
    @NonNull ViewableRecordEditationFactory viewableRecordEditationFactory;
    @NonNull RecordHoldingLoader recordHoldingLoader;
    @NonNull RecordHoldingUpserter recordHoldingUpserter;
    @NonNull Deleter<RecordHoldingDeletionCommand> recordHoldingDeleter;
    @NonNull RecordDeleter recordDeleter;
    @NonNull RecordHoldingDiscarder recordHoldingDiscarder;
    @NonNull RecordMerger recordMerger;
    @NonNull CachingRecordEditLevelProvider recordEditLevelProvider;
    @NonNull DocumentContentTypeResolver documentContentTypeResolver;
    @NonNull TemplateLoadingExportDescriptorLoader templateLoadingExportDescriptorLoader;
    @NonNull Field007DefinitionsLoader field007DefinitionsLoader;
    @NonNull Field008DefinitionsLoader field008DefinitionsLoader;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull ViewableItemsConverter viewableItemsConverter;
    @NonNull ContextualProvider<Department, @NonNull Boolean> documentFondsDisabledProvider;
    @NonNull AuthorityField008DefinitionsLoader authorityField008DefinitionsLoader;
    @NonNull AuthenticatedAcceptableValuesResolver<Record, Department> assignableRecordDepartmentsAuthenticatedContextualProvider;
    @NonNull FieldValueCommandResolver fieldValueCommandResolver;
    @NonNull Saver<RecordWithAttachmentSaveCommand, ?> recordWithAttachmentSaver;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull DirectoryLoader directoryLoader;
    @NonNull PageSearchLoader<MapBackedParams, IdentifiedFile, RangePaging> fileSearchLoader;
    @NonNull FileDataStreamer fileDataStreamer;
    @NonNull Provider<@NonNull User> portaroUserProvider;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull ExecutorService executorService;
    @NonNull LocaleResolver localeResolver;
    @NonNull CurrentPageFactory<CurrentPage> currentPageFactory;
    @NonNull ContextualProvider<Department, LoginView> loginViewDepartmentedProvider;
    @NonNull RepresentableUserLoader representableUserLoader;
    @NonNull ContextualProvider<Department, List<String>> forbiddenMenuItems;
    @NonNull AuthenticatedContextualProvider<Department, List<MenuItem>> menuItemsAuthenticatedContextualProvider;
    @NonNull CurrentAuthWebResolver currentAuthWebResolver;
    @NonNull ContextualBiFunction<User, UserAuthentication, Department, Integer> numberOfUserAttentionRequiringActionsProvider;
    @NonNull UserByBasicUserLoader userLoader;
    @NonNull ByIdLoadable<FieldType<?>, FieldTypeId> fieldTypeLoader;
    @NonNull RecordFieldEditor recordFieldEditor;
    @NonNull RecordStatusResolver recordStatusResolver;
    @NonNull SimilarRecordsLoader similarRecordsLoader;
    @NonNull LastVisitedDocumentsList lastVisitedDocuments;
    @NonNull NewestDocumentsService newestDocumentsService;
    @NonNull ContextualVisibleDepartmentsLoader assignableDepartmentsLoader;
    @NonNull AuthenticatedContextualFunction<Record, Department, List<RecordHolding>> visibleRecordHoldingsResolver;

    @Bean
    public RecordEditationApiController recordEditationApiController() {
        return new RecordEditationApiController(
                recordEditationFactory,
                recordEditationRepository,
                recordDeleter,
                currentAuthEditableFondsLoader,
                fieldValueCommandResolver,
                viewableRecordEditationFactory,
                userInteractionRecordEditationFactory(),
                fieldTypeLoader,
                recordFieldEditor
        );
    }

    @Bean
    public UserInteractionRecordEditationFactory userInteractionRecordEditationFactory() {
        return new UserInteractionRecordEditationFactory(recordFieldEditor);
    }

    @Bean
    public FieldsDefinitionsApiController fieldsDefinitionsApiController() {
        return new FieldsDefinitionsApiController(
                field007DefinitionsLoader,
                field008DefinitionsLoader,
                authorityField008DefinitionsLoader
        );
    }

    @Bean
    public RecordApiController recordApiController() {
        return new RecordApiController(
                recordLoader,
                recordStringIdPrefixDispatchingLoader,
                recordEditationFactory,
                recordMerger,
                recordEditLevelProvider,
                fieldValueCommandResolver,
                recordFieldEditor,
                relatedRecordsCountLoader(),
                recordArticlesLoader(),
                recordPartsLoader(),
                settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_PARTS_CONTAINING_FIELD_NUMBERS),
                settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES)
                        .andThen(recordStatusesByIdsLoader::getAllByIds),
                settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS),
                documentFondsDisabledProvider,
                recordsToViewableRecordItemsWithoutExportsConverter(),
                recordCopier(),
                recordHoldingsSubtreeDeleter(),
                fieldTypeLoader
        );
    }

    @Bean
    public RecordExtrasController recordExtrasController() {
        return new RecordExtrasController(
                recordsFromFilesCreatorService()
        );
    }

    @Bean
    public RecordDetailViewApiController recordDetailViewApiController() {
        CompositeRecordShowListener documentShowListener = new CompositeRecordShowListener();
        documentShowListener.addListener(recordShowCountSaver);
        documentShowListener.addListener(lastVisitedDocuments());

        return new RecordDetailViewApiController(
                recordStringIdPrefixDispatchingLoader,
                genericRecordDetailViewFactory(),
                documentShowListener,
                authoritySourceDocumentProvider
        );
    }

    @Bean
    public SimilarDocumentsApiController similarDocumentsApiController() {
        return new SimilarDocumentsApiController(
                similarRecordsLoader,
                recordsToViewableRecordItemsWithoutExportsConverter(),
                settingLoader.getOnRootProvider(SettingKeys.SIMILAR_DOCUMENTS_LIMIT)
        );
    }

    @Bean
    public VisitedApiController visitedApiController() {
        return new VisitedApiController(
                lastVisitedDocuments,
                recordsToViewableRecordItemsWithoutExportsConverter()
        );
    }

    @Bean
    public NewestDocumentsApiController newestDocumentsApiController() {
        return new NewestDocumentsApiController(
                newestDocumentsService,
                recordsToViewableRecordItemsWithoutExportsConverter()
        );
    }

    @Bean
    public RecordForbiddenEvaluator recordForbiddenEvaluator() {
        return new RecordForbiddenEvaluator(
                settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES)
                        .andThen(recordStatusesByIdsLoader::getAllByIds),
                settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS)
        );
    }

    @Bean
    public RecordsFromFilesCreatorService recordsFromFilesCreatorService() {
        return new RecordsFromFilesCreatorService(
                recordEditationFactory,
                recordEditationHelper,
                recordEntryFieldTypeIdResolver,
                recordWithAttachmentSaver,
                recordDeleter,
                new SideThreadAuthenticationIsolator(authenticationHolder, portaroUserProvider, executorService),
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public TransactionalRecordCopier recordCopier() {
        return new TransactionalRecordCopier(
                recordEditationFactory,
                directoryLoader,
                fileSearchLoader,
                fileDataStreamer,
                recordWithAttachmentSaver,
                defaultTransactionTemplateFactory.get(),
                recordFieldEditor
        );
    }

    @Bean
    public Deleter<RecordHoldingsSubtreeDeletionCommand> recordHoldingsSubtreeDeleter() {
        return new RecordHoldingsSubtreeDeleter(departmentAccessor, recordHoldingLoader, recordHoldingDeleter);
    }

    @Bean
    public RecordBulkEditApiController recordBulkEditApiController() {
        return new RecordBulkEditApiController(
                new RecordBulkeditor(
                        conversionService,
                        recordEditationFactory,
                        recordFieldEditor
                )
        );
    }

    @Bean
    public RecordRelationsApiController recordRelationsApiController() {
        return new RecordRelationsApiController(
                recordHierarchyLoader,
                settingLoader.getOnRootProvider(SettingKeys.AUTHORITY_HIERARCHY_ROOT).throwingWhenNull()
        );
    }

    @Bean
    public RecordHierarchyApiController recordHierarchyApiController() {
        return new RecordHierarchyApiController(recordHierarchyLoader);
    }

    @Bean
    public RecordRevisionApiController recordRevisionApiController() {
        return new RecordRevisionApiController(recordRevisionLoader);
    }

    @Bean
    public RecordHoldingApiController recordHoldingApiController() {
        return new RecordHoldingApiController(
                recordHoldingLoader,
                recordHoldingUpserter,
                recordHoldingDeleter,
                recordHoldingDiscarder,
                assignableDepartmentsLoader,
                visibleRecordHoldingsResolver
        );
    }

    @Bean
    public RecordMailApiController recordMailApiController() {
        return new RecordMailApiController(
                mailService,
                templateEngine,
                exemplarSearchLoader,
                exemplarListSorter,
                departmentAccessor,
                translator,
                serverUrlProvider.throwingWhenNull(),
                recordsToViewableRecordParagraphItemsConverter()
        );
    }


    @Bean
    public CitationApiController citationApiController() {
        return new CitationApiController(citationService());
    }


    @Bean
    public RatingApiController ratingApiController() {
        return new RatingApiController(
                ratingLoader,
                ratingSaver,
                settingLoader.getDepartmentedProvider(SettingKeys.RATING_ENABLED).toEnabledAsserter(value -> value, FEATURE_NAME, null)
        );
    }


    @Bean
    public DocumentPageController documentPageController() {
        return new DocumentPageController(
                nonDetailedDocumentByKindedIdLoader,
                recordIdByField1ValueLoader,
                publicContextPath,
                botFinder,
                recordSearchService,
                new IdsToRecordsConverter()
                        .withDocumentSupport(nonDetailedDocumentByKindedIdLoader)
                        .withRecordSupport(nonDetailedRichRecordLoader)
        );
    }


    @Bean
    public AuthorityPageController authorityPageController() {
        return new AuthorityPageController(
                publicContextPath,
                authorityByKindedIdLoader
        );
    }


    @Bean
    public CurrentPageController currentPageController() {
        return new CurrentPageController(
                currentPageFactory,
                localeResolver,
                publicContextPath,
                loginViewDepartmentedProvider,
                representableUserLoader,
                forbiddenMenuItems,
                menuItemsAuthenticatedContextualProvider,
                currentAuthWebResolver,
                numberOfUserAttentionRequiringActionsProvider,
                userLoader
        );
    }


    @Bean
    public PageDataApiController pageDataApiController() {
        return new PageDataApiController(
                pageDataProviders()
        );
    }

    @Bean
    public Map<String, ContextualFunction<UserAuthentication, Department, ?>> pageDataProviders() {
        return new HashMap<>();
    }


    @Bean
    public RecordPageController recordPageController() {
        return new RecordPageController(
                searchFactoryResolver,
                publicContextPath,
                nonDetailedRichRecordLoader,
                coverService
        );
    }


    @Bean
    public GenericRecordDetailViewFactory genericRecordDetailViewFactory() {
        return new GenericRecordDetailViewFactory(
                documentDetailViewManager(),
                authorityDetailViewFactory(),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.LAYOUT_DOCUMENT_DETAIL),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.LAYOUT_AUTHORITY_DETAIL),
                settingLoader.getDepartmentedFondedValuesProvider(CatalogWebSettingsKeys.LAYOUT_RECORD_DETAIL)
        );
    }


    @Bean
    public AuthorityDetailViewFactory authorityDetailViewFactory() {
        return new AuthorityDetailViewFactory(
                recordsToViewableRecordItemsWithExportsConverter(),
                translator,
                new StringTemplateParagraph(templateEngine, translator, settingLoader.getDepartmentedProvider(SettingKeys.AUTHORITY_TEMPLATE), recordsToViewableRecordParagraphItemsConverter()),
                settingLoader.getDepartmentedProvider(SettingKeys.AUTHORITY_DETAIL_TAB_ENABLED),
                settingLoader.getDepartmentedProvider(SettingKeys.RECORD_MARC_TAB_ENABLED),
                securityManager,
                ContextIgnoringContextualProvider.of(300),
                ContextIgnoringContextualProvider.of(200),
                ContextIgnoringContextualProvider.of(100),
                ContextIgnoringContextualProvider.of(400),
                ContextIgnoringContextualProvider.of(60),
                recordHierarchyLoader
        );
    }


    @Bean
    public DocumentDetailViewFactory documentDetailViewManager() {
        return new DocumentDetailViewFactory(
                recordsToViewableRecordItemsWithExportsConverter(),
                translator,
                new StringTemplateParagraph(templateEngine, translator, settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_NEXT_TO_COVER_TEMPLATE), recordsToViewableRecordParagraphItemsConverter()),
                new StringTemplateParagraph(templateEngine, translator, settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_NEXT_TO_HEADER_TEMPLATE), recordsToViewableRecordParagraphItemsConverter()),
                new StringTemplateParagraph(templateEngine, translator, settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_UNDER_COVER_TEMPLATE), recordsToViewableRecordParagraphItemsConverter()),
                settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_DETAIL_TAB_ENABLED),
                settingLoader.getDepartmentedProvider(SettingKeys.RECORD_MARC_TAB_ENABLED),
                settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_EXEMPLARS_TAB_ENABLED),
                settingLoader.getDepartmentedProvider(SettingKeys.COMMENT_STYLE),
                settingLoader.getDepartmentedProvider(SettingKeys.CITATION_SERVICE),
                citationService(),
                settingLoader.getDepartmentedProvider(SettingKeys.RATING_ENABLED),
                settingLoader.getDepartmentedProvider(SettingKeys.EXEMPLAR_TABS_BY_BUILDINGS_ENABLED),
                settingLoader.getDepartmentedProvider(SettingKeys.EXEMPLAR_STATUSES),
                externalDetailRecordLinkLoader,
                searchElsewhereRecordLinkLoader,
                exemplarSearchLoader,
                exemplarListSorter,
                documentExemplarsViewManager(),
                exportDescriptorLoader,
                securityManager,
                departmentAccessor,
                settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_ARTICLES_TAB_ENABLED),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.EXEMPLARS),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.ARTICLES),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.PARTS),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.CITATION),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.NONE),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.TOC),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.DETAIL),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.MARC),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.LOANS),
                settingLoader.getDepartmentedProvider(CatalogWebSettingsKeys.OPERATIONS),
                ContextIgnoringContextualProvider.of(20),
                assignableRecordDepartmentsAuthenticatedContextualProvider
        );
    }


    @Bean
    public DocumentExemplarsViewFactory documentExemplarsViewManager() {
        return new DocumentExemplarsViewFactory(
                securityManager,
                settingLoader.getDepartmentedProvider(SettingKeys.EXEMPLAR_DEFAULT_COLUMNS),
                settingLoader.getDepartmentedProvider(SettingKeys.EXEMPLAR_COLUMNS_BY_FONDS),
                settingLoader.getDepartmentedProvider(SettingKeys.ISSUE_COLUMNS),
                settingLoader.getDepartmentedProvider(SettingKeys.EXEMPLAR_TABS_BY_BUILDINGS_ENABLED),
                settingLoader.getDepartmentedProvider(SettingKeys.EXEMPLAR_STATUSES),
                currentAuthEditableDepartmentsLoader);
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_SESSION, proxyMode = ScopedProxyMode.INTERFACES)
    public LastVisitedDocumentsList lastVisitedDocuments() {
        return new LimitedSizeLastVisitedDocumentsList(5);
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<RecordTemplatedBodyMailSendRequest> recordTemplatedBodyMailSendRequestDefaulter() {
        return (formObject, department, currentAuth) -> {
            if (formObject.getRecipientEmail() == null) {
                BasicUser activeUser = currentAuth.getActiveUser();
                Optional<String> email = contactManager.getEmail(activeUser).map(Email::value);
                if (email.isPresent()) {
                    formObject = formObject.withRecipientEmail(email.get());
                }
            }
            if (formObject.getSubject() == null) {
                Locale locale = settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE).getOn(department);
                formObject = formObject.withSubject(Texts.ofMessageCodedWithLocalizedArgs("mail.informaceODokumentuX", formObject.getRecord().getText()).localize(translator, department, locale));
            }
            if (formObject.getTemplate() == null) {
                formObject = formObject.withTemplate(Templates.TEMPLATE_DOCUMENT_MAIL);
            }
            return formObject;
        };
    }

    @Bean
    public AcceptableValuesResolver<RecordTemplatedBodyMailSendRequest, TemplateDescriptor> recordTemplatedBodyMailSendRequestAcceptableTemplatesResolver() {
        return (request, ctx) -> templateDescriptorLoader.getAllByType(Templates.TEMPLATE_DOCUMENT_MAIL.getType());
    }

    @Bean
    public RelatedRecordsCountLoader relatedRecordsCountLoader() {
        return new RelatedRecordsCountLoaderAppserverSearch(
                recordSearchService,
                currentAuthShowableFondsLoader,
                departmentAccessor,
                settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES)
                        .andThen(recordStatusesByIdsLoader::getAllByIds),
                settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS)
        );
    }

    @Bean
    public RecordRelatedRecordsLoader recordArticlesLoader() {
        return new SpringDbRecordArticlesLoader(
                notCriticalJdbcTemplate,
                queryFactory,
                richRecordLoader
        );
    }

    @Bean
    public RecordRelatedRecordsLoader recordPartsLoader() {
        return new SpringDbRecordPartsLoader(
                notCriticalJdbcTemplate,
                queryFactory,
                richRecordLoader
        );
    }

    @Bean
    public Map<String, RecordParagraph> externalDocumentExports() {
        return Map.of(
                "portaroSearchItemParagraph", new TemplateDescriptorParagraph(templateEngine, translator, TEMPLATE_DOCUMENT_EXTERNAL_SEARCH, recordsToViewableRecordParagraphItemsConverter())
        );
    }

    @Bean
    public Map<String, RecordParagraph> documentExports() {
        var exports = new HashMap<String, RecordParagraph>(4);
        exports.put("portaroSearchItemParagraph", new StringTemplateParagraph(templateEngine, translator, settingLoader.getDepartmentedProvider(SettingKeys.SEARCH_DOCUMENT_TEMPLATE), recordsToViewableRecordParagraphItemsConverter()));
        exports.put("portaroSearchItemMoreParagraph", new StringTemplateParagraph(templateEngine, translator, settingLoader.getDepartmentedProvider(SettingKeys.SEARCH_DOCUMENT_MORE_TEMPLATE), recordsToViewableRecordParagraphItemsConverter()));
        exports.put("citationDefault", null);
        exports.put("portaroRecordNewsParagraph", new StringTemplateParagraph(templateEngine, translator, settingLoader.getDepartmentedProvider(RecordSettingKeys.NEWS_TEMPLATE), recordsToViewableRecordParagraphItemsConverter()));
        return exports;
    }

    @Bean
    public Map<String, RecordParagraph> externalAuthorityExports() {
        return Map.of(
                "portaroSearchItemParagraph", new TemplateDescriptorParagraph(templateEngine, translator, TEMPLATE_AUTHORITY_EXTERNAL_SEARCH, recordsToViewableRecordParagraphItemsConverter())
        );
    }

    @Bean
    public Map<String, RecordParagraph> authorityExports() {
        return Map.of(
                "portaroSearchItemParagraph", new StringTemplateParagraph(templateEngine, translator, settingLoader.getDepartmentedProvider(SettingKeys.SEARCH_AUTHORITY_TEMPLATE), recordsToViewableRecordParagraphItemsConverter()),
                "portaroRecordNewsParagraph", new StringTemplateParagraph(templateEngine, translator, settingLoader.getDepartmentedProvider(SettingKeys.SEARCH_DOCUMENT_MORE_TEMPLATE), recordsToViewableRecordParagraphItemsConverter())
        );
    }

    @Bean
    public ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordParagraphItemsConverter() {
        return new RecordToViewableRecordsConverter(
                new RecordExportsGenerator.NoopRecordExportsGenerator(),
                recordPropertiesGenerator,
                translator,
                serverUrlProvider,
                settingLoader.getDepartmentedProvider(SettingKeys.RECORD_DETAIL_FIELDS_FILTERS_BY_FONDS),
                settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_DETAIL_FIELDS_FILTER),
                settingLoader.getDepartmentedProvider(SettingKeys.SHOW_ALL_RECORD_FIELDS_ENABLED),
                ContextIgnoringContextualProvider.of(FieldTypeFilter.allAutonomousPassing()),
                ContextIgnoringContextualProvider.of(false),
                settingLoader.getOnRootProvider(SettingKeys.CENTRAL_INDEX_ENABLED),
                recordForbiddenEvaluator(),
                recordStatusResolver
        );
    }

    @Bean
    public ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordItemsWithExportsConverter() {
        return new RecordToViewableRecordsConverter(
                recordExportsGenerator(),
                recordPropertiesGenerator,
                translator,
                serverUrlProvider,
                settingLoader.getDepartmentedProvider(SettingKeys.RECORD_DETAIL_FIELDS_FILTERS_BY_FONDS),
                settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_DETAIL_FIELDS_FILTER),
                settingLoader.getDepartmentedProvider(SettingKeys.SHOW_ALL_RECORD_FIELDS_ENABLED),
                ContextIgnoringContextualProvider.of(FieldTypeFilter.allAutonomousPassing()),
                ContextIgnoringContextualProvider.of(false),
                settingLoader.getOnRootProvider(SettingKeys.CENTRAL_INDEX_ENABLED),
                recordForbiddenEvaluator(),
                recordStatusResolver
        );
    }

    @Bean
    public ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordItemsWithoutExportsConverter() {
        return new RecordToViewableRecordsConverter(
                new RecordExportsGenerator.NoopRecordExportsGenerator(),
                recordPropertiesGenerator,
                translator,
                serverUrlProvider,
                settingLoader.getDepartmentedProvider(SettingKeys.RECORD_DETAIL_FIELDS_FILTERS_BY_FONDS),
                settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_DETAIL_FIELDS_FILTER),
                settingLoader.getDepartmentedProvider(SettingKeys.SHOW_ALL_RECORD_FIELDS_ENABLED),
                ContextIgnoringContextualProvider.of(FieldTypeFilter.allAutonomousPassing()),
                ContextIgnoringContextualProvider.of(false),
                settingLoader.getOnRootProvider(SettingKeys.CENTRAL_INDEX_ENABLED),
                recordForbiddenEvaluator(),
                recordStatusResolver
        );
    }

    @Bean
    public RecordExportsGenerator recordExportsGenerator() {
        return new DefaultRecordExportsGenerator(
                documentExports(),
                externalDocumentExports(),
                authorityExports(),
                externalAuthorityExports(),
                exemplarSearchLoader,
                exemplarListSorter,
                departmentAccessor,
                serverUrlProvider.throwingWhenNull()
        );
    }

    @Bean
    public Function<String, Exporter<List<Record>>> documentsDocExporterByPathLoader() {
        return templatePath -> new HtmlFileDocumentsExporter("records.doc", BasicTemplateDescriptor.parse(templatePath), templateEngine, translator, recordsToViewableRecordParagraphItemsConverter());
    }

    @Bean
    public RisFileDocumentExporter documentRisExporter() {
        return new RisFileDocumentExporter("record.ris", Templates.TEMPLATE_DOCUMENT_RIS, templateEngine, documentContentTypeResolver, translator, recordsToViewableRecordParagraphItemsConverter());
    }

    @Bean
    public MultipleItemsExporterBySingleItemExporter<Record> documentsRisExporter() {
        return new MultipleItemsExporterBySingleItemExporter<>("records.ris", documentRisExporter(), "\n\n");
    }

    @Bean
    public XlsFileExporterByCsvExporter<List<Record>> documentsXlsExporter() {
        return new XlsFileExporterByCsvExporter<>("records.xls", recordsCsvExporter());
    }

    @Bean
    public CsvFileRecordsExporter recordsCsvExporter() {
        return new CsvFileRecordsExporter("records.csv", Templates.TEMPLATE_RECORDS_CSV, templateEngine, translator, recordsToViewableRecordParagraphItemsConverter());
    }

    @Bean
    public Converter<List<? extends Record>, List<StrictRecordMarcDto>> recordsToRecordMarcDtosConverter() {
        RecordsToMarcDtosConverter converter = new BasicRecordsToMarcDtosConverter(
                new InstantToStringConverter(CommonsConstants.UTC_ZONE_PROVIDER, MarcConstants.MARCXML_INSTANT_FORMATTER),
                DateRangeToStringConverter.ofInternalFormat(),
                DatetimeRangeToTimezonedStringConverter.ofInternalFormat(),
                new ConjunctionObjectFilter<>(
                        new LessThan1000RecordFieldFilter<>(),
                        By.<Field<?>>anyCode(FieldTypes.RECORD_ID_FIELD_CODE, FieldTypes.DOCUMENT_KINDED_ID_FIELD_CODE, FieldTypes.AUTHORITY_KINDED_ID_FIELD_CODE, FieldTypes.FOND_FIELD_CODE, FieldTypes.TOC_FIELD_CODE, FieldTypes.USER_FIELD_CODE).negate()
                ));
        return new ChainingConverter<>(converter, source -> source.stream().map(IdentifiedValue::value).toList());
    }

    @Bean
    public Converter<Record, String> recordToMarcXmlConverter() {
        Converter<StrictRecordMarcDto, String> recordMarcDtoToStringConverter = new JacksonRecordMarcDtoToStringConverter();
        return new ChainingConverter<>(new SingleItemConverterByListConverter<>(recordsToRecordMarcDtosConverter()), recordMarcDtoToStringConverter).throwWhenNull();
    }

    @Bean
    public CitationService citationService() {
        Map<String, CitationService> loadersByNames = Map.of(
                CitationServiceRoutingProxy.SERVICE_OFF, new CitationServiceNoOp(),
                CitationServiceRoutingProxy.SERVICE_TEMPLATE, new CitationServiceTemplateEngine(
                        Templates.TEMPLATE_DOCUMENT_CITATION,
                        templateEngine,
                        translator,
                        recordsToViewableRecordParagraphItemsConverter()
                ),
                CitationServiceRoutingProxy.SERVICE_CITACE_COM, new CitationServiceCitaceCom(
                        rest,
                        CitationServiceCitaceCom.ALL_AVAILABLE_STYLES,
                        settingLoader.getOnRootProvider(SettingKeys.CITATION_DOCTYPES_BY_FONDS),
                        recordToMarcXmlConverter()
                ),
                CitationServiceRoutingProxy.SERVICE_CITACE_COM_SINGLE, new CitationServiceCitaceCom(
                        rest,
                        CitationServiceCitaceCom.SINGLE_MODE_AVAILABLE_STYLES,
                        settingLoader.getOnRootProvider(SettingKeys.CITATION_DOCTYPES_BY_FONDS),
                        recordToMarcXmlConverter()
                )
        );
        return new CitationServiceRoutingProxy(settingLoader.getOnRootProvider(SettingKeys.CITATION_SERVICE), loadersByNames);
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        viewableItemsConverter.register(Record.class, ViewableItemsConverter.ViewMode.STANDARD, recordsToViewableRecordItemsWithExportsConverter());
        viewableItemsConverter.registerStandardNoop(RecordOperation.class);

        permissionRegistry.add(SecurityActions.VISITED_DOCUMENTS_USE, (auth, ctx, unused) -> PermissionResult.allow());

        if (featureManager.isEnabled(FeatureManager.FEATURE_EXPORTS)) {
            Exporter<Record> documentCsvExporter = new SingleItemExporterByMultipleItemsExporter<>(new CsvFileRecordsExporter("record.csv", Templates.TEMPLATE_RECORDS_CSV, templateEngine, translator, recordsToViewableRecordParagraphItemsConverter()));

            Exporter<Record> documentXlsExporter = new SingleItemExporterByMultipleItemsExporter<>(new XlsFileExporterByCsvExporter<>("record.xls", recordsCsvExporter()));

            PrefixRemovingStringToEConverter<Exporter<?>> documentDocExporterByPathLoader = new PrefixRemovingStringToEConverter<>(CatalogConstants.Export.Exporters.RECORD_DOC, templatePath -> new SingleItemExporterByMultipleItemsExporter<>(documentsDocExporterByPathLoader().apply(templatePath)));

            Function<String, Exporter<?>> searchedRecordsCsvExporterByPathLoader = templatePath -> SearchedItemsExporter.of(new CsvFileRecordsExporter("records.csv", BasicTemplateDescriptor.parse(templatePath), templateEngine, translator, recordsToViewableRecordParagraphItemsConverter()), SEARCHED_RECORDS_LIMIT_ITEMS);

            Exporter<Search<MapBackedParams, Record, RangePaging>> searchedRecordsXlsExporter = SearchedItemsExporter.of(documentsXlsExporter(), SEARCHED_RECORDS_LIMIT_ITEMS);

            Function<String, Exporter<Search<MapBackedParams, Record, RangePaging>>> searchedRecordsDocExporterByPathLoader = templatePath -> SearchedItemsExporter.of(documentsDocExporterByPathLoader().apply(templatePath), SEARCHED_RECORDS_LIMIT_ITEMS);

            Exporter<Search<MapBackedParams, Record, RangePaging>> searchedItemsRisExporter = SearchedItemsExporter.of(documentsRisExporter(), SEARCHED_RECORDS_LIMIT_ITEMS);

            //RECORD
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.RECORD_CSV, documentCsvExporter);
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.RECORD_XLS, documentXlsExporter);
            exporterResolver.addFactory(CatalogConstants.Export.Exporters.RECORD_DOC, documentDocExporterByPathLoader);
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.RECORD_RIS, documentRisExporter());

            //RECORDS
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.RECORDS_CSV, recordsCsvExporter());
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.RECORDS_XLS, documentsXlsExporter());
            exporterResolver.addFactory(CatalogConstants.Export.Exporters.RECORDS_DOC, new PrefixRemovingStringToEConverter<>(CatalogConstants.Export.Exporters.RECORDS_DOC, documentsDocExporterByPathLoader()));
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.RECORDS_RIS, documentsRisExporter());

            //SEARCHED RECORDS
            exporterResolver.addFactory(CatalogConstants.Export.Exporters.SEARCHED_RECORDS_CSV, new PrefixRemovingStringToEConverter<>(CatalogConstants.Export.Exporters.SEARCHED_RECORDS_CSV, searchedRecordsCsvExporterByPathLoader));
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.SEARCHED_RECORDS_XLS, searchedRecordsXlsExporter);
            exporterResolver.addFactory(CatalogConstants.Export.Exporters.SEARCHED_RECORDS_DOC, new PrefixRemovingStringToEConverter<>(CatalogConstants.Export.Exporters.SEARCHED_RECORDS_DOC, searchedRecordsDocExporterByPathLoader));
            exporterResolver.addStatic(CatalogConstants.Export.Exporters.SEARCHED_RECORDS_RIS, searchedItemsRisExporter);
        }

        templateLoadingExportDescriptorLoader
                .registerTemplated(List.of(CatalogConstants.Export.Tag.RECORD, ExportConstants.Tag.CSV, ExportConstants.Tag.DOWNLOAD), CatalogConstants.Export.Exporters.RECORD_CSV, Templates.TEMPLATE_RECORDS_CSV.getType(), ofMessageCoded("export.CsvButton"))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.RECORD, ExportConstants.Tag.XLS, ExportConstants.Tag.DOWNLOAD), CatalogConstants.Export.Exporters.RECORD_XLS, Templates.TEMPLATE_RECORDS_CSV.getType(), ofMessageCoded("export.XlsButton"))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.RECORD, ExportConstants.Tag.DOC, ExportConstants.Tag.DOWNLOAD), CatalogConstants.Export.Exporters.RECORD_DOC, Templates.TEMPLATE_DOCUMENTS_DOC.getType(), ofMessageCoded("export.DocButton"))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.RECORD, ExportConstants.Tag.RIS, ExportConstants.Tag.DOWNLOAD), CatalogConstants.Export.Exporters.RECORD_RIS, Templates.TEMPLATE_DOCUMENT_RIS.getType(), ofMessageCoded("export.RisButton"))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.SEARCHED_RECORDS, ExportConstants.Tag.CSV, ExportConstants.Tag.DOWNLOAD), CatalogConstants.Export.Exporters.SEARCHED_RECORDS_CSV, Templates.TEMPLATE_RECORDS_CSV.getType(), ofMessageCoded("export.CsvButton"), ofArgumentedMessageCoded("export.LimitXRecords", SEARCHED_RECORDS_LIMIT_ITEMS))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.SEARCHED_RECORDS, ExportConstants.Tag.XLS, ExportConstants.Tag.DOWNLOAD), CatalogConstants.Export.Exporters.SEARCHED_RECORDS_XLS, Templates.TEMPLATE_RECORDS_CSV.getType(), ofMessageCoded("export.XlsButton"), ofArgumentedMessageCoded("export.LimitXRecords", SEARCHED_RECORDS_LIMIT_ITEMS))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.SEARCHED_RECORDS, ExportConstants.Tag.DOC, ExportConstants.Tag.DOWNLOAD), CatalogConstants.Export.Exporters.SEARCHED_RECORDS_DOC, Templates.TEMPLATE_DOCUMENTS_DOC.getType(), ofMessageCoded("export.DocButton"), ofArgumentedMessageCoded("export.LimitXRecords", SEARCHED_RECORDS_LIMIT_ITEMS))
                .registerTemplated(List.of(CatalogConstants.Export.Tag.SEARCHED_RECORDS, ExportConstants.Tag.RIS, ExportConstants.Tag.DOWNLOAD), CatalogConstants.Export.Exporters.SEARCHED_RECORDS_RIS, Templates.TEMPLATE_DOCUMENT_RIS.getType(), ofMessageCoded("export.RisButton"), ofArgumentedMessageCoded("export.LimitXRecords", SEARCHED_RECORDS_LIMIT_ITEMS));
    }

}
