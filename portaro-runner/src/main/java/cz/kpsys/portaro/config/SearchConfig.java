package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualFunction;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.commons.cache.CacheBackedRepository;
import cz.kpsys.portaro.commons.cache.CacheCleaningSaver;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.cache.GuavaTimedDynamicCache;
import cz.kpsys.portaro.commons.contextual.CompositeAllValuesContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.*;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.core.editor.DispatchingSearchValueEditorLoader;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.datatype.DatatypedAcceptableValuesRegistry;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.exemplar.BasicExemplar;
import cz.kpsys.portaro.exemplar.ExemplarConstants;
import cz.kpsys.portaro.exemplar.discard.Discardion;
import cz.kpsys.portaro.file.FileConstants;
import cz.kpsys.portaro.file.FileSearchParams;
import cz.kpsys.portaro.inventory.InventoryConstants;
import cz.kpsys.portaro.inventory.match.Match;
import cz.kpsys.portaro.loan.LoanConstants;
import cz.kpsys.portaro.loan.reminder.LoanReminderConstants;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.messages.constants.MessageConstants;
import cz.kpsys.portaro.payment.PaymentConstants;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordHeaderToLuceneValueConverter;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.datasource.Datasource;
import cz.kpsys.portaro.record.document.SpringDbKat14YearExtremesLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.isbn.IsbnChecker;
import cz.kpsys.portaro.record.isbn.IsbnDisjunctionAddingTermConverter;
import cz.kpsys.portaro.record.search.KeywordsParserAppserver;
import cz.kpsys.portaro.record.search.RawableSearchServiceCacheResolver;
import cz.kpsys.portaro.record.search.SubkindFieldTermToFondListConverter;
import cz.kpsys.portaro.record.search.restriction.*;
import cz.kpsys.portaro.record.sec.CurrentAuthFondsLoader;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.facet.*;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.field.*;
import cz.kpsys.portaro.search.history.SearchHistoryImpl;
import cz.kpsys.portaro.search.keywords.SearchedKeywordsLoader;
import cz.kpsys.portaro.search.keywords.SearchedKeywordsSaver;
import cz.kpsys.portaro.search.keywords.SpringDbSearchedKeywordsLoader;
import cz.kpsys.portaro.search.keywords.SpringDbSearchedKeywordsSaver;
import cz.kpsys.portaro.search.lucene.FacetKeyIdentified;
import cz.kpsys.portaro.search.lucene.KeywordsParser;
import cz.kpsys.portaro.search.lucene.QToLuceneQueryConverterByPattern;
import cz.kpsys.portaro.search.params.DefaultParamsModifier;
import cz.kpsys.portaro.search.params.ParamsModifier;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.convert.RestrictionDeserializerWithSearchFields;
import cz.kpsys.portaro.search.restriction.convert.RestrictionSerializer;
import cz.kpsys.portaro.search.restriction.convert.SingleStringMatcherValueTermMatcher;
import cz.kpsys.portaro.search.restriction.deserialize.json.RestrictionDeserializerPostModifyingDecorator;
import cz.kpsys.portaro.search.restriction.matcher.GtEq;
import cz.kpsys.portaro.search.restriction.matcher.In;
import cz.kpsys.portaro.search.restriction.matcher.Lt;
import cz.kpsys.portaro.search.restriction.matcher.LtEq;
import cz.kpsys.portaro.search.restriction.serialize.lucene.*;
import cz.kpsys.portaro.search.view.*;
import cz.kpsys.portaro.setting.FondedValues;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.stats.StatsConstants;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.view.ViewableItemsConverter;
import cz.kpsys.portaro.view.web.rest.FacetTypeCreationRequest;
import cz.kpsys.portaro.view.web.rest.FacetTypeEditationRequest;
import cz.kpsys.portaro.view.web.rest.FacetTypeEditationRequestToFacetTypeConverter;
import cz.kpsys.portaro.web.ResponseHelper;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Range;
import org.jspecify.annotations.Nullable;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cache.CacheManager;
import org.springframework.cache.interceptor.CacheResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.format.support.FormattingConversionService;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.web.context.WebApplicationContext;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;

import static cz.kpsys.portaro.app.CatalogConstants.Search.Lucene.*;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SearchConfig {

    @NonNull QueryFactory queryFactory;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull NamedParameterJdbcOperations notCriticalJdbcTemplate;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull ExecutorService executorService;
    @NonNull DatatypableStringConverter datatypableStringConverter;
    @NonNull CacheService cacheService;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull SimpleModule objectMapperModule;
    @NonNull SettingLoader settingLoader;
    @NonNull ByIdLoadable<@Nullable AllValuesProvider<? extends LabeledIdentified<?>>, ScalarDatatype> acceptableValuesProviderLoader;
    @NonNull CurrentAuthFondsLoader currentAuthShowableFondsLoader;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull ContextualProvider<Department, List<Location>> readableLocationsContextualProvider;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull AllValuesProvider<Fond> enabledAuthorityFondsProvider;
    @NonNull AllValuesProvider<Fond> enabledDocumentFondsProvider;
    @NonNull Translator<Department> translator;
    @NonNull DatatypedAcceptableValuesRegistry allowedDatatypeToAllValuesProviderMap;
    @NonNull DatatypedAcceptableValuesRegistry allDatatypeToAllValuesProviderMap;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull FormattingConversionService conversionService;
    @NonNull ObjectMapper objectMapper;
    @NonNull CacheManager cacheManager;
    @NonNull Function<String, List<Fond>> kindToEnabledFondsExpander;
    @NonNull Function<String, List<Fond>> subkindToEnabledFondsExpander;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull EntityManager entityManager;
    @NonNull Codebook<Fond, Integer> fondLoader;

    @Bean
    public SearchFactoryResolver searchFactoryResolver() {
        return new SearchFactoryResolver();
    }

    @Bean
    public SearchedKeywordsLoader searchedKeywordsLoader() {
        return new SpringDbSearchedKeywordsLoader(
                notCriticalJdbcTemplate,
                queryFactory,
                StaticProvider.of(3)
        );
    }

    @Bean
    public SearchedKeywordsSaver searchedKeywordsSaver() {
        return new SpringDbSearchedKeywordsSaver(notCriticalJdbcTemplate, queryFactory, executorService);
    }

    @Bean
    public KeywordsParser keywordsParser() {
        return new KeywordsParserAppserver(
                mappingAppserver,
                ExemplarConstants.Datatype.SIGNATURE,
                ExemplarConstants.Datatype.ACCESS_NUMBER
        );
    }

    @Bean
    public CompositeAllValuesProvider<SearchField> searchFieldsProvider() {
        return CompositeAllValuesProvider.of(
                customSearchFieldLoader(),
                ConvertingAllValuesProvider.byItemConverter(facetTypeLoader(), FacetType::toSearchField),
                StaticSearchFields.CODEBOOK
        );
    }

    @Bean
    public CompositeByIdLoader<SearchField, String> searchFieldLoader() {
        return CompositeByIdLoader.of(
                customSearchFieldLoader(),
                AllValuesProvidedCodebook.ofIdentified(ConvertingAllValuesProvider.byItemConverter(facetTypeLoader(), FacetType::toSearchField)),
                StaticSearchFields.CODEBOOK
        );
    }

    @Bean
    public Codebook<CustomSearchField, String> customSearchFieldLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedByJpa(CustomSearchFieldEntity.class)
                .convertedEachBy(new EntityToCustomSearchFieldConverter())
                .staticCached(CustomSearchField.class.getSimpleName())
                .build();
    }

    @Bean
    public AuthenticatedContextualProvider<Department, List<ViewableSearchField>> viewableSearchFieldsProvider() {
        return new ViewableSearchFieldsContextualProvider<>(
                settingLoader.getDepartmentedProvider(SettingKeys.FORM_SEARCH_FIELDS),
                searchFieldLoader(),
                departmentedSearchFieldToViewableSearchFieldConverter()
        );
    }

    @Bean
    public AuthenticatedContextualFunction<SearchField, Department, ViewableSearchField> departmentedSearchFieldToViewableSearchFieldConverter() {
        return new DepartmentedSearchFieldToViewableSearchFieldConverter<>(searchValueEditorLoader(), datatypeBySearchFieldLoader());
    }

    @Bean
    public DispatchingLuceneQueryFieldLoader luceneQueryFieldLoader() {
        Map<String, List<LuceneQueryField>> staticFields = new HashMap<>();
        staticFields.put(StaticSearchFields.WHATEVER.getId(), List.of(
                LuceneQueryField.create(FIELD_WHATEVER, CoreConstants.Datatype.TEXT)));
        staticFields.put(StaticSearchFields.NAME.getId(), List.of(
                LuceneQueryField.create(FIELD_NAME, CoreConstants.Datatype.TEXT)));
        staticFields.put(RecordConstants.SearchFields.FOND.getId(), List.of(
                LuceneQueryField.create(FIELD_FOND, RecordConstants.Datatype.FOND)));
        staticFields.put(RecordConstants.SearchFields.RECORD_RELATED_RECORD.getId(), List.of(
                LuceneQueryField.create(FIELD_RECORD_RELATED_RECORD, RecordConstants.Datatype.RECORD_HEADER)));
        staticFields.put(StaticSearchFields.DEPARTMENT.getId(), List.of(
                LuceneQueryField.create(FIELD_DEP, CoreConstants.Datatype.DEPARTMENT)));
        staticFields.put(StaticSearchFields.UNRESTRICTED_DEPARTMENT.getId(), List.of(
                LuceneQueryField.create(FIELD_UNRESTRICTED_DEP, CoreConstants.Datatype.DEPARTMENT)));
        staticFields.put(StaticSearchFields.DRAFT_DEPARTMENT.getId(), List.of(
                LuceneQueryField.create(FIELD_DRAFT_DEP, CoreConstants.Datatype.DEPARTMENT)));
        staticFields.put(ExemplarConstants.SearchFields.LOCATION.getId(), List.of(
                LuceneQueryField.create(FIELD_LOCATION, ExemplarConstants.Datatype.LOCATION)));
        staticFields.put(RecordConstants.SearchFields.DOCUMENT_FOND.getId(), List.of(
                LuceneQueryField.create(FIELD_FOND, RecordConstants.Datatype.DOCUMENT_FOND)));
        staticFields.put(RecordConstants.SearchFields.RECORD_STATUS.getId(), List.of(
                LuceneQueryField.create(FIELD_RECORD_STATUS, RecordConstants.Datatype.RECORD_STATUS)));
        staticFields.put(RecordConstants.SearchFields.DOCUMENT_ISBN.getId(), List.of(
                LuceneQueryField.create(FIELD_ISBN, RecordConstants.Datatype.ISBN)));
        staticFields.put(RecordConstants.SearchFields.DOCUMENT_ISSN.getId(), List.of(
                LuceneQueryField.create(FIELD_ISSN, RecordConstants.Datatype.ISSN)));
        staticFields.put(RecordConstants.SearchFields.DOCUMENT_ISBN_OR_ISSN.getId(), List.of(
                LuceneQueryField.create(FIELD_ISBN, RecordConstants.Datatype.ISBN),
                LuceneQueryField.create(FIELD_ISSN, RecordConstants.Datatype.ISSN)
        ));
        staticFields.put(RecordConstants.SearchFields.DOCUMENT_NAME.getId(), List.of(
                LuceneQueryField.create(FIELD_DOCUMENT_NAME, CoreConstants.Datatype.TEXT)
        ));
        staticFields.put(ExemplarConstants.SearchFields.DOCUMENT_SIGNATURE.getId(), List.of(
                LuceneQueryField.create(FIELD_DOCUMENT_SIGNATURE, ExemplarConstants.Datatype.SIGNATURE)
        ));
        staticFields.put(ExemplarConstants.SearchFields.DOCUMENT_ACCESS_NUMBER.getId(), List.of(
                LuceneQueryField.create(FIELD_DOCUMENT_ACCESS_NUMBER, ExemplarConstants.Datatype.ACCESS_NUMBER)
        ));
        staticFields.put(RecordConstants.SearchFields.RECORD_SOURCE_RECORD.getId(), List.of(
                LuceneQueryField.create(FIELD_RECORD_SOURCE_DOCUMENT_NAME, CoreConstants.Datatype.TEXT)
        ));
        staticFields.put(RecordConstants.SearchFields.AUTHOR.getId(), List.of(
                LuceneQueryField.create(FIELD_DOCUMENT_AUTHOR, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_DOCUMENT_AUTHOR_CORPORATION, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_DOCUMENT_AUTHOR_ACTION, CoreConstants.Datatype.TEXT)));
        staticFields.put(RecordConstants.SearchFields.DOCUMENT_YEAR.getId(), List.of(
                LuceneQueryField.create(FIELD_DOCUMENT_YEAR, CoreConstants.Datatype.NUMBER)
        ));
        staticFields.put(RecordConstants.SearchFields.PUBLISHER.getId(), List.of(
                LuceneQueryField.create(FIELD_DOCUMENT_PUBLISHER_OLD_FORM, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_DOCUMENT_PUBLISHER, CoreConstants.Datatype.TEXT)
        ));
        staticFields.put(StaticSearchFields.NOTE.getId(), List.of(
                LuceneQueryField.create(FIELD_DOCUMENT_NOTE, CoreConstants.Datatype.TEXT) // Warn! field 500 is note only in documents - in authorities field 500 is "see also"
        ));
        staticFields.put(RecordConstants.SearchFields.AUTHORITY_NAME.getId(), List.of(
                LuceneQueryField.create(FIELD_AUTHORITY_PERSON_NAME, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_AUTHORITY_CORPORATION_NAME, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_AUTHORITY_ACTION_NAME, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_AUTHORITY_KEYWORD_NAME, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_AUTHORITY_GEOGRAPHICS_NAME, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_AUTHORITY_UNIFIED_NAME, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_AUTHORITY_CHRONOLOGIC_KEYWORD_NAME, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_RECORD_SOURCE_DOCUMENT_NAME, CoreConstants.Datatype.TEXT),
                LuceneQueryField.create(FIELD_AUTHORITY_CONSPECT, CoreConstants.Datatype.TEXT)
        ));
        staticFields.put(RecordConstants.SearchFields.CNA.getId(), List.of(LuceneQueryField.create(FIELD_AUTHORITY_CNA, CoreConstants.Datatype.TEXT)));
        staticFields.put(RecordConstants.SearchFields.AUTHORITY_FOND.getId(), List.of(
                LuceneQueryField.create(FIELD_FOND, RecordConstants.Datatype.AUTHORITY_FOND)
        ));
        staticFields.put(RecordConstants.SearchFields.RECORD_ID.getId(), List.of(
                LuceneQueryField.create(FIELD_RECORD_ID, RecordConstants.Datatype.RECORD)
        ));
        staticFields.put(RecordConstants.SearchFields.RECORD_CREATION_DATE.getId(), List.of(
                LuceneQueryField.create(FIELD_RECORD_CREATION_DATE, CoreConstants.Datatype.DATETIME)
        ));
        staticFields.put(RecordConstants.SearchFields.RECORD_FILE_CATEGORY.getId(), List.of(
                LuceneQueryField.create(FIELD_RECORD_FILE_CATEGORY, FileConstants.Datatype.FILE_CATEGORY)
        ));
        staticFields.put(LoanConstants.SearchFields.RECORD_LOAN_SOURCE.getId(), List.of(
                LuceneQueryField.create(FIELD_LOAN_SOURCE, CoreConstants.Datatype.TEXT)
        ));
        staticFields.put(RecordConstants.SearchFields.TABLE_OF_CONTENT.getId(), List.of(
                LuceneQueryField.create(FIELD_TABLE_OF_CONTENT, CoreConstants.Datatype.TEXT)
        ));
        return new DispatchingLuceneQueryFieldLoader(staticFields, customSearchFieldLoader());
    }

    @Bean
    public DispatchingLuceneSortFieldLoader luceneSortFieldLoader() {
        return new DispatchingLuceneSortFieldLoader();
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_SESSION, proxyMode = ScopedProxyMode.INTERFACES)
    public AllProvidingRepository<Search<MapBackedParams, ?, Paging>, UUID> searchHistory() {
        return new SearchHistoryImpl();
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_SESSION, proxyMode = ScopedProxyMode.INTERFACES)
    public SameTypeSettableProvider<@NonNull Integer> documentDefaultPageSizeProvider() {
        return new FallbackingSettableProvider<>(settingLoader.getOnRootProvider(SettingKeys.DOCUMENT_SEARCH_PAGE_SIZE));
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_SESSION, proxyMode = ScopedProxyMode.INTERFACES)
    public SameTypeSettableProvider<Integer> authorityPageSizeProvider() {
        return new FallbackingSettableProvider<>(settingLoader.getOnRootProvider(SettingKeys.AUTHORITY_SEARCH_PAGE_SIZE));
    }

    @Bean
    public Codebook<FacetType, Integer> facetTypeLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(new SpringDbFacetTypeLoader(jdbcTemplate, queryFactory, facetTypeSortingLoader()))
                .staticCached(FacetType.class.getSimpleName())
                .build();
    }

    @Bean
    public Saver<FacetType, FacetType> facetTypeSaver() {
        return CacheCleaningSaver.fromGenericCleaner(
                new PostConvertingSaver<>(
                        new PreConvertingSaver<>(
                                new FacetTypeToEntityConverter(),
                                new FlushingJpaSaver<>(new SimpleJpaRepository<>(FacetTypeEntity.class, entityManager))
                        ),
                        new FacetTypeFromEntityConverter(facetTypeSortingLoader(), facetDefinitionTypeLoader(), facetScopeLoader())
                ), cacheService.createCleanerFor(FacetType.class.getSimpleName())
        );
    }

    @Bean
    public FacetTypeCreator facetTypeCreator() {
        return new FacetTypeCreator(
                facetTypeSaver(),
                defaultTransactionTemplateFactory.get(),
                facetTypeLoader()
        );
    }

    @Bean
    public FacetTypeUpdater facetTypeUpdater() {
        return new FacetTypeUpdater(
                facetTypeSaver(),
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<FacetTypeCreationRequest> facetTypeRequestDefaulter() {
        return new FacetTypeCreationRequest.FacetTypeRequestDefaulter(facetTypeLoader());
    }

    @Bean
    public Converter<FacetTypeEditationRequest, FacetType> facetTypeRequestToFacetTypeConverter() {
        return new FacetTypeEditationRequestToFacetTypeConverter();
    }

    @Bean
    public CompositeSearchFormFactory searchFormFactory() {
        return new CompositeSearchFormFactory();
    }

    @Bean
    public ViewableItemsConverter viewableItemsConverter() {
        return new ViewableItemsConverter();
    }

    @Bean
    public SearchTextResolver searchTitleResolver() {
        return new SearchTitleResolver(translator);
    }

    @Bean
    public SearchTextResolver searchSubtitleResolver() {
        return new EmptySearchTextResolver();
    }

    @Bean
    public Repository<Search<? extends MapBackedParams, ?, Paging>, UUID> searchRepository() {
        return CacheBackedRepository.ofIdentified(GuavaTimedDynamicCache.ofIdentified(Duration.ofMinutes(10), false));
    }

    @Bean
    public SearchValueEditorLoader<Department> searchValueEditorLoader() {
        return new DispatchingSearchValueEditorLoader(
                acceptableValuesProviderLoader,
                readableLocationsContextualProvider,
                currentAuthShowableFondsLoader,
                departmentAccessor
        );
    }

    @Bean
    public DatatypeBySearchFieldLoader datatypeBySearchFieldLoader() {
        return new DatatypeBySearchFieldLoaderByLuceneQueryFieldLoader();
    }

    @Bean
    public CacheResolver rawableSearchServiceCacheResolver() {
        return new RawableSearchServiceCacheResolver(cacheManager);
    }

    @Bean
    public ParamsModifier<DefaultGettableAndSettableSearchParams> fondRelatedParamsExpander() {
        return new DefaultParamsModifier<>()
                .withExpandingParam(CoreSearchParams.KIND, RecordConstants.SearchParams.FOND, kindToEnabledFondsExpander)
                .withExpandingParam(CoreSearchParams.SUBKIND, RecordConstants.SearchParams.FOND, subkindToEnabledFondsExpander)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);
    }

    @Bean
    public ContextualProvider<Department, Range<Integer>> documentYearExtremeRangeProvider() {
        return settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_MIN_YEAR)
                .andThenFastReturningNull(minYear -> Range.of(minYear, LocalDate.now().getYear()))
                .fallbacked(new SpringDbKat14YearExtremesLoader(jdbcTemplate, queryFactory).cached());
    }

    @Bean
    public Codebook<SortingItem, String> facetTypeSortingLoader() {
        return new StaticCodebook<>(
                FacetTypeSorting.SORTING_FREQ_DESC,
                FacetTypeSorting.SORTING_ALPH_ASC,
                FacetTypeSorting.SORTING_ALPH_DESC,
                FacetTypeSorting.SORTING_CHRONO_ASC,
                FacetTypeSorting.SORTING_CHRONO_DESC,
                FacetTypeSorting.SORTING_NUM_ASC,
                FacetTypeSorting.SORTING_NUM_DESC
        );
    }

    @Bean
    public Codebook<FacetScope, Integer> facetScopeLoader() {
        return FacetScope.CODEBOOK;
    }

    @Bean
    public Codebook<FacetDefinitionType, Integer> facetDefinitionTypeLoader() {
        return FacetDefinitionType.CODEBOOK;
    }

    @Bean
    public CompositeByIdLoader<SortingItem, String> searchSortingLoader() {
        CompositeByIdLoader<SortingItem, String> loader = CompositeByIdLoader.of(
                recordSearchSortingLoader(),
                authoritySearchSortingLoader(),
                exemplarSearchSortingLoader(),
                facetTypeSortingLoader(),
                matchSearchSortingLoader()
        );
        loader.addFallback(new AllByIdsLoadableByIdLoaderAdapter<>(SortingItem::ofSimpleId)); // fallback, mozna by tu uz nemusel byt, ale muselo by se to poradne otestovat
        return loader;
    }

    @Bean
    public Provider<SortingItem> defaultRecordSearchSortingProvider() {
        return DefaultProvider.byFirst(settingLoader.getOnRootProvider(SettingKeys.SEARCH_SORTINGS));
    }

    @Bean
    public Codebook<SortingItem, String> recordSearchSortingLoader() {
        return AllValuesProvidedCodebook.ofIdentified(() -> settingLoader.getOnRootProvider(SettingKeys.SEARCH_SORTINGS).get());
    }

    @Bean
    public Provider<SortingItem> defaultAuthoritySearchSortingProvider() {
        return DefaultProvider.byFirst(authoritySearchSortingLoader());
    }

    @Bean
    public Codebook<SortingItem, String> authoritySearchSortingLoader() {
        return AllValuesProvidedCodebook.ofIdentified(() -> settingLoader.getOnRootProvider(SettingKeys.AUTHORITY_SEARCH_SORTINGS).get());
    }

    @Bean
    public ContextualProvider<Department, @NonNull FondedValues<@NonNull List<SortingItem>>> departmentedFondedRecordSearchSortingsLoader() {
        return settingLoader.getDepartmentedFondedValuesProvider(SettingKeys.SEARCH_SORTINGS);
    }

    @Bean
    public ByIdLoadable<Datasource, String> datasourceLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(allDatasetsProvider())
                .build();
    }

    @Bean
    public CompositeAddableAllValuesProvider<Datasource> allDatasetsProvider() {
        return CompositeAddableAllValuesProvider.ofEmpty();
    }

    @Bean
    public CompositeAllValuesContextualProvider<Department, Datasource> allowedDatasetsProvider() {
        return new CompositeAllValuesContextualProvider<>();
    }

    @Bean
    public Provider<SortingItem> defaultExemplarSearchSortingProvider() {
        return DefaultProvider.byId(exemplarSearchSortingLoader(), BasicExemplar.Fields.accessNumber);
    }

    @Bean
    public Codebook<SortingItem, String> exemplarSearchSortingLoader() {
        return new StaticCodebook<>(
                SortingItem.ofSimpleAsc(BasicExemplar.Fields.accessNumber),
                SortingItem.ofSimpleDesc(BasicExemplar.Fields.accessNumber),
                SortingItem.ofSimpleDesc(BasicExemplar.Fields.signature),
                SortingItem.ofSimpleAsc(Discardion.Fields.discardNumber)
        );
    }

    @Bean
    public Provider<SortingItem> defaultMatchSearchSortingProvider() {
        return DefaultProvider.byId(matchSearchSortingLoader(), Match.Fields.accessNumber);
    }

    @Bean
    public Codebook<SortingItem, String> matchSearchSortingLoader() {
        return new StaticCodebook<>(
                SortingItem.ofSimpleAsc(Match.Fields.accessNumber),
                SortingItem.ofSimpleDesc(Match.Fields.accessNumber),
                SortingItem.ofSimpleAsc(Match.Fields.signature),
                SortingItem.ofSimpleDesc(Match.Fields.signature),
                SortingItem.ofSimpleAsc(Match.Fields.barCode),
                SortingItem.ofSimpleDesc(Match.Fields.barCode)
        );
    }

    @Bean
    public MapToMapSearchParamsConverter mapToMapSearchParamsConverter() {
        Set<String> notMappableButAllowedQueryParams = Set.of(
                SearchViewConstants.PAGE_NUMBER_PARAM,
                SearchViewConstants.PAGE_SIZE_PARAM,
                SearchViewConstants.EXPORTS_PARAM,
                SearchViewConstants.FOCUSED_FIELD_TYPE_ID_PARAM,
                ResponseHelper.FORMAT_PARAMETER_NAME,
                CatalogWebConstants.LOCALE_URL_PARAMETER_NAME,
                SearchViewConstants.CACHE,
                SearchViewConstants.AFTER_POSITION,
                SearchViewConstants.BEFORE_POSITION,
                SearchViewConstants.SEARCH_DATE_PARAM,
                SearchViewConstants.SORTING,
                CatalogWebConstants.ACTIVE_TAB_NAME
        );

        return new MapToMapSearchParamsConverter(mappableSearchParamsRegistry(), notMappableButAllowedQueryParams);
    }

    @Bean
    public MappableSearchParamsRegistry mappableSearchParamsRegistry() {
        MappableSearchParamsRegistry mappableSearchParamsRegistry = new MappableSearchParamsRegistry(conversionService);

        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.KIND);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.SUBKIND);

        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.DEPARTMENT);

        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.NAME);

        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.Q);

        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.INITIATOR);

        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.DATASOURCE);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.DATASOURCE_GROUP);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.CENTRAL_INDEX_ENABLED);

        mappableSearchParamsRegistry.registerDatatyped(InventoryConstants.SearchParams.INVENTORY);

        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.TYPE);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.TITLE);

        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.FROM_DATE);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.INCLUDE_DRAFT);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.INCLUDE_DELETED);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.INCLUDE_ACTIVE);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.TO_DATE);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.QT);

        mappableSearchParamsRegistry.registerDatatyped(FileSearchParams.DIRECTORY);
        mappableSearchParamsRegistry.registerDatatyped(FileSearchParams.ROOT_DIRECTORY);
        mappableSearchParamsRegistry.registerDatatyped(FileSearchParams.FILE_CATEGORY);
        mappableSearchParamsRegistry.registerDatatyped(FileSearchParams.FILE_PROCESSING_STATE);
        mappableSearchParamsRegistry.registerDatatyped(FileSearchParams.FILENAME_EXTENSION);
        mappableSearchParamsRegistry.registerDatatyped(FileSearchParams.FILENAME);
        mappableSearchParamsRegistry.registerDatatyped(FileSearchParams.FORBIDDEN_FILE);
        mappableSearchParamsRegistry.registerDatatyped(FileSearchParams.FORBIDDEN_FILE_CATEGORY);
        mappableSearchParamsRegistry.registerDatatyped(FileSearchParams.MINIMAL_FILE_SIZE_KB);

        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.OPERATED_SUBKIND);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.OPERATED_ROOT_FOND);
        if (false) {
            // only here to explicitly say that we do not use operatedFond in frontend
            mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.OPERATED_FOND);
        }
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.OPERATION_TYPE);

        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.INCLUSIVE_FROM_CREATION_DATE);
        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.EXCLUSIVE_TO_CREATION_DATE);
        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.CREATION_DATE);
        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.INCLUSIVE_FROM_PAY_DATE);
        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.EXCLUSIVE_TO_PAY_DATE);
        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.PAY_DATE);
        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.PAYMENT_STATE);
        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.PROVIDER);
        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.TRANSACTION_ID);

        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.OWNER);
        mappableSearchParamsRegistry.registerDatatyped(PaymentConstants.SearchParams.AMOUNT_TYPE);

        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.CREATOR);

        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.RAW_QUERY);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.FINAL_RAW_QUERY);

        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.FACET_RESTRICTION);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.FACETS_ENABLED);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.RIGHT_HAND_EXTENSION);
        mappableSearchParamsRegistry.registerDatatyped(CoreSearchParams.FIELD);

        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.PREFIX);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.DIACRITICAL_PREFIX);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.FORBIDDEN_RECORD);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.RECORD);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.EXEMPLAR_STATUS);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.FORBIDDEN_EXEMPLAR);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.ACQUISITION_WAY);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.ROOT_FOND);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.FOND);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.FOND_TYPE);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.CONSTRAINTS_RECORD_FIELD_TYPE);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.RECORD_RELATED_RECORD);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.INCLUDE_EXCLUDED);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.FIELD_TYPE_ID);

        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.INCLUDE_FAKE);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.LOCATION);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.EXEMPLAR_TYPE);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.INCREASE_YEAR);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.BAR_CODE);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.ACCESS_NUMBER);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.ACCESS_NUMBER_START);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.ACCESS_NUMBER_END);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.SIGNATURE);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.SIGNATURE_START);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.SIGNATURE_END);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.DISCARD_NUMBER_START);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.DISCARD_NUMBER_END);
        mappableSearchParamsRegistry.registerDatatyped(ExemplarConstants.SearchParams.BUNDLED_VOLUME_YEAR);

        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.USERNAME);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.USERNAME_IGNORE_CASE);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.EMAIL);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.LAST_NAME);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.CARD_NUMBER);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.BAR_CODE);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.CASE_INSENSITIVE_NET_ID);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.OPENID);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.RFID_USER_ID);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.READER_CATEGORY);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.USER_TYPE);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.INCLUDE_ANONYMIZED);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.EDIT_LEVEL);
        mappableSearchParamsRegistry.registerDatatyped(UserConstants.SearchParams.USER_SERVICE_PROP);

        mappableSearchParamsRegistry.registerDatatyped(LoanConstants.SearchParams.LENDER);
        mappableSearchParamsRegistry.registerDatatyped(LoanConstants.SearchParams.LOAN_STATE);
        mappableSearchParamsRegistry.registerDatatyped(LoanConstants.SearchParams.EXEMPLAR);
        mappableSearchParamsRegistry.registerDatatyped(LoanConstants.SearchParams.LOAN_REQUEST_ID);
        mappableSearchParamsRegistry.registerDatatyped(LoanConstants.SearchParams.SHIPMENT_ITEM);
        mappableSearchParamsRegistry.registerDatatyped(LoanConstants.SearchParams.LEND_FROM_DATE);
        mappableSearchParamsRegistry.registerDatatyped(LoanConstants.SearchParams.LEND_TO_DATE);
        mappableSearchParamsRegistry.registerDatatyped(LoanConstants.SearchParams.INCLUDE_NOT_SHIPPING);
        mappableSearchParamsRegistry.registerDatatyped(LoanConstants.SearchParams.EXEMPLAR_Q);

        mappableSearchParamsRegistry.registerDatatyped(LoanReminderConstants.SearchParams.LOAN_REMINDER_TYPE);
        mappableSearchParamsRegistry.registerDatatyped(LoanReminderConstants.SearchParams.SENT_INCLUDED);

        mappableSearchParamsRegistry.registerDatatyped(MessageConstants.SearchParams.CREATE_DATE);
        mappableSearchParamsRegistry.registerDatatyped(MessageConstants.SearchParams.SENDER_USER);
        mappableSearchParamsRegistry.registerDatatyped(MessageConstants.SearchParams.TARGET_USER);
        mappableSearchParamsRegistry.registerDatatyped(MessageConstants.SearchParams.SEVERITY);
        mappableSearchParamsRegistry.registerDatatyped(MessageConstants.SearchParams.TOPIC);
        mappableSearchParamsRegistry.registerDatatyped(MessageConstants.SearchParams.MESSAGE_MEDIUM);
        mappableSearchParamsRegistry.registerDatatyped(MessageConstants.SearchParams.MESSAGE);
        mappableSearchParamsRegistry.registerDatatyped(MessageConstants.SearchParams.MESSAGES);
        mappableSearchParamsRegistry.registerDatatyped(MessageConstants.SearchParams.MESSAGE_STATUS);
        mappableSearchParamsRegistry.registerDatatyped(MessageConstants.SearchParams.THREAD);

        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.CNA);

        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.ISBN);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.ISSN);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.ISBN_OR_ISSN);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.AUTHOR);
        mappableSearchParamsRegistry.registerDatatyped(RecordConstants.SearchParams.PUBLICATION_YEAR);

        mappableSearchParamsRegistry.registerDatatyped(InventoryConstants.SearchParams.MATCH_STATE);

        mappableSearchParamsRegistry.registerDatatyped(StatsConstants.SearchParams.TIME_GRANULARITY);

        mappableSearchParamsRegistry.registerDatatyped(InventoryConstants.SearchParams.INVENTORY_STATE);

        return mappableSearchParamsRegistry;
    }


    @Bean
    public RecordSearchParamsStringQueryBuilder<MapBackedParams> luceneSearchQueryBuilder() {
        return new RecordSearchParamsStringQueryBuilder<>(restrictionToLuceneQueryConverter(), fondLoader)
                .withDocumentConjunctionModifier(MapBackedParams.class, new CommonDocumentParamsConjunctionModifier(departmentAccessor, enabledFondsProvider, fondLoader))
                .withDocumentConjunctionModifier(MapBackedParams.class, new DocumentFieldsSearchParamsRestrictionModifier(new QToLuceneQueryConverterByPattern(settingLoader.getDepartmentedProvider(SettingKeys.GLOBAL_SEARCH_TEMPLATE_1), keywordsParser())))
                .withAuthorityConjunctionModifier(MapBackedParams.class, new CommonAuthorityParamsConjunctionModifier(enabledFondsProvider))
                .withAuthorityConjunctionModifier(MapBackedParams.class, new AuthorityFieldsSearchParamsRestrictionModifier(new QToLuceneQueryConverterByPattern(settingLoader.getDepartmentedProvider(SettingKeys.AUTHORITY_GLOBAL_SEARCH_QUERY_TEMPLATE), keywordsParser())))
                .withRestrictionModifierFor(MapBackedParams.class, new RawQueryAddingConjunctionModifier())
                .withRestrictionModifierFor(MapBackedParams.class, new FieldedQAddingConjunctionModifier(searchFieldLoader()));
    }


    @Bean
    public Converter<Restriction<? extends SearchField>, String> restrictionToLuceneQueryConverter() {
        Converter<Restriction<? extends LuceneQueryField>, String> luceneFieldRestrictionToQuery = new RestrictionToLuceneQueryConverterBuilder(keywordsParser(), luceneQueryFieldLoader())
                .registerEqValueConverter(Integer.class, new IdToLuceneValueConverter())
                .registerEqValueConverter(String.class, new StringToLuceneValueConverter())
                .registerEqValueConverter(UUID.class, new UuidToLuceneValueConverter())
                .registerEqValueConverter(LocalDate.class, new LocalDateToLuceneValueConverter())
                .registerBetweenValueItemConverter(LocalDate.class, new LocalDateToLuceneValueConverter())
                .registerEqValueConverter(Instant.class, new InstantToLuceneValueConverter())
                .registerBetweenValueItemConverter(Instant.class, new InstantToLuceneValueConverter())
                .registerEqValueConverter(RecordIdFondPair.class, new RecordHeaderToLuceneValueConverter())
                .registerBetweenValueItemConverter(Identified.class, new IdentifiedToLuceneValueConverter())
                .registerEqValueConverter(Identified.class, new IdentifiedToLuceneValueConverter())
                .buildLuceneFieldRestrictionToQuery();


        Converter<Restriction<? extends SearchField>, Restriction<? extends LuceneQueryField>> simpleSearchFieldRestrictionToLuceneFieldRestriction = new RestrictionToLuceneQueryConverterBuilder(keywordsParser(), luceneQueryFieldLoader())
                .buildSearchFieldRestrictionToLuceneFieldRestriction();


        Converter<Restriction<? extends SearchField>, Restriction<? extends LuceneQueryField>> searchFieldRestrictionToLuceneFieldRestriction = new RestrictionToLuceneQueryConverterBuilder(keywordsParser(), luceneQueryFieldLoader())
                .configureRecursiveSearchFieldTermConverter(recursiveTermConverter -> {
                    recursiveTermConverter.addByMatcherClass(In.class, new RestrictionToLuceneQueryConverterBuilder.InMatcherTermToDisjunctionIntermediateConverter());
                    recursiveTermConverter.addByMatcherClass(Lt.class, new RestrictionToLuceneQueryConverterBuilder.LtMatcherToBetweenIntermediateConverter());
                    recursiveTermConverter.addByMatcherClass(LtEq.class, new RestrictionToLuceneQueryConverterBuilder.LtEqMatcherToBetweenIntermediateConverter());
                    recursiveTermConverter.addByMatcherClass(GtEq.class, new RestrictionToLuceneQueryConverterBuilder.GtEqMatcherToBetweenIntermediateConverter());
                    recursiveTermConverter.addByField(StaticSearchFields.SUBKIND, new SubkindFieldTermToFondListConverter(enabledDocumentFondsProvider, enabledAuthorityFondsProvider));
                })
                .configureNonRecursiveSearchFieldTermConverter(nonRecursiveTermConverter -> {
                    nonRecursiveTermConverter.addFirst(new SingleStringMatcherValueTermMatcher<>(IsbnChecker::isValidIsbn), new IsbnDisjunctionAddingTermConverter<SearchField>(RecordConstants.SearchFields.DOCUMENT_ISBN).andThen(simpleSearchFieldRestrictionToLuceneFieldRestriction));
                    nonRecursiveTermConverter.addFirst(new SingleStringMatcherValueTermMatcher<>(IsbnChecker::isValidIssn), new IsbnDisjunctionAddingTermConverter<SearchField>(RecordConstants.SearchFields.DOCUMENT_ISSN).andThen(simpleSearchFieldRestrictionToLuceneFieldRestriction));
                })
                .buildSearchFieldRestrictionToLuceneFieldRestriction();

        return searchFieldRestrictionToLuceneFieldRestriction
                .andThen(luceneFieldRestrictionToQuery);
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        allowedDatatypeToAllValuesProviderMap.registerEquals(CoreConstants.Datatype.SEARCH_FIELD, searchFieldsProvider());
        allDatatypeToAllValuesProviderMap.registerEquals(CoreConstants.Datatype.SEARCH_FIELD, ConvertingAllValuesProvider.byItemConverter(searchFieldsProvider(), searchField -> {
            MultiText text = MultiText.ofTexts("{} ({})", searchField.text(), Texts.ofNative(ObjectUtil.firstNotNull(searchField.sourceDescription(), SearchField.UNKNOWN_SOURCE_DESCRIPTION)));
            return new LabeledId<>(searchField.id(), text);
        }));

        converterRegisterer
                .registerForStringId(CacheMode.class, CacheMode.CODEBOOK)
                .registerForStringId(Datasource.class, datasourceLoader())
                .registerForStringId(FacetKeyIdentified.class, id -> new FacetKeyIdentified.StringIdToFacetKeyIdentifiedConverter().convert(id))
                .registerForIntegerId(FacetDefinitionType.class, FacetDefinitionType.CODEBOOK)
                .registerForIntegerId(FacetScope.class, FacetScope.CODEBOOK)
                .registerForStringId(SearchField.class, searchFieldLoader())
                .registerForStringId(SortingItem.class, searchSortingLoader())
                .registerForUuidId(Search.class, searchRepository());

        conversionService.addConverter(String.class, Restriction.class, source -> {
            try {
                return objectMapper.readValue(source, Restriction.class);
            } catch (IOException e) {
                throw new RuntimeException("Cannot deserialize json to %s".formatted(Restriction.class.getSimpleName()), e);
            }
        });

        objectMapperModule
                .addSerializer(Restriction.class, new RestrictionSerializer())
                .addDeserializer(Restriction.class, new RestrictionDeserializerPostModifyingDecorator(new RestrictionDeserializerWithSearchFields(), datatypableStringConverter, datatypeBySearchFieldLoader()));
    }
}
