package cz.kpsys.portaro.config;

import com.fasterxml.jackson.core.type.TypeReference;
import cz.kpsys.portaro.action.ActionSaver;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualFunction;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.autocomplete.AutocompleteByGroupLoader;
import cz.kpsys.portaro.autocomplete.AutocompleteByGroupLoaderDispatcher;
import cz.kpsys.portaro.commons.contextual.CompositeListContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.SameTypeSettableProvider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.SystemInstitution;
import cz.kpsys.portaro.exemplar.exemplarstatus.ExemplarStatus;
import cz.kpsys.portaro.export.ExportDescriptorLoader;
import cz.kpsys.portaro.licence.FeatureEnabledProvider;
import cz.kpsys.portaro.licence.FeatureManager;
import cz.kpsys.portaro.object.PhraseAutocompleteByGroupLoader;
import cz.kpsys.portaro.object.PhraseEntityLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordSettingKeys;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.datasource.Datasource;
import cz.kpsys.portaro.record.fond.ContextualFondedProvider;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.facet.FacetType;
import cz.kpsys.portaro.search.facet.FacetTypeCreator;
import cz.kpsys.portaro.search.facet.FacetTypeUpdater;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.factory.VelocitySearcherFactory;
import cz.kpsys.portaro.search.factory.VelocitySearcherImpl;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.keywords.SearchedKeywordsAutocompleteLoader;
import cz.kpsys.portaro.search.keywords.SearchedKeywordsLoader;
import cz.kpsys.portaro.search.keywords.SearchedKeywordsSaver;
import cz.kpsys.portaro.search.params.ParamsModifier;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.view.CompositeSearchFormFactory;
import cz.kpsys.portaro.search.view.SearchTextResolver;
import cz.kpsys.portaro.search.view.SearchViewFactory;
import cz.kpsys.portaro.search.view.ViewableSearchField;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.FondedValues;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.view.ViewableItemsConverter;
import cz.kpsys.portaro.view.web.rest.AutocompleteApiController;
import cz.kpsys.portaro.view.web.rest.FacetTypeApiController;
import cz.kpsys.portaro.view.web.rest.FacetTypeEditationRequest;
import cz.kpsys.portaro.view.web.rest.QueryConversionApiController;
import cz.kpsys.portaro.view.web.rest.search.SearchApiController;
import cz.kpsys.portaro.view.web.rest.search.SearchFormApiController;
import cz.kpsys.portaro.view.web.rest.search.SearchHistoryApiController;
import cz.kpsys.portaro.view.web.rss.RssController;
import cz.kpsys.portaro.web.page.ModelAndPageViewFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.CoreConstants.Datatype.DATATYPE_PREFIX_PHRASE;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SearchViewConfig {

    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull SettingLoader settingLoader;
    @NonNull FeatureManager featureManager;
    @NonNull Translator<Department> translator;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull ContextualProvider<Department, @NonNull List<Datasource>> allowedDatasetsProvider;
    @NonNull AllValuesProvider<Fond> readerShowableDocumentFondsProvider;
    @NonNull AllByIdsLoadable<Record, UUID> nonDetailedDocumentLoader;
    @NonNull Repository<Search<? extends MapBackedParams, ?, Paging>, UUID> searchRepository;
    @NonNull ContextualProvider<Department, @NonNull FondedValues<@NonNull List<SortingItem>>> departmentedFondedRecordSearchSortingsLoader;
    @NonNull AllValuesProvider<SortingItem> authoritySearchSortingLoader;
    @NonNull AllValuesProvider<SortingItem> exemplarSearchSortingLoader;
    @NonNull AllValuesProvider<SortingItem> matchSearchSortingLoader;
    @NonNull SameTypeSettableProvider<@NonNull Integer> documentDefaultPageSizeProvider;
    @NonNull AllProvidingRepository<Search<MapBackedParams, ?, Paging>, UUID> searchHistory;
    @NonNull PageSearchLoader<MapBackedParams, UUID, RangePaging> newestDocumentsLoader;
    @NonNull ContextualProvider<Department, @NonNull SystemInstitution> systemInstitutionProvider;
    @NonNull SearchedKeywordsLoader searchedKeywordsLoader;
    @NonNull Converter<Restriction<? extends SearchField>, String> restrictionToLuceneQueryConverter;
    @NonNull Converter<Restriction<? extends SearchField>, String> restrictionToZServerQueryConverter;
    @NonNull PhraseEntityLoader phraseEntityLoader;
    @NonNull SearchFactoryResolver searchFactoryResolver;
    @NonNull ActionSaver actionSaver;
    @NonNull ViewableItemsConverter viewableItemsConverter;
    @NonNull CompositeSearchFormFactory searchFormFactory;
    @NonNull SearchTextResolver searchTitleResolver;
    @NonNull SearchTextResolver searchSubtitleResolver;
    @NonNull Codebook<FacetType, Integer> facetTypeLoader;
    @NonNull ByIdLoadable<RecordStatus, Integer> recordStatusLoader;
    @NonNull ByIdLoadable<ExemplarStatus, Integer> exemplarStatusLoader;
    @NonNull ExportDescriptorLoader exportDescriptorLoader;
    @NonNull SearchedKeywordsSaver searchedKeywordsSaver;
    @NonNull SecurityManager securityManager;
    @NonNull AuthenticatedContextualFunction<SearchField, Department, ViewableSearchField> departmentedSearchFieldToViewableSearchFieldConverter;
    @NonNull MapToMapSearchParamsConverter mapToMapSearchParamsConverter;
    @NonNull ModelAndPageViewFactory modelAndPageViewFactory;
    @NonNull Converter<FacetTypeEditationRequest, FacetType> facetTypeEditationRequestFacetTypeConverter;
    @NonNull ContextualProvider<Department, org.apache.commons.lang3.Range<Integer>> documentYearExtremeRangeProvider;
    @NonNull AuthenticatedContextualProvider<Department, List<ViewableSearchField>> viewableSearchFieldsProvider;
    @NonNull ParamsModifier<DefaultGettableAndSettableSearchParams> fondRelatedParamsExpander;
    @NonNull FacetTypeCreator facetTypeCreator;
    @NonNull FacetTypeUpdater facetTypeUpdater;


    @Bean
    public AutocompleteApiController autocompleteApiController() {
        AutocompleteByGroupLoader autocompleteByGroupLoader = new AutocompleteByGroupLoaderDispatcher()
                .withByGroupLoader(DATATYPE_PREFIX_PHRASE, new PhraseAutocompleteByGroupLoader(phraseEntityLoader).caseInsensitive())
                .withLoader("searched-terms", new SearchedKeywordsAutocompleteLoader(searchedKeywordsLoader))
                .withStaticStringValues("prefix-university-degrees", "Bc.", "BcA.", "DiS.", "dr.", "Ing.", "Ing. arch.", "JUDr.", "MDDr.", "MgA.", "Mgr.", "MUDr.", "MVDr.", "Ph.", "PharmDr.", "PhDr.", "RNDr.", "Th.", "ThDr.", "ThLic.")
                .withStaticStringValues("suffix-university-degrees", "DiS.", "doc.", "dr.", "DSc.", "Ph.", "prof.", "Th.", "Ph.D.", "Th.D.", "CSc.", "DrSc.", "PaedDr.", "PhMr.");
        return new AutocompleteApiController(autocompleteByGroupLoader);
    }

    @Bean
    public FacetTypeApiController facetTypeController() {
        return new FacetTypeApiController(facetTypeLoader, facetTypeEditationRequestFacetTypeConverter, facetTypeCreator, facetTypeUpdater);
    }

    @Bean
    public SearchApiController searchApiController() {
        return new SearchApiController(
                actionSaver,
                searchFactoryResolver,
                publicContextPath,
                searchRepository,
                documentDefaultPageSizeProvider,
                searchViewFactory(),
                searchHistory,
                searchedKeywordsSaver,
                pagingParamsRepository()
        );
    }

    @Bean
    public QueryConversionApiController queryConversionApiController() {
        return new QueryConversionApiController(restrictionToLuceneQueryConverter, restrictionToZServerQueryConverter);
    }

    @Bean
    public SearchHistoryApiController searchHistoryApiController() {
        return new SearchHistoryApiController(
                translator,
                searchHistory,
                searchTitleResolver,
                publicContextPath,
                departmentedSearchFieldToViewableSearchFieldConverter
        );
    }

    @Bean
    public SearchFormApiController searchFormApiController() {
        return new SearchFormApiController(
                viewableSearchFieldsProvider,
                settingLoader.getOnRootProvider(SettingKeys.BASIC_MODE_SEARCH_FIELDS_COUNT),
                new FeatureEnabledProvider(featureManager, FeatureManager.FEATURE_SEARCH_PARAMETERS_HISTORY)
        );
    }

    @Bean
    public RssController rssController() {
        return new RssController(
                newestDocumentsLoader,
                settingLoader.getOnRootProvidedList(RecordSettingKeys.FORBIDDEN_RECORDS),
                settingLoader.getOnRootProvidedList(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES),
                settingLoader.getOnRootProvidedList(RecordSettingKeys.NEWS_EXEMPLAR_STATUSES),
                recordStatusLoader,
                exemplarStatusLoader,
                readerShowableDocumentFondsProvider,
                nonDetailedDocumentLoader,
                systemInstitutionProvider,
                serverUrlProvider.throwingWhenNull(),
                modelAndPageViewFactory
        );
    }

    @Bean
    public SearchViewFactory searchViewFactory() {
        return new SearchViewFactory(
                viewableItemsConverter,
                CompositeListContextualFunction.ofDistinctResultItems(
                        new ContextualFondedProvider<>(
                                departmentedFondedRecordSearchSortingsLoader,
                                _ -> authoritySearchSortingLoader.getAll()
                        )
                ),
                exemplarSearchSortingLoader,
                matchSearchSortingLoader,
                settingLoader.getDepartmentedProvider(SettingKeys.DEFAULT_EXPANDED_FACETS),
                serverUrlProvider.throwingWhenNull(),
                allowedDatasetsProvider,
                settingLoader.getDepartmentedProvider(RecordSettingKeys.CYRILLIC_ENABLED),
                searchTitleResolver,
                searchSubtitleResolver,
                exportDescriptorLoader,
                searchFormFactory,
                securityManager,
                documentYearExtremeRangeProvider,
                fondRelatedParamsExpander,
                pagingParamsRepository()
        );
    }

    @Bean
    @Scope(proxyMode = ScopedProxyMode.INTERFACES)
    public VelocitySearcherFactory untypedSearcherFactory() {
        return (currentAuth, ctx, _) -> new VelocitySearcherImpl(searchFactoryResolver, mapToMapSearchParamsConverter, currentAuth, ctx);
    }

    private SingleValueRepository<Paging, String> pagingParamsRepository() {
        return InJsonTokenSingleValueRepository.ofDefaultObjectMapper(new TypeReference<>() {});
    }
}
