package cz.kpsys.portaro.config;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.LabeledValuesGroup;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.core.editor.AcceptableValuesProviderLoaderDispatcher;
import cz.kpsys.portaro.core.editor.DispatchingValueEditorByDatatypeLoader;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.datatype.DatatypableStringConverterImpl;
import cz.kpsys.portaro.datatype.DatatypedAcceptableValuesRegistry;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.ExemplarConstants;
import cz.kpsys.portaro.exemplar.acquisitionway.AcquisitionWay;
import cz.kpsys.portaro.exemplar.customvalue.CustomValue;
import cz.kpsys.portaro.exemplar.exchangeset.ExchangeExemplarSet;
import cz.kpsys.portaro.exemplar.exemplarstatus.ExemplarStatus;
import cz.kpsys.portaro.exemplar.loancategory.LoanCategory;
import cz.kpsys.portaro.exemplar.thematicgroup.ThematicGroup;
import cz.kpsys.portaro.file.FileConstants;
import cz.kpsys.portaro.file.filecategory.FileCategory;
import cz.kpsys.portaro.form.editedproperty.AnnotationBasedEditedPropertyResolver;
import cz.kpsys.portaro.form.editedproperty.CompositeEditedPropertyResolver;
import cz.kpsys.portaro.form.editedproperty.EditedPropertyResolver;
import cz.kpsys.portaro.form.editedproperty.StaticEditedPropertyResolver;
import cz.kpsys.portaro.form.editor.ValueEditorByDatatypeLoader;
import cz.kpsys.portaro.form.editor.resolver.ValueEditorByPropertyResolver;
import cz.kpsys.portaro.form.editor.resolver.ValueEditorByPropertyResolverByByDatatypeResolver;
import cz.kpsys.portaro.form.editor.resolver.ValueEditorByPropertyResolverByExtractingFromEditedProperty;
import cz.kpsys.portaro.form.editor.resolver.ValueEditorByPropertyResolverComposite;
import cz.kpsys.portaro.form.form.AnnotationFormByFormObjectFactory;
import cz.kpsys.portaro.form.form.ConfirmedValidatorImpl;
import cz.kpsys.portaro.form.form.FormByFormObjectFactory;
import cz.kpsys.portaro.form.formfield.AnnotationEnabledFormFieldsResolver;
import cz.kpsys.portaro.form.formfield.EnabledFormFieldsResolver;
import cz.kpsys.portaro.form.formfield.FormFieldsNamesProvider;
import cz.kpsys.portaro.form.formfield.RequiredFormFieldsValidatorImpl;
import cz.kpsys.portaro.form.validation.ValidationsResolver;
import cz.kpsys.portaro.form.valueeditor.*;
import cz.kpsys.portaro.form.valueeditor.acceptableroot.AcceptableRootValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.accessnumber.AccessNumberValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.bool.BooleanValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.date.DateValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.html.HtmlValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.inlinerecordsearch.InlineRecordSearchValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.list.ListValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.multipleacceptable.MultipleAcceptableValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.number.NumberValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.object.ObjectValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.password.PasswordValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.price.PriceValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.range.RangeValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.scannabletext.ScannableTextValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.searchoredit.SearchOrEditValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.signature.SignatureValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.singleacceptable.SingleAcceptableValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.year.YearValueEditorResolver;
import cz.kpsys.portaro.formannotation.annotations.form.ConfirmedValidator;
import cz.kpsys.portaro.formannotation.annotations.formfield.RequiredFormFields;
import cz.kpsys.portaro.formannotation.annotations.formfield.RequiredFormFieldsValidator;
import cz.kpsys.portaro.formconfig.form.FormModifierByBeanNameProvider;
import cz.kpsys.portaro.formconfig.formfield.FormFieldsNamesByBeanNameProvider;
import cz.kpsys.portaro.formconfig.valueeditor.*;
import cz.kpsys.portaro.loan.LoanConstants;
import cz.kpsys.portaro.loan.lending.extern.ExternalLoanService;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.object.JpaPhraseEntityLoader;
import cz.kpsys.portaro.object.PhraseEntity;
import cz.kpsys.portaro.object.PhraseEntityLoader;
import cz.kpsys.portaro.object.SpringDbAcceptableValuesGroupDefValLoader;
import cz.kpsys.portaro.record.ForeignDatabase;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.edit.view.RecordValueEditorByDatatypeResolver;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.lucene.FacetKeyIdentified;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;

import static cz.kpsys.portaro.databasestructure.AcceptableValuesDb.DEF_VAL.DEF_VAL;
import static cz.kpsys.portaro.databasestructure.AcceptableValuesDb.DEF_VAL.KOD_VAL;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ValueEditorConfig {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull ConversionService conversionService;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull Codebook<Fond, Integer> enabledAuthorityFondsProvider;
    @NonNull AllValuesProvider<Fond> enabledDocumentFondsProvider;
    @NonNull AllValuesProvider<@NonNull Fond> readerShowableFondsProvider;
    @NonNull AllValuesProvider<Fond> readerShowableAuthorityFondsProvider;
    @NonNull AllValuesProvider<Fond> readerShowableDocumentFondsProvider;
    @NonNull AllValuesProvider<RecordStatus> recordStatusLoader;
    @NonNull AllValuesProvider<Location> locationLoader;
    @NonNull AllValuesProvider<Department> departmentLoader;
    @NonNull ByIdLoadable<LabeledValuesGroup<LabeledIdentified<String>, String>, Object> iniAcceptableValuesGroupLoader;
    @NonNull EntityManager entityManager;
    @NonNull AllValuesProvider<FileCategory> fileCategoryLoader;
    @NonNull AllValuesProvider<FileCategory> allowedFileCategoryLoader;
    @NonNull BeanFactory beanFactory;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull Provider<Department> currentDepartmentProvider;


    @Bean
    @Scope(proxyMode = ScopedProxyMode.INTERFACES)
    public DatatypableStringConverter datatypableStringConverter() {
        return new DatatypableStringConverterImpl(
                conversionService,
                typesByDatatypes(),
                defValAcceptableValuesGroupLoader(),
                iniAcceptableValuesGroupLoader
        );
    }


    @Bean
    public Map<ScalarDatatype, Class<?>> typesByDatatypes() {
        Map<ScalarDatatype, Class<?>> map = new HashMap<>();
        map.put(CoreConstants.Datatype.TEXT, String.class);
        map.put(CoreConstants.Datatype.NUMBER, Double.class);
        map.put(CoreConstants.Datatype.BOOLEAN, Boolean.class);
        map.put(CoreConstants.Datatype.UUID, UUID.class);
        map.put(CoreConstants.Datatype.URL, String.class);
        map.put(CoreConstants.Datatype.DATE, LocalDate.class);
        map.put(CoreConstants.Datatype.DATETIME, Instant.class);
        map.put(CoreConstants.Datatype.DATE_RANGE, DateRange.class);
        map.put(CoreConstants.Datatype.DATETIME_RANGE, DatetimeRange.class);
        map.put(CoreConstants.Datatype.YEAR, Integer.class);
        map.put(CoreConstants.Datatype.DEPARTMENT, Department.class);
        map.put(RecordConstants.Datatype.FOND, Fond.class);
        map.put(RecordConstants.Datatype.DOCUMENT_FOND, Fond.class);
        map.put(RecordConstants.Datatype.AUTHORITY_FOND, Fond.class);
        map.put(RecordConstants.Datatype.RECORD_STATUS, RecordStatus.class);
        map.put(ExemplarConstants.Datatype.EXEMPLAR_STATUS, ExemplarStatus.class);
        map.put(ExemplarConstants.Datatype.CUSTOM_VALUE, CustomValue.class);
        map.put(ExemplarConstants.Datatype.LOCATION, Location.class);
        map.put(ExemplarConstants.Datatype.EXCHANGE_EXEMPLAR_SET, ExchangeExemplarSet.class);
        map.put(UserConstants.Datatype.READER_CATEGORY, ReaderCategory.class);
        map.put(ExemplarConstants.Datatype.THEMATIC_GROUP, ThematicGroup.class);
        map.put(ExemplarConstants.Datatype.ACQUISITION_WAY, AcquisitionWay.class);
        map.put(LoanConstants.Datatype.LOAN_CATEGORY, LoanCategory.class);
        map.put(CoreConstants.Datatype.DATABASE, ForeignDatabase.class);
        map.put(FileConstants.Datatype.FILE_CATEGORY, FileCategory.class);
        map.put(CoreConstants.Datatype.FACET_VALUE, FacetKeyIdentified.class);
        map.put(CoreConstants.Datatype.SEARCH_FIELD, SearchField.class);
        map.put(LoanConstants.Datatype.EXTERNAL_LOAN_SERVICE, ExternalLoanService.class);
        return map;
    }


    @Bean
    public ByIdLoadable<LabeledValuesGroup<LabeledIdentified<String>, String>, Object> defValAcceptableValuesGroupLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(new SpringDbAcceptableValuesGroupDefValLoader(jdbcTemplate, queryFactory))
                .staticCached(LabeledValuesGroup.class.getSimpleName())
                .fallbackWhenNotFoundById(id -> {
                    log.warn("V tabulce {} chybi skupina s {} = {}", DEF_VAL, KOD_VAL, id);
                    return new LabeledValuesGroup<>(id);
                })
                .build();
    }

    @Bean
    public PhraseEntityLoader phraseEntityLoader() {
        return new JpaPhraseEntityLoader(new SimpleJpaRepository<>(PhraseEntity.class, entityManager));
    }


    @Bean
    @Scope(proxyMode = ScopedProxyMode.INTERFACES) //kvuli circular dep. na FieldTypeLoaderByDtoLoader musi byt proxy
    public ValueEditorByDatatypeLoader valueEditorByDatatypeLoader() {
        return new DispatchingValueEditorByDatatypeLoader(
                acceptableValuesProviderLoader(),
                RecordValueEditorByDatatypeResolver.createWithStandardBehaviour(fondLoader)
        );
    }


    @Bean
    public AcceptableValuesProviderLoaderDispatcher acceptableValuesProviderLoader() {
        return new AcceptableValuesProviderLoaderDispatcher(
                allowedDatatypeToAllValuesProviderMap(),
                defValAcceptableValuesGroupLoader(),
                iniAcceptableValuesGroupLoader
        );
    }


    @Bean
    public DatatypedAcceptableValuesRegistry allowedDatatypeToAllValuesProviderMap() {
        return new DatatypedAcceptableValuesRegistry()
                .registerEquals(RecordConstants.Datatype.FOND, readerShowableFondsProvider)
                .registerEquals(RecordConstants.Datatype.DOCUMENT_FOND, readerShowableDocumentFondsProvider)
                .registerEquals(RecordConstants.Datatype.AUTHORITY_FOND, readerShowableAuthorityFondsProvider)
                .registerEquals(RecordConstants.Datatype.RECORD_STATUS, recordStatusLoader)
                .registerEquals(CoreConstants.Datatype.DEPARTMENT, departmentLoader)
                .registerEquals(ExemplarConstants.Datatype.LOCATION, locationLoader)
                .registerEquals(FileConstants.Datatype.FILE_CATEGORY, allowedFileCategoryLoader);
    }


    /**
     * Napr. pro ini, aby v editorech byly vsechny fondy a ne jen filtrovane.
     */
    @Bean
    public DatatypedAcceptableValuesRegistry allDatatypeToAllValuesProviderMap() {
        //pro ini upravime allowedAcceptableValuesProviderByDatatypeMap tak, aby v ni byly vsechny fondy
        return new DatatypedAcceptableValuesRegistry(allowedDatatypeToAllValuesProviderMap())
                .registerEquals(RecordConstants.Datatype.FOND, enabledFondsProvider)
                .registerEquals(RecordConstants.Datatype.DOCUMENT_FOND, enabledDocumentFondsProvider)
                .registerEquals(RecordConstants.Datatype.AUTHORITY_FOND, enabledAuthorityFondsProvider)
                .registerEquals(FileConstants.Datatype.FILE_CATEGORY, fileCategoryLoader);
    }

    @Bean
    public FormFieldsNamesProvider formFieldsNamesProvider() {
        return new FormFieldsNamesByBeanNameProvider(beanFactory, authenticationHolder, currentDepartmentProvider);
    }

    @Bean
    public EnabledFormFieldsResolver enabledFormFieldsResolver() {
        return new AnnotationEnabledFormFieldsResolver(formFieldsNamesProvider());
    }

    @Bean
    public List<AnnotationsAwareValueEditorResolver> valueEditorAnnotationsResolvers() {
        // Magic due to avoid circular dependency - see initializeValueEditorAnnotationsResolvers()
        return new ArrayList<>();
    }

    @Bean
    public AnnotationBasedEditedPropertyResolver annotationBasedEditedPropertyResolver() {
        return new AnnotationBasedEditedPropertyResolver(valueEditorAnnotationsResolvers());
    }

    @Bean
    public ValueEditorByPropertyResolver valueEditorByPropertyResolver() {
        return new ValueEditorByPropertyResolverComposite(List.of(
                new ValueEditorByPropertyResolverByExtractingFromEditedProperty(),
                new ValueEditorByPropertyResolverByByDatatypeResolver(valueEditorByDatatypeLoader())
        ));
    }

    @Bean
    public FormByFormObjectFactory formByFormObjectFactory() {
        EditedPropertyResolver staticEditedPropertyResolver = new StaticEditedPropertyResolver(valueEditorByPropertyResolver()); // static resolver is currently not used - ??remove??

        EditedPropertyResolver compositeEditedPropertyResolver = new CompositeEditedPropertyResolver(List.of(
                staticEditedPropertyResolver,
                annotationBasedEditedPropertyResolver()
        ));

        return new AnnotationFormByFormObjectFactory(enabledFormFieldsResolver(), compositeEditedPropertyResolver, new FormModifierByBeanNameProvider(beanFactory));
    }

    @Bean
    public FormObjectModelValuePrototypeFactory formObjectPrototypeFactory() {
        return new FormObjectModelValuePrototypeFactory(new ValueEditorPrototypeByBeanNameProvider(beanFactory));
    }

    @Bean
    public Provider<@NonNull RequiredFormFieldsValidator> requiredFormFieldsValidatorProvider() {
        return () -> new RequiredFormFieldsValidatorImpl(formFieldsNamesProvider());
    }

    @Bean
    public Provider<@NonNull ConfirmedValidator> confirmedValidatorProvider() {
        return ConfirmedValidatorImpl::new;
    }

    @Bean
    public AcceptableValuesProvider<Department> acceptableValuesProviderResolver() {
        return new AcceptableValuesByBeanNameProvider(beanFactory, authenticationHolder);
    }

    @Bean
    public AcceptableValueProvider<Department> acceptableValueProviderResolver() {
        return new AcceptableValueByBeanNameProvider(beanFactory, authenticationHolder);
    }

    @Bean
    public CanDoActionProvider<Department> canDoActionProviderResolver() {
        return new CanDoActionByBeanNameProvider(beanFactory, authenticationHolder);
    }

    @Bean
    public AnnotationValueEditorModifier<Department> departmentValueEditorByFormObjectAndFieldNameModifier() {
        return new AnnotationValueEditorByBeanNameModifier(beanFactory);
    }

    @Bean
    public ValidationsResolver validationsResolver() {
        return new ValidationsResolver(List.of(RequiredFormFields.Involve.class));
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeValueEditorAnnotationsResolvers() {
        valueEditorAnnotationsResolvers().addAll(List.of(
                new BooleanValueEditorResolver(validationsResolver(), departmentValueEditorByFormObjectAndFieldNameModifier()),
                new TextValueEditorResolver(validationsResolver(), departmentValueEditorByFormObjectAndFieldNameModifier()),
                new DateValueEditorResolver(validationsResolver()),
                new AcceptableRootValueEditorResolver<>(validationsResolver(), acceptableValueProviderResolver()),
                new SingleAcceptableValueEditorResolver(validationsResolver(), acceptableValuesProviderResolver()),
                new MultipleAcceptableValueEditorResolver(validationsResolver(), acceptableValuesProviderResolver()),
                new SearchOrEditValueEditorResolver(validationsResolver(), departmentValueEditorByFormObjectAndFieldNameModifier()),
                new ScannableTextValueEditorResolver(validationsResolver(), departmentValueEditorByFormObjectAndFieldNameModifier()),
                new AccessNumberValueEditorResolver(validationsResolver(), canDoActionProviderResolver()),
                new SignatureValueEditorResolver(validationsResolver(), canDoActionProviderResolver()),
                new NumberValueEditorResolver(validationsResolver(), departmentValueEditorByFormObjectAndFieldNameModifier()),
                new YearValueEditorResolver(validationsResolver()),
                new RangeValueEditorResolver(departmentValueEditorByFormObjectAndFieldNameModifier()),
                new HtmlValueEditorResolver(validationsResolver()),
                new ListValueEditorResolver(validationsResolver(), annotationBasedEditedPropertyResolver(), formObjectPrototypeFactory(), departmentValueEditorByFormObjectAndFieldNameModifier()),
                new ObjectValueEditorResolver(validationsResolver(), formByFormObjectFactory(), formObjectPrototypeFactory()),
                new PasswordValueEditorResolver(validationsResolver(), departmentValueEditorByFormObjectAndFieldNameModifier()),
                new PriceValueEditorResolver(validationsResolver(), acceptableValuesProviderResolver()),
                new InlineRecordSearchValueEditorResolver(validationsResolver())
        ));
    }
}
