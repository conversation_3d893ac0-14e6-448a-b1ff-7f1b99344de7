<script lang="ts">
    import type {Document, User} from 'typings/portaro.be.types';
    import type {UserDetailView} from 'src/features/user/types';
    import type {UserAccountReactivePageData, UserAccountStaticPageData} from 'src/features/user/kp-user-account-page/types';
    import {createPageContext} from 'shared/layouts/page-context';
    import {exists} from 'shared/utils/custom-utils';

    export let user: User | null;
    export let userDetailView: UserDetailView | null;
    export let userRecord: Document | null;

    $: if (exists(user) && exists(userDetailView)) createPageContext<UserAccountStaticPageData, UserAccountReactivePageData>({user, detailView: userDetailView, userRecord});
</script>

{#if exists(user) && exists(userDetailView)}
    <slot/>
{/if}