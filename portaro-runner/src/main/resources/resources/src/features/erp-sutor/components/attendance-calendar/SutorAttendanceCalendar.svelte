<script lang="ts">
    import type {YearMonth} from 'shared/components/kp-calendar/types';
    import type {DayAttendance, SummaryPaymentState, WorkAttendanceOverview} from './types';
    import type {UUID} from 'typings/portaro.be.types';
    import {getInjector} from 'core/svelte-context/context';
    import {AttendanceCalendarService} from './attendance-calendar.service';
    import {onMount} from 'svelte';
    import {DateTime} from 'luxon';
    import {exists} from 'shared/utils/custom-utils';
    import {fade} from 'svelte/transition';
    import AttendanceCalendar from './components/AttendanceCalendar.svelte';
    import AttendanceSummary from './components/summary/AttendanceSummary.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';

    export let userRecordId: UUID;
    export let type: 'last-week' | 'month-with-summary';
    export let withoutLoadAnimations = false;
    export let highlightedDate: DateTime | null = null;
    export let yearMonth: YearMonth | null = null;
    export let summaryPaymentStates: SummaryPaymentState[] | null = null;

    const service = getInjector().getByClass(AttendanceCalendarService);

    let workAttendance: WorkAttendanceOverview | DayAttendance[];
    let loading = true;
    $: loadError = !loading && !exists(workAttendance);

    onMount(async () => {
        await loadWorkAttendance();
    });

    async function loadWorkAttendance() {
        loading = true;
        const today = DateTime.now();

        if (type === 'last-week') {
            const endDate = (exists(highlightedDate) ? highlightedDate : DateTime.now()).plus({days: 1});
            const oneWeekBefore = endDate.minus({weeks: 1});
            workAttendance = await service.getWorkAttendanceDays(userRecordId, oneWeekBefore.toJSDate(), endDate.toJSDate());
        }

        if (type === 'month-with-summary') {
            const year = exists(yearMonth) ? Number(yearMonth.split('-')[0]) : today.year;
            const month = exists(yearMonth) ? Number(yearMonth.split('-')[1]) : today.month;

            workAttendance = await service.getWorkAttendanceOverview(userRecordId, year, month);
        }

        loading = false;
    }

    function isMonthAttendanceWithSummary(attendance: WorkAttendanceOverview | DayAttendance[]): attendance is WorkAttendanceOverview {
        return 'summary' in attendance && 'days' in attendance;
    }

    $: if (exists(yearMonth) && type === 'month-with-summary') {
        loadWorkAttendance();
    }
</script>

<div class="sutor-attendance-calendar-wrapper">
    <KpLoadableContainer {loading} {loadError} withoutAnimations="{withoutLoadAnimations}">
        <div class="sutor-attendance-calendar" in:fade={{duration: withoutLoadAnimations ? 300 : 0}}>
            {#if isMonthAttendanceWithSummary(workAttendance)}
                <AttendanceSummary summary="{workAttendance.summary}" {summaryPaymentStates}/>
                <AttendanceCalendar attendanceDays="{workAttendance.days}"/>
            {:else}
                <AttendanceCalendar {highlightedDate} attendanceDays="{workAttendance}"/>
            {/if}
        </div>
    </KpLoadableContainer>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .sutor-attendance-calendar-wrapper {
        display: flex;
        flex-direction: column;
        gap: @spacing-m;

        .sutor-attendance-calendar {
            font-size: @font-size-sm;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: @spacing-ml;
        }
    }
</style>