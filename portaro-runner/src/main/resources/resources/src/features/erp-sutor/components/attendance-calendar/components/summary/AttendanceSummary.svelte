<script lang="ts">
    import type {AttendanceSummary, SummaryPaymentState} from '../../types';
    import {SummarySectionType} from '../../types';
    import {exists} from 'shared/utils/custom-utils';
    import AttendanceSummaryWorkSection from './sections/AttendanceSummaryWorkSection.svelte';
    import AttendanceSummaryOvertimeSection from './sections/AttendanceSummaryOvertimeSection.svelte';
    import AttendanceSummaryAbsenceSection from './sections/AttendanceSummaryAbsenceSection.svelte';
    import AttendanceSummaryOnCallDutySection from './sections/AttendanceSummaryOnCallDutySection.svelte';
    import AttendanceSummaryLegendSection from './sections/AttendanceSummaryLegendSection.svelte';
    import AttendanceSummaryIdpSection from 'src/features/erp-sutor/components/attendance-calendar/components/summary/sections/AttendanceSummaryIdpSection.svelte';

    export let summary: AttendanceSummary;
    export let summaryPaymentStates: SummaryPaymentState[] | null = null;

    $: overtimePaymentState = summaryPaymentStates?.find((state) => state.type === SummarySectionType.OVERTIME);
    $: onCallDutyPaymentState = summaryPaymentStates?.find((state) => state.type === SummarySectionType.ON_CALL_DUTY);
    $: idpPaymentState = summaryPaymentStates?.find((state) => state.type === SummarySectionType.IDP);
</script>

<div class="attendance-summary-container">
    <AttendanceSummaryLegendSection includeSummaryStates="{exists(summaryPaymentStates)}"/>
    <AttendanceSummaryWorkSection work="{summary.work}" commitment="{summary.commitment}" holiday="{summary.holiday}"/>
    <AttendanceSummaryOvertimeSection overtime="{summary.overtime}" paymentState="{overtimePaymentState}"/>
    <AttendanceSummaryAbsenceSection absence="{summary.absence}"/>
    <AttendanceSummaryOnCallDutySection onCallDuty="{summary.onCallDuty}" paymentState="{onCallDutyPaymentState}"/>
    <AttendanceSummaryIdpSection idp="{summary.idp}" paymentState="{idpPaymentState}"/>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .attendance-summary-container {
        width: 100%;
        display: flex;
        gap: @spacing-ml;
    }
</style>
