<script lang="ts">
    import type {IdpSummary, SummaryPaymentState} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import {SummarySectionType} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import AttendanceSummarySection from '../AttendanceSummarySection.svelte';
    import AttendanceCell from '../../AttendanceCell.svelte';
    import AttendanceSummaryColumn from '../AttendanceSummaryColumn.svelte';

    export let idp: IdpSummary;
    export let paymentState: SummaryPaymentState | null = null;
</script>

<AttendanceSummarySection type="{SummarySectionType.IDP}" {paymentState}>
    <AttendanceSummaryColumn>
        <AttendanceCell header>Celkový čas</AttendanceCell>
        <AttendanceCell durationItem="{idp}"/>
    </AttendanceSummaryColumn>
</AttendanceSummarySection>
