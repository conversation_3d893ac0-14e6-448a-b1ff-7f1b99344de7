<script lang="ts">
    import type {DataViewPageConfiguration, DataViewTabsConfiguration} from './types';
    import type {Rec} from 'typings/portaro.be.types';
    import type {TabButton, TabPageDefinitionsMap} from 'shared/ui-widgets/tabset/types';
    import {exists, ignoreUnusedProperties} from 'shared/utils/custom-utils';
    import {createDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import {getInjector} from 'core/svelte-context/context';
    import {ErpFondPermissionsContextService} from 'src/features/erp/services/erp-fond-permissions-context.service';
    import {REFRESH_DATA_VIEW_GRID_EVENT} from './types';
    import ErpPageLayout from 'src/features/erp/components/ErpPageLayout.svelte';
    import ErpHeadingBar from 'src/features/erp/components/ErpHeadingBar.svelte';
    import ErpTabbedSubpagesContainer from 'src/features/erp/components/erp-tabbed-subpages/ErpTabbedSubpagesContainer.svelte';
    import TabbedSubpagesRouter from 'src/features/erp/components/erp-tabbed-subpages/TabbedSubpagesRouter.svelte';
    import DataViewGridTab from 'src/features/erp-sutor/pages/data-view/DataViewGridTab.svelte';
    import DataViewCreateRecordButton from 'src/features/erp-sutor/pages/data-view/DataViewCreateRecordButton.svelte';
    import DivisionsHierarchyTableTab from 'src/features/erp-sutor/pages/data-view/custom-tab-components/divisions/DivisionsHierarchyTableTab.svelte';
    import DivisionsDiagramTab from 'src/features/erp-sutor/pages/data-view/custom-tab-components/divisions/DivisionsDiagramTab.svelte';
    import CreateDivisionButton from 'src/features/erp-sutor/pages/data-view/custom-tab-components/divisions/CreateDivisionButton.svelte';
    import CreateEmployeeButton from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/CreateEmployeeButton.svelte';
    import EmptyTab from 'src/features/erp-sutor/pages/data-view/EmptyTab.svelte';
    import EmployeesAttendanceTab from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/monthattendace/EmployeesAttendanceTab.svelte';

    export let configuration: DataViewPageConfiguration;

    const eventContext = createDataViewEventContext();
    const fondPermissionsService = getInjector().getByClass(ErpFondPermissionsContextService);

    const definedComponents = {
        employeesAttendance: EmployeesAttendanceTab,
        employeesCreateButton: CreateEmployeeButton,

        divisionsCreateButton: CreateDivisionButton,
        divisionsHierarchyTable: DivisionsHierarchyTableTab,
        divisionsDiagram: DivisionsDiagramTab
    };

    const {tabs, tabButtons} = createTabsConfiguration();

    function createTabsConfiguration(): DataViewTabsConfiguration {
        const fondPermissions = fondPermissionsService.getFondPermissionsValue();

        const configuredTabs: TabPageDefinitionsMap = {};
        const configuredTabButtons: TabButton[] = [];

        configuration.tabs.forEach((tab) => {
            if (exists(tab.customComponent) && fondPermissionsService.hasFondPermissions(fondPermissions, tab.showPermissionFond, tab.editPermissionFond)) {
                configuredTabs[`tab-${tab.id}`] = {
                    component: definedComponents[tab.customComponent.componentName],
                    props: tab.customComponent.props
                };

                configuredTabButtons.push({
                    id: `tab-${tab.id}`,
                    label: tab.name,
                    icon: tab.icon,
                    tabPageWithoutPadding: exists(tab.customComponent.noTabPadding) ? tab.customComponent.noTabPadding : false
                });
            }

            if (!exists(tab.fondId)) {
                return;
            }

            if (!exists(tab.showPermissionFond) && !exists(tab.editPermissionFond) && !fondPermissionsService.hasEditFondPermissions(fondPermissions, tab.fondId)) {
                return;
            }

            if ((exists(tab.showPermissionFond) || exists(tab.editPermissionFond)) && !fondPermissionsService.hasFondPermissions(fondPermissions, tab.showPermissionFond, tab.editPermissionFond)) {
                return;
            }

            configuredTabs[`tab-${tab.id}`] = {
                component: DataViewGridTab,
                props: {
                    fondId: tab.fondId,
                    referenceFieldTypeId: tab.referenceFieldTypeId,
                    referenceRecordId: tab.referenceRecordId,
                    qt: tab.customSearchQt,
                    recordLinkFieldTypeId: tab.recordLinkFieldTypeId
                }
            };

            configuredTabButtons.push({
                id: `tab-${tab.id}`,
                label: tab.name,
                icon: tab.icon,
                tabPageWithoutPadding: true,
                tabPageContainerClass: 'record-grid-tab-page'
            });
        });

        if (configuredTabButtons.length === 0) {
            configuredTabButtons.push({
                id: 'tab-empty',
                label: 'Empty'
            });

            configuredTabs['tab-empty'] = {
                component: EmptyTab,
                props: {}
            };
        }

        return {
            tabs: configuredTabs,
            tabButtons: configuredTabButtons
        };
    }

    const handleRecordCreated = (event: CustomEvent<Rec>) => {
        ignoreUnusedProperties(event);
        eventContext.dispatchEvent(new CustomEvent(REFRESH_DATA_VIEW_GRID_EVENT));
    };
</script>

<ErpPageLayout pageClass="sutor-data-view-page" gap="0px" withoutContentPadding>
    {#if exists(configuration.heading)}
        <ErpHeadingBar slot="heading" title="{configuration.heading.text}" icon="{configuration.heading.icon}">
            <svelte:fragment slot="additional-content">
                {#if exists(configuration.heading.createButtonCustomComponent)}
                    <svelte:component this="{definedComponents[configuration.heading.createButtonCustomComponent]}"/>
                {:else if exists(configuration.heading.createButton)}
                    <DataViewCreateRecordButton rootFondId="{configuration.heading.createButton.rootFondId}"
                                                buttonLabel="{configuration.heading.createButton.label}"
                                                on:record-created={handleRecordCreated}/>
                {/if}
            </svelte:fragment>
        </ErpHeadingBar>
    {/if}

    <ErpTabbedSubpagesContainer {tabButtons}
                                let:activeTab
                                additionalClasses="sutor-data-view-tabbed-subpages">

        <TabbedSubpagesRouter {tabs} {activeTab}/>
    </ErpTabbedSubpagesContainer>
</ErpPageLayout>