<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {YearMonth} from 'shared/components/kp-calendar/types';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {getIdFromGridFieldValue, getTextFromGridFieldValue} from 'src/features/erp-sutor/sutor-utils';
    import {getInjector} from 'core/svelte-context/context';
    import {getDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import {MonthAttendanceService} from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/monthattendance.service';
    import {FOND_MONTHLY_ATTENDANCE} from 'src/features/erp-sutor/sutor-fonds';
    import {REFRESH_ROW_DATA_VIEW_GRID_EVENT} from 'src/features/erp-sutor/pages/data-view/types';
    import GridFieldValue from 'shared/ui-widgets/grid/GridFieldValue.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import SutorAttendanceCalendar from 'src/features/erp-sutor/components/attendance-calendar/SutorAttendanceCalendar.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import TextNoteEditor from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/TextNoteEditor.svelte';

    export let record: RecordRow;
    export let yearMonth: YearMonth;

    const eventContext = getDataViewEventContext();
    const service = getInjector().getByClass(MonthAttendanceService);

    let savingEditedTextNote = false;

    $: personNumberField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.personNumberFieldId);
    $: employeeField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.employeeFieldId);
    $: contractTypeField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.contractTypeFieldId);
    $: divisionField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.divisionFieldId);
    $: superiorField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.superiorFieldId);
    $: stateField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.stateFieldId);
    $: employerField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.employerFieldId);

    const inProgressColor = '#FFFFFF';
    const toCheckColor = '#E8F1F9';
    const returnedColor = '#FFECEF';
    const checkedColor = '#E9F9E8';
    const closedColor = '#EEEEEE';

    const stateColorMap = {
        INPROGRESS: inProgressColor,
        TOCHECK: toCheckColor,
        TOCHECK_FIXED: toCheckColor,
        RETURNED: returnedColor,
        RETURNED_CHECKED: checkedColor,
        CHECKED: checkedColor,
        CHECKED_FIXED: checkedColor,
        CLOSED: closedColor
    };

    $: bgColor = stateColorMap[getIdFromGridFieldValue(stateField)];

    const handleEditClick = async () => {
        const updatedRecordRow = await service.editMonthAttendance(record);
        eventContext.dispatchEvent(new CustomEvent<RecordRow>(REFRESH_ROW_DATA_VIEW_GRID_EVENT, {detail: updatedRecordRow}));
    };

    const handleUpdateTextNote = async (event: CustomEvent<string>) => {
        savingEditedTextNote = true;
        const updatedRecord = await service.editMonthAttendanceTextNote(record, event.detail);
        eventContext.dispatchEvent(new CustomEvent<RecordRow>(REFRESH_ROW_DATA_VIEW_GRID_EVENT, {detail: updatedRecord}));
        savingEditedTextNote = false;
    };
</script>

<tr style:--bg-color="{bgColor}">
    <td>
        <GridFieldValue field="{personNumberField}"/>
    </td>

    <td>
        <GridFieldValue field="{employeeField}">
            <KpUserAvatar userRecordId="{employeeField?.recordReference?.id}" sizePx="{32}"/>
            {getTextFromGridFieldValue(employeeField)}
        </GridFieldValue>
    </td>

    <td>
        <GridFieldValue field="{contractTypeField}"/>
    </td>

    <td>
        <GridFieldValue field="{divisionField}"/>
    </td>

    <td>
        <GridFieldValue field="{superiorField}">
            <KpUserAvatar userRecordId="{superiorField?.recordReference?.id}" sizePx="{32}"/>
            {getTextFromGridFieldValue(superiorField)}
        </GridFieldValue>
    </td>

    <td>
        <GridFieldValue field="{stateField}"/>
    </td>

    <td>
        <GridFieldValue field="{employerField}"/>
    </td>

    <td>
        <KpButton buttonSize="xs" buttonStyle="primary" on:click={handleEditClick}>
            <IconedContent icon="edit">
                Upravit
            </IconedContent>
        </KpButton>
    </td>
</tr>

<tr style:--bg-color="{bgColor}">
    <td colspan="8">
        <div class="attendance-calendar-container">
            <SutorAttendanceCalendar userRecordId="{record.id}"
                                     type="month-with-summary"
                                     withoutLoadAnimations
                                     {yearMonth}/>
        </div>
    </td>
</tr>

<tr style:--bg-color="{bgColor}">
    <td colspan="8">
        <div class="textnote-container">
            <TextNoteEditor {record} {savingEditedTextNote} on:update-text-note={handleUpdateTextNote}/>
        </div>
    </td>
</tr>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    tr {
        position: relative;
        isolation: isolate;

        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            transition: background-color 0.3s ease-in-out;
            background-color: var(--bg-color);
        }

        &:first-child {
            &::after {
                border-top-left-radius: @border-radius-large;
                border-top-right-radius: @border-radius-large;
            }

            td {
                &:first-child {
                    padding-left: @spacing-m;
                }

                &:last-child {
                    padding-right: @spacing-m;
                }
            }
        }

        &:nth-child(3)::after {
            border-bottom-left-radius: @border-radius-large;
            border-bottom-right-radius: @border-radius-large;
        }
    }

    .textnote-container {
        width: 100%;
        padding: @spacing-m;
        margin-bottom: @spacing-l;
    }

    .attendance-calendar-container {
        width: 100%;
        padding: 0 @spacing-m;
        margin-bottom: @spacing-s;
    }
</style>