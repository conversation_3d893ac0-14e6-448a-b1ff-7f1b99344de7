<script lang="ts">
    import type {RecordSearchParams} from 'typings/portaro.be.types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {YearMonth} from 'shared/components/kp-calendar/types';
    import type {SearchManager} from 'src/features/search/search-manager/search-manager';
    import {asRecordRow} from 'src/features/record-grid/lib/types-utils';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {onDestroy, onMount} from 'svelte';
    import {getDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import {exists, isFunction} from 'shared/utils/custom-utils';
    import {getInjector} from 'core/svelte-context/context';
    import {fade} from 'svelte/transition';
    import {MonthAttendanceService} from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/monthattendance.service';
    import {FOND_MONTHLY_ATTENDANCE} from 'src/features/erp-sutor/sutor-fonds';
    import {REFRESH_DATA_VIEW_GRID_EVENT, REFRESH_ROW_DATA_VIEW_GRID_EVENT} from 'src/features/erp-sutor/pages/data-view/types';
    import {SUTOR_MONTH_ATTENDANCE_STATES} from 'src/features/erp-sutor/sutor-constants';
    import KpPageableSearchResults from 'src/features/search/kp-search-results/KpPageableSearchResults.svelte';
    import KpSearchToolbar from 'src/features/search/kp-search-toolbar/KpSearchToolbar.svelte';
    import KpSearchContext from 'src/features/search/kp-search-context/KpSearchContext.svelte';
    import KpSearchFieldTypeFiltersContainer from 'src/features/search/kp-search-field-type-filter/KpSearchFieldTypeFiltersContainer.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import AttendanceEmployeeItem from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/AttendanceEmployeeItem.svelte';
    import KpSearchFieldTypeFilter from 'src/features/search/kp-search-field-type-filter/KpSearchFieldTypeFilter.svelte';
    import MonthSelector from 'src/features/erp-sutor/components/attendance-calendar/MonthSelector.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import BulkStateEditor from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/BulkStateEditor.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    const eventContext = getDataViewEventContext();
    const service = getInjector().getByClass(MonthAttendanceService);
    let getSearchManager: () => SearchManager<RecordRow>;
    let yearMonth: YearMonth | null = null;

    let anyFiltersActive = false;
    let changingStateBulk = false;

    onMount(() => {
        eventContext.addEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
        eventContext.addEventListener(REFRESH_ROW_DATA_VIEW_GRID_EVENT, handleRefreshRow);
    });

    onDestroy(() => {
        eventContext.removeEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
        eventContext.removeEventListener(REFRESH_ROW_DATA_VIEW_GRID_EVENT, handleRefreshRow);
    });

    function createStaticParams(yearMonthParam: YearMonth): RecordSearchParams {
        return {
            kind: [Kind.KIND_RECORD],
            type: SearchType.TYPE_TABLE,
            rootFond: [{id: FOND_MONTHLY_ATTENDANCE.fond}],
            pageSize: 20,
            [FOND_MONTHLY_ATTENDANCE.yearMonthFieldFilterSearchParam]: yearMonthParam
        };
    }

    const handleRefresh = () => {
        if (!isFunction(getSearchManager)) {
            return;
        }

        const searchManager = getSearchManager();
        searchManager.refreshSearch();
    };

    const handleRefreshRow = (event: CustomEvent<RecordRow>) => {
        if (!isFunction(getSearchManager)) {
            return;
        }

        const searchManager = getSearchManager();
        searchManager.getPagination().replaceItemById(event.detail);
    };

    const handleMonthSelected = (event: CustomEvent<YearMonth>) => {
        if (!exists(yearMonth)) {
            yearMonth = event.detail;
            return;
        }

        yearMonth = event.detail;

        if (!isFunction(getSearchManager)) {
            return;
        }

        const searchManager = getSearchManager();

        searchManager.newSearchWithPartialParams({
            [FOND_MONTHLY_ATTENDANCE.yearMonthFieldFilterSearchParam]: event.detail
        });
    };

    const handleSetState = async (event: CustomEvent<keyof typeof SUTOR_MONTH_ATTENDANCE_STATES>) => {
        if (!isFunction(getSearchManager)) {
            return;
        }

        const searchManager = getSearchManager();
        const params = searchManager.getCompleteParams();

        changingStateBulk = true;
        await service.setState(params, SUTOR_MONTH_ATTENDANCE_STATES[event.detail]);
        searchManager.refreshSearch();

        changingStateBulk = false;
    };
</script>

<Flex direction="row" alignItems="center" gap="l">
    <MonthSelector on:month-selected={handleMonthSelected}/>
    <Spacer flex="1"/>
    <BulkStateEditor {anyFiltersActive} {changingStateBulk} on:set-state={handleSetState}/>
</Flex>

{#if exists(yearMonth)}
    <KpSearchContext localSearch staticParams="{createStaticParams(yearMonth)}" let:searchManager bind:getSearchManager>
        <KpSearchFieldTypeFiltersContainer fondId="{FOND_MONTHLY_ATTENDANCE.fond}" bind:anyFiltersActive>
            <div class="sutor-employees-attendance-container" class:no-filters-active={!anyFiltersActive}>
                <KpSearchToolbar/>

                {#if changingStateBulk}
                    <div class="bulk-state-changing-overlay" in:fade={{duration: 250}}>
                        <KpLoadingBlock/>
                        <p class="text-muted">Nastavuji stav na vyhledaných záznamech... Tato operace může trvat několik minut.</p>
                    </div>
                {/if}

                <KpPageableSearchResults noRefreshLoading pagination="{searchManager.getPagination()}" let:paginationData>
                    <KpBarebonesTable fontSize="13px" stickyHeader="{!changingStateBulk}">
                        <tr slot="header">
                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.employeeFieldId}">
                                    Osobní číslo
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.employeeFieldId}">
                                    Pracovník
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.contractTypeFieldId}">
                                    Typ smlouvy
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.divisionFieldId}">
                                    Divize
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.superiorFieldId}">
                                    Nadřízený
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.stateFieldId}">
                                    Stav schválení
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.employerFieldId}">
                                    Zaměstnavatel
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>Akce</th>
                        </tr>

                        <svelte:fragment slot="body">
                            {#if anyFiltersActive}
                                {#each paginationData.items as itemRow(itemRow.id)}
                                    {@const record = asRecordRow(itemRow)}

                                    <AttendanceEmployeeItem {record} {yearMonth}/>
                                {/each}
                            {:else}
                                <tr>
                                    <td class="no-filters-active-row" colspan="8">
                                        <IconedContent icon="info" orientation="vertical" align="center" justify="center" fillAvailableSpace>
                                            Nejprve nastavte alespoň jeden filtr pro zobrazení docházky
                                        </IconedContent>
                                    </td>
                                </tr>
                            {/if}
                        </svelte:fragment>
                    </KpBarebonesTable>
                </KpPageableSearchResults>
            </div>
        </KpSearchFieldTypeFiltersContainer>
    </KpSearchContext>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .bulk-state-changing-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.5);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: @spacing-m;
        z-index: 1;
    }

    :global {
        .sutor-employees-attendance-container {
            .kp-pageable-search-results {
                margin-top: @spacing-m;
            }

            &.no-filters-active .kp-pageable-search-results {
                .pagination-buttons,
                .pagination-page-selector,
                .loading-container {
                    display: none;
                }
            }
        }
    }

    .no-filters-active-row {
        height: 250px;
        color: var(--accent-blue-new);
    }
</style>