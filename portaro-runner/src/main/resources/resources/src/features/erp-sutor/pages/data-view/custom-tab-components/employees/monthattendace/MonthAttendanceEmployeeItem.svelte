<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {YearMonth} from 'shared/components/kp-calendar/types';
    import type {SummaryPaymentState} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {getIdFromGridFieldValue, getTextFromGridFieldValue} from 'src/features/erp-sutor/sutor-utils';
    import {getInjector} from 'core/svelte-context/context';
    import {getDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import {MonthAttendanceService} from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/monthattendace/services/monthattendance.service';
    import {SummarySectionType} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import {exists} from 'shared/utils/custom-utils';
    import {FOND_MONTHLY_ATTENDANCE} from 'src/features/erp-sutor/sutor-fonds';
    import {REFRESH_ROW_DATA_VIEW_GRID_EVENT} from 'src/features/erp-sutor/pages/data-view/types';
    import GridFieldValue from 'shared/ui-widgets/grid/GridFieldValue.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import SutorAttendanceCalendar from 'src/features/erp-sutor/components/attendance-calendar/SutorAttendanceCalendar.svelte';
    import MonthAttendanceItemDropdown from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/monthattendace/MonthAttendanceItemDropdown.svelte';
    import MonthAttendanceTextNoteEditor from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/monthattendace/MonthAttendanceTextNoteEditor.svelte';
    import {monthAttendancePaymentStateNames} from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/monthattendace/payment-states';
    import {SUTOR_MONTH_ATTENDANCE_STATES} from 'src/features/erp-sutor/sutor-constants';

    export let record: RecordRow;
    export let yearMonth: YearMonth;

    const eventContext = getDataViewEventContext();
    const service = getInjector().getByClass(MonthAttendanceService);

    let savingEditedTextNote = false;

    $: personNumberField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.personNumberFieldId);
    $: employeeField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.employeeFieldId);
    $: contractTypeField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.contractTypeFieldId);
    $: divisionField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.divisionFieldId);
    $: superiorField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.superiorFieldId);
    $: stateField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.stateFieldId);
    $: employerField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.employerFieldId);
    $: overtimePaymentStateField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.overtimePaymentStateFieldId);
    $: onCallDutyPaymentStateField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.onCallPaymentStateFieldId);
    $: idpPaymentStateField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.idpPaymentStateFieldId);

    $: overtimeId = getIdFromGridFieldValue(overtimePaymentStateField);
    $: onCallDutyId = getIdFromGridFieldValue(onCallDutyPaymentStateField);
    $: idpId = getIdFromGridFieldValue(idpPaymentStateField);

    $: summaryPaymentStates = createPaymentStates(overtimeId, onCallDutyId, idpId);

    function createPaymentStates(overtimeIdValue: string | null, onCallDutyIdValue: string | null, idpIdValue: string | null): SummaryPaymentState[] | null {
        const states: SummaryPaymentState[] = [];

        if (exists(overtimeIdValue)) {
            states.push({type: SummarySectionType.OVERTIME, value: monthAttendancePaymentStateNames[overtimeIdValue]});
        }

        if (exists(onCallDutyIdValue)) {
            states.push({type: SummarySectionType.ON_CALL_DUTY, value: monthAttendancePaymentStateNames[onCallDutyIdValue]});
        }

        if (exists(idpIdValue)) {
            states.push({type: SummarySectionType.IDP, value: monthAttendancePaymentStateNames[idpIdValue]});
        }

        if (states.length === 0) {
            return null;
        }

        return states;
    }

    const inProgressColor = '#FFFFFF';
    const toCheckColor = '#E8F1F9';
    const returnedColor = '#FFECEF';
    const checkedColor = '#E9F9E8';
    const closedColor = '#EEEEEE';

    const stateColorMap = {
        [SUTOR_MONTH_ATTENDANCE_STATES.IN_PROGRESS]: inProgressColor,
        [SUTOR_MONTH_ATTENDANCE_STATES.TO_CHECK]: toCheckColor,
        [SUTOR_MONTH_ATTENDANCE_STATES.TOCHECK_FIXED]: toCheckColor,
        [SUTOR_MONTH_ATTENDANCE_STATES.RETURNED]: returnedColor,
        [SUTOR_MONTH_ATTENDANCE_STATES.RETURNED_CHECKED]: checkedColor,
        [SUTOR_MONTH_ATTENDANCE_STATES.CHECKED]: checkedColor,
        [SUTOR_MONTH_ATTENDANCE_STATES.CHECKED_FIXED]: checkedColor,
        [SUTOR_MONTH_ATTENDANCE_STATES.CLOSED]: closedColor
    };

    $: bgColor = stateColorMap[getIdFromGridFieldValue(stateField)];

    const handleUpdateTextNote = async (event: CustomEvent<string>) => {
        savingEditedTextNote = true;
        const updatedRecord = await service.editMonthAttendanceTextNote(record, event.detail);
        eventContext.dispatchEvent(new CustomEvent<RecordRow>(REFRESH_ROW_DATA_VIEW_GRID_EVENT, {detail: updatedRecord}));
        savingEditedTextNote = false;
    };
</script>

<tr style:--bg-color="{bgColor}">
    <td>
        <GridFieldValue field="{personNumberField}"/>
    </td>

    <td>
        <GridFieldValue field="{employeeField}">
            <KpUserAvatar userRecordId="{employeeField?.recordReference?.id}" sizePx="{32}"/>
            {getTextFromGridFieldValue(employeeField)}
        </GridFieldValue>
    </td>

    <td>
        <GridFieldValue field="{contractTypeField}"/>
    </td>

    <td>
        <GridFieldValue field="{divisionField}"/>
    </td>

    <td>
        <GridFieldValue field="{superiorField}">
            <KpUserAvatar userRecordId="{superiorField?.recordReference?.id}" sizePx="{32}"/>
            {getTextFromGridFieldValue(superiorField)}
        </GridFieldValue>
    </td>

    <td>
        <GridFieldValue field="{stateField}"/>
    </td>

    <td>
        <GridFieldValue field="{employerField}"/>
    </td>

    <td>
        <MonthAttendanceItemDropdown {record}/>
    </td>
</tr>

<tr style:--bg-color="{bgColor}">
    <td colspan="8">
        <div class="attendance-calendar-container">
            <SutorAttendanceCalendar userRecordId="{record.id}"
                                     type="month-with-summary"
                                     withoutLoadAnimations
                                     {summaryPaymentStates}
                                     {yearMonth}/>
        </div>
    </td>
</tr>

<tr style:--bg-color="{bgColor}">
    <td colspan="8">
        <div class="textnote-container">
            <MonthAttendanceTextNoteEditor {record} {savingEditedTextNote} on:update-text-note={handleUpdateTextNote}/>
        </div>
    </td>
</tr>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    tr {
        position: relative;
        isolation: isolate;

        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            transition: background-color 0.3s ease-in-out;
            background-color: var(--bg-color);
        }

        &:first-child {
            &::after {
                border-top-left-radius: @border-radius-large;
                border-top-right-radius: @border-radius-large;
            }

            td {
                &:first-child {
                    padding-left: @spacing-m;
                }

                &:last-child {
                    padding-right: @spacing-m;
                }
            }
        }

        &:nth-child(3)::after {
            border-bottom-left-radius: @border-radius-large;
            border-bottom-right-radius: @border-radius-large;
        }
    }

    .textnote-container {
        width: 100%;
        padding: @spacing-m;
        margin-bottom: @spacing-l;
    }

    .attendance-calendar-container {
        width: 100%;
        padding: 0 @spacing-m;
        margin-bottom: @spacing-s;
    }
</style>