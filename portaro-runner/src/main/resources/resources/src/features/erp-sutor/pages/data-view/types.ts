import type {UIcons} from 'shared/ui-widgets/uicons/types';
import type {TabButton, TabPageDefinitionsMap} from 'shared/ui-widgets/tabset/types';
import type {FieldTypeId, UUID} from 'typings/portaro.be.types';

export type FondId = number;

export const REFRESH_DATA_VIEW_GRID_EVENT = 'refresh-dataview-grid';
export const REFRESH_ROW_DATA_VIEW_GRID_EVENT = 'refresh-row-dataview-grid';

export interface DataViewTabsConfiguration {
    tabs: TabPageDefinitionsMap;
    tabButtons: TabButton[];
}

export interface DataViewPagesConfiguration {
    definedDataViewPages: DataViewPageConfiguration[];
}

export interface DataViewPageConfiguration {
    id: string;
    route: string;
    heading: DataViewPageHeadingConfiguration;
    tabs: DataViewTabConfiguration[];
}

export interface DataViewPageHeadingConfiguration {
    text: string;
    icon?: UIcons;
    createButton?: DataViewPageCreateButtonConfiguration;
    createButtonCustomComponent?: string;
}

export interface DataViewTabConfiguration {
    id: string;
    name: string;
    fondId?: FondId;
    showPermissionFond?: FondId;
    editPermissionFond?: FondId;
    referenceFieldTypeId?: FieldTypeId;
    referenceRecordId?: UUID;
    customSearchQt?: string;
    recordLinkFieldTypeId?: FieldTypeId;
    icon?: UIcons;
    customComponent?: DataViewTabCustomComponentConfiguration;
}

export interface DataViewPageCreateButtonConfiguration {
    label: string;
    rootFondId: FondId;
}

export interface DataViewTabCustomComponentConfiguration {
    componentName: string;
    noTabPadding?: boolean;
    props?: Record<string, any>;
}
