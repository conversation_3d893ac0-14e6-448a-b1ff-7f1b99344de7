<script lang="ts">
    import {getPageContext} from 'shared/layouts/page-context';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {DocumentDetailTabsetService} from 'src/features/record/kp-document-detail-tabset/document-detail-tabset.service';
    import {onMount} from 'svelte';
    import KpMasonryGridItem from 'shared/layouts/masonry-grid/KpMasonryGridItem.svelte';
    import KpMasonryGrid from 'shared/layouts/masonry-grid/KpMasonryGrid.svelte';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import KpRecordMarcTable from '../../../../record/kp-record-marc-table/KpRecordMarcTable.svelte';
    import KpCustomParagraph from 'shared/value-editors/internal/editors/search-or-edit/templates/KpCustomParagraph.svelte';
    import KpRecordOperationMini from 'src/features/record/operation/KpRecordOperationMini.svelte';
    import KpPaginatedPanelList from 'shared/components/kp-paginated-panel-list/KpPaginatedPanelList.svelte';
    import type {
        DocumentDetailReactivePageData,
        DocumentDetailStaticPageData
    } from 'src/features/record/kp-document-detail-page/types';

    const service = getInjector().getByClass(DocumentDetailTabsetService);
    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();
    const model = pageContext.staticData.model;
    const localize = getLocalization();

    const operationPageableList = service.createOperationPageableList(model.record);

    onMount(() => {
        operationPageableList.loadFirstPage();
    });
</script>

<KpMasonryGrid gridTemplateColumns="repeat(auto-fill, minmax(600px, 1fr))"
               additionalClasses="document-detail-grid">

    <KpMasonryGridItem animate="{false}">
        <KpGenericPanel hasBodyPadding="{false}">
            <KpCustomParagraph content="{model.nextToCoverParagraph}"
                               customClass="paragraph-next-to-cover sutor-record-info-paragraph"/>
        </KpGenericPanel>
    </KpMasonryGridItem>

    <KpMasonryGridItem animate="{false}">
        <KpGenericPanel hasBodyPadding="{false}">
            <span slot="heading">Systemist debug - MARC informace</span>
            <KpRecordMarcTable fields="{model.record.fields}"/>
        </KpGenericPanel>
    </KpMasonryGridItem>

    <KpMasonryGridItem animate="{false}">
        <KpGenericPanel>
            <span slot="heading">{localize(/* @kp-localization record.ProcessingHistory */ 'record.ProcessingHistory')}</span>
            <KpPaginatedPanelList pageableList="{operationPageableList}"
                                  isInPaddedPanel="{true}"
                                  let:item>
                <KpRecordOperationMini operation="{item}"/>
            </KpPaginatedPanelList>
        </KpGenericPanel>
    </KpMasonryGridItem>
</KpMasonryGrid>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    :global {
        .document-detail-grid .sutor-record-info-paragraph {
            display: flex;
            flex-direction: column;

            .record-info-row {
                padding: 8px @spacing-m;
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-bottom: 1px solid @themed-border-default;

                &.title {
                    font-weight: 500;
                }

                &:empty {
                    display: none;
                }

                &:last-child {
                    border-bottom: none;
                }
            }
        }
    }

    :global {
        .document-detail-grid .marc-table {
            tr > th {
                padding: @spacing-sm @spacing-ml;
                font-weight: 500;
            }

            &.table-color-accented > thead > tr > th {
                color: @themed-text-default;
                border-bottom: 1px solid @themed-border-default;
            }
        }
    }
</style>