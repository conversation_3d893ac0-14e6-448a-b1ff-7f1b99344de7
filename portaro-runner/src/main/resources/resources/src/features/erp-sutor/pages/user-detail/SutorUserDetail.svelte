<script lang="ts">
    import type {User} from 'typings/portaro.be.types';
    import type {DocumentDetailReactivePageData, DocumentDetailStaticPageData} from 'src/features/record/kp-document-detail-page/types';
    import type {UserDetailView} from 'src/features/user/types';
    import type {SutorEmployeeDetail} from 'src/features/erp-sutor/pages/user-detail/types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {onMount} from 'svelte';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {SutorUserDetailService} from 'src/features/erp-sutor/pages/user-detail/sutor-user-detail.service';
    import {getPageContext} from 'shared/layouts/page-context';
    import {exists} from 'shared/utils/custom-utils';
    import {DocumentDetailTabsetService} from 'src/features/record/kp-document-detail-tabset/document-detail-tabset.service';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import KpUserAccountContactPart from 'src/features/user/kp-user-account-page/parts/KpUserAccountContactPart.svelte';
    import UserRecordDetailContext from 'src/features/erp-sutor/components/UserRecordDetailContext.svelte';
    import KpUserAccountAddressPart from 'src/features/user/kp-user-account-page/parts/KpUserAccountAddressPart.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';
    import UserDetailGrid from 'src/features/erp-sutor/pages/user-detail/UserDetailGrid.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import LabeledValue from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/overviews/components/LabeledValue.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import KpMasonryGridItem from 'shared/layouts/masonry-grid/KpMasonryGridItem.svelte';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import KpPaginatedPanelList from 'shared/components/kp-paginated-panel-list/KpPaginatedPanelList.svelte';
    import KpRecordMarcTable from 'src/features/record/kp-record-marc-table/KpRecordMarcTable.svelte';
    import KpRecordOperationMini from 'src/features/record/operation/KpRecordOperationMini.svelte';
    import KpMasonryGrid from 'shared/layouts/masonry-grid/KpMasonryGrid.svelte';

    export let user: User;
    export let userDetailView: UserDetailView | null;

    const service = getInjector().getByClass(SutorUserDetailService);
    const documentService = getInjector().getByClass(DocumentDetailTabsetService);
    const localize = getLocalization();
    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();
    const model = pageContext.staticData.model;

    let loading = true;
    let employeeDetail: SutorEmployeeDetail | null = null;

    const operationPageableList = documentService.createOperationPageableList(model.record);

    onMount(async () => {
        employeeDetail = await service.getUserDetailData(model.record.id);
        await operationPageableList.loadFirstPage();
        loading = false;
    });
</script>

<KpLoadableContainer {loading} fillAvailableSpace>
    <Spacer direction="vertical" size="m"/>

    <Flex direction="row" gap="xl" alignItems="center">
        <div class="user-name-container">
            <KpUserAvatar userRecordId="{model.record.id}" sizePx="{128}" allowEdit/>
            <Spacer direction="horizontal" size="xl"/>
            <h1>{pipe(user, loc())}</h1>
        </div>

        {#if exists(employeeDetail) && exists(employeeDetail.homeDepartment)}
            <LabeledValue label="Domovská divize pracovníka">
                {pipe(employeeDetail.homeDepartment, loc())}
            </LabeledValue>
        {/if}
    </Flex>

    <Spacer direction="vertical" size="xl"/>
    <Spacer direction="vertical" size="xxl"/>

    <Flex direction="column">
        {#if exists(employeeDetail)}
            <UserDetailGrid {employeeDetail}/>
        {/if}

        <Spacer direction="vertical" size="xxl"/>
        <Spacer direction="vertical" size="xxl"/>
        <KpHeading type="h2">Dodatečné informace</KpHeading>
        <Spacer direction="vertical" size="xxl"/>

        <KpMasonryGrid gridTemplateColumns="repeat(auto-fill, minmax(600px, 1fr))" additionalClasses="project-detail-grid">
            <UserRecordDetailContext {user} {userDetailView} userRecord="{model.record}">
                {#if user.addresses.length > 0}
                    <KpMasonryGridItem animate="{false}">
                        <KpUserAccountAddressPart/>
                    </KpMasonryGridItem>
                {/if}

                {#if user.contacts.length > 0}
                    <KpMasonryGridItem animate="{false}">
                        <KpUserAccountContactPart/>
                    </KpMasonryGridItem>
                {/if}
            </UserRecordDetailContext>

            <KpMasonryGridItem animate="{false}">
                <KpGenericPanel hasBodyPadding="{false}">
                    <span slot="heading">Systemist debug - MARC informace</span>
                    <KpRecordMarcTable fields="{model.record.fields}"/>
                </KpGenericPanel>
            </KpMasonryGridItem>

            <KpMasonryGridItem animate="{false}">
                <KpGenericPanel>
                    <span slot="heading">{localize(/* @kp-localization record.ProcessingHistory */ 'record.ProcessingHistory')}</span>
                    <KpPaginatedPanelList pageableList="{operationPageableList}"
                                          isInPaddedPanel="{true}"
                                          let:item>

                        <KpRecordOperationMini operation="{item}"/>
                    </KpPaginatedPanelList>
                </KpGenericPanel>
            </KpMasonryGridItem>
        </KpMasonryGrid>

        <Spacer direction="vertical" size="m"/>
    </Flex>
</KpLoadableContainer>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .user-name-container {
        display: flex;
        align-items: center;

        h1 {
            margin: 0;
            width: min-content;
            font-weight: 500;
        }
    }
</style>