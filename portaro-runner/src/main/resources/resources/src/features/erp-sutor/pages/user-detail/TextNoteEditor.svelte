<script lang="ts">
    import type {TextValueEditorOptions} from 'shared/value-editors/internal/editors/text/types';
    import {createEventDispatcher} from 'svelte';
    import {fly} from 'svelte/transition';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let textNote: string;
    export let editing = false;

    const dispatch = createEventDispatcher<{'update-text-note': string}>();

    let textValue = textNote;

    function getAceEditorOptions(): TextValueEditorOptions {
        return {
            type: 'rich-textarea',
            aceOptions: {
                showPrintMargin: false
            }
        };
    }

    const handleEditClick = () => {
        editing = true;
    };

    const handleUpdateTextNote = () => {
        dispatch('update-text-note', textValue);
        editing = false;
    };

    const handleCancelEdit = () => {
        editing = false;
    };
</script>

<Flex class="report-text-note-editor" direction="column" gap="xs">
    <Flex alignItems="center">
        <span class="editor-heading">Poznámka</span>

        <Spacer direction="horizontal" size="m"/>

        {#if !editing}
            <KpIconButton noBackground icon="edit" on:click={handleEditClick}/>
        {:else}
            <KpButton buttonStyle="danger-new" tooltipLabel="Zrušit" on:click={handleCancelEdit}>
                <IconedContent icon="cross-circle"/>
            </KpButton>

            <Spacer direction="horizontal" size="s"/>

            <KpButton buttonStyle="success-new" tooltipLabel="Uložit" on:click={handleUpdateTextNote}>
                <IconedContent icon="check-circle"/>
            </KpButton>
        {/if}
    </Flex>

    {#key editing}
        <div class="anim-container" in:fly={{y: 10, duration: 250}}>
            {#if !editing}
                {#if textValue}
                    <span class="text-note">{textNote}</span>
                {:else}
                    <small class="text-muted">(nic zde není)</small>
                {/if}
            {:else}
                <KpValueEditor type="text"
                               options="{getAceEditorOptions()}"
                               bind:model="{textValue}"/>
            {/if}
        </div>
    {/key}
</Flex>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .editor-heading {
        font-size: @font-size-small;
        color: @themed-text-muted;
    }

    .text-note {
        white-space: pre-wrap;
        font-size: @font-size-large;
    }

    :global {
        .report-text-note-editor .ace-editor-wrapper {
            .ace-editor {
                height: 200px;
                min-height: 200px;
            }
        }
    }
</style>