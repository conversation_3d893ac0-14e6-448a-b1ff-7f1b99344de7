import type {SutorEmployeeDetail} from 'src/features/erp-sutor/pages/user-detail/types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class SutorUserDetailDataService {
    public static serviceName = 'sutorUserDetailDataService';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public async getUserDetailData(userId: string): Promise<SutorEmployeeDetail> {
        return this.ajaxService
            .createRequest(`/employee/detail/${userId}`)
            .get();
    }
}