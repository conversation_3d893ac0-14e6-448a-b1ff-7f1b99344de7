import type {SutorEmployeeDetail} from 'src/features/erp-sutor/pages/user-detail/types';
import type {GridCellEditRequest, RecordRow} from 'src/features/record-grid/lib/types';
import type {SutorUserDetailDataService} from 'src/features/erp-sutor/pages/user-detail/sutor-user-detail.data-service';
import type {RecordGridDataService} from 'src/features/record-grid/services/record-grid.data-service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {RecordGridService} from 'src/features/record-grid/services/record-grid.service';
import type FinishedResponseInteractionService from 'src/shared/services/finished-response-interaction.service';
import {convertFieldTypeIdToFieldId} from 'shared/utils/record-field-utils';
import {FOND_EMPLOYMENT_STATUS} from 'src/features/erp-sutor/sutor-fonds';

export class SutorUserDetailService {
    public static serviceName = 'sutorUserDetailService';

    /*@ngInject*/
    constructor(private sutorUserDetailDataService: SutorUserDetailDataService,
                private recordGridDataService: RecordGridDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private toastMessageService: ToastMessageService,
                private recordGridService: RecordGridService) {
    }

    public async getUserDetailData(userId: string): Promise<SutorEmployeeDetail | null> {
        try {
            return await this.sutorUserDetailDataService.getUserDetailData(userId);
        } catch {
            return null;
        }
    }

    public async editEmploymentStatusTextNote(record: RecordRow, textNote: string): Promise<RecordRow | null> {
        try {
            const editRequests: GridCellEditRequest[] = [
                {
                    record: record.id,
                    fieldId: convertFieldTypeIdToFieldId(FOND_EMPLOYMENT_STATUS.textNoteFieldId, [0, 0]),
                    setFieldValueRequest: {value: textNote}
                }
            ];

            await this.recordGridDataService.editCells({cellEditRequests: editRequests});
            this.toastMessageService.showSuccess('Poznámka byla úspěšně změněna');

            return await this.recordGridService.loadSingleRow(record.id);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            return null;
        }
    }
}