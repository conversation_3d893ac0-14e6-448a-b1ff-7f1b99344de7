// States
import type {FieldTypeId} from 'typings/portaro.be.types';

export const SUTOR_REFERENCE_STATES = {
    NEW: 'NEW',
    DONE: 'DONE',
    IN_PROGRESS: 'INPROGRESS'
} as const;

export const SUTOR_REPORT_STATES = {
    CONFIRMED: 'CONFIRMED',
    IN_PROGRESS: 'IN_PROGRESS',
    TO_CONFIRM: 'TO_CONFIRM',
    RETURNED: 'RETURNED'
} as const;

export const SUTOR_MONTH_ATTENDANCE_STATES = {
    IN_PROGRESS: 'INPROGRESS',
    TO_CHECK: 'TOCHECK',
    RETURNED: 'RETURNED',
    CHECKED: 'CHECKED',
    CLOSED: 'CLOSED',
    RETURNED_CHECKED: 'RETURNED_CHECKED',
    TOCHECK_FIXED: 'TOCHECK_FIXED',
    CHECKED_FIXED: 'CHECKED_FIXED'
};

// Page settings
export const SUTOR_REPORTS_PROCESSING_PAGE = {
    fond: 90,
    fondMaterial: 92
};

export const SUTOR_REPORTS_APPROVING_PAGE = {
    fond: 91,
    reportRefreshEvent: 'sutor-reports-approving-refresh-event'
};

export const SUTOR_REPORTS_OPTIMISATION_PAGE = {
    fond: 102,
    userListFond: 21,

    reportServerTemplateMD: 'montazni-denik-aktualni',
    reportServerTemplateOverallOverview: 'predavaci-protokol-aktualni'
};

export const SUTOR_LOGBOOK_PAIRING_PAGE = {
    logbooksFond: 104,
    logbookReferenceField: 'd1002.main' as FieldTypeId
};