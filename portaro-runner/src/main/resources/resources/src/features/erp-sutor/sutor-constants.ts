// States
import type {FieldTypeId} from 'typings/portaro.be.types';

export const SUTOR_REFERENCE_STATES = {
    NEW: 'new',
    DONE: 'done',
    IN_PROGRESS: 'inprogress'
} as const;

export const SUTOR_REPORT_STATES = {
    CONFIRMED: 'confirmed',
    IN_PROGRESS: 'in_progress',
    TO_CONFIRM: 'to_confirm',
    RETURNED: 'returned'
} as const;

export const SUTOR_MONTH_ATTENDANCE_STATES = {
    IN_PROGRESS: 'inprogress',
    TO_CHECK: 'tocheck',
    RETURNED: 'returned',
    CHECKED: 'checked',
    CLOSED: 'closed',
    RETURNED_CHECKED: 'returned_checked',
    TOCHECK_FIXED: 'tocheck_fixed',
    CHECKED_FIXED: 'checked_fixed'
};

export const SUTOR_MONTH_ATTENDANCE_PAYMENT_STATE = {
    NOTHING: 'nothing',
    D: 'd',
    FA: 'fa',
    OF: 'of',
    OF_IN_BONUS: 'of_in_bonus'
};

// Page settings
export const SUTOR_REPORTS_PROCESSING_PAGE = {
    fond: 90,
    fondMaterial: 92
};

export const SUTOR_REPORTS_APPROVING_PAGE = {
    fond: 91,
    reportRefreshEvent: 'sutor-reports-approving-refresh-event'
};

export const SUTOR_REPORTS_OPTIMISATION_PAGE = {
    fond: 102,
    userListFond: 21,

    reportServerTemplateMD: 'montazni-denik-aktualni',
    reportServerTemplateOverallOverview: 'predavaci-protokol-aktualni'
};

export const SUTOR_LOGBOOK_PAIRING_PAGE = {
    logbooksFond: 104,
    logbookReferenceField: 'd3002.main' as FieldTypeId
};