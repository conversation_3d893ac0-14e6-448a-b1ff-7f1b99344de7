import type {FieldTypeId} from 'typings/portaro.be.types';
import {SUTOR_REFERENCE_STATES, SUTOR_REPORT_STATES} from 'src/features/erp-sutor/sutor-constants';

export const FOND_PROJECT = {
    fond: 1,

    // All supported project subfonds
    subfonds: [4, 5],

    projectNameFieldId: 'd1110.a' as FieldTypeId,
    projectNumberFieldId: 'd1110.z' as FieldTypeId,
    responsibleUserFieldId: 'd1112.main' as FieldTypeId,
    customerFieldId: 'd1010.a' as FieldTypeId,
    contractorFieldId: 'd1012.main' as FieldTypeId,
    contractFieldId: 'd1015.main' as FieldTypeId,
    stateFieldId: 'd1105' as FieldTypeId,
    typeFieldId: 'd1106.t' as FieldTypeId,
    parentProjectFieldId: 'd1120.main' as FieldTypeId,

    // Subfonds
    REPORTED: {
        fond: 5
    },
    CONTRACTED: {
        fond: 4,

        startDateFieldId: 'd1041.d' as FieldTypeId,
        endDateFieldId: 'd1043.d' as FieldTypeId,
        priceFieldId: 'd1150.c' as FieldTypeId,
        priceCurrencyFieldId: 'd1150.j' as FieldTypeId
    }
};

export const FOND_CUSTOMER_PROJECT = {
    fond: 15
};

export const FOND_REFERENCE = {
    fond: 80,

    nameFieldId: 'd8000.a' as FieldTypeId,
    stateFieldId: 'd8005.h' as FieldTypeId,
    userFieldId: 'd8010.main' as FieldTypeId,
    dateFieldId: 'd8015.d' as FieldTypeId,
    totalHoursFieldId: 'd8020.m' as FieldTypeId,

    // Reference state chip styles
    STATE_CHIP_STYLES: {
        [SUTOR_REFERENCE_STATES.NEW]: 'brand-orange-new',
        [SUTOR_REFERENCE_STATES.DONE]: 'success-new',
        [SUTOR_REFERENCE_STATES.IN_PROGRESS]: 'accent-blue-new'
    } as const
};

export const FOND_PERSON = {
    fond: 7,

    positionFieldId: 'd1725' as FieldTypeId,
    employerFieldId: 'd1750' as FieldTypeId,
    nameFieldId: 'd1710' as FieldTypeId,
    firstNameFieldId: 'd1710.b' as FieldTypeId,
    surnameFieldId: 'd1710.a' as FieldTypeId,
    titleFieldId: 'd1710.c' as FieldTypeId,
    pohodaIdFieldId: 'd1720.b' as FieldTypeId,
    birthDateFieldId: 'd1740.a' as FieldTypeId,
    birthNumberFieldId: 'd1740.b' as FieldTypeId,
    birthPlaceFieldId: 'd1740.c' as FieldTypeId,
    nationalityFieldId: 'd1740.f' as FieldTypeId,
    genderFieldId: 'd1740.l' as FieldTypeId
};

export const FOND_MONTHLY_ATTENDANCE = {
    fond: 89,

    employeeFieldId: 'd8980.main' as FieldTypeId,
    personNumberFieldId: 'd8981.a' as FieldTypeId,
    contractTypeFieldId: 'd8981.e' as FieldTypeId,
    divisionFieldId: 'd8983.a' as FieldTypeId,
    yearMonthFieldId: 'd8907' as FieldTypeId,
    superiorFieldId: 'd8984.a' as FieldTypeId,
    yearMonthFieldFilterSearchParam: 'field_d8907_a_val',
    stateFieldId: 'd8905.s' as FieldTypeId,
    employerFieldId: 'd8982.a' as FieldTypeId,
    textNoteFieldId: 'd8990.t' as FieldTypeId,

    overtimePaymentStateFieldId: 'd8920.a' as FieldTypeId,
    onCallPaymentStateFieldId: 'd8920.b' as FieldTypeId,
    idpPaymentStateFieldId: 'd8920.c' as FieldTypeId
};

export const FOND_PROJECT_BUSINESS_ITEM = {
    fond: 75,

    subfonds: [76, 78],
    nonDateSubfonds: [76],
    dateSubfonds: [78],

    WORK_ITEM: {
        fond: 76
    },
    INSTALLATION_ITEM: {
        fond: 78,

        dateValSearchField: 'field_d2030_d_val',
        parentLinkSearchField: 'field_d1002_main_link',
        dateFieldId: 'd2030.d' as FieldTypeId,
        customerProjectFieldId: 'd1670.main.z' as FieldTypeId
    }
};

export const FOND_REPORT = {
    fond: 10,

    subfondsForApproval: [20, 21, 22, 26, 37], // All supported report subfonds
    subfondsForLogbookPairing: [20, 21, 22, 44, 45, 38],

    // Shared fields
    nameFieldId: 'd245.a' as FieldTypeId,
    projectFieldId: 'd1002.main' as FieldTypeId,
    parentFieldId: 'd1002.main' as FieldTypeId,
    parentLinkSearchField: 'field_d1002_main_link',
    projectItemFieldId: 'd2025.main' as FieldTypeId,
    dateFieldId: 'd2030.d' as FieldTypeId,
    dateValSearchField: 'field_d2030_d_val',
    stateFieldId: 'd2096.s' as FieldTypeId,
    customerProjectFieldId: 'd1670.main.z' as FieldTypeId,
    customerProjectLinkSearchField: 'field_d1670_main_link',
    textNoteFieldId: 'd2091.a' as FieldTypeId,
    descriptionFieldId: 'd2010.a' as FieldTypeId,
    referenceFieldId: 'd2012.a' as FieldTypeId,
    installationLogbookFieldId: 'd2025.main' as FieldTypeId,
    workerFieldId: 'd2050.a' as FieldTypeId,
    mzdovaPolozkaWorkerFieldId: 'd2051.main' as FieldTypeId,
    machineFieldId: 'd2060.main' as FieldTypeId,
    divisionFieldId: 'd2037.a' as FieldTypeId,
    reportedPersonStandardFieldId: 'd2070.m' as FieldTypeId,
    reportedPersonOvertimeFieldId: 'd2070.n' as FieldTypeId,
    reportedMachineFieldId: 'd2070.o' as FieldTypeId,
    chargedPersonStandardFieldId: 'd2070.m' as FieldTypeId,
    chargedPersonOvertimeFieldId: 'd2070.n' as FieldTypeId,
    chargedMachineFieldId: 'd2070.o' as FieldTypeId,
    personUnitFieldId: 'd2054.main.j' as FieldTypeId,
    machineUnitFieldId: 'd2064.main.j' as FieldTypeId,

    stateValueSearchField: 'field_d2096_s_val',

    // Report state chip styles
    STATE_CHIP_STYLES: {
        [SUTOR_REPORT_STATES.TO_CONFIRM]: 'accent-blue-new',
        [SUTOR_REPORT_STATES.CONFIRMED]: 'success-new',
        [SUTOR_REPORT_STATES.RETURNED]: 'danger-new'
    } as const,

    // Subfonds
    MACHINE_WITH_OPERATOR: {
        fond: 20
    },
    WORK: {
        fond: 21,

        typeFieldId: 'd2035.t' as FieldTypeId,
        placeFieldId: 'd2094.a' as FieldTypeId,
        partnerFieldId: 'd2093.a' as FieldTypeId,
        jobTypeFieldId: 'd2040' as FieldTypeId
    },
    MACHINE: {
        fond: 22
    },
    ABSENCE: {
        fond: 26,

        typeFieldId: 'd2035.t' as FieldTypeId
    },
    OPERATION: {
        fond: 37
    },
    OPTIMISATION: {
        fond: 24,

        typeFieldId: 'd2035.t' as FieldTypeId
    }
};

export const FOND_JOB_FILE = {
    fond: 83,

    birthNumberFieldId: 'd1845.b' as FieldTypeId,
    identityCardNumberFieldId: 'd1870.m' as FieldTypeId,
    genderFieldId: 'd1845.l' as FieldTypeId,
    birthDateFieldId: 'd1845.a' as FieldTypeId,
    birthPlaceFieldId: 'd1845.c' as FieldTypeId,
    nationalityFieldId: 'd1845.f' as FieldTypeId,
    idCardNumberFieldId: 'd1870.d' as FieldTypeId,
    idCardValidityFieldId: 'd1870.e' as FieldTypeId,
    educationLevelFieldId: 'd1845.j' as FieldTypeId,
    educationInstitutionFieldId: 'd1845.k' as FieldTypeId,
    healthInsuranceCodeFieldId: 'd1870.g' as FieldTypeId,
    maternityLeaveStartFieldId: 'd1860.a' as FieldTypeId,
    maternityLeaveEndFieldId: 'd1860.b' as FieldTypeId,
    parentalLeaveStartFieldId: 'd1860.c' as FieldTypeId,
    parentalLeaveEndFieldId: 'd1860.d' as FieldTypeId,
    invalidityFieldId: 'd1870.i' as FieldTypeId,
    claimedChildrenFieldId: 'd1860.e' as FieldTypeId,
    unclaimedChildrenFieldId: 'd1860.f' as FieldTypeId,
    distrainmentTypeFieldId: 'd1860.g' as FieldTypeId // Exekuce
};

export const FOND_EMPLOYMENT_STATUS = {
    fond: 88,

    personalNumberFieldId: 'd1815.a' as FieldTypeId,
    employmentStartDateFieldId: 'd1820.b' as FieldTypeId,
    trialPeriodEndDateFieldId: 'd1820.c' as FieldTypeId,
    employmentEndDateFieldId: 'd1820.d' as FieldTypeId,
    workPositionFieldId: 'd1825.a' as FieldTypeId,
    divisionFieldId: 'd1851.a' as FieldTypeId,
    bossFieldId: 'd1852' as FieldTypeId,
    contractTypeFieldId: 'd1820.e' as FieldTypeId,
    contractDateFieldId: 'd1820.f' as FieldTypeId,
    contractCategoryFieldId: 'd1820.k' as FieldTypeId,
    employerFieldId: 'd1850.a' as FieldTypeId,
    employerIcoFieldId: 'd1850.i' as FieldTypeId,
    typeOfTerminationFieldId: 'd1861.b' as FieldTypeId,
    sideOfTerminationFieldId: 'd1861.a' as FieldTypeId,
    reasonForTerminationFieldId: 'd1861.c' as FieldTypeId,
    textNoteFieldId: 'd1890.t' as FieldTypeId
};