import register from '@kpsys/angularjs-register';
import {sutorReportReferencesRoutes} from 'src/features/erp-sutor/pages/reports/report-references-page/lib/sutor-report-references.routes';
import {SutorHomePageService} from 'src/features/erp-sutor/pages/homepage/services/sutor-home-page.service';
import {SutorHomePageDataService} from 'src/features/erp-sutor/pages/homepage/services/sutor-home-page.data-service';
import {SutorReportReferencesService} from 'src/features/erp-sutor/pages/reports/report-references-page/lib/sutor-report-references.service';
import {sutorReportsProcessingRoutes} from './pages/reports/reports-processing-page/lib/sutor-reports-processing.routes';
import {SutorReportsProcessingService} from './pages/reports/reports-processing-page/lib/sutor-reports-processing.service';
import {SutorReportsApprovingService} from './pages/reports/reports-approving-page/lib/sutor-reports-approving.service';
import {sutorReportsApprovingRoutes} from './pages/reports/reports-approving-page/lib/sutor-reports-approving.routes';
import {AttendanceCalendarDataService} from './components/attendance-calendar/attendance-calendar.data-service';
import {AttendanceCalendarService} from './components/attendance-calendar/attendance-calendar.service';
import {TabbedSubpagesUrlManagementService} from 'src/features/erp/components/erp-tabbed-subpages/tabbed-subpages-url-management.service';
import {SutorReportsOptimisationService} from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/services/sutor-reports-optimisation.service';
import {SutorReportsOptimisationDataService} from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/services/sutor-reports-optimisation.data-service';
import {dataViewRoutes} from 'src/features/erp-sutor/pages/data-view/data-view.routes';
import {ReportReferencesDataService} from 'src/features/erp-sutor/pages/reports/report-references.data-service';
import {SutorLogbookPairingService} from './pages/project-detail/logbook-pairing/services/sutor-logbook-pairing.service';
import {MonthAttendanceDataService} from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/monthattendace/services/monthattendace.data-service';
import {MonthAttendanceService} from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/monthattendace/services/monthattendance.service';
import {SutorUserDetailDataService} from 'src/features/erp-sutor/pages/user-detail/sutor-user-detail.data-service';
import {SutorUserDetailService} from 'src/features/erp-sutor/pages/user-detail/sutor-user-detail.service';

export default register('portaro.features.sutor', ['ui.router'])
    // Tabbed subpages URL management
    .service(TabbedSubpagesUrlManagementService.serviceName, TabbedSubpagesUrlManagementService)

    // Work attendance calendar
    .service(AttendanceCalendarDataService.serviceName, AttendanceCalendarDataService)
    .service(AttendanceCalendarService.serviceName, AttendanceCalendarService)

    // Data-view
    .config(dataViewRoutes)

    // Landing
    .service(SutorHomePageDataService.serviceName, SutorHomePageDataService)
    .service(SutorHomePageService.serviceName, SutorHomePageService)

    // Report references
    .config(sutorReportReferencesRoutes)
    .service(ReportReferencesDataService.serviceName, ReportReferencesDataService)
    .service(SutorReportReferencesService.serviceName, SutorReportReferencesService)

    // Report reference processing
    .config(sutorReportsProcessingRoutes)
    .service(SutorReportsProcessingService.serviceName, SutorReportsProcessingService)

    // Reports approving
    .config(sutorReportsApprovingRoutes)
    .service(SutorReportsApprovingService.serviceName, SutorReportsApprovingService)

    // Report optimisation
    .service(SutorReportsOptimisationDataService.serviceName, SutorReportsOptimisationDataService)
    .service(SutorReportsOptimisationService.serviceName, SutorReportsOptimisationService)

    // Logbook pairing
    .service(SutorLogbookPairingService.serviceName, SutorLogbookPairingService)

    // Month attendance
    .service(MonthAttendanceDataService.serviceName, MonthAttendanceDataService)
    .service(MonthAttendanceService.serviceName, MonthAttendanceService)

    // Employee detail
    .service(SutorUserDetailDataService.serviceName, SutorUserDetailDataService)
    .service(SutorUserDetailService.serviceName, SutorUserDetailService)

    .name();