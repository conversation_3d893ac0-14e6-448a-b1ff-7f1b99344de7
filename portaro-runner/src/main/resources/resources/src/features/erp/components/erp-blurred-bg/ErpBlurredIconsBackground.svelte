<script lang="ts">
    import {range} from 'shared/utils/array-utils';
    import FloatingIcon from './FloatingIcon.svelte';
</script>

<div class="erp-blurred-logos-background-container">
    {#each range(1, 15) as i (i)}
        <FloatingIcon>
            <img class="sutor-logo-icon" alt="sutor-logo" src="/erp/assets/logo-no-text.svg"/>
        </FloatingIcon>
    {/each}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .erp-blurred-logos-background-container {
        border-top-left-radius: @border-radius-xxl;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        flex: 1;
        z-index: -1;
        overflow: hidden;
        filter: blur(75px);

        .sutor-logo-icon {
            user-select: none;
            height: 500px;
            opacity: 0.4;
        }
    }
</style>