import type {ActionResponse, ProvidedSeeking, SeekedSeeking, Seeking, SeekingProvision} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';
import {transferify} from 'shared/utils/data-service-utils';
import {uuid} from 'shared/utils/custom-utils';

/**
 * @ngdoc service
 * @name seekingDataService
 * @module portaro.uc.user
 *
 *
 * @description
 * Data service for providing exemplar exchange between buildings
 */
export default class SeekingDataService {
    public static readonly serviceName = 'seekingDataService';

    public static readonly GET_ROUTE = 'seekings';
    public static readonly EDIT_ROUTE = 'seekings/edit';
    public static readonly CANCEL_ROUTE = 'seekings/cancel';
    public static readonly CREATE_REQUEST_ROUTE = 'seekings/create-request';
    public static readonly CREATE_PROVIDED_ROUTE = 'seekings/create-provided';
    public static readonly CREATE_ROUTE = 'seekings/create-effective';
    public static readonly COMMENCE_ROUTE = 'seekings/commence';
    public static readonly CREATE_PROVISION_ROUTE = 'seekings/create-provision';
    public static readonly AUTO_SEARCH_PROVISIONS_ROUTE = 'seekings/auto-search-provisions';
    public static readonly EDIT_PROVISION_ROUTE = 'seekings/provisions/edit';
    public static readonly CANCEL_PROVISION_ROUTE = 'seekings/provisions/cancel';
    public static readonly ACTIVATE_UPCOMING_PROVISION_ROUTE = 'seekings/upcoming-provision-activate';
    public static readonly EDIT_SEEKED_ACTIVE_PROVISION_ROUTE = 'seekings/active-provision-edit';
    public static readonly EDIT_PROVIDED_ACTIVE_PROVISION_PATH = 'seekings/provided-active-provision-edit';
    public static readonly SEND_ACTIVE_PROVISION_MESSAGE_ROUTE = 'seekings/send-active-provision-message';
    public static readonly ACCEPT_ACTIVE_PROVISION_ROUTE = 'seekings/accept-active-provision';
    public static readonly CONDITIONALLY_ACCEPT_ACTIVE_PROVISION_ROUTE = 'seekings/conditionally-accept-active-provision';
    public static readonly ACCEPT_ACTIVE_PROVISION_CONDITION_ROUTE = 'seekings/accept-active-provision-condition';
    public static readonly SEND_ACTIVE_PROVISION_ROUTE = 'seekings/send-active-provision-exemplar';
    public static readonly RECEIVE_ACTIVE_PROVISION_EXEMPLAR_ROUTE = 'seekings/receive-active-provision-exemplar';
    public static readonly SEND_BACK_ACTIVE_PROVISION_ROUTE = 'seekings/send-back-active-provision-exemplar';
    public static readonly RECEIVE_BACK_ACTIVE_PROVISION_ROUTE = 'seekings/receive-back-active-provision-exemplar';
    public static readonly SEEKER_CANCEL_ACTIVE_PROVISION_ROUTE = 'seekings/seeker-cancel-active-provision';
    public static readonly PROVIDER_CANCEL_ACTIVE_PROVISION_ROUTE = 'seekings/provider-cancel-active-provision';
    public static readonly CREATE_ZISKEJ_LINK_ROUTE = 'ziskej/create-ziskej-link';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public async getById<E extends Seeking>(id: string): Promise<E> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.GET_ROUTE}/${id}`)
            .get<E>();
    }

    public async createSeekingRequest(object: any): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.CREATE_REQUEST_ROUTE}`)
            .post<ActionResponse>(transferify(object));
    }

    public async createSeekedSeeking(object: any): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.CREATE_ROUTE}`)
            .post<ActionResponse>(transferify(object));
    }

    public async createProvidedSeeking(object: any): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.CREATE_PROVIDED_ROUTE}`)
            .post<ActionResponse>(transferify(object));
    }

    public async editSeekingData(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.EDIT_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async cancel(seeking: Seeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.CANCEL_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async commenceIllSeeking(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.COMMENCE_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async createSeekingProvision(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.CREATE_PROVISION_ROUTE}`)
            .post({id: uuid(), seeking: seeking.id});
    }

    public async autoSearchSeekingProvisions(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.AUTO_SEARCH_PROVISIONS_ROUTE}`)
            .post({id: seeking.id});
    }

    public async editSeekingProvision(seeking: SeekedSeeking, seekingProvision: SeekingProvision): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.EDIT_PROVISION_ROUTE}`)
            .post({id: seekingProvision.id});
    }

    public async cancelSeekingProvision(seeking: SeekedSeeking, seekingProvision: SeekingProvision): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.CANCEL_PROVISION_ROUTE}`)
            .post({id: seekingProvision.id});
    }

    public async editSeekedSeekingActiveProvisionData(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.EDIT_SEEKED_ACTIVE_PROVISION_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async editProvidedSeekingActiveProvisionData(seeking: ProvidedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.EDIT_PROVIDED_ACTIVE_PROVISION_PATH}`)
            .post<ActionResponse>({id: seeking.id, confirmed: false});
    }

    public async activateSeekingUpcomingProvision(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.ACTIVATE_UPCOMING_PROVISION_ROUTE}`)
            .post<ActionResponse>({id: seeking.id, confirmed: false});
    }

    public async sendSeekingActiveProvisionMessage(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.SEND_ACTIVE_PROVISION_MESSAGE_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
        }

    public async acceptSeekingActiveProvision(seeking: ProvidedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.ACCEPT_ACTIVE_PROVISION_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async conditionallyAcceptSeekingActiveProvision(seeking: ProvidedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.CONDITIONALLY_ACCEPT_ACTIVE_PROVISION_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async acceptSeekingActiveProvisionCondition(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.ACCEPT_ACTIVE_PROVISION_CONDITION_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async receiveSeekingActiveProvisionExemplar(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.RECEIVE_ACTIVE_PROVISION_EXEMPLAR_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async sendIllSeekingActiveProvisionExemplar(seeking: ProvidedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.SEND_ACTIVE_PROVISION_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async sendBackSeekingActiveProvisionExemplar(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.SEND_BACK_ACTIVE_PROVISION_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async receiveBackIllSeekingActiveProvisionExemplar(seeking: ProvidedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.RECEIVE_BACK_ACTIVE_PROVISION_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async seekerCancelSeekingActiveProvision(seeking: Seeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.SEEKER_CANCEL_ACTIVE_PROVISION_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async providerCancelSeekingActiveProvision(seeking: Seeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.PROVIDER_CANCEL_ACTIVE_PROVISION_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }

    public async createZiskejLink(seeking: SeekedSeeking): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${SeekingDataService.CREATE_ZISKEJ_LINK_ROUTE}`)
            .post<ActionResponse>({id: seeking.id});
    }
}
