import {ngAsync} from 'shared/utils/ng-@decorators';
import type {ActionResponse, Loan} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class CancellationDataService {
    public static serviceName = 'cancellationDataService';

    public static readonly LOAN_ITEMS_ROUTE = 'loan-items';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    // TODO asi bych zmenil POST na DELETE (backend)
    @ngAsync()
    public async cancelLoan(loan: Loan): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${CancellationDataService.LOAN_ITEMS_ROUTE}/${loan.id}/cancel`)
            .post({loan: loan.id});
    }

}