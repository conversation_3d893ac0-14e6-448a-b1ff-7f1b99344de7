<script lang="ts">
    import {pipe} from 'core/utils';
    import {getDateFormatter, getLocalization} from 'core/svelte-context/context';
    import type {Loan} from 'typings/portaro.be.types';
    import {Kind} from 'shared/constants/portaro.constants';
    import Label from 'shared/components/kp-label/Label.svelte';

    export let loan: Loan;

    const dateFormatter = getDateFormatter();
    const localize = getLocalization();
</script>

<div class="loan-mini">
    <div class="details">
        <div>
            <strong>{loan.state.text}</strong>
            <span class="text-muted">{localize(/* @kp-localization commons.byUser */ 'commons.byUser')}</span>
            <Label labeled={loan.user} explicitKind={Kind.KIND_USER}/>
        </div>

        {#if loan.exemplar}
            <div>
                <span class="text-muted">{localize(/* @kp-localization exemplar.Exemplar.abbr */ 'exemplar.Exemplar.abbr')}</span>
                {loan.exemplar.text}
            </div>
        {/if}

        <div>
            <span class="text-muted">{localize(/* @kp-localization department.Department.abbr */ 'department.Department.abbr')}</span>
            {loan.department.text}
        </div>
    </div>

    <div class="dates">
        {#if loan.lendDate}
            <div>{pipe(loan.lendDate, dateFormatter('d.M.yyyy'))}</div>
        {/if}

        {#if loan.endDate}
            <div>{pipe(loan.endDate, dateFormatter('d.M.yyyy'))}</div>
        {/if}
    </div>
</div>

<style lang="less">
    .loan-mini {
        display: flex;
        justify-content: space-between;
    }
</style>