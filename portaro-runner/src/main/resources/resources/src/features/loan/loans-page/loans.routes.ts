import type {StateProvider, Transition} from '@uirouter/angularjs';
import {includesAllFrom} from 'shared/utils/array-utils';
import {KpSvelteComponentWrapperComponent} from 'src/core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function loansRoutes($stateProvider: StateProvider) {
    let loansModule: {default: any;};

    $stateProvider
        .state('loans', {
            url: '/loans?loanState',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => loansModule.default,

                /*@ngInject*/
                props: ($transition$: Transition) => {
                    const loanState = $transition$.params()?.loanState;
                    const toProcess = Array.isArray(loanState) && loanState.length === 2 && includesAllFrom(loanState, ['UNSENT_RESERVATION', 'UNPROCESSED_ORDER']);
                    return {toProcess};
                }
            },
            lazyLoad: async () => {
                loansModule = await import(/* webpackChunkName: "loans-page" */ './KpLoansPage.svelte');
                return null;
            }
        });
}