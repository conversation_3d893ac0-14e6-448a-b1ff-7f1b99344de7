<script lang="ts">
    import type {Row} from '@tanstack/svelte-table';
    import type {HierarchicalRecordRow} from '../../lib/types';
    import type {RecordGridDataManager} from 'src/features/record-grid/lib/record-grid-data-manager';
    import type {DepartmentResponse, RecordHolding} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {findFirstOrGetDefault, isNotEmpty} from 'shared/utils/array-utils';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import KpDropdownMenuButton from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuButton.svelte';
    import KpDropdownMenuItem from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuItem.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';
    import {exists} from 'shared/utils/custom-utils';

    type DepartmentWithOptionalHolding = {department: DepartmentResponse, holding: RecordHolding | null};

    export let recordGridDataManager: RecordGridDataManager;
    export let row: Row<HierarchicalRecordRow>;

    $: currentRecordRow = row.original;
    $: visibleRecordHoldings = currentRecordRow.visibleRecordHoldings ?? [];
    $: assignableDepartments = currentRecordRow.assignableDepartments ?? [];
    let departmentsWithOptionalHoldings: DepartmentWithOptionalHolding[];
    $: departmentsWithOptionalHoldings = assignableDepartments.map((department) => ({department, holding: findAssignedHolding(department)}));
    $: onlySingleHolding = visibleRecordHoldings.length === 1;

    function findAssignedHolding(department: DepartmentResponse): RecordHolding | null {
        return findFirstOrGetDefault(visibleRecordHoldings, (holding) => holding.department.id === department.id, null);
    }

    let processing = false;

    async function handleShareWithDepartmentClick(department: DepartmentResponse) {
        processing = true;
        await recordGridDataManager.createRecordHolding(currentRecordRow, department);
        processing = false;
    }

    async function handleUnshareWithDepartmentClick(recordHolding: RecordHolding) {
        processing = true;
        await recordGridDataManager.deleteRecordHolding(currentRecordRow, recordHolding);
        processing = false;
    }
</script>

<div class="kp-grid-holdings-management-cell">
    {#if isNotEmpty(departmentsWithOptionalHoldings)}
        <KpDropdownMenuButton isDisabled="{processing}" buttonSize="xs" additionalButtonClasses="share-with-btn">
            <svelte:fragment slot="button">
                <IconedContent icon="link">
                    <KpLoadingInline showWhen="{processing}" size="xs"/>
                </IconedContent>
            </svelte:fragment>

            <svelte:fragment slot="menu">
                {#each departmentsWithOptionalHoldings as {department, holding} (department.id)}
                    {#if exists(holding)}
                        <KpDropdownMenuItem isDisabled="{onlySingleHolding}" on:click={() => handleUnshareWithDepartmentClick(holding)}>
                            <IconedContent trailingIcon="check" justify="start">
                                {pipe(department, loc())}
                            </IconedContent>
                        </KpDropdownMenuItem>
                    {:else}
                        <KpDropdownMenuItem on:click={() => handleShareWithDepartmentClick(department)}>
                            {pipe(department, loc())}
                        </KpDropdownMenuItem>
                    {/if}
                {/each}
            </svelte:fragment>
        </KpDropdownMenuButton>
    {/if}

    {#each visibleRecordHoldings as recordHolding(recordHolding.id)}
        <KpChipTag chipSize="sm" squaredCorners customPadding={5}>
            {pipe(recordHolding.department, loc())}
        </KpChipTag>
    {/each}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .kp-grid-holdings-management-cell {
        display: flex;
        align-items: center;
        overflow: hidden;
        height: 100%;
        gap: @spacing-xs;

        :global(.share-with-btn) {
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
</style>