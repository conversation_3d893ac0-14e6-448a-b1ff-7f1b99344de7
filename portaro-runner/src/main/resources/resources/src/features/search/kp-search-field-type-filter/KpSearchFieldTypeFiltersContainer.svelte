<script lang="ts">
    import type {FondFieldTypeDefinitions} from 'src/features/record/field-types/types';
    import type {RecordSearchParams} from 'typings/portaro.be.types';
    import type {Conjunction} from 'src/features/search/search-criteria/criteria.types';
    import type {FilteredValue} from 'src/features/search/kp-search-field-type-filter/search-field-type-filters.context';
    import {onDestroy, onMount} from 'svelte';
    import {getInjector} from 'core/svelte-context/context';
    import {createSearchFieldTypeFiltersContext} from 'src/features/search/kp-search-field-type-filter/search-field-type-filters.context';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {FieldTypeDataService} from 'src/features/record/field-types/field-type-data.service';
    import {getSearchContext} from 'src/features/search/kp-search-context/search-context';
    import {transferify} from 'shared/utils/data-service-utils';
    import {isEqual} from 'lodash-es';

    export let fondId: number;
    export let fieldTypeDefinitions: FondFieldTypeDefinitions | null = null;

    const fieldTypeDataService = getInjector().getByClass(FieldTypeDataService);
    const context = createSearchFieldTypeFiltersContext();
    const searchContext = getSearchContext<unknown, RecordSearchParams>();

    export let anyFiltersActive = false; // For binding from parent

    const filteredValuesSubscription = context.filteredValues$.subscribe((filteredValues) => {
        const value = createQt(filteredValues);
        anyFiltersActive = exists(value) && Object.keys(filteredValues).length > 0;

        const newQt = value === null ? null : JSON.stringify(value);
        const currentQt = searchContext.getFrontendParams().qt ?? null;

        if (isEqual(newQt, currentQt) || !newQt) {
            return;
        }

        searchContext.newSearchWithPartialParams({qt: newQt});
    });

    onMount(async () => {
        if (exists(fieldTypeDefinitions)) {
            context.setFieldTypeDefinitions(fieldTypeDefinitions);
            return;
        }

        const fondFieldTypeDefinitions = await fieldTypeDataService.loadFondFieldTypeDefinitions(fondId);
        context.setFieldTypeDefinitions(fondFieldTypeDefinitions);
    });

    onDestroy(() => {
        cleanup(filteredValuesSubscription);
    });

    function createQt(filteredValues: Record<string, FilteredValue>): Conjunction | null {
        if (Object.keys(filteredValues).length === 0) {
            return null;
        }

        const createSearchObject = (value: FilteredValue) => {
            if (value.datatype?.name === 'DATE' && typeof value.val === 'string') {
                const dateValue = value.val.replace(/-/g, '');

                if (exists(value.filterType)) {
                    return {
                        between: {
                            to: value.filterType === 'eqOrLess' ? dateValue : null,
                            from: value.filterType === 'eqOrMore' ? dateValue : null
                        }
                    };
                }

                return {
                    eq: {
                        value: dateValue
                    }
                };
            }

            if (typeof value.val === 'string') {
                return {
                    startsWithWords: {
                        value: value.val
                    }
                };
            }

            if (typeof value.val === 'number') {
                if (!exists(value.filterType)) {
                    return {
                        between: {
                            from: value.val,
                            to: value.val
                        }
                    };
                }

                return {
                    between: {
                        to: value.filterType === 'eqOrLess' ? value.val : null,
                        from: value.filterType === 'eqOrMore' ? value.val : null
                    }
                };
            }

            return {
                eq: {
                    value: transferify(value.val, true)
                }
            };
        };

        return {
            and: Object.entries(filteredValues).map(([filterParam, value]) => {
                return {
                    field: {
                        id: filterParam,
                        text: filterParam
                    },
                    ...createSearchObject(value)
                };
            })
        };
    }
</script>

<slot/>