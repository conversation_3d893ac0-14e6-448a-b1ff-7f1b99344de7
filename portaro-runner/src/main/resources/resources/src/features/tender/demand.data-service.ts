import type {Demand} from './types';
import type {AjaxService} from 'core/data-services/ajax.service';
import {ngAsync} from 'shared/utils/ng-@decorators';
import type {UserId} from 'src/features/user/types';

export class DemandDataService {
    public static serviceName = 'demandDataService';

    public static readonly ROUTE = 'demands';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getDemandsByTenderId(tenderId: number, offersScope: 'none' | 'single-supplier' | 'all-suppliers', offersSupplier: UserId | null): Promise<Demand[]> {
        offersSupplier = offersSupplier ? offersSupplier : undefined; // we dont want to send ~null~ value to the server
        return this.ajaxService
            .createRequest(DemandDataService.ROUTE)
            .get({tender: tenderId, offersScope, offersSupplier});
    }
}