import type {OfferingStateDto, Tender} from './types';
import type {ViewableFile} from 'typings/portaro.be.types';
import type {FileUploadService} from '../file/file-upload.service';
import type {AjaxService} from 'core/data-services/ajax.service';
import {ngAsync} from 'shared/utils/ng-@decorators';
import {responseToData} from 'shared/utils/data-service-utils';
import type {UserId} from 'src/features/user/types';

export class TenderDataService {
    public static serviceName = 'tenderDataService';

    public static readonly ROUTE = 'tenders';
    public static readonly AGREEMENTS_CONFIRM_ROUTE = 'agreements/confirm';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService, private fileUploadService: FileUploadService) {
    }

    @ngAsync()
    public async query(params: {active?: boolean}): Promise<Tender[]> {
        return this.ajaxService
                   .createRequest(TenderDataService.ROUTE)
                   .get(params);
    }

    @ngAsync()
    public async getById(id: number): Promise<Tender> {
        return this.ajaxService
                   .createRequest(`${TenderDataService.ROUTE}/${id}`)
                   .get();
    }

    @ngAsync()
    public async getOfferingStates(tenderId: number, activeUserId: UserId): Promise<OfferingStateDto[]> {
        return this.ajaxService
                   .createRequest(`${TenderDataService.ROUTE}/${tenderId}/offering-states`)
                   .get<OfferingStateDto[]>({supplier: activeUserId});
    }

    @ngAsync()
    public async confirmAgreement(tenderId: number, supplierUserId: UserId): Promise<any> {
        return this.ajaxService
                   .createRequest(`${TenderDataService.ROUTE}/${tenderId}/${TenderDataService.AGREEMENTS_CONFIRM_ROUTE}`)
                   .get({supplier: supplierUserId});
    }

    @ngAsync()
    public async uploadAgreement(tenderId: number, supplierUserId: UserId, file: File): Promise<ViewableFile> {
        try {
            return await this.fileUploadService.upload(`/api/tenders/${tenderId}/agreements/signed?supplier=${supplierUserId}`, file)
        } catch (httpError) {
            throw responseToData(httpError);
        }
    }
}