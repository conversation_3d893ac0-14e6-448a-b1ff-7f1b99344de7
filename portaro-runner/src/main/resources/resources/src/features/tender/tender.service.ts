import type {Auth} from 'typings/portaro.be.types';
import type {Demand, Offer, OfferRequest, Tender, ViewableTender} from './types';
import type {TenderDataService} from './tender.data-service';
import type {DemandDataService} from './demand.data-service';
import type {OfferDataService} from './offer.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import {OfferingState} from './offering-state';
import {resolveErrorMessage} from 'shared/utils/error-utils';
import {states} from 'shared/constants/portaro.constants';

export class TenderService {
    public static serviceName = 'tenderService';

    /*@ngInject*/
    constructor(private currentAuth: Auth, private tenderDataService: TenderDataService, private demandDataService: DemandDataService, private offerDataService: OfferDataService, private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async getAll(params: {active?: boolean}): Promise<Tender[]> {
        return this.tenderDataService.query(params);
    }

    public async getTenderById(id: number): Promise<Tender> {
        return this.tenderDataService.getById(id);
    }

    public async onAgreementFileSelect(files: FileList, tender: ViewableTender): Promise<void> {
        try {
            await this.tenderDataService.uploadAgreement(tender.id, 'current', files[0]);
            await this.reloadOfferingStates(tender);
        } catch (e) {
            alert(resolveErrorMessage(e));
        }
    }

    public async getDemandsByTender(tender: ViewableTender): Promise<Demand[]> {
        let offersScope: 'none' | 'single-supplier' | 'all-suppliers' = 'none';
        if (this.currentAuth.activeUser.role.includes('ROLE_LIBRARIAN')) {
            offersScope = 'all-suppliers';
        } else if (this.currentAuth.activeUser.role.includes('ROLE_SUPPLIER')) {
            offersScope = 'single-supplier';
        }
        const offersSupplier = offersScope === 'single-supplier' ? 'current' : null;
        const demands = await this.demandDataService.getDemandsByTenderId(tender.id, offersScope, offersSupplier);
        demands.map((demand) => this.fillDemand(demand));
        return demands;
    }

    public async confirmAgreement(tender: ViewableTender): Promise<void> {
        await this.tenderDataService.confirmAgreement(tender.id, 'current');
        await this.reloadOfferingStates(tender);
    }

    public async reloadOfferingStates(tender: ViewableTender): Promise<void> {
        const offeringStates = await this.newOfferingStates(tender);
        tender.offers$.next(offeringStates);
    }

    public async newOfferingStates(tender: ViewableTender): Promise<OfferingState[]> {
        const offeringStatesDto = await this.tenderDataService.getOfferingStates(tender.id, 'current');
        const offeringStates = offeringStatesDto.map((dto) => new OfferingState(dto));
        if (offeringStates.length > 1) {
            throw new Error('Multiple offeringStates are not supported yet');
        }
        return offeringStates;
    }

    public async removeOffer(offer: Offer, tender: ViewableTender): Promise<void> {
        await this.offerDataService.remove(offer.id);
        await this.reloadOfferingStates(tender);
    }

    public async saveOffer(offer: OfferRequest): Promise<Offer> {
        try {
            return await this.offerDataService.save(offer);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    private fillDemand(demand: Demand): void {
        demand.offerCreation = {
            state: states.NORMAL,
            offer: {
                demandId: demand.id,
                quantity: demand.desiredQuantity,
                price: null,
                note: null
            }
        };
    }
}