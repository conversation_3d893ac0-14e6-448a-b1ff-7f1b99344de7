import type {UUID} from 'typings/portaro.be.types';
import type {Observable} from 'rxjs';
import {BehaviorSubject, map} from 'rxjs';
import type {SseService} from 'shared/realtime/sse.service';
import type {ThreadsDataService} from 'src/features/threads/services/threads.data-service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {LogService} from 'core/logging/log.service';
import type CurrentAuthService from 'shared/services/current-auth.service';
import {exists} from 'shared/utils/custom-utils';
import ThreadToastNotificationCard from 'src/features/threads/shared-components/ThreadToastNotificationCard.svelte';
import type {MessageReceivedEventData, ParticipantAddedEventData, ParticipantRemovedEventData, RichThread, Thread, ThreadCreatedEventData, ThreadDeletedEventData, ThreadPlacement} from '../threads.types';

export interface ThreadsContext {
    initialized: boolean;
    loadError: boolean;
    threads: Thread[];
    unreadMessages: Map<UUID, number>;
    unreadMentions: Map<UUID, number>;
    unreadThreadsCount: number;
}

type OpenThreadId = `${UUID}-${ThreadPlacement}`;

export class ThreadsContextService {
    public static serviceName = 'threadsContextService';

    private readonly eventTarget: EventTarget;
    private readonly context: BehaviorSubject<ThreadsContext>;
    private readonly openThreads: BehaviorSubject<OpenThreadId[]>;

    /*@ngInject*/
    constructor(private sseService: SseService,
                private threadsDataService: ThreadsDataService,
                private currentAuthService: CurrentAuthService,
                private logService: LogService,
                private toastMessageService: ToastMessageService) {

        this.eventTarget = new EventTarget();
        this.context = new BehaviorSubject<ThreadsContext>(this.createThreadContextObject(false, false, []));
        this.openThreads = new BehaviorSubject<OpenThreadId[]>([]);
    }

    public setup(): void {
        // New messages
        this.sseService.addMessageListener<MessageReceivedEventData>('new-message', (data) => this.handleThreadMessage(data));
        this.sseService.addMessageListener<MessageReceivedEventData>('mention', (data) => this.handleThreadMessage(data));

        // Thread creation/deletion
        this.sseService.addMessageListener<ThreadCreatedEventData>('thread-created', (data) => this.handleThreadCreated(data));
        this.sseService.addMessageListener<ThreadDeletedEventData>('thread-deleted', (data) => this.handleThreadDeleted(data));

        // Participant changes
        this.sseService.addMessageListener<ParticipantAddedEventData>('participant-added', (data) => this.handleParticipantAdded(data));
        this.sseService.addMessageListener<ParticipantRemovedEventData>('participant-removed', (data) => this.handleParticipantRemoved(data));
        this.sseService.addMessageListener<ParticipantAddedEventData>('user-joined', (data) => this.handleParticipantAdded(data));
        this.sseService.addMessageListener<ParticipantRemovedEventData>('user-left', (data) => this.handleParticipantRemoved(data));

        // Reload threads on login
        this.currentAuthService.currentAuth$().subscribe(() => {
            this.loadUserThreads();
        });
    }

    public addOpenThread(thread: Thread, placement: ThreadPlacement): void {
        const currentOpenThreads = this.openThreads.getValue();
        const threadId: OpenThreadId = `${thread.id}-${placement}`;

        if (!currentOpenThreads.includes(threadId)) {
            this.openThreads.next([...currentOpenThreads, threadId]);
        }
    }

    public removeOpenThread(thread: Thread, placement: ThreadPlacement): void {
        const currentOpenThreads = this.openThreads.getValue();
        const threadId: OpenThreadId = `${thread.id}-${placement}`;
        const filteredThreads = currentOpenThreads.filter((id) => id !== threadId);

        if (filteredThreads.length !== currentOpenThreads.length) {
            this.openThreads.next(filteredThreads);
        }
    }

    public addThread(newThread: Thread): void {
        const currentContextValue = this.context.getValue();
        this.context.next({
            ...currentContextValue,
            threads: [newThread, ...currentContextValue.threads]
        });
    }

    public removeThread(threadIdToRemove: UUID): void {
        const currentContextValue = this.context.getValue();
        this.context.next({
            ...currentContextValue,
            threads: currentContextValue.threads.filter((thread) => thread.id !== threadIdToRemove)
        });
    }

    public async updateThreadInContext(threadIdToUpdate: UUID): Promise<void> {
        const updatedThread = await this.threadsDataService.getThreadById(threadIdToUpdate);
        if (!exists(updatedThread)) {
            return;
        }

        const currentContextValue = this.context.getValue();
        const updatedThreads = currentContextValue.threads.map((thread) => thread.id === updatedThread.id ? updatedThread : thread);
        this.context.next({
            ...currentContextValue,
            threads: updatedThreads
        });
    }

    public async markMessagesAsRead(thread: Thread): Promise<void> {
        const currentUser = this.currentAuthService.getCurrentAuthValue().activeUser;
        await this.threadsDataService.userReadAllThreadMessages(thread, currentUser.id);

        const currentContextValue = this.context.getValue();

        const unreadMessages = currentContextValue.unreadMessages;
        const unreadMentions = currentContextValue.unreadMentions;
        unreadMessages.delete(thread.id);
        unreadMentions.delete(thread.id);

        this.context.next({
            ...currentContextValue,
            unreadMessages,
            unreadMentions,
            unreadThreadsCount: this.countThreadThreads(unreadMessages)
        });
    }

    public isThreadOpen$(threadId: UUID, placement?: ThreadPlacement): Observable<boolean> {
        return this.openThreads.pipe(
            map((openThreads) => {
                if (!exists(placement)) {
                    return openThreads.some((id) => id.startsWith(threadId));
                }
                return exists(openThreads.find((id) => id === `${threadId}-${placement}`));
            })
        );
    }

    public getContextValue(): ThreadsContext {
        return this.context.getValue();
    }

    public get context$(): Observable<ThreadsContext> {
        return this.context.asObservable();
    }

    public get eventBus(): EventTarget {
        return this.eventTarget;
    }

    private async loadUserThreads(): Promise<void> {
        try {
            const threads = await this.threadsDataService.getUserThreads('current');
            this.context.next(this.createThreadContextObject(true, false, threads));
        } catch {
            this.context.next(this.createThreadContextObject(true, true, []));
        }
    }

    private createThreadContextObject(initialized: boolean, loadError: boolean, threads: RichThread[]): ThreadsContext {
        const unreadMessages = new Map(threads.map((thread) => [thread.id, thread.unreadMessagesCount]));
        const unreadMentions = new Map(threads.map((thread) => [thread.id, thread.unreadMentionsCount]));

        return {
            initialized,
            loadError,
            threads,
            unreadMessages,
            unreadMentions,
            unreadThreadsCount: this.countThreadThreads(unreadMessages)
        };
    }

    private handleThreadMessage(data: MessageReceivedEventData): void {
        if (this.isThreadOpen(data.threadId)) {
            return;
        }

        const currentContextValue = this.context.getValue();

        const unreadMessages = currentContextValue.unreadMessages;
        const currentUnreadCount = unreadMessages.get(data.threadId) || 0;
        unreadMessages.set(data.threadId, currentUnreadCount + 1);

        this.context.next({
            ...currentContextValue,
            unreadMessages,
            unreadThreadsCount: this.countThreadThreads(unreadMessages)
        });

        this.toastMessageService.showCustomComponentToast(ThreadToastNotificationCard, {
            messageData: data
        });
    }

    private async handleParticipantAdded(data: ParticipantAddedEventData): Promise<void> {
        const currentThreads = this.context.getValue().threads;
        const thread = currentThreads.find((t) => t.id === data.threadId);
        const currentAuth = this.currentAuthService.getCurrentAuthValue();

        if (!exists(thread) && data.addedParticipant.id === currentAuth.activeUser.id) {
            await this.loadThreadAndPutIntoContext(data.threadId);
            return;
        }

        await this.updateThreadInContext(data.threadId);
    }

    private async handleParticipantRemoved(data: ParticipantRemovedEventData): Promise<void> {
        const currentAuth = this.currentAuthService.getCurrentAuthValue();

        if (data.removedParticipant.id === currentAuth.activeUser.id) {
            this.removeThread(data.threadId);
            return;
        }

        await this.updateThreadInContext(data.threadId);
    }

    private async handleThreadCreated(data: ThreadCreatedEventData): Promise<void> {
        await this.loadThreadAndPutIntoContext(data.threadId);
    }

    // TODO: Vyřešit případ, kdy se smaže thread, který je otevřený na fullscreen page
    private handleThreadDeleted(data: ThreadDeletedEventData): void {
        this.removeThread(data.threadId);
    }

    private isThreadOpen(threadId: UUID): boolean {
        const currentOpenThreads = this.openThreads.getValue();
        return currentOpenThreads.some((id) => id.startsWith(threadId));
    }

    private async loadThreadAndPutIntoContext(threadId: UUID): Promise<void> {
        try {
            const thread = await this.threadsDataService.getThreadById(threadId);
            this.addThread(thread);
        } catch (e) {
            this.logService.error('Can not load created thread', e);
        }
    }

    private countThreadThreads(unreadMessages: Map<UUID, number>): number {
        let unreadThreads = 0;

        unreadMessages.forEach((count) => {
            if (count > 0) {
                unreadThreads++;
            }
        });

        return unreadThreads;
    };
}