<script lang="ts">
    import type {UserAccountReactivePageData, UserAccountStaticPageData} from '../types';
    import type {RelatedRecordsTable} from '../../../record/kp-related-records-table/types';
    import type {Tab<PERSON><PERSON>on, TabId, TabPageDefinitionsMap} from 'shared/ui-widgets/tabset/types';
    import {getPageContext} from 'shared/layouts/page-context';
    import {exists} from 'shared/utils/custom-utils';
    import SutorUserAccountAttendanceTab from './tab-pages/SutorUserAccountAttendanceTab.svelte';
    import SutorUserAccountDetailTab from './tab-pages/SutorUserAccountDetailTab.svelte';
    import KpMediaViewerInline from '../../../media-viewer/KpMediaViewerInline.svelte';
    import KpRelatedRecordsTable from '../../../record/kp-related-records-table/KpRelatedRecordsTable.svelte';
    import ErpTabbedSubpagesContainer from 'src/features/erp/components/erp-tabbed-subpages/ErpTabbedSubpagesContainer.svelte';
    import ErpPageLayout from 'src/features/erp/components/ErpPageLayout.svelte';
    import SutorUserAccountTopBar from 'src/features/user/kp-user-account-page/sutor-parts/components/SutorUserAccountTopBar.svelte';
    import TabbedSubpagesRouter from 'src/features/erp/components/erp-tabbed-subpages/TabbedSubpagesRouter.svelte';

    export let relatedTables: RelatedRecordsTable[] = [];

    const pageContext = getPageContext<UserAccountStaticPageData, UserAccountReactivePageData>();
    const userRecord = pageContext.staticData.userRecord;

    const tabButtons: TabButton[] = [
        {
            id: 'tab-detail',
            label: 'Přehled',
            icon: 'info'
        }
    ];

    const tabs: TabPageDefinitionsMap = {
        'tab-detail': {component: SutorUserAccountDetailTab}
    };

    if (exists(userRecord)) {
        // Add work attendance into the tabset
        tabButtons.push({
            id: 'tab-attendance',
            label: 'Docházka',
            icon: 'calendar'
        });

        tabs['tab-attendance'] = {component: SutorUserAccountAttendanceTab};

        // Add files explorer into the tabset
        tabButtons.push({
            id: 'tab-files',
            label: 'Soubory',
            icon: 'file',
            tabPageWithoutPadding: true
        });

        tabs['tab-files'] = {
            component: KpMediaViewerInline,
            props: {
                record: userRecord
            }
        };

        // Add related tables into tabset
        relatedTables.forEach((relatedTable) => {
            tabButtons.push({
                id: `tab-relatedtable-${relatedTable.tableId}`,
                label: relatedTable.title,
                icon: 'grid-alt'
            });
        });
    }

    function isTableTab(tabId: TabId): boolean {
        return tabId.includes('tab-relatedtable-');
    }
</script>

<ErpPageLayout pageClass="sutor-user-account-tabbed-page" gap="0px" withoutContentPadding>
    <SutorUserAccountTopBar/>

    <ErpTabbedSubpagesContainer {tabButtons} additionalClasses="user-account-tabbed-subpages" let:activeTab>
        {#if isTableTab(activeTab)}
            {@const relatedTable = relatedTables.find((table) => `tab-relatedtable-${table.tableId}` === activeTab)}

            <KpRelatedRecordsTable record="{userRecord}"
                                   fondId="{relatedTable.fondId}"
                                   relations="{relatedTable.relations}"
                                   referenceFieldTypeId="{relatedTable.referenceFieldTypeId}"/>
        {:else}
            <TabbedSubpagesRouter {tabs} {activeTab}/>
        {/if}
    </ErpTabbedSubpagesContainer>
</ErpPageLayout>