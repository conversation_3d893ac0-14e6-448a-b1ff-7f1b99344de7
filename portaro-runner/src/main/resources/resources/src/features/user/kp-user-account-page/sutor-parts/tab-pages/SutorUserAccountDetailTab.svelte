<script lang="ts">
    import type {UserAccountReactivePageData, UserAccountStaticPageData} from '../../types';
    import {getPageContext} from 'shared/layouts/page-context';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {exists} from 'shared/utils/custom-utils';
    import KpUserAccountAddressPart from '../../parts/KpUserAccountAddressPart.svelte';
    import KpUserAccountContactPart from '../../parts/KpUserAccountContactPart.svelte';
    import KpUserAccountInformationPart from '../../parts/KpUserAccountInformationPart.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import KpMasonryGrid from 'shared/layouts/masonry-grid/KpMasonryGrid.svelte';
    import KpMasonryGridItem from 'shared/layouts/masonry-grid/KpMasonryGridItem.svelte';
    import KpRecordDetailTable from '../../../../record/kp-record-detail-table/KpRecordDetailTable.svelte';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';

    const pageContext = getPageContext<UserAccountStaticPageData, UserAccountReactivePageData>();
    const user = pageContext.staticData.user;
    const userRecord = pageContext.staticData.userRecord;
</script>

<div class="user-name-container">
    <KpUserAvatar userRecordId="{user.rid}" sizePx="{84}"/>
    <h1>{pipe(user, loc())}</h1>
</div>

<KpMasonryGrid gridTemplateColumns="repeat(auto-fill, minmax(600px, 1fr))"
               additionalClasses="user-info-grid">

    {#if exists(userRecord)}
        <KpMasonryGridItem animate="{false}">
            <KpGenericPanel hasBodyPadding="{false}">
                <KpRecordDetailTable rows="{userRecord.detailTableRows}"/>
            </KpGenericPanel>
        </KpMasonryGridItem>
    {:else}
        <KpMasonryGridItem animate="{false}">
            <KpUserAccountInformationPart/>
        </KpMasonryGridItem>
    {/if}

    <KpMasonryGridItem animate="{false}">
        <KpUserAccountAddressPart/>
    </KpMasonryGridItem>

    <KpMasonryGridItem animate="{false}">
        <KpUserAccountContactPart/>
    </KpMasonryGridItem>
</KpMasonryGrid>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .user-name-container {
        display: flex;
        gap: @spacing-m;
        align-items: center;

        h1 {
            margin: 0;
            width: min-content;
            font-weight: 500;
        }
    }

    :global {
        .user-info-grid .detail-table {
            tr > th {
                padding: @spacing-sm @spacing-ml !important;
            }

            &.table-color-accented > thead > tr > th {
                color: @themed-text-default;
                border-bottom: 1px solid @themed-border-default;
            }
        }
    }
</style>