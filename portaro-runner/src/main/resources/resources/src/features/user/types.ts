import type {Link, Price, UUID} from 'typings/portaro.be.types';
import type {ViewLayout} from 'shared/layouts/grid-system/built-layout/types';
import type {LabeledMappedAction} from 'shared/defined-actions/types';

export interface DelayedReturnPenalty {
    penalizedLoansCount: number;
    price: Price;
}

export interface UsersView {
    canLoanReturnAny: boolean;
    canUserCreate: boolean;
    canSoftwareUserCreate: boolean;
    canPaymentsPrint: boolean;
}

export interface UserDetailView {
    viewLayout: ViewLayout;
    renewAllShowing: boolean;
    moneyShowing: boolean;
    loansShowing: boolean;
    userEditationShowing: boolean;
    enabledPaymentProviderNames: string[];
    canLoanLend: boolean;
    canLoanRenew: boolean;
    canLoanReturnAny: boolean;
    canLoanCancelAny: boolean;
    canLoanConfirmationPrint: boolean;
    canMigrateFromUserToAny: boolean;
    canSeekingRequestCreate: boolean;
    canPaymentDelete: boolean;
    canPaymentsReceiptPrint: boolean;
    canDebtDelete: boolean;
    canUserSdiRequestsShow: boolean;
    canUsersShow: boolean;
    canAuthSwitchActiveUser: boolean;
    canUserEdit: boolean;
    canUserDelete: boolean;
    canUserPreferencesEdit: boolean;
    canUserRegistrationAgreementPrint: boolean;
    canUserRegistrationPeriodExtend: boolean;
    canSendEmailToUser: boolean;
    canSendSmsToUser: boolean;
    canShowLibrarianMessage: boolean;
    canUserPasswordChange: boolean;
    canUserCredentialsDelete: boolean;
    canUserMojeIDLinkDelete: boolean;
    canUserBankIDLinkDelete: boolean;
    showUserDiscountRequestButton: boolean;
    showUserReceiptOfPaymentPrintButton: boolean;
    showUserRegistrationAgreementPrintButton: boolean;
    showUserLoanConfirmationPrintButton: boolean;
    showLibrarianMessage: boolean;
    exportLinks: Link[];
    userAttentionRequiringActions: LabeledMappedAction[];
    isSutorSutinLayout: boolean;
}

export type UserId = UUID | number | 'current';