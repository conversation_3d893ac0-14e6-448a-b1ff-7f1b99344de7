import type {BarcodeFormat} from './barcode-scanner.types';

export class Barcode {

    constructor(public readonly text: string,
                public readonly codeFormat: BarcodeFormat,
                public readonly error: string,
                public readonly image?: ImageData) {
    }

    public static equals(x: Barcode, y: Barcode): boolean {

        return x.text === y.text
            && x.codeFormat === y.codeFormat
            && x.error === y.error;
    }

    public isEmpty(): boolean {
        return !this.text;
    }
}