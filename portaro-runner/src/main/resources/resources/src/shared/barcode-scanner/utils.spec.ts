import {from} from 'rxjs';
import {validateResults} from './utils';
import {toArray} from 'rxjs/operators';
import {Barcode} from './barcode';
import {BarcodeFormat} from './barcode-scanner.types';

describe('barcode-scanner utils tests', () => {
    describe('validateResults', () => {

        const testBarcodes1 = [
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('QWEQWEQWETE', BarcodeFormat.EAN_13, ''),
            new Barcode('QWEQWEQWETE', BarcodeFormat.EAN_13, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('dsfdsfs', BarcodeFormat.EAN_13, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
        ];

        const testBarcodes2 = [
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('', BarcodeFormat.ANY, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('', BarcodeFormat.ANY, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('', BarcodeFormat.ANY, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
        ];

        const testBarcodes3 = [
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('gffsgdfhgfdhgfd', BarcodeFormat.EAN_13, ''),
            new Barcode('gffsgdfhgfdhgfd', BarcodeFormat.EAN_13, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('gffsgdfhgfdhgfd', BarcodeFormat.EAN_13, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            new Barcode('dsfdsfs', BarcodeFormat.EAN_13, ''),
            new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
        ]


       it('should get most frequent value', (done) => {

            const expectedBarcodes = [
                new Barcode('QWEQWEQWETE', BarcodeFormat.EAN_13, ''),
                new Barcode('QWEQWEQWETE', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            ]

            from(testBarcodes1).pipe(validateResults(true, 3), toArray()).subscribe(
                (barcodes) => {
                    expect(barcodes).toEqual(expectedBarcodes);
                    done();
                });
       });

        it('should allow empty values', (done) => {

            const expectedBarcodes = [
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('', BarcodeFormat.ANY, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            ]

            from(testBarcodes2).pipe(validateResults(true, 3), toArray()).subscribe(
                (barcodes) => {
                    expect(barcodes).toEqual(expectedBarcodes);
                    done();
                });
        });

        it('should not allow empty values', (done) => {

           const expectedBarcodes = [
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            ]

            from(testBarcodes2).pipe(validateResults(false, 3), toArray()).subscribe(
                (barcodes) => {
                    expect(barcodes).toEqual(expectedBarcodes);
                    done();
                });
        });

        it('should work with smaller buffer', (done) => {

             from(testBarcodes1).pipe(validateResults(false, 1), toArray()).subscribe(
                (barcodes) => {
                    expect(barcodes).toEqual(testBarcodes1);
                    done();
                });
        });

        it('should work with bigger buffer', (done) => {

            const expectedBarcodes = [
                new Barcode('gffsgdfhgfdhgfd', BarcodeFormat.EAN_13, ''),
                new Barcode('gffsgdfhgfdhgfd', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
                new Barcode('1234567891234', BarcodeFormat.EAN_13, ''),
            ]

            from(testBarcodes3).pipe(validateResults(false, 5), toArray()).subscribe(
                (barcodes) => {
                    expect(barcodes).toEqual(expectedBarcodes);
                    done();
                });
        });

    });
});
