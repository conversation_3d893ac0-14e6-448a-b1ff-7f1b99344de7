import type { BarcodeScanner, ScannerSettings} from '../barcode-scanner.types';
import {BarcodeFormat} from '../barcode-scanner.types';
import type { Subscription} from 'rxjs';
import {Subject} from 'rxjs';
import type {Exception, Result, BrowserCodeReader, DecodeHintType} from '@zxing/library';
import {Barcode} from '../barcode';
import {BarcodeFormatMapper} from '../barcode-format-mapper';


export class ZxingJsBarcodeScanner implements BarcodeScanner {

    private barcodeScanner: BrowserCodeReader;
    private formatMapper: BarcodeFormatMapper;
    private isScannerInitialized = false;
    private isScannerStarted = false;
    private result$: Subject<Barcode> = new Subject<Barcode>();
    private subscription: Subscription;


    constructor(private readonly videoSourceElement: HTMLVideoElement, private readonly scanSettings: ScannerSettings) {}

    public async lazyInitScanner() {
        const zxing= await import(/* webpackChunkName: "zxingjs-dist" */ '@zxing/library');
        this.formatMapper = new BarcodeFormatMapper(zxing.BarcodeFormat);
        const hints = this.constructDecodeHints(zxing.DecodeHintType, this.scanSettings);
        this.barcodeScanner = new zxing.BrowserBarcodeReader(this.scanSettings.timeBetweenScansInMillis, hints);
        this.barcodeScanner.reset();
        this.isScannerInitialized = true;
    }

    public async startScanner(onResult: (result: Barcode) => void): Promise<void> {

        if (!this.isScannerInitialized) {
            throw new Error('Scanner is not initialized')
        }

        if (this.isStarted()) {
            throw new Error('Scanner is already started');
        }

        this.isScannerStarted = true;
        this.subscription = this.result$.subscribe(onResult)

        await this.barcodeScanner.decodeFromVideoDevice(
            undefined,
            this.videoSourceElement,
            (result, error) => this.processResult(result, error));
    }

    public stopScanner(): void {

        if (!this.isStarted()) {
            return;
        }

        this.subscription.unsubscribe();

        this.barcodeScanner.stopAsyncDecode();
        this.barcodeScanner.stopContinuousDecode();
        this.barcodeScanner.reset();

        this.isScannerStarted = false;
    }

    public isStarted(): boolean {
        return this.isScannerStarted;
    }

    private constructDecodeHints(decodeHintType: typeof DecodeHintType, scanSettings: ScannerSettings) {

        const hints = new Map();

        hints.set(decodeHintType.TRY_HARDER, scanSettings.tryHarder);
        hints.set(decodeHintType.PURE_BARCODE, true);

        if (scanSettings.codeFormats?.length > 0) {
            const formats = scanSettings.codeFormats.map((format) => this.formatMapper.mapFromInternal(format));
            hints.set(decodeHintType.POSSIBLE_FORMATS, formats);
        }

        return hints;
    }

    private processResult(result: Result, error: Exception) {

        let barcode: Barcode;
        if (result) {
            barcode = new Barcode(result.getText(), this.formatMapper.mapToInternal(result.getBarcodeFormat()), '');
        } else {
            barcode = new Barcode('', BarcodeFormat.ANY, error.name);
        }
        this.result$.next(barcode);
    }
}