<script lang="ts">
    import type {CalendarDate, CalendarView} from 'shared/components/kp-calendar/types';
    import type {ButtonStyle, ComponentSize} from 'shared/ui-widgets/types';
    import type {Placement} from '@floating-ui/core';
    import {KpCalendarManager} from 'shared/components/kp-calendar/kp-calendar-manager';
    import {areCalendarDatesEqual, getPaddedMonthDays} from 'shared/components/kp-calendar/utils';
    import {createEventDispatcher, onDestroy} from 'svelte';
    import {fly} from 'svelte/transition';
    import {getCurrentLanguage} from 'core/svelte-context/context';
    import {cleanup, exists, uuid} from 'shared/utils/custom-utils';
    import {createFloatingActions} from 'svelte-floating-ui';
    import {floatingUiCommonSettings} from 'shared/utils/floating-ui-common-settings';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import KpPopoverPanel from 'shared/ui-widgets/popover/KpPopoverPanel.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let startDate: CalendarDate | null = null;
    export let selectedDate: CalendarDate | null = null;
    export let buttonStyle: ButtonStyle = 'default';
    export let buttonSize: ComponentSize = 'md';
    export let placement: Placement = 'bottom-end';
    export let additionalPopoverButtonClasses = '';
    export let additionalPopoverPanelClasses = '';
    export let highlightedDates: CalendarDate[] = [];

    const id = `kp-calendar-popover-${uuid()}`;
    const buttonId = `${id}-popover-button`;
    const panelId = `${id}-popover-panel`;

    const [floatingRef, floatingContent] = createFloatingActions(floatingUiCommonSettings(placement));

    const dispatch = createEventDispatcher<{'date-selected': CalendarDate}>();
    const calendarManager = KpCalendarManager.createNavigableCalendar({
        type: 'monthView',
        startDate
    });

    let calendarView: CalendarView;
    const calendarViewSubscription = calendarManager.getView$().subscribe((view) => calendarView = view);

    onDestroy(() => {
        cleanup(calendarViewSubscription);
    });

    function getCalendarLabel(view: CalendarView): string {
        const formatter = new Intl.DateTimeFormat(getCurrentLanguage(), {month: 'long'});
        const monthDate = new Date(view.year, view.month - 1, 1);
        return `${view.year} - ${formatter.format(monthDate)}`;
    }
</script>

<KpButton id="{buttonId}"
          {buttonSize}
          {buttonStyle}
          additionalClasses="{additionalPopoverButtonClasses}"
          use={[floatingRef]}
          popovertarget="{panelId}">

    <slot name="button">
        <IconedContent icon="calendar">
            Vyberte datum
        </IconedContent>
    </slot>
</KpButton>

<KpPopoverPanel additionalClasses="kp-calendar-popover-panel {additionalPopoverPanelClasses}"
                use={[floatingContent]}
                id="{panelId}">

    <div slot="popover-title" class="toolbar-row">
        <span>{getCalendarLabel(calendarView)}</span>

        <KpIconButton icon="angle-small-left" noBackground on:click={() => calendarManager.navigate(-1)}/>
        <KpIconButton icon="angle-small-right" noBackground on:click={() => calendarManager.navigate(1)}/>
    </div>

    {#key calendarView.month}
        <div class="grid-calendar" in:fly="{{x: 20 * calendarView.navigateDirection, duration: 250}}">
            {#each getPaddedMonthDays(calendarView.days) as day}
                <button class="calendar-day"
                        class:selected-day={exists(selectedDate) && areCalendarDatesEqual(selectedDate, day)}
                        class:padding-day={day.padding}
                        class:highlighted-day={highlightedDates.some((highlightedDate) => areCalendarDatesEqual(highlightedDate, day))}
                        on:click={() => dispatch('date-selected', day)}>

                    <span class="date">{day.day}</span>
                </button>
            {/each}
        </div>
    {/key}
</KpPopoverPanel>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    :global {
        .kp-calendar-popover-panel {
            --popover-bg-color: @themed-body-bg;
            --popover-border-color: @themed-border-default;
            --popover-border-radius: @border-radius-large;
            --popover-title-bg-color: @themed-body-bg;
            --popover-title-border-color: @themed-border-default;
            --popover-title-padding: @spacing-sm @spacing-m;
            --popover-content-padding: @spacing-m;

            width: 300px;
        }
    }

    .toolbar-row {
        display: flex;
        align-items: center;
        gap: @spacing-s;

        span {
            font-weight: 500;
            flex: 1;
        }
    }

    .grid-calendar {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(7, 1fr);

        .calendar-day {
            position: relative;
            width: 100%;
            height: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            outline: none;
            border: none;
            background: none;
            cursor: pointer;
            isolation: isolate;

            &:before {
                content: '';
                position: absolute;
                width: calc(100% - 6px);
                height: calc(100% - 6px);
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                border-radius: @border-radius-default;
                background-color: transparent;
                transition: background-color 0.3s ease-in-out;
                z-index: -1;
            }

            &:hover {
                &:before {
                    background-color: @themed-panel-bg;
                }
            }

            &.highlighted-day {
                &:before {
                    background-color: @themed-body-bg-blue-highlighted;
                }
            }

            &.selected-day {
                color: white;

                &:before {
                    background-color: var(--brand-orange-new);
                }
            }

            &.padding-day {
                opacity: 0.5;
            }
        }
    }
</style>