import type {CalendarDate, CalendarDatesRange, CalendarDay} from './types';
import {DateTime, Interval, Zone} from 'luxon';
import {exists, ignoreUnusedProperties} from 'shared/utils/custom-utils';
import {constructJsDate} from 'shared/utils/date-utils';

export function getCalendarYear(year: number): CalendarDatesRange {
    const yearDateTime = DateTime.fromObject({year, month: 1, day: 1});

    const startOfYear = yearDateTime.startOf('year');
    const endOfYear = yearDateTime.endOf('year');

    return {
        start: calendarDateFromLuxon(startOfYear),
        end: calendarDateFromLuxon(endOfYear)
    };
}

export function getCalendarMonth(month: number, year: number): CalendarDatesRange {
    const monthDateTime = DateTime.fromObject({year, month, day: 1});

    const startOfMonth = monthDateTime.startOf('month');
    const endOfMonth = monthDateTime.endOf('month');

    return {
        start: calendarDateFromLuxon(startOfMonth),
        end: calendarDateFromLuxon(endOfMonth)
    };
}

export function getCalendarWeek(weekNumber: number, year: number): CalendarDatesRange {
    const weekDateTime = DateTime.fromObject({weekday: 1, weekYear: year, weekNumber});

    const startOfWeek = weekDateTime.startOf('week');
    const endOfWeek = weekDateTime.endOf('week');

    return {
        start: calendarDateFromLuxon(startOfWeek),
        end: calendarDateFromLuxon(endOfWeek)
    };
}

export function getCalendarDay(day: number, month: number, year: number): CalendarDate {
    return {day, month, year};
}

export function areCalendarDatesEqual(date1: CalendarDate, date2: CalendarDate): boolean {
    return date1.day === date2.day && date1.month === date2.month && date1.year === date2.year;
}

export function luxonDateFromCalendarDate(date: CalendarDate, zone?: 'UTC' | undefined): DateTime {
    if (exists(zone)) {
        return DateTime.fromObject({year: date.year, month: date.month, day: date.day}, {zone});
    }

    return DateTime.fromObject({year: date.year, month: date.month, day: date.day});
}

export function luxonInclusiveInterval(date1: DateTime, date2: DateTime): Interval<true> {
    const interval = Interval.fromDateTimes(date1, date2.plus({days: 1}));

    if (!interval.isValid) {
        throw new Error(`Invalid interval created between ${date1.toISO()} and ${date2.toISO()}`);
    }

    return interval;
}

const defaultCalendarDayModifier = (date: DateTime) => {
    ignoreUnusedProperties(date);
    return {};
};

export function getDaysFromRangeInclusive(calendarDatesRange: CalendarDatesRange, dayObjModifier: (date: DateTime) => Record<string, any> = defaultCalendarDayModifier): CalendarDay[] {
    const startDate = luxonDateFromCalendarDate(calendarDatesRange.start);
    const endDate = luxonDateFromCalendarDate(calendarDatesRange.end);
    return getDaysBetweenDatesInclusive(startDate, endDate, dayObjModifier);
}

export function getDaysBetweenDatesInclusive(date1: DateTime, date2: DateTime, dayObjModifier: (date: DateTime) => Record<string, any> = defaultCalendarDayModifier): CalendarDay[] {
    const days: CalendarDay[] = [];

    luxonInclusiveInterval(date1, date2).splitBy({days: 1}).forEach((interval) => {
        if (interval.isValid) {
            const date = interval.start; // Oh it exists, you little worrisome TypeScript

            days.push({
                day: date.day,
                month: date.month,
                year: date.year,
                week: date.weekNumber,
                weekDay: date.weekday,
                ...dayObjModifier(date)
            });
        }
    });

    return days;
}

export function luxonIsSameDay(date1: DateTime, date2: DateTime): boolean {
    return date1.hasSame(date2, 'day');
}

export function luxonStartOfWeek(date: DateTime): DateTime {
    return date.startOf('week').set({hour: 0, minute: 0, second: 0, millisecond: 0});
}

export function calendarDateFromLuxon(date: DateTime): CalendarDate {
    return {
        day: date.day,
        month: date.month,
        year: date.year
    };
}

export function getTodayCalendarDate(): CalendarDate {
    return calendarDateFromLuxon(DateTime.now());
}

export function calendarDateToISODate(date: CalendarDate): string {
    return luxonDateFromCalendarDate(date).toISODate();
}

export function calendarDateFromISODate(isoDate: string): CalendarDate {
    return calendarDateFromLuxon(DateTime.fromISO(isoDate));
}

export function jsDateFromCalendarDate(date: CalendarDate): Date {
    return constructJsDate(date.day, date.month, date.year);
}

/**
 * @module calendar-utils
 * @name getPaddedMonthDays
 *
 * @param {CalendarDay[]} monthDays - Array of `CalendarDay` objects representing the days in a month.
 * @returns {CalendarDay[]} - Array of `CalendarDay` objects, padded to align with a full week (Monday-Sunday).
 *
 * @description
 * Takes an array of days representing a month and pads it with additional days to ensure the calendar aligns with full weeks.
 * Padding days are added at the start (before Monday) and at the end (after Sunday) as necessary.
 *
 * Each padded day includes a `padding: true` property to indicate that it does not belong to the main month.
 *
 * ### Example:
 * For a month starting on a Thursday (weekDay = 4) and ending on a Saturday (weekDay = 6), the resulting array:
 * - Adds 3 days before (Monday, Tuesday, Wednesday) at the start.
 * - Adds 1 day after (Sunday) at the end.
 */
export function getPaddedMonthDays(monthDays: CalendarDay[]): CalendarDay[] {
    if (!monthDays || monthDays.length === 0) {
        return [];
    }

    const firstDay = monthDays[0];
    const lastDay = monthDays[monthDays.length - 1];

    // Determine how many padding days are needed at the start or at the end
    const daysToPrepend = (firstDay.weekDay - 1 + 7) % 7;
    const daysToAppend = (7 - lastDay.weekDay) % 7;

    const prependStartDate = luxonDateFromCalendarDate(firstDay).minus({days: daysToPrepend});
    const prependEndDate = luxonDateFromCalendarDate(firstDay).minus({days: 1});
    const appendStartDate = luxonDateFromCalendarDate(lastDay).plus({days: 1});
    const appendEndDate = luxonDateFromCalendarDate(lastDay).plus({days: daysToAppend});

    const withPaddingDayObjModifier = (date: DateTime) => {
        ignoreUnusedProperties(date);
        return {padding: true};
    };

    return [...getDaysBetweenDatesInclusive(prependStartDate, prependEndDate, withPaddingDayObjModifier), ...monthDays, ...getDaysBetweenDatesInclusive(appendStartDate, appendEndDate, withPaddingDayObjModifier)];
}