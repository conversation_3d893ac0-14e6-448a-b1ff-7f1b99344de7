<script lang="ts">
    import type {UUID} from 'typings/portaro.be.types';
    import {minidenticon} from 'minidenticons';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {getGlobalEventBus, getInjector} from 'core/svelte-context/context';
    import {onDestroy, onMount} from 'svelte';
    import {fade} from 'svelte/transition';
    import {clickFileSelect} from 'shared/utils/click-file-select';
    import {RecordCoverService} from 'src/features/record/kp-cover/record-cover.service';
    import CurrentAuthService, {UserRoles} from 'shared/services/current-auth.service';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let userRecordId: UUID | null = null;
    export let sizePx: number;
    export let allowEdit = false;

    const currentAuthService = getInjector().getByClass(CurrentAuthService);
    const recordCoverService = getInjector().getByClass(RecordCoverService);
    const globalEventBus = getGlobalEventBus();

    const UPDATE_AVATAR_EVENT = 'update-user-avatar';

    let editable = false;
    const currentAuthSubscription = currentAuthService.currentAuth$().subscribe((auth) => {
        if (!allowEdit) {
            return;
        }

        if (auth.evided && exists(userRecordId) && (auth.activeUser.rid === userRecordId || auth.role.includes(UserRoles.ROLE_ADMIN))) {
            editable = true;
        }
    });

    const noUserRecord = !exists(userRecordId);
    let imageSrc: string | null = null;

    onMount(async () => {
        await loadImageSrc();
        globalEventBus.addEventListener(UPDATE_AVATAR_EVENT, handleUpdateAvatarEvent);
    });

    onDestroy(() => {
        cleanup(currentAuthSubscription);
        globalEventBus.removeEventListener(UPDATE_AVATAR_EVENT, handleUpdateAvatarEvent);
    });

    async function loadImageSrc(): Promise<void> {
        imageSrc = null;

        if (noUserRecord) {
            return;
        }

        const fallbackSvg = `data:image/svg+xml;utf8,${encodeURIComponent(minidenticon((userRecordId)))}`;
        const coverPath = `/records/${userRecordId}/cover?withoutFallback=true`;

        try {
            const imageLoaded = await tryLoadImage(coverPath);
            imageSrc = imageLoaded ? coverPath : fallbackSvg;
        } catch {
            imageSrc = fallbackSvg;
        }
    }

    function tryLoadImage(src: string): Promise<boolean> {
        return new Promise((resolve) => {
            const img = new Image();

            img.onload = () => {
                resolve(true);
            };

            img.onerror = () => {
                resolve(false);
            };

            img.src = src;
        });
    }

    const handleFileSelectClick = async () => {
        const selectedFiles = await clickFileSelect(false);
        if (!exists(selectedFiles) || selectedFiles.length === 0) {
            return;
        }

        const newCoverFile = selectedFiles[0];
        const success = await recordCoverService.uploadNewCoverImage(userRecordId, newCoverFile);

        if (!success) {
            return;
        }

        globalEventBus.dispatchEvent(new CustomEvent(UPDATE_AVATAR_EVENT, {detail: userRecordId}));
    };

    const handleUpdateAvatarEvent = (event: CustomEvent<UUID>) => {
        if (event.detail !== userRecordId) {
            return;
        }

        loadImageSrc();
    };
</script>

<span class="kp-user-avatar" class:no-user-record={noUserRecord} style:--size="{sizePx}px">
    {#if noUserRecord}
        <span class="icon-container">
            <UIcon icon="user"/>
        </span>
    {:else if exists(imageSrc)}
        <img src="{imageSrc}" alt="User avatar" transition:fade={{duration: 250}}/>
    {/if}

    {#if editable}
        <span class="select-cover-button-container">
            <KpIconButton icon="add-image"
                          buttonSize="md"
                          tooltipLabel="Vybrat nový profilový obrázek"
                          on:click={handleFileSelectClick}/>
        </span>
    {/if}
</span>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .kp-user-avatar {
        position: relative;
        display: flex;
        border: 1px solid @themed-border-default;
        background-color: @themed-body-bg;
        width: var(--size);
        height: var(--size);
        flex-grow: 0;
        flex-shrink: 0;
        border-radius: calc(var(--size) / 2);

        &:hover,
        &:focus-visible,
        &:focus-within {
            .select-cover-button-container {
                opacity: 1;
                transform: translateY(0);
            }
        }

        &.no-user-record {
            background-color: @themed-panel-bg;

            .icon-container {
                color: @themed-text-muted;
            }
        }

        .icon-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: calc(var(--size) / 2);
        }

        .select-cover-button-container {
            z-index: 1;
            position: absolute;
            top: @spacing-sm;
            right: @spacing-sm;
            transform: translateY(@spacing-xs);
            opacity: 0;
            transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
        }

        img {
            border-radius: calc(var(--size) / 2);
            overflow: hidden;
            pointer-events: none;
            user-select: none;
            width: 100%;
            height: 100%;
        }
    }
</style>