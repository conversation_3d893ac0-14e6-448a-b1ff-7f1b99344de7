<script lang="ts">
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import {getInjector, getLogger} from 'core/svelte-context/context';

    export let cislo: number;
    export let text: string;
    export let bool: boolean;
    export let objekt: Record<string, any>;

    const injector = getInjector();
    const logger = getLogger();

    logger.warn('hello from custom element');
</script>

<KpGenericPanel>
    <span slot="heading" class="xxx">Header</span>

    <div>
        Here it is: <br>
        {cislo} {text} {bool} {objekt}
        <hr>
        {typeof injector} {injector}
    </div>

    <span slot="footer" class="yyy">Footer</span>
</KpGenericPanel>

<style lang="less">
    .xxx {
        color: red;
    }

    .yyy {
        color: blue;
    }
</style>