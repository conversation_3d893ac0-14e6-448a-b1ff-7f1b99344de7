import type {ActionResponse} from 'typings/portaro.be.types';
import type {LogService} from 'core/logging/log.service';
import type {ActionRequestMethod} from '../constants/portaro.constants';
import {actionRequestMethods} from '../constants/portaro.constants';
import type {AjaxService} from 'core/data-services/ajax.service';
import {assertUnreachable} from 'shared/utils/custom-utils';

export default class ActionRequestDataService {
    public static serviceName = 'actionRequestDataService';

    /*@ngInject*/
    constructor(private logService: LogService, private ajaxService: AjaxService) {
    }

    public async request(requestUrl: string,
                         requestMethod: ActionRequestMethod = actionRequestMethods.GET,
                         requestBody?: Record<string, any>): Promise<ActionResponse> {

        switch (requestMethod) {
            case actionRequestMethods.GET:  return this.ajaxService.withoutBaseUrl().createRequest(requestUrl).get(requestBody ?? {});
            case actionRequestMethods.POST:  return this.ajaxService.withoutBaseUrl().createRequest(requestUrl).post(requestBody ?? {});
            case actionRequestMethods.DELETE:  return this.ajaxService.withoutBaseUrl().createRequest(requestUrl).delete();
            default: {
                this.logService.error(`Request method ${requestMethod} is not supported`);
                assertUnreachable(requestMethod, `Request method ${requestMethod} is not supported`)
            }
        }
    }
}
