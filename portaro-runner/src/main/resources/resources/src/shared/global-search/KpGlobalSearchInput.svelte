<script lang="ts">
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpGlobalSearchInputPresenter} from 'shared/global-search/kp-global-search-input.presenter';
    import {autocomplete} from 'shared/ui-widgets/autocomplete/use.autocomplete';
    import {slide} from 'svelte/transition';
    import {exists} from 'shared/utils/custom-utils';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';
    import AutocompleteWrapper from 'shared/ui-widgets/autocomplete/AutocompleteWrapper.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let inputId: string;
    export let additionalInputClasses = '';
    export let autofocusOrCompactOpened = false;
    export let additionalButtonClasses = '';
    export let compact = false;
    export let placeholder = '';

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpGlobalSearchInputPresenter>(KpGlobalSearchInputPresenter.presenterName);
    const queryStore = presenter.getSearchQueryStore();
    const debounceTimeMs = 300;
    const minQueryLength = 4;

    export let searchInputElement: HTMLInputElement | null = null;
    let searchButtonElement: HTMLButtonElement | null = null;
    let loading = false;
    let autocompleteHintSelected = false;

    $: loadingSpinnerTopOffset = searchInputElement ? searchInputElement.getBoundingClientRect().height / 2 - 8 : 0;
    $: dropdownTopOffset = searchInputElement ? searchInputElement.getBoundingClientRect().height + 2 : 0;
    $: searchButtonHeight = searchInputElement ? searchInputElement.getBoundingClientRect().height : 0;
    $: if (autofocusOrCompactOpened && searchInputElement) {
        searchInputElement.focus();
    }

    function checkSearchButtonEmpty(element: HTMLButtonElement | null) {
        const elementExists = exists(element);
        if (!elementExists) {
            return false;
        }
        const containsTextContent = element.textContent && !/^\s*$/.test(element.textContent);
        const containsChildren = element.children.length > 0;
        return !containsTextContent && !containsChildren;
    }

    const handleSearch = async (inputValue: string): Promise<string[]> => {
        if (inputValue.length < minQueryLength) {
            return [];
        }
        loading = true;
        const result = await presenter.getAutocompleteHints(inputValue);
        loading = false;
        return result;
    }

    const handleSearchEnter = (event: KeyboardEvent) => {
        if ((event.key === 'Enter' || event.key === 'Return') && !autocompleteHintSelected) {
            presenter.triggerGlobalSearch($queryStore);
        }
    }

    const handleAutocompleteInput = (event: CustomEvent<string>) => {
        presenter.triggerGlobalSearch(event.detail);
    }

    const handleAutocompleteHintsSelect = (event: CustomEvent<boolean>) => {
        autocompleteHintSelected = event.detail;
    }
</script>

<form class="kp-global-search-input input-group" role="search" on:submit|preventDefault>
    <div class="search-input-container">
        <AutocompleteWrapper search="{handleSearch}"
                             topOffset="{dropdownTopOffset}px"
                             currentInputValue={$queryStore}
                             debounce="{debounceTimeMs}"
                             additionalContainerClasses="kp-global-search-input-autocomplete-wrapper"
                             let:autocompleteController>

            <input type="search"
                   bind:this={searchInputElement}
                   bind:value={$queryStore}
                   on:keydown={handleSearchEnter}
                   class="search-input {additionalInputClasses}"
                   style="--focusBorderColor: {window.globalSearchButtonColor}CC"
                   id="{inputId}"
                   autocomplete="off"
                   name="q"
                   placeholder="{placeholder}"
                   on:autocomplete-input={handleAutocompleteInput}
                   on:autocomplete-hint-select={handleAutocompleteHintsSelect}
                   use:autocomplete={autocompleteController}
                   on:blur
                   on:focus/>
        </AutocompleteWrapper>

        {#if loading}
            <div class="loader-container" style="top: {loadingSpinnerTopOffset}px">
                <KpLoadingInline size="xs"/>
            </div>
        {/if}
    </div>

    {#if !compact || (compact && autofocusOrCompactOpened)}
        <div class="input-group-btn" transition:slide={{duration: 250, axis: 'x'}}>
            <button type="button"
                    style="height: {searchButtonHeight}px"
                    class="search-button btn {additionalButtonClasses}"
                    aria-label="{localize(/* @kp-localization commons.hledat */ 'commons.hledat')}"
                    bind:this={searchButtonElement}
                    on:click={() => presenter.triggerGlobalSearch($queryStore)}>

                <slot>
                    <UIcon icon="search"/>
                </slot>

                {#if checkSearchButtonEmpty(searchButtonElement)}
                    <UIcon icon="search"/>
                {/if}
            </button>
        </div>
    {/if}
</form>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .search-input-container {
        position: relative;
        display: block;
        flex: 1;

        .search-input {
            width: 100%;
            border-radius: @input-border-radius 0 0 @input-border-radius;

            &:focus {
                border-color: var(--focusBorderColor);
            }

            &:focus-visible {
                outline: none !important;
                box-shadow: none !important;
            }
        }

        .loader-container {
            position: absolute;
            z-index: 100;
            right: 10px;
        }
    }

    .search-button {
        display: flex;
        align-items: center;
        margin: 0;
        background-color: var(--global-search-btn-color);
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-left: 1px solid transparent;
        transition: filter 0.2s ease-in-out;
        color: #fff;

        &:hover, &:active {
            filter: brightness(80%);
        }
    }

    :global {
        .kp-global-search-input-autocomplete-wrapper {
            position: relative;
            display: flex;
            width: 100%;
        }
    }
</style>