import type {ActionReturn} from 'svelte/action';
import {exists} from 'shared/utils/custom-utils';

interface StyleProperty {
    property: string,
    value: string
}

export function setStyleProperty(node: HTMLBodyElement, parameters: StyleProperty): ActionReturn<StyleProperty> {
    function updateStyle({property, value}) {
        if (exists(property) && exists(value)) {
            node.style.setProperty(property, value);
        }
    }

    updateStyle(parameters);

    return {
        update(newParameters) {
            updateStyle(newParameters);
        }
    };
}