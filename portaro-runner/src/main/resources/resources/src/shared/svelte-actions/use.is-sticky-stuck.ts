import type {ActionReturn} from 'svelte/action';
import {assertIsElement} from 'shared/utils/types-utils';

export const STICKY_CHANGE_EVENT_TYPE = 'sticky-change';

interface Parameters {
    disabled: boolean;
}

const defaultParams: Parameters = {
    disabled: false
};

interface StickyDetail {
    stuck: boolean;
}

interface Attributes {
    'on:sticky-change'?: (e: CustomEvent<StickyDetail>) => void;
}

export function isStickyStuck(
    node: HTMLElement,
    customParams: Partial<Parameters> = defaultParams
): ActionReturn<Parameters, Attributes> {
    const params: Parameters = {...defaultParams, ...customParams};
    let isDisabled = params.disabled;
    let stuck = false;

    assertIsElement(node);

    // Create a sentinel element just before the sticky node
    const sentinel = document.createElement('div');
    sentinel.style.position = 'absolute';
    sentinel.style.top = '0';
    sentinel.style.height = '1px';
    sentinel.style.width = '1px';
    sentinel.style.pointerEvents = 'none';
    node.parentElement?.insertBefore(sentinel, node);

    const observer = new IntersectionObserver(
        ([entry]) => {
            if (isDisabled) return;

            const currentlyStuck = entry.intersectionRatio === 0;

            if (currentlyStuck !== stuck) {
                stuck = currentlyStuck;
                node.dispatchEvent(
                    new CustomEvent<StickyDetail>(STICKY_CHANGE_EVENT_TYPE, {
                        detail: {stuck}
                    })
                );
            }
        },
        {threshold: [0], rootMargin: '0px 0px 0px 0px'}
    );

    observer.observe(sentinel);

    return {
        update(parameters) {
            isDisabled = parameters.disabled;
        },
        destroy() {
            observer.disconnect();
            sentinel.remove();
        }
    };
}