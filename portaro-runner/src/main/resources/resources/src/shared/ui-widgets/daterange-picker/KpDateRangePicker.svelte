<script lang="ts">
    import type {IntervalString} from './date-range-util';
    import type {CalendarView} from 'shared/components/kp-calendar/types';
    import type {DateRangeGranularity} from 'shared/ui-widgets/daterange-picker/types';
    import {DateTime} from 'luxon';
    import {isDateInInterval, parseObjectToIntervalString} from './date-range-util';
    import {parseIntervalStringToObject} from './date-range-util';
    import {cleanup} from 'shared/utils/custom-utils';
    import {KpCalendarManager} from 'shared/components/kp-calendar/kp-calendar-manager';
    import {createEventDispatcher, onDestroy} from 'svelte';
    import {fly} from 'svelte/transition';
    import {getCurrentLanguage} from 'core/svelte-context/context';
    import {getPaddedMonthDays, luxonDateFromCalendarDate} from 'shared/components/kp-calendar/utils';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';

    export let interval: IntervalString;
    export let granularity: DateRangeGranularity;

    const dispatch = createEventDispatcher<{'interval-changed': IntervalString}>();
    const parsedIntervalObject = parseIntervalStringToObject(interval);

    let from = parsedIntervalObject.from;
    let to = parsedIntervalObject.to;
    $: intervalObject = {from, to};

    type SelectingType = 'from' | 'to';
    let selecting: SelectingType = 'from';

    const calendarManager = KpCalendarManager.createNavigableCalendar({
        type: 'monthView'
    });

    let calendarView: CalendarView;
    const calendarViewSubscription = calendarManager.getView$().subscribe((view) => calendarView = view);

    onDestroy(() => {
        cleanup(calendarViewSubscription);
    });

    function getCalendarLabel(view: CalendarView): string {
        const formatter = new Intl.DateTimeFormat(getCurrentLanguage(), {month: 'long'});
        const monthDate = new Date(view.year, view.month - 1, 1);
        return `${view.year} - ${formatter.format(monthDate)}`;
    }

    function getSelectionTypeLabel(type: SelectingType): string {
        return type === 'from' ? 'Od' : 'Do';
    }

    const handleDateClick = (date: DateTime) => {
        if (selecting === 'from') {
            from = date;
            selecting = 'to';
        } else {
            to = date;
            selecting = 'from';
        }

        dispatch('interval-changed', parseObjectToIntervalString(intervalObject));
    };

    const handleToggleSelectingType = () => {
        selecting = selecting === 'from' ? 'to' : 'from';
    };
</script>

<div class="kp-date-range-picker">
    <div class="toolbar-row">
        <KpIconButton icon="angle-small-left" on:click={() => calendarManager.navigate(-1)}/>
        <Spacer direction="horizontal" size="s"/>
        <KpIconButton icon="angle-small-right" on:click={() => calendarManager.navigate(1)}/>
        <Spacer direction="horizontal" size="m"/>

        <span class="view-label">{getCalendarLabel(calendarView)}</span>

        <Spacer flex="1"/>
        <Spacer direction="horizontal" size="m"/>

        <KpButton on:click={handleToggleSelectingType}>Vybíráte: {getSelectionTypeLabel(selecting)}</KpButton>
    </div>

    {#key calendarView.month}
        <div class="grid-calendar" in:fly="{{x: 20 * calendarView.navigateDirection, duration: 250}}">
            {#each getPaddedMonthDays(calendarView.days) as day (day)}
                {@const luxonDate = luxonDateFromCalendarDate(day, 'UTC')}
                {@const isStartOfInterval = DateTime.isDateTime(intervalObject.from) && luxonDate.hasSame(intervalObject.from, 'day')}
                {@const isEndOfInterval = DateTime.isDateTime(intervalObject.to) && luxonDate.hasSame(intervalObject.to, 'day')}
                {@const isInInterval = isDateInInterval(luxonDate, intervalObject)}
                {@const isToday = luxonDate.hasSame(DateTime.now(), 'day')}

                <button class="calendar-day"
                        on:click={() => handleDateClick(luxonDate)}
                        class:padding-day={day.padding}
                        class:highlighted={isInInterval}
                        class:start-or-end={isStartOfInterval || isEndOfInterval}
                        class:today={isToday}>

                    <span class="date">{day.day}</span>
                </button>
            {/each}
        </div>
    {/key}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .toolbar-row {
        display: flex;
        align-items: center;
        padding: @spacing-sm @spacing-m;

        .view-label {
            font-weight: 500;
        }
    }

    .grid-calendar {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(7, 1fr);

        .calendar-day {
            position: relative;
            width: 100%;
            height: 55px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            outline: none;
            border: none;
            background: none;
            cursor: pointer;
            isolation: isolate;

            &:before {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: transparent;
                transition: background-color 0.3s ease-in-out;
                z-index: -1;
            }

            &:hover {
                &:before {
                    background-color: @themed-panel-bg;
                }
            }

            &.highlighted {
                &:before {
                    background-color: @themed-body-bg-blue-highlighted;
                }
            }

            &.start-or-end {
                color: white;

                &:before {
                    background-color: var(--accent-blue-new);
                }
            }

            &.padding-day {
                opacity: 0.5;
            }
        }
    }
</style>