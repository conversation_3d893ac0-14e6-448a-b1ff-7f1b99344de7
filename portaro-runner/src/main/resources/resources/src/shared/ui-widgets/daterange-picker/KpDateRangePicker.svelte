<script lang="ts">
    import type {DateRangeInterval, IntervalString} from './date-range-util';
    import type {CalendarView} from 'shared/components/kp-calendar/types';
    import type {DateRangeGranularity} from 'shared/ui-widgets/daterange-picker/types';
    import {DateTime} from 'luxon';
    import {isDateInInterval, parseObjectToIntervalString, isIntervalInvalid} from './date-range-util';
    import {parseIntervalStringToObject} from './date-range-util';
    import {cleanup} from 'shared/utils/custom-utils';
    import {KpCalendarManager} from 'shared/components/kp-calendar/kp-calendar-manager';
    import {createEventDispatcher, onDestroy} from 'svelte';
    import {fly} from 'svelte/transition';
    import {getCurrentLanguage} from 'core/svelte-context/context';
    import {getPaddedMonthDays, luxonDateFromCalendarDate} from 'shared/components/kp-calendar/utils';
    import {formatIntervalForUser} from 'shared/ui-widgets/daterange-picker/date-range-format-util';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let interval: IntervalString;
    export let granularity: DateRangeGranularity;

    const dispatch = createEventDispatcher<{'interval-changed': IntervalString}>();
    const parsedIntervalObject = parseIntervalStringToObject(interval);
    const dayNames = ['PO', 'ÚT', 'ST', 'ČT', 'PÁ', 'SO', 'NE'];

    let from = parsedIntervalObject.from;
    let to = parsedIntervalObject.to;

    $: intervalObject = {from, to};
    $: isInvalid = isIntervalInvalid(intervalObject);

    type SelectingType = 'from' | 'to';
    let selecting: SelectingType = 'from';

    const calendarManager = KpCalendarManager.createNavigableCalendar({
        type: 'monthView'
    });

    let calendarView: CalendarView;
    const calendarViewSubscription = calendarManager.getView$().subscribe((view) => calendarView = view);

    onDestroy(() => {
        cleanup(calendarViewSubscription);
    });

    function getCalendarLabel(view: CalendarView): string {
        const formatter = new Intl.DateTimeFormat(getCurrentLanguage(), {month: 'long'});
        const monthDate = new Date(view.year, view.month - 1, 1);
        return `${view.year} - ${formatter.format(monthDate)}`;
    }

    function getSelectionTypeLabel(type: SelectingType): string {
        return type === 'from' ? 'Od' : 'Do';
    }

    function getFormattedIntervalString(intervalObj: DateRangeInterval): string {
        return formatIntervalForUser(intervalObj, granularity);
    }

    const handleDateClick = (date: DateTime) => {
        if (selecting === 'from') {
            from = date;
            selecting = 'to';
        } else {
            to = date;
            selecting = 'from';
        }

        dispatch('interval-changed', parseObjectToIntervalString({from, to}));
    };

    const handleToggleSelectingType = () => {
        selecting = selecting === 'from' ? 'to' : 'from';
    };

    const handleSetInfinityBound = () => {
        if (selecting === 'from') {
            from = '-infinity';
            selecting = 'to';
        } else {
            to = 'infinity';
            selecting = 'from';
        }

        dispatch('interval-changed', parseObjectToIntervalString({from, to}));
    };
</script>

<div class="kp-date-range-picker">
    <div class="toolbar-container">
        <Flex direction="row" alignItems="center">
            <KpIconButton icon="angle-small-left" on:click={() => calendarManager.navigate(-1)}/>
            <Spacer direction="horizontal" size="s"/>
            <KpIconButton icon="angle-small-right" on:click={() => calendarManager.navigate(1)}/>
            <Spacer direction="horizontal" size="m"/>

            <span class="view-label">{getCalendarLabel(calendarView)}</span>

            <Spacer flex="1"/>
            <Spacer direction="horizontal" size="m"/>

            <KpButton on:click={handleToggleSelectingType}>Vybíráte: {getSelectionTypeLabel(selecting)}</KpButton>
            <Spacer direction="horizontal" size="m"/>
            <KpButton on:click={handleSetInfinityBound}>{getSelectionTypeLabel(selecting)} ∞</KpButton>
        </Flex>

        <Spacer direction="vertical" size="m"/>

        <Flex direction="row" alignItems="center">
            Vybraný interval: {getFormattedIntervalString(intervalObject)}
        </Flex>
    </div>

    {#key calendarView.month}
        <div class="grid-calendar" in:fly="{{x: 20 * calendarView.navigateDirection, duration: 250}}">
            {#each getPaddedMonthDays(calendarView.days) as day, index (day)}
                {@const luxonDate = luxonDateFromCalendarDate(day, 'UTC')}
                {@const isStartOfInterval = DateTime.isDateTime(intervalObject.from) && luxonDate.hasSame(intervalObject.from, 'day')}
                {@const isEndOfInterval = DateTime.isDateTime(intervalObject.to) && luxonDate.hasSame(intervalObject.to, 'day')}
                {@const isInInterval = isDateInInterval(luxonDate, intervalObject)}
                {@const isToday = luxonDate.hasSame(DateTime.now(), 'day')}

                <button class="calendar-day"
                        on:click={() => handleDateClick(luxonDate)}
                        class:invalid-interval={isInvalid}
                        class:padding-day={day.padding}
                        class:highlighted={isInInterval}
                        class:start-or-end={isStartOfInterval || isEndOfInterval}
                        class:today={isToday}>

                    {#if index < 7}
                        <span class="day-name">{dayNames[index]}</span>
                    {/if}

                    {#if isStartOfInterval || isEndOfInterval}
                        <span class="start-end">{isStartOfInterval ? 'OD' : 'DO'}</span>
                    {/if}

                    <span class="date">{day.day}</span>
                </button>
            {/each}
        </div>
    {/key}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .kp-date-range-picker {
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;
    }

    .toolbar-container {
        display: flex;
        flex-direction: column;
        padding: @spacing-sm @spacing-m @spacing-ml;
        border-bottom: 1px solid @themed-border-default;

        .view-label {
            font-weight: 500;
        }
    }

    .grid-calendar {
        display: grid;
        margin: 0 @spacing-m @spacing-m;
        grid-template-columns: repeat(7, 1fr);
        border-radius: @border-radius-large;
        overflow: hidden;

        .calendar-day {
            position: relative;
            width: 100%;
            height: 55px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            outline: none;
            border: none;
            background: none;
            cursor: pointer;
            isolation: isolate;

            --start-end-color: var(--accent-blue-new);

            .date {
                font-size: @font-size-small;
                font-weight: 500;
            }

            .day-name,
            .start-end {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                color: @themed-text-muted;
                font-size: @font-size-xs;
            }

            .day-name {
                top: 2px;
            }

            .start-end {
                bottom: 2px;
            }

            &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                background-color: transparent;
                transition: background-color 0.3s ease-in-out;
                z-index: -1;
            }

            &:hover {
                &:before {
                    background-color: @themed-panel-bg;
                }
            }

            &.invalid-interval {
                --start-end-color: var(--danger-red);
            }

            &.highlighted {
                &:before {
                    background-color: @themed-body-bg-blue-highlighted;
                }
            }

            &.start-or-end {
                color: white;

                &:before {
                    background-color: var(--start-end-color);
                }

                .day-name,
                .start-end {
                    color: white;
                    opacity: 0.75;
                }
            }

            &.padding-day {
                opacity: 0.5;
            }
        }
    }

    :global {
        .kp-date-range-picker .toolbar-container .kp-button {
            height: 30px;
            display: flex;
            align-items: center;
        }
    }
</style>