import {DateTime} from 'luxon';
import {formatIntervalForUser} from './date-range-format-util';
import type {DateRangeInterval} from './date-range-util';

describe('date-range-format-util', () => {
    describe('formatIntervalForUser', () => {
        describe('empty intervals', () => {
            it('should return "prázdný interval" for null interval', () => {
                const result = formatIntervalForUser(null as any);
                expect(result).toBe('prázdný interval');
            });

            it('should return "prázdný interval" for undefined interval', () => {
                const result = formatIntervalForUser(undefined as any);
                expect(result).toBe('prázdný interval');
            });

            it('should return "prázdný interval" for interval with no from and to', () => {
                const interval: DateRangeInterval = {};
                const result = formatIntervalForUser(interval);
                expect(result).toBe('prázdný interval');
            });

            it('should return "prázdný interval" for interval with undefined from and to', () => {
                const interval: DateRangeInterval = {from: undefined, to: undefined};
                const result = formatIntervalForUser(interval);
                expect(result).toBe('prázdný interval');
            });
        });

        describe('invalid intervals', () => {
            it('should return error message for interval where to is before from', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-02T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-01T00:00:00Z')
                };
                const result = formatIntervalForUser(interval);
                expect(result).toBe('OD nemůže být později než DO');
            });

            it('should return error message for interval where to is same day but earlier time than from', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T15:00:00Z'),
                    to: DateTime.fromISO('2021-01-01T09:00:00Z')
                };
                const result = formatIntervalForUser(interval);
                expect(result).toBe('OD nemůže být později než DO');
            });
        });

        describe('partial intervals', () => {
            it('should format interval with only from date', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T00:00:00Z')
                };
                const result = formatIntervalForUser(interval);
                expect(result).toBe('od 15.06.2021');
            });

            it('should format interval with only to date', () => {
                const interval: DateRangeInterval = {
                    to: DateTime.fromISO('2021-06-15T00:00:00Z')
                };
                const result = formatIntervalForUser(interval);
                expect(result).toBe('do 15.06.2021');
            });

            it('should format interval with from as negative infinity', () => {
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2021-06-15T00:00:00Z')
                };
                const result = formatIntervalForUser(interval);
                expect(result).toBe('od -∞ do 15.06.2021');
            });

            it('should format interval with to as positive infinity', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T00:00:00Z'),
                    to: 'infinity'
                };
                const result = formatIntervalForUser(interval);
                expect(result).toBe('od 15.06.2021 do ∞');
            });
        });

        describe('complete intervals', () => {
            it('should format interval with both dates (date-only granularity)', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T08:30:00Z'),
                    to: DateTime.fromISO('2021-06-20T18:45:00Z')
                };
                const result = formatIntervalForUser(interval, 'date-only');
                expect(result).toBe('od 15.06.2021 do 20.06.2021');
            });

            it('should format interval with both dates (date-and-time granularity)', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T08:30:00Z'),
                    to: DateTime.fromISO('2021-06-20T18:45:00Z')
                };
                const result = formatIntervalForUser(interval, 'date-and-time');
                expect(result).toBe('od 15.06.2021 08:30 do 20.06.2021 18:45');
            });

            it('should format interval with both infinities', () => {
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: 'infinity'
                };
                const result = formatIntervalForUser(interval);
                expect(result).toBe('od -∞ do ∞');
            });
        });

        describe('same date intervals', () => {
            it('should format same date interval (date-only granularity)', () => {
                const sameDate = DateTime.fromISO('2021-06-15T14:30:00Z');
                const interval: DateRangeInterval = {
                    from: sameDate,
                    to: sameDate
                };
                const result = formatIntervalForUser(interval, 'date-only');
                expect(result).toBe('od 15.06.2021 do 15.06.2021');
            });

            it('should format same date interval (date-and-time granularity)', () => {
                const sameDate = DateTime.fromISO('2021-06-15T14:30:00Z');
                const interval: DateRangeInterval = {
                    from: sameDate,
                    to: sameDate
                };
                const result = formatIntervalForUser(interval, 'date-and-time');
                expect(result).toBe('od 15.06.2021 14:30 do 15.06.2021 14:30');
            });

            it('should format same date with different times (date-only granularity)', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T08:00:00Z'),
                    to: DateTime.fromISO('2021-06-15T18:30:00Z')
                };
                const result = formatIntervalForUser(interval, 'date-only');
                expect(result).toBe('od 15.06.2021 do 15.06.2021');
            });

            it('should format same date with different times (date-and-time granularity)', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T08:00:00Z'),
                    to: DateTime.fromISO('2021-06-15T18:30:00Z')
                };
                const result = formatIntervalForUser(interval, 'date-and-time');
                expect(result).toBe('od 15.06.2021 08:00 do 15.06.2021 18:30');
            });
        });

        describe('timezone handling', () => {
            it('should format dates in different timezones correctly', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T10:00:00+02:00'), // 08:00 UTC
                    to: DateTime.fromISO('2021-06-20T15:30:00-05:00')   // 20:30 UTC
                };
                const result = formatIntervalForUser(interval, 'date-and-time');
                expect(result).toBe('od 15.06.2021 08:00 do 20.06.2021 20:30');
            });
        });

        describe('default granularity', () => {
            it('should use date-only granularity by default', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T08:30:00Z'),
                    to: DateTime.fromISO('2021-06-20T18:45:00Z')
                };
                const result = formatIntervalForUser(interval);
                expect(result).toBe('od 15.06.2021 do 20.06.2021');
            });
        });
    });
});
