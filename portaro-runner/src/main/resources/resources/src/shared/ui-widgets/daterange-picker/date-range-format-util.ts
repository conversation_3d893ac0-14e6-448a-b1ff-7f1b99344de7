import type {DateRangeGranularity} from 'shared/ui-widgets/daterange-picker/types';
import type {DateRangeInterval} from 'shared/ui-widgets/daterange-picker/date-range-util';
import {exists} from 'shared/utils/custom-utils';
import {DateTime} from 'luxon';
import {isIntervalInvalid} from 'shared/ui-widgets/daterange-picker/date-range-util';
import {NEGATIVE_INFINITY, POSITIVE_INFINITY} from 'shared/ui-widgets/daterange-picker/date-range-util';

export function formatIntervalForUser(interval: DateRangeInterval, granularity: DateRangeGranularity = 'date-only'): string {
    // Handle null/undefined intervals
    if (!exists(interval)) {
        return 'prázdný interval';
    }

    // Handle invalid intervals
    if (isIntervalInvalid(interval)) {
        return 'OD nemůže být později než DO';
    }

    // Handle empty intervals
    if (!exists(interval.from) && !exists(interval.to)) {
        return 'prázdný interval';
    }

    // Format individual bounds
    const formatBound = (bound: DateTime | typeof NEGATIVE_INFINITY | typeof POSITIVE_INFINITY | undefined): string => {
        if (!exists(bound)) {
            return '';
        }

        if (bound === NEGATIVE_INFINITY) {
            return '-∞';
        }

        if (bound === POSITIVE_INFINITY) {
            return '∞';
        }

        if (DateTime.isDateTime(bound)) {
            if (granularity === 'date-and-time') {
                return bound.toFormat('dd.MM.yyyy HH:mm');
            } else {
                return bound.toFormat('dd.MM.yyyy');
            }
        }

        return '';
    };

    const fromFormatted = formatBound(interval.from);
    const toFormatted = formatBound(interval.to);

    // Handle partial intervals
    if (!exists(interval.from) && exists(interval.to)) {
        return `do ${toFormatted}`;
    }

    if (exists(interval.from) && !exists(interval.to)) {
        return `od ${fromFormatted}`;
    }

    // Handle complete intervals
    if (exists(interval.from) && exists(interval.to)) {
        return `od ${fromFormatted} do ${toFormatted}`;
    }

    return 'prázdný interval';
}