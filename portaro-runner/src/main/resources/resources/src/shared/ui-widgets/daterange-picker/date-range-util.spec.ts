import {DateTime} from 'luxon';
import {
    parseIntervalStringToObject,
    parseObjectToIntervalString,
    areIntervalsEqual,
    isDateInInterval,
    isIntervalInvalid,
    type IntervalString,
    type DateRangeInterval
} from './date-range-util';

describe('date-range-util', () => {
    describe('parseIntervalStringToObject', () => {
        describe('empty intervals', () => {
            it('should parse empty interval string', () => {
                const result = parseIntervalStringToObject('empty');
                expect(result).toEqual({});
            });
        });

        describe('inclusive intervals', () => {
            it('should parse inclusive interval with both dates', () => {
                const intervalString = '["2021-01-01T00:00:00Z","2021-01-02T00:00:00Z"]' as IntervalString;
                const result = parseIntervalStringToObject(intervalString);

                expect(result.from).toEqual(DateTime.fromISO('2021-01-01T00:00:00Z').toUTC());
                expect(result.to).toEqual(DateTime.fromISO('2021-01-02T00:00:00Z').toUTC());
            });

            it('should parse inclusive interval with start date and positive infinity', () => {
                const intervalString = '["2021-01-01T00:00:00Z",infinity]' as IntervalString;
                const result = parseIntervalStringToObject(intervalString);

                expect(result.from).toEqual(DateTime.fromISO('2021-01-01T00:00:00Z').toUTC());
                expect(result.to).toBe('infinity');
            });

            it('should parse inclusive interval with negative infinity and end date', () => {
                const intervalString = '[-infinity,"2021-01-02T00:00:00Z"]' as IntervalString;
                const result = parseIntervalStringToObject(intervalString);

                expect(result.from).toBe('-infinity');
                expect(result.to).toEqual(DateTime.fromISO('2021-01-02T00:00:00Z').toUTC());
            });

            it('should parse inclusive interval with both infinities', () => {
                const intervalString = '[-infinity,infinity]' as IntervalString;
                const result = parseIntervalStringToObject(intervalString);

                expect(result.from).toBe('-infinity');
                expect(result.to).toBe('infinity');
            });
        });

        describe('exclusive intervals', () => {
            it('should parse exclusive start interval by adding 1 second', () => {
                const intervalString = '("2021-01-01T00:00:00Z","2021-01-02T00:00:00Z"]' as IntervalString;
                const result = parseIntervalStringToObject(intervalString);

                expect(result.from).toEqual(DateTime.fromISO('2021-01-01T00:00:01Z').toUTC());
                expect(result.to).toEqual(DateTime.fromISO('2021-01-02T00:00:00Z').toUTC());
            });

            it('should parse exclusive end interval by subtracting 1 second', () => {
                const intervalString = '["2021-01-01T00:00:00Z","2021-01-02T00:00:00Z")' as IntervalString;
                const result = parseIntervalStringToObject(intervalString);

                expect(result.from).toEqual(DateTime.fromISO('2021-01-01T00:00:00Z').toUTC());
                expect(result.to).toEqual(DateTime.fromISO('2021-01-01T23:59:59Z').toUTC());
            });

            it('should parse fully exclusive interval', () => {
                const intervalString = '("2021-01-01T00:00:00Z","2021-01-02T00:00:00Z")' as IntervalString;
                const result = parseIntervalStringToObject(intervalString);

                expect(result.from).toEqual(DateTime.fromISO('2021-01-01T00:00:01Z').toUTC());
                expect(result.to).toEqual(DateTime.fromISO('2021-01-01T23:59:59Z').toUTC());
            });

            it('should not adjust exclusive bounds for infinity values', () => {
                const intervalString = '(-infinity,infinity)' as IntervalString;
                const result = parseIntervalStringToObject(intervalString);

                expect(result.from).toBe('-infinity');
                expect(result.to).toBe('infinity');
            });
        });

        describe('timezone handling', () => {
            it('should convert different timezones to UTC', () => {
                const intervalString = '["2021-01-01T10:00:00+02:00","2021-01-02T15:30:00-05:00"]' as IntervalString;
                const result = parseIntervalStringToObject(intervalString);

                expect(result.from).toEqual(DateTime.fromISO('2021-01-01T08:00:00Z').toUTC());
                expect(result.to).toEqual(DateTime.fromISO('2021-01-02T20:30:00Z').toUTC());
            });
        });

        describe('error handling', () => {
            it('should throw error for invalid start bracket', () => {
                expect(() => {
                    parseIntervalStringToObject('{invalid,bracket}' as IntervalString);
                }).toThrowError('Invalid start bracket: {');
            });

            it('should throw error for invalid end bracket', () => {
                expect(() => {
                    parseIntervalStringToObject('[invalid,bracket}' as IntervalString);
                }).toThrowError('Invalid end bracket: }');
            });

            it('should throw error for missing comma', () => {
                expect(() => {
                    parseIntervalStringToObject('[no-comma-here]' as IntervalString);
                }).toThrowError('Invalid interval string (missing comma): [no-comma-here]');
            });

            it('should throw error for invalid date format', () => {
                expect(() => {
                    parseIntervalStringToObject('["invalid-date","2021-01-02T00:00:00Z"]' as IntervalString);
                }).toThrowError('Invalid date format: invalid-date');
            });
        });
    });

    describe('parseObjectToIntervalString', () => {
        describe('empty intervals', () => {
            it('should return empty for undefined interval', () => {
                const result = parseObjectToIntervalString(undefined as any);
                expect(result).toBe('empty');
            });

            it('should return empty for interval with no from and to', () => {
                const result = parseObjectToIntervalString({});
                expect(result).toBe('empty');
            });

            it('should return empty for interval with undefined from and to', () => {
                const result = parseObjectToIntervalString({from: undefined, to: undefined});
                expect(result).toBe('empty');
            });
        });

        describe('normal intervals', () => {
            it('should convert interval with both dates to string', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('["2021-01-01T00:00:00.000Z","2021-01-02T00:00:00.000Z"]');
            });

            it('should convert interval with start date and positive infinity', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z'),
                    to: 'infinity'
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('["2021-01-01T00:00:00.000Z",infinity]');
            });

            it('should convert interval with negative infinity and end date', () => {
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('[-infinity,"2021-01-02T00:00:00.000Z"]');
            });

            it('should convert interval with both infinities', () => {
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: 'infinity'
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('[-infinity,infinity]');
            });

            it('should convert dates to UTC', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T10:00:00+02:00'),
                    to: DateTime.fromISO('2021-01-02T15:30:00-05:00')
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('["2021-01-01T08:00:00.000Z","2021-01-02T20:30:00.000Z"]');
            });

            it('should handle undefined from with defined to', () => {
                const interval: DateRangeInterval = {
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('[-infinity,"2021-01-02T00:00:00.000Z"]');
            });

            it('should handle defined from with undefined to', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z')
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('["2021-01-01T00:00:00.000Z",infinity]');
            });
        });

        describe('invalid intervals', () => {
            it('should return empty for interval where to is before from', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-02T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-01T00:00:00Z') // Before from
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('empty');
            });

            it('should return empty for interval where to is same day but earlier time than from', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T15:00:00Z'),
                    to: DateTime.fromISO('2021-01-01T09:00:00Z') // Earlier time same day
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('empty');
            });

            it('should return empty for interval with microsecond difference where to < from', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T12:00:00.001Z'),
                    to: DateTime.fromISO('2021-01-01T12:00:00.000Z') // 1ms before from
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('empty');
            });

            it('should handle invalid intervals with timezone differences', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T14:00:00+02:00'), // 12:00 UTC
                    to: DateTime.fromISO('2021-01-01T13:00:00+02:00')   // 11:00 UTC (before from)
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('empty');
            });

            it('should handle mixed DateTime and infinity correctly', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z'),
                    to: 'infinity' // Should work correctly
                };
                const result = parseObjectToIntervalString(interval);

                expect(result).toBe('["2021-01-01T00:00:00.000Z",infinity]');
            });
        });
    });

    describe('areIntervalsEqual', () => {
        describe('equal intervals', () => {
            it('should return true for identical intervals', () => {
                const intervalA: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };
                const intervalB: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };

                expect(areIntervalsEqual(intervalA, intervalB)).toBe(true);
            });

            it('should return true for same moments in different timezones', () => {
                const intervalA: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T10:00:00+02:00'),
                    to: DateTime.fromISO('2021-01-02T15:30:00-05:00')
                };
                const intervalB: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T08:00:00Z'),
                    to: DateTime.fromISO('2021-01-02T20:30:00Z')
                };

                expect(areIntervalsEqual(intervalA, intervalB)).toBe(true);
            });

            it('should return true for intervals with infinity values', () => {
                const intervalA: DateRangeInterval = {
                    from: '-infinity',
                    to: 'infinity'
                };
                const intervalB: DateRangeInterval = {
                    from: '-infinity',
                    to: 'infinity'
                };

                expect(areIntervalsEqual(intervalA, intervalB)).toBe(true);
            });

            it('should return true for empty intervals', () => {
                const intervalA: DateRangeInterval = {};
                const intervalB: DateRangeInterval = {};

                expect(areIntervalsEqual(intervalA, intervalB)).toBe(true);
            });
        });

        describe('unequal intervals', () => {
            it('should return false for different start dates', () => {
                const intervalA: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };
                const intervalB: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-02T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };

                expect(areIntervalsEqual(intervalA, intervalB)).toBe(false);
            });

            it('should return false for different end dates', () => {
                const intervalA: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };
                const intervalB: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-03T00:00:00Z')
                };

                expect(areIntervalsEqual(intervalA, intervalB)).toBe(false);
            });

            it('should return false when one has infinity and other has date', () => {
                const intervalA: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };
                const intervalB: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };

                expect(areIntervalsEqual(intervalA, intervalB)).toBe(false);
            });

            it('should return false when one interval is empty and other is not', () => {
                const intervalA: DateRangeInterval = {};
                const intervalB: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-02T00:00:00Z')
                };

                expect(areIntervalsEqual(intervalA, intervalB)).toBe(false);
            });

            it('should return true for two identical invalid intervals', () => {
                const intervalA: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-02T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-01T00:00:00Z')
                };
                const intervalB: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-02T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-01T00:00:00Z')
                };

                expect(areIntervalsEqual(intervalA, intervalB)).toBe(true);
            });

            it('should return false for different invalid intervals', () => {
                const intervalA: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-02T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-01T00:00:00Z')
                };
                const intervalB: DateRangeInterval = {
                    from: DateTime.fromISO('2021-01-03T00:00:00Z'),
                    to: DateTime.fromISO('2021-01-01T00:00:00Z')
                };

                expect(areIntervalsEqual(intervalA, intervalB)).toBe(false);
            });
        });
    });

    describe('round-trip conversion', () => {
        it('should maintain consistency when converting object to string and back', () => {
            const originalInterval: DateRangeInterval = {
                from: DateTime.fromISO('2021-01-01T10:00:00+02:00'),
                to: DateTime.fromISO('2021-01-02T15:30:00-05:00')
            };

            const intervalString = parseObjectToIntervalString(originalInterval);
            const parsedInterval = parseIntervalStringToObject(intervalString);

            expect(areIntervalsEqual(originalInterval, parsedInterval)).toBe(true);
        });

        it('should maintain consistency for infinity intervals', () => {
            const originalInterval: DateRangeInterval = {
                from: '-infinity',
                to: 'infinity'
            };

            const intervalString = parseObjectToIntervalString(originalInterval);
            const parsedInterval = parseIntervalStringToObject(intervalString);

            expect(areIntervalsEqual(originalInterval, parsedInterval)).toBe(true);
        });

        it('should maintain consistency for empty intervals', () => {
            const originalInterval: DateRangeInterval = {};

            const intervalString = parseObjectToIntervalString(originalInterval);
            const parsedInterval = parseIntervalStringToObject(intervalString);

            expect(areIntervalsEqual(originalInterval, parsedInterval)).toBe(true);
        });
    });

    describe('isDateInInterval', () => {
        describe('empty intervals', () => {
            it('should return false for empty interval (both from and to undefined)', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {};

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return false for interval with both from and to null', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {from: undefined, to: undefined};

                expect(isDateInInterval(date, interval)).toBe(false);
            });
        });

        describe('finite intervals', () => {
            it('should return true for date within interval bounds', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });

            it('should return false for date before interval start', () => {
                const date = DateTime.fromISO('2021-05-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return false for date after interval end', () => {
                const date = DateTime.fromISO('2021-07-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return true for date exactly at interval start', () => {
                const date = DateTime.fromISO('2021-06-01T00:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });

            it('should return true for date exactly at interval end', () => {
                const date = DateTime.fromISO('2021-06-30T23:59:59Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });
        });

        describe('intervals with negative infinity', () => {
            it('should return true for any date when from is negative infinity and date is before to', () => {
                const date = DateTime.fromISO('1900-01-01T00:00:00Z');
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });

            it('should return false when from is negative infinity but date is after to', () => {
                const date = DateTime.fromISO('2021-07-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return true when from is negative infinity and date equals to', () => {
                const date = DateTime.fromISO('2021-06-30T23:59:59Z');
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });
        });

        describe('intervals with positive infinity', () => {
            it('should return true for any date when to is positive infinity and date is after from', () => {
                const date = DateTime.fromISO('2100-12-31T23:59:59Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: 'infinity'
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });

            it('should return false when to is positive infinity but date is before from', () => {
                const date = DateTime.fromISO('2021-05-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: 'infinity'
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return true when to is positive infinity and date equals from', () => {
                const date = DateTime.fromISO('2021-06-01T00:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: 'infinity'
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });
        });

        describe('intervals with both infinities', () => {
            it('should return true for any valid date when both bounds are infinity', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: 'infinity'
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });
        });

        describe('partial intervals', () => {
            it('should return false when only from is defined and date is after from', () => {
                const date = DateTime.fromISO('2021-07-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z')
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return false when only from is defined and date is before from', () => {
                const date = DateTime.fromISO('2021-05-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z')
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return false when only to is defined and date is before to', () => {
                const date = DateTime.fromISO('2021-05-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return false when only to is defined and date is after to', () => {
                const date = DateTime.fromISO('2021-07-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });
        });

        describe('invalid intervals (to < from)', () => {
            it('should return false for any date when to is before from', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-30T00:00:00Z'), // After to
                    to: DateTime.fromISO('2021-06-01T00:00:00Z')    // Before from
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return false when date is within the reversed range', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-20T00:00:00Z'), // After to
                    to: DateTime.fromISO('2021-06-10T00:00:00Z')    // Before from
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return false when date equals from in invalid interval', () => {
                const date = DateTime.fromISO('2021-06-30T00:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-30T00:00:00Z'), // After to
                    to: DateTime.fromISO('2021-06-01T00:00:00Z')    // Before from
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should return false when date equals to in invalid interval', () => {
                const date = DateTime.fromISO('2021-06-01T00:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-30T00:00:00Z'), // After to
                    to: DateTime.fromISO('2021-06-01T00:00:00Z')    // Before from
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should handle invalid intervals with same date but different times', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T15:00:00Z'), // 3 PM
                    to: DateTime.fromISO('2021-06-15T09:00:00Z')    // 9 AM (before from)
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should handle invalid intervals with timezone differences', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T16:00:00+02:00'), // 14:00 UTC
                    to: DateTime.fromISO('2021-06-15T14:00:00+02:00')    // 12:00 UTC (before from)
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should not treat intervals with mixed infinity and DateTime as invalid', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: 'infinity' // Valid interval - should work
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });

            it('should handle negative infinity correctly', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2021-06-30T00:00:00Z')
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });
        });

        describe('timezone handling', () => {
            it('should correctly compare dates in different timezones', () => {
                const date = DateTime.fromISO('2021-06-15T10:00:00+02:00'); // Same as 08:00:00Z
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T07:00:00Z'),
                    to: DateTime.fromISO('2021-06-15T09:00:00Z')
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });

            it('should handle interval bounds in different timezones', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T14:00:00+02:00'), // Same as 12:00:00Z
                    to: DateTime.fromISO('2021-06-15T18:00:00+02:00')   // Same as 16:00:00Z
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });

            it('should return false when timezone conversion puts date outside bounds', () => {
                const date = DateTime.fromISO('2021-06-15T06:00:00+02:00'); // Same as 04:00:00Z
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T07:00:00Z'),
                    to: DateTime.fromISO('2021-06-15T09:00:00Z')
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });
        });

        describe('error handling', () => {
            it('should throw error for invalid DateTime object', () => {
                const invalidDate = DateTime.invalid('test invalid');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(() => isDateInInterval(invalidDate, interval)).toThrowError('Invalid DateTime provided');
            });

            it('should throw error for non-DateTime object', () => {
                const nonDateTime = 'not-a-datetime' as any;
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(() => isDateInInterval(nonDateTime, interval)).toThrowError('Invalid DateTime provided');
            });

            it('should throw error for null date', () => {
                const nullDate = null as any;
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(() => isDateInInterval(nullDate, interval)).toThrowError('Invalid DateTime provided');
            });

            it('should throw error for undefined date', () => {
                const undefinedDate = undefined as any;
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-30T23:59:59Z')
                };

                expect(() => isDateInInterval(undefinedDate, interval)).toThrowError('Invalid DateTime provided');
            });
        });

        describe('edge cases', () => {
            it('should handle millisecond precision differences', () => {
                const date = DateTime.fromISO('2021-06-15T12:00:00.123Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T12:00:00.122Z'),
                    to: DateTime.fromISO('2021-06-15T12:00:00.124Z')
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });

            it('should return false for date exactly equal to exclusive bound (simulated)', () => {
                // Since isDateInInterval works with inclusive bounds, we test boundary behavior
                const date = DateTime.fromISO('2021-06-15T12:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T12:00:01Z'), // Exclusive-like behavior
                    to: DateTime.fromISO('2021-06-15T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(false);
            });

            it('should handle leap year dates', () => {
                const date = DateTime.fromISO('2020-02-29T12:00:00Z'); // Leap year
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2020-02-28T00:00:00Z'),
                    to: DateTime.fromISO('2020-03-01T23:59:59Z')
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });

            it('should handle very old dates', () => {
                const date = DateTime.fromISO('1900-01-01T00:00:00Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('1800-01-01T00:00:00Z'),
                    to: DateTime.fromISO('2000-01-01T00:00:00Z')
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });

            it('should handle far future dates', () => {
                const date = DateTime.fromISO('2100-12-31T23:59:59Z');
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2100-01-01T00:00:00Z'),
                    to: 'infinity'
                };

                expect(isDateInInterval(date, interval)).toBe(true);
            });
        });
    });

    describe('isIntervalInvalid', () => {
        describe('valid intervals', () => {
            it('should return false for empty interval (both from and to undefined)', () => {
                const interval: DateRangeInterval = {};
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false for interval with only from defined', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z')
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false for interval with only to defined', () => {
                const interval: DateRangeInterval = {
                    to: DateTime.fromISO('2021-06-30T00:00:00Z')
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false for normal valid interval', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-30T00:00:00Z')
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false for interval where from equals to', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T12:00:00Z'),
                    to: DateTime.fromISO('2021-06-15T12:00:00Z')
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false for interval with negative infinity from', () => {
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2021-06-30T00:00:00Z')
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false for interval with positive infinity to', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: 'infinity'
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false for interval with both infinities', () => {
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: 'infinity'
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false for valid interval with timezone differences', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T10:00:00+02:00'), // 08:00 UTC
                    to: DateTime.fromISO('2021-06-01T12:00:00+02:00')   // 10:00 UTC
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });
        });

        describe('invalid intervals', () => {
            it('should return true when to is before from (different days)', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-30T00:00:00Z'),
                    to: DateTime.fromISO('2021-06-01T00:00:00Z')
                };
                expect(isIntervalInvalid(interval)).toBe(true);
            });

            it('should return true when to is before from (same day, different times)', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T15:00:00Z'), // 3 PM
                    to: DateTime.fromISO('2021-06-15T09:00:00Z')   // 9 AM
                };
                expect(isIntervalInvalid(interval)).toBe(true);
            });

            it('should return true for minimal time difference (milliseconds)', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T12:00:00.001Z'),
                    to: DateTime.fromISO('2021-06-15T12:00:00.000Z')
                };
                expect(isIntervalInvalid(interval)).toBe(true);
            });

            it('should return true when to is before from with timezone conversion', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-15T16:00:00+02:00'), // 14:00 UTC
                    to: DateTime.fromISO('2021-06-15T14:00:00+02:00')   // 12:00 UTC
                };
                expect(isIntervalInvalid(interval)).toBe(true);
            });

            it('should return true for large time difference in wrong order', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2022-12-31T23:59:59Z'),
                    to: DateTime.fromISO('2021-01-01T00:00:00Z')
                };
                expect(isIntervalInvalid(interval)).toBe(true);
            });
        });

        describe('edge cases with infinity', () => {
            it('should return false when from is positive infinity (valid case)', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: 'infinity' // This is a valid case
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false when from is negative infinity (valid case)', () => {
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2021-06-01T00:00:00Z') // This is a valid case
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false when both are infinities (valid case)', () => {
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: 'infinity' // This is a valid case
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });
        });

        describe('edge cases with null/undefined', () => {
            it('should return false for null interval', () => {
                const interval = null as any;
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false for undefined interval', () => {
                const interval = undefined as any;
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false when from is null and to is DateTime', () => {
                const interval: DateRangeInterval = {
                    from: null as any,
                    to: DateTime.fromISO('2021-06-01T00:00:00Z')
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false when from is DateTime and to is null', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: null as any
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false when both from and to are null', () => {
                const interval: DateRangeInterval = {
                    from: null as any,
                    to: null as any
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });
        });

        describe('edge cases with invalid DateTime objects', () => {
            it('should return false when from is invalid DateTime', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.invalid('test invalid'),
                    to: DateTime.fromISO('2021-06-01T00:00:00Z')
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false when to is invalid DateTime', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2021-06-01T00:00:00Z'),
                    to: DateTime.invalid('test invalid')
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });

            it('should return false when both are invalid DateTime objects', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.invalid('test invalid 1'),
                    to: DateTime.invalid('test invalid 2')
                };
                expect(isIntervalInvalid(interval)).toBe(false);
            });
        });
    });
});