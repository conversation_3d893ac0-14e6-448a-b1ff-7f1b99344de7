<script lang="ts">
    import {MenuItem} from '@rgossiaux/svelte-headlessui';
    import {exists} from 'shared/utils/custom-utils';

    export let isDisabled = false;
    export let showWhen = true;
    export let dataQa: string | null = null;
    export let additionalClasses = '';
    export let href: string | null = null;
</script>

{#if showWhen}
    <MenuItem as="li" disabled="{isDisabled}" let:disabled let:active on:click>
        {#if exists(href) && !isDisabled}
            <a {href} class="dropdown-menu-item {additionalClasses}" data-qa="{dataQa}" class:disabled class:active>
                <slot/>
            </a>
        {:else}
            <span class="dropdown-menu-item {additionalClasses}" data-qa="{dataQa}" class:disabled class:active>
                <slot/>
            </span>
        {/if}
    </MenuItem>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .dropdown-menu-item {
        display: block;
        padding: @spacing-xs @spacing-ml;
        clear: both;
        font-weight: normal;
        color: var(--text-default);
        white-space: nowrap;
        text-decoration: none;
        cursor: pointer;

        &.disabled {
            opacity: 0.5;
            background-color: var(--disabled-bg);
            cursor: not-allowed;
        }

        &.active {
            background-color: var(--body-bg-blue-highlighted);
        }
    }
</style>