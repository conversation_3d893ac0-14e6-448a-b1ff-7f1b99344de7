import type {Table} from '@tanstack/svelte-table';
import type {Readable} from 'svelte/store';
import type {GridCommand, GridSettings, GridState, KeyboardEventModifiers, MouseEventModifiers, SelectedState, SerializedCellCoordinates} from 'shared/ui-widgets/grid/types';
import type {Subject} from 'rxjs';
import {CellCoordinates} from 'shared/ui-widgets/grid/types';
import {KeyBindings} from 'shared/ui-widgets/grid/state/key-bindings';
import {get, readonly} from 'svelte/store';
import {exists, isNull, isNullOrUndefined} from 'shared/utils/custom-utils';
import {isInEditMode, isInNormalMode} from './state-query';
import {getColumnByIndex} from 'shared/ui-widgets/grid/utils';
import {distinctStore} from 'shared/utils/store-utils';
import {isArrow, isDeleteOrBackspace, isPrintableCharacterKey, allowOnlyShiftModifier, SPECIAL_KEYS} from './key-utils';

export function createEmptyState(): GridState {
    return {
        cursor: {
            physicalCursor: null,
            virtualCursor: null
        },
        depended: {
            depended: new Set()
        },
        edit: {
            editedCell: null
        },
        hover: {
            hoverCursor: null,
            hoverRow: new Set(),
            hoverColumn: new Set()
        },
        selected: {
            allSelected: new Set(),
            selectedTop: new Set(),
            selectedBottom: new Set(),
            selectedLeft: new Set(),
            selectedRight: new Set()
        }
    };
}

export class GridStateManager {
    #state$ = distinctStore(createEmptyState());
    private keyBindings = new KeyBindings();

    // TODO: remove table$ reference if possible
    constructor(private settings: GridSettings<any>, private command$: Subject<GridCommand>, private table$: Readable<Table<any>>) {
        this.keyBindings
            .addBinding({
                matcher: (key) => key === SPECIAL_KEYS.ESCAPE,
                handler: () => this.updateState((state) => this.handleEscape(state))})
            .addBinding({
                matcher: (key) => key === SPECIAL_KEYS.ENTER,
                handler: () => this.updateState((state) => this.handleEnter(state))})
            .addBinding({
                matcher: (key, _, state) => {
                    return isInNormalMode(state) && isDeleteOrBackspace(key);
                },
                handler: () => {
                    this.updateState((state) => this.handleBackspaceAndDelete(state));
                }
            })
            .addBinding({
                matcher: (key, _, state) => {
                    return isInNormalMode(state) && isArrow(key);
                },
                handler: (key, modifiers) => {
                    this.updateState((state) => this.handleArrow(state, key, get(table$), modifiers));
                }
            })
            .addBinding({
                matcher: (key, modifiers, state) => {
                    return isInNormalMode(state) && isPrintableCharacterKey(key) && allowOnlyShiftModifier(modifiers);
                },
                handler: (key) => {
                    this.updateState((state) => this.handlePrintableCharacter(state, key));
                }
            });
    }

    public get state$() {
        return readonly(this.#state$);
    }

    public forceExitEditMode() {
        this.updateState((state) => this.exitEditMode(state));
    }

    public handleMouseDown(cellCoordinates: CellCoordinates | null, {shiftKey, ctrlKey}: MouseEventModifiers) {
        if (isNullOrUndefined(cellCoordinates)) {
            this.updateState((state) => {
                state = this.exitEditMode(state);
                state = this.resetCursors(state);
                state = this.resetSelection(state);
                return state;
            });
            return;
        }

        this.updateState((state) => {
            if (!cellCoordinates.isEqualTo(state.edit.editedCell)) {
                state = this.exitEditMode(state);
            }

            if (ctrlKey && this.settings.selectionEnabled) {
                if (state.selected.allSelected.has(cellCoordinates.serialize())) {
                    state = this.removeFromSelection(state, cellCoordinates);
                    state = this.resetSelectionIfEqualToPhysicalCursor(state);
                    state = this.setPhysicalCursorTo(state, cellCoordinates);
                } else {
                    state = this.addToSelection(state, state.cursor.physicalCursor); // add current position (for initial selection)
                    state = this.addToSelection(state, cellCoordinates);
                    state = this.setPhysicalCursorTo(state, cellCoordinates);
                    state = this.resetSelectionIfEqualToPhysicalCursor(state);
                }

                return state;
            }

            if (shiftKey && this.settings.selectionEnabled) {
                state = this.setVirtualCursorTo(state, cellCoordinates);
                state = this.updateSelection(state, state.cursor.physicalCursor, state.cursor.virtualCursor);
                state = this.resetSelectionIfEqualToPhysicalCursor(state);
                return state;
            }

            state = this.setPhysicalCursorTo(state, cellCoordinates);
            state = this.resetSelection(state);
            return state;
        });
    }

    public handleMouseOver(cellCoordinates: CellCoordinates | null, table: Table<any>, {dragging}: MouseEventModifiers) {
        if (isNullOrUndefined(cellCoordinates)) {
            this.updateState((state) => {
                state = this.resetHover(state);
                return state;
            });
            return;
        }

        this.updateState((state) => {
            this.setHoverCursor(state, cellCoordinates);

            if (this.settings.hoverRows) {
                state = this.setHoverRow(state, cellCoordinates, table);
            }

            if (this.settings.hoverColumns) {
                state = this.setHoverColumn(state, cellCoordinates, table);
            }

            if (dragging && this.settings.selectionEnabled) {
                state = this.setVirtualCursorTo(state, cellCoordinates);
                state = this.updateSelection(state, state.cursor.physicalCursor, state.cursor.virtualCursor);
                state = this.resetSelectionIfEqualToPhysicalCursor(state);
            }

            return state;
        });
    }

    public handleClickOutside() {
        this.updateState((state) => {
            state = this.exitEditMode(state);
            state = this.resetCursors(state);
            state = this.resetSelection(state);
            return state;
        });
    }

    public handleMouseLeave() {
        this.updateState((state) => this.resetHover(state));
    }

    // TODO: here wa injected table for arrow navigation, temporarily change to get in handler, IF POSSIBLE RETURN it here
    public handleKeyDown(key: string, table: Table<any>, eventModifiers: KeyboardEventModifiers) {
        this.keyBindings.handleKey(key, eventModifiers, this.#state$.getValue());
    }

    public hasKeyBinding(key: string, eventModifiers: KeyboardEventModifiers) {
        return this.keyBindings.hasKeyBinding(key, eventModifiers, this.#state$.getValue());
    }

    public handleDoubleClick(cellCoordinates: CellCoordinates) {
        if (!this.settings.editable) {
            return;
        }

        this.updateState((state) => {
            return this.enterEditMode(state, cellCoordinates);
        });
    }

    public handleGridFocus(table: Table<any>) {
        if (table.getRowModel().rows.length === 0) {
            return;
        }
        this.updateState((state) => {
            return this.setPhysicalCursorTo(state, new CellCoordinates({rowIndex: 0, columnIndex: 0}));
        });
    }

    public forceEditModeIn(cellCoordinates: CellCoordinates) {
        if (!this.settings.editable) {
            return;
        }

        this.updateState((state) => {
            state = this.resetCursors(state);
            state = this.resetSelection(state);
            state = this.setPhysicalCursorTo(state, cellCoordinates);
            return this.enterEditMode(state, cellCoordinates);
        });
    }

    private resetCursors(state: GridState): GridState {
        const cursor = {
            physicalCursor: null,
            virtualCursor: null
        };

        state = this.resetDepended(state);

        return {...state, cursor};
    }

    private setVirtualCursorTo(state: GridState, virtualCursor: CellCoordinates): GridState {
        const cursor = {
            physicalCursor: state.cursor.physicalCursor,
            virtualCursor: virtualCursor.copy()
        };
        return {...state, cursor};
    }

    private setPhysicalCursorTo(state: GridState, physicalCursor: CellCoordinates): GridState {
        const cursor = {
            virtualCursor: state.cursor.virtualCursor,
            physicalCursor: physicalCursor.copy()
        };

        state = this.setDepended(state, physicalCursor, get(this.table$));

        return {...state, cursor};
    }

    private resetSelection(state: GridState): GridState {
        const selected = {
            allSelected: new Set(),
            selectedTop: new Set(),
            selectedBottom: new Set(),
            selectedLeft: new Set(),
            selectedRight: new Set()
        } satisfies SelectedState;
        return {...state, cursor: {...state.cursor, virtualCursor: null}, selected};
    }

    private addToSelection(state: GridState, cell: CellCoordinates): GridState {
        const selected = {
            allSelected: new Set(state.selected.allSelected),
            selectedTop: new Set(state.selected.selectedTop),
            selectedBottom: new Set(state.selected.selectedBottom),
            selectedLeft: new Set(state.selected.selectedLeft),
            selectedRight: new Set(state.selected.selectedRight)
        } satisfies SelectedState;

        const serializedCell = cell.serialize();
        selected.allSelected.add(serializedCell);

        const topNeighbor = cell.up().serialize();
        if (selected.allSelected.has(topNeighbor)) {
            selected.selectedBottom.delete(topNeighbor);
        } else {
            selected.selectedTop.add(serializedCell);
        }

        const rightNeighbor = cell.right().serialize();
        if (selected.allSelected.has(rightNeighbor)) {
            selected.selectedLeft.delete(rightNeighbor);
        } else {
            selected.selectedRight.add(serializedCell);
        }

        const bottomNeighbor = cell.down().serialize();
        if (selected.allSelected.has(bottomNeighbor)) {
            selected.selectedTop.delete(bottomNeighbor);
        } else {
            selected.selectedBottom.add(serializedCell);
        }

        const leftNeighbor = cell.left().serialize();
        if (selected.allSelected.has(leftNeighbor)) {
            selected.selectedRight.delete(leftNeighbor);
        } else {
            selected.selectedLeft.add(serializedCell);
        }

        return {...state, selected};
    }

    private removeFromSelection(state: GridState, cell: CellCoordinates): GridState {
        const selected = {
            allSelected: new Set(state.selected.allSelected),
            selectedTop: new Set(state.selected.selectedTop),
            selectedBottom: new Set(state.selected.selectedBottom),
            selectedLeft: new Set(state.selected.selectedLeft),
            selectedRight: new Set(state.selected.selectedRight)
        } satisfies SelectedState;

        const serializedCell = cell.serialize();
        selected.allSelected.delete(serializedCell);

        const topNeighbor = cell.up().serialize();
        if (selected.allSelected.has(topNeighbor)) {
            selected.selectedBottom.add(topNeighbor);
        } else {
            selected.selectedTop.delete(serializedCell);
        }

        const rightNeighbor = cell.right().serialize();
        if (selected.allSelected.has(rightNeighbor)) {
            selected.selectedLeft.add(rightNeighbor);
        } else {
            selected.selectedRight.delete(serializedCell);
        }

        const bottomNeighbor = cell.down().serialize();
        if (selected.allSelected.has(bottomNeighbor)) {
            selected.selectedTop.add(bottomNeighbor);
        } else {
            selected.selectedBottom.delete(serializedCell);
        }

        const leftNeighbor = cell.left().serialize();
        if (selected.allSelected.has(leftNeighbor)) {
            selected.selectedRight.add(leftNeighbor);
        } else {
            selected.selectedLeft.delete(serializedCell);
        }

        return {...state, selected};
    }

    private updateSelection(state: GridState, from: CellCoordinates, to: CellCoordinates): GridState {
        const allSelected = new Set<SerializedCellCoordinates>();
        const selectedTop = new Set<SerializedCellCoordinates>();
        const selectedBottom = new Set<SerializedCellCoordinates>();
        const selectedLeft = new Set<SerializedCellCoordinates>();
        const selectedRight = new Set<SerializedCellCoordinates>();

        // Determine the selection boundaries
        const rowStart = Math.min(from.rowIndex, to.rowIndex);
        const rowEnd = Math.max(from.rowIndex, to.rowIndex);
        const colStart = Math.min(from.columnIndex, to.columnIndex);
        const colEnd = Math.max(from.columnIndex, to.columnIndex);

        // Iterate through the selection bounds
        for (let row = rowStart; row <= rowEnd; row++) {
            for (let col = colStart; col <= colEnd; col++) {
                const cellCoordinates = new CellCoordinates({
                    rowIndex: row,
                    columnIndex: col
                });
                const serializedCell = cellCoordinates.serialize();

                // Add to the main selected set
                allSelected.add(serializedCell);

                // Identify edge cells
                if (row === rowStart) selectedTop.add(serializedCell);
                if (row === rowEnd) selectedBottom.add(serializedCell);
                if (col === colStart) selectedLeft.add(serializedCell);
                if (col === colEnd) selectedRight.add(serializedCell);
            }
        }

        return {...state, selected: {allSelected, selectedTop, selectedBottom, selectedLeft, selectedRight}};
    }

    private resetSelectionIfEqualToPhysicalCursor(state: GridState): GridState {
        if (isNull(state.cursor.physicalCursor)) {
            return state;
        }
        if (state.selected.allSelected.size !== 1) {
            return state;
        }
        if (state.selected.allSelected.has(state.cursor.physicalCursor.serialize())) {
            return this.resetSelection(state);
        }
        return state;
    }

    private resetHover(state: GridState): GridState {
        return {...state, hover: {hoverCursor: null, hoverRow: new Set(), hoverColumn: new Set()}};
    }

    private resetDepended(state: GridState): GridState {
        return {...state, depended: {depended: new Set()}};
    }

    private setHoverCursor(state: GridState, cell: CellCoordinates): GridState {
        return {...state, hover: {...state.hover, hoverCursor: cell.copy()}};
    }

    private setHoverRow(state: GridState, cellCoordinates: CellCoordinates, table: Table<any>): GridState {
        const serializedHoverCoordinates = table.getRowModel().rows
            .at(cellCoordinates.rowIndex)
            .getVisibleCells()
            .map((_, columnIndex) => new CellCoordinates({rowIndex: cellCoordinates.rowIndex, columnIndex}))
            .map((hoverCoordinates) => hoverCoordinates.serialize());
        return {...state, hover: {...state.hover, hoverRow: new Set(serializedHoverCoordinates)}};
    }

    private setHoverColumn(state: GridState, cellCoordinates: CellCoordinates, table: Table<any>): GridState {
        const serializedHoverCoordinates = table.getRowModel().rows
            .map((row) => row.getVisibleCells().at(cellCoordinates.columnIndex))
            .map((_, rowIndex) => new CellCoordinates({rowIndex, columnIndex: cellCoordinates.columnIndex}))
            .map((hoverCoordinates) => hoverCoordinates.serialize());
        return {...state, hover: {...state.hover, hoverColumn: new Set(serializedHoverCoordinates)}};
    }

    // TODO: This will have to support highlighting on different record rows as well
    private setDepended(state: GridState, cellCoordinates: CellCoordinates, table: Table<any>): GridState {
        const column = getColumnByIndex(table, cellCoordinates.columnIndex);

        if (exists(column) && exists(column.columnDef.meta.staticDependentFieldTypeIds)) {
            const highlightCoordinates = column.columnDef.meta.staticDependentFieldTypeIds.flatMap((fieldTypeId) => {
                const columns = table.getAllFlatColumns().filter((col) => col.id.startsWith(fieldTypeId)); // Take all columns and sub-columns (d2020, d2020.a, d2020.main.a, e.g.)
                return columns.map((col) => new CellCoordinates({
                    rowIndex: cellCoordinates.rowIndex,
                    columnIndex: col.getIndex()
                }));
            });

            return {
                ...state,
                depended: {
                    depended: new Set(highlightCoordinates.map((coords) => coords.serialize()))
                }
            };
        }

        return this.resetDepended(state);
    }

    private enterEditMode(state: GridState, cellCoordinates: CellCoordinates, initialInput?: string): GridState {
        return {...state, edit: {editedCell: cellCoordinates.copy(), initialInput}};
    }

    private exitEditMode(state: GridState): GridState {
        return {...state, edit: {editedCell: null}};
    }

    private handleArrowUp(state: GridState, {shiftKey, ctrlKey}: KeyboardEventModifiers): GridState {
        if (ctrlKey) {
            state = this.setPhysicalCursorTo(state, state.cursor.physicalCursor.withRowIndex(0));
            state = this.resetSelection(state);
            return state;
        }

        if (shiftKey && this.settings.selectionEnabled) {
            if (isNull(state.cursor.virtualCursor)) {
                state = this.setVirtualCursorTo(state, state.cursor.physicalCursor);
            }

            if (state.cursor.virtualCursor.underRow(0)) {
                state = this.setVirtualCursorTo(state, state.cursor.virtualCursor.up());
                state = this.updateSelection(state, state.cursor.physicalCursor, state.cursor.virtualCursor);
                state = this.resetSelectionIfEqualToPhysicalCursor(state);
            }
            return state;
        }

        if (state.cursor.physicalCursor.underRow(0)) {
            state = this.setPhysicalCursorTo(state, state.cursor.physicalCursor.up());
            state = this.resetSelection(state);
        }
        return state;
    }

    private handleArrowDown(state: GridState, table: Table<any>, {
        shiftKey,
        ctrlKey
    }: KeyboardEventModifiers): GridState {
        const lastRowIndex = table.getRowModel().rows.length - 1;

        if (ctrlKey) {
            state = this.setPhysicalCursorTo(state, state.cursor.physicalCursor.withRowIndex(lastRowIndex));
            state = this.resetSelection(state);
            return state;
        }
        if (shiftKey && this.settings.selectionEnabled) {
            if (isNull(state.cursor.virtualCursor)) {
                state = this.setVirtualCursorTo(state, state.cursor.physicalCursor);
            }

            if (state.cursor.virtualCursor.aboveRow(lastRowIndex)) {
                state = this.setVirtualCursorTo(state, state.cursor.virtualCursor.down());
                state = this.updateSelection(state, state.cursor.physicalCursor, state.cursor.virtualCursor);
                state = this.resetSelectionIfEqualToPhysicalCursor(state);
            }
            return state;
        }

        if (state.cursor.physicalCursor.aboveRow(lastRowIndex)) {
            state = this.setPhysicalCursorTo(state, state.cursor.physicalCursor.down());
            state = this.resetSelection(state);
        }
        return state;
    }

    private handleArrowLeft(state: GridState, {shiftKey, ctrlKey}: KeyboardEventModifiers): GridState {
        if (ctrlKey) {
            state = this.setPhysicalCursorTo(state, state.cursor.physicalCursor.withColumnIndex(0));
            state = this.resetSelection(state);
            return state;
        }
        if (shiftKey && this.settings.selectionEnabled) {
            if (isNull(state.cursor.virtualCursor)) {
                state = this.setVirtualCursorTo(state, state.cursor.physicalCursor);
            }

            if (state.cursor.virtualCursor.rightFromColumn(0)) {
                state = this.setVirtualCursorTo(state, state.cursor.virtualCursor.left());
                state = this.updateSelection(state, state.cursor.physicalCursor, state.cursor.virtualCursor);
                state = this.resetSelectionIfEqualToPhysicalCursor(state);
            }
            return state;
        }
        if (state.cursor.physicalCursor.rightFromColumn(0)) {
            state = this.setPhysicalCursorTo(state, state.cursor.physicalCursor.left());
            state = this.resetSelection(state);
        }
        return state;
    }

    private handleArrowRight(state: GridState, table: Table<any>, {
        shiftKey,
        ctrlKey
    }: KeyboardEventModifiers): GridState {
        const lastColumnIndex = table.getVisibleLeafColumns().length - 1;

        if (ctrlKey) {
            state = this.setPhysicalCursorTo(state, state.cursor.physicalCursor.withColumnIndex(lastColumnIndex));
            state = this.resetSelection(state);
            return state;
        }
        if (shiftKey && this.settings.selectionEnabled) {
            if (isNull(state.cursor.virtualCursor)) {
                state = this.setVirtualCursorTo(state, state.cursor.physicalCursor);
            }

            if (state.cursor.virtualCursor.leftFromColumn(lastColumnIndex)) {
                state = this.setVirtualCursorTo(state, state.cursor.virtualCursor.right());
                state = this.updateSelection(state, state.cursor.physicalCursor, state.cursor.virtualCursor);
                state = this.resetSelectionIfEqualToPhysicalCursor(state);
            }
            return state;
        }
        if (state.cursor.physicalCursor.leftFromColumn(lastColumnIndex)) {
            state = this.setPhysicalCursorTo(state, state.cursor.physicalCursor.right());
            state = this.resetSelection(state);
        }
        return state;
    }

    private handleArrow(state: GridState, key: string, table: Table<any>, eventModifiers: KeyboardEventModifiers): GridState {
        if (isInEditMode(state)) {
            return state;
        }

        if (isNull(state.cursor.physicalCursor)) {
            return state;
        }
        switch (key) {
            case 'ArrowUp':
                return this.handleArrowUp(state, eventModifiers);
            case 'ArrowDown':
                return this.handleArrowDown(state, table, eventModifiers);
            case 'ArrowLeft':
                return this.handleArrowLeft(state, eventModifiers);
            case 'ArrowRight':
                return this.handleArrowRight(state, table, eventModifiers);
            default:
                throw new Error('Unreachable - all arrows should be handled');
        }
    }

    private handleEscape(state: GridState): GridState {
        if (isInEditMode(state)) {
            this.rollbackCellValue(state);
            return this.exitEditMode(state);
        }
        return state;
    }

    private handleEnter(state: GridState): GridState {
        if (!this.settings.editable) {
            return state;
        }

        if (isNull(state.cursor.physicalCursor)) {
            return state;
        }

        if (isInEditMode(state)) {
            return this.exitEditMode(state);
        }

        return this.enterEditMode(state, state.cursor.physicalCursor);
    }

    private handlePrintableCharacter(state: GridState, character: string): GridState {
        if (!this.settings.editable) {
            return state;
        }

        if (isInEditMode(state)) {
            return state;
        }

        if (isNull(state.cursor.physicalCursor)) {
            return state;
        }

        return this.enterEditMode(state, state.cursor.physicalCursor, character);
    }

    private handleBackspaceAndDelete(state: GridState): GridState {
        if (!this.settings.editable) {
            return state;
        }
        if (isInEditMode(state)) {
            return state;
        }
        if (exists(state.cursor.physicalCursor)) {
            this.deleteCell(state);
            return state;
        }
        return state;
    }

    private updateState(updater: (_: GridState) => GridState) {
        this.#state$.update(updater);
    }

    private deleteCell(state: GridState) {
        this.command$.next(this.cellDeleteCommand(state.cursor.physicalCursor.copy()));
    }

    private rollbackCellValue(state: GridState) {
        this.command$.next(this.cellRollbackCommand(state.edit.editedCell.copy()));
    }

    private cellDeleteCommand(coordinates: CellCoordinates): GridCommand {
        return {target: coordinates, type: 'delete'};
    }

    private cellRollbackCommand(coordinates: CellCoordinates): GridCommand {
        return {target: coordinates, type: 'rollback-cell-change'};
    }
}
