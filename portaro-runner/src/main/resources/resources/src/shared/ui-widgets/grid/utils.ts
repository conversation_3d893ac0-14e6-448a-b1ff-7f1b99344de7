import type {CellContext, ColumnDefTemplate, Row} from '@tanstack/table-core';
import type {Identified} from 'typings/portaro.be.types';
import type {ViewableDepartment} from 'typings/portaro.fe.types';
import type {Writable} from 'svelte/store';
import type {Column, Table, TableOptions} from '@tanstack/svelte-table';
import type {BaseCellCoordinates, SerializedCellCoordinates} from 'shared/ui-widgets/grid/types';
import type {TableState, Updater} from '@tanstack/table-core';
import type {RenderConfig} from 'svelte-render';
import {SEPARATOR_CHAR} from 'shared/ui-widgets/grid/constants';
import {assertTrue, exists, isNullOrUndefined} from 'shared/utils/custom-utils';
import {CellCoordinates} from 'shared/ui-widgets/grid/types';
import {assertIsNonEmptyString} from 'shared/utils/string-utils';
import {stringifyArgument} from 'shared/utils/error-utils';
import {assertIsFunction} from 'shared/utils/types-utils';

export function getRenderConfig<CONTEXT extends Record<string, any>>(columnDefTemplate: ColumnDefTemplate<CONTEXT>, context: CONTEXT): RenderConfig {
    if (isNullOrUndefined(columnDefTemplate)) {
        return '';
    }
    assertIsFunction(columnDefTemplate);
    const renderConfig = columnDefTemplate(context);
    if (isNullOrUndefined(renderConfig)) {
        return '';
    }

    return renderConfig;
}

export function getTopMostColumn<TData>(column: Column<TData>): Column<TData> {
    let current = column;
    while (exists(current.parent)) {
        current = current.parent;
    }
    return current;
}

export function getSiblingRowsByRow<TData>(row: Row<TData>): Row<TData>[] {
    return row.getParentRow().subRows;
}

export function isFirstSubRow<TData>(row: Row<TData>): boolean {
    assertTrue(row.depth > 0, `Row ${stringifyArgument(row)} must be sub row`);
    return row.index === 0;
}

export function isLastSubRow<TData>(row: Row<TData>): boolean {
    assertTrue(row.depth > 0, `Row ${stringifyArgument(row)} must be sub row`);
    return row.index === getSiblingRowsByRow(row).length - 1;
}

export function getAncestorRowAtDepth<TData>(row: Row<TData>, depth: number) {
    assertTrue(depth >= 0, 'You can not go above root');
    assertTrue(depth < row.depth, 'Ancestor must have lower depth than current row');

    return row.getParentRows().at(depth);
}

export function isLastColumnInMainColumnGroup<TData>(column: Column<TData>) {
    const parentColumn = column.parent;

    if (isNullOrUndefined(parentColumn)) {
        // No parent means it's already a main column, and hence the last in itself
        return true;
    }

    const subColumns = parentColumn.columns;
    return subColumns.at(-1) === column;
}

export function defaultRowId<TData extends Identified<any>>(): (originalRow: TData, index: number, parent?: Row<TData>) => string {
    return (row, _, parentRow) => `${parentRow ? [parentRow.id, row.id].join('.') : row.id}`;
}

export function defaultCellContent<TData extends Identified<any>>(): ColumnDefTemplate<CellContext<TData, string>> {
    return ({cell}) => cell.getValue<string>();
}

export function defaultGlobalFilter(rowContainsSearchPhrase: (row: Row<ViewableDepartment>, searchPhrase: string) => boolean): (originalRow: Row<any>, columnId: string, filterValue: string) => boolean {
    return (originalRow: Row<any>, columnId: string, filterValue: string): boolean => {
        const searchPhrase = filterValue.toLowerCase();
        const found = rowContainsSearchPhrase(originalRow, searchPhrase);
        const foundInSubtree = originalRow.getLeafRows().some((row) => rowContainsSearchPhrase(row, searchPhrase));
        if (foundInSubtree) {
            originalRow.toggleExpanded(true);
        }
        return found || foundInSubtree;
    };
}

export function getStateOf<T>(value: T, updaterOrValue: Updater<T>) {
    if (updaterOrValue instanceof Function) {
        return updaterOrValue(value);
    } else {
        return updaterOrValue;
    }
}

type TableStateSlice = {
    [K in keyof TableState]: { [P in K]: TableState[K] };
}[keyof TableState];

export function updateTableState<TData>(tableOptions: Writable<TableOptions<TData>>, stateSlice: TableStateSlice) {
    tableOptions.update((currentTableOptions) => ({
        ...currentTableOptions,
        state: {
            ...currentTableOptions.state,
            ...stateSlice
        }
    }));
}

export function replaceTableData<TData>(tableOptions: Writable<TableOptions<TData>>, data: TData[]) {
    tableOptions.update((currentTableOptions) => ({
        ...currentTableOptions,
        data
    }));
}

export function appendTableData<TData>(tableOptions: Writable<TableOptions<TData>>, data: TData[]) {
    tableOptions.update((currentTableOptions) => ({
        ...currentTableOptions,
        data: [...currentTableOptions.data, ...data]
    }));
}

export function prependTableData<TData>(tableOptions: Writable<TableOptions<TData>>, data: TData[]) {
    tableOptions.update((currentTableOptions) => ({
        ...currentTableOptions,
        data: [...data, ...currentTableOptions.data]
    }));
}

export function serializeCellCoordinates(cellCoordinates: BaseCellCoordinates): SerializedCellCoordinates {
    return `${cellCoordinates.rowIndex}${SEPARATOR_CHAR}${cellCoordinates.columnIndex}`;
}

export function parseCellCoordinates(serializedCellCoordinates: SerializedCellCoordinates): BaseCellCoordinates {
    assertIsNonEmptyString(serializedCellCoordinates);
    const [rowIndex, columnIndex] = serializedCellCoordinates.split(SEPARATOR_CHAR);
    return {
        rowIndex: parseInt(rowIndex, 10),
        columnIndex: parseInt(columnIndex, 10)
    };
}

export function getColumnByIndex<ROW>(table: Table<ROW>, index: number) {
    return getVisibleOrderedLeafColumns(table).at(index);
}

export function getRowByIndex<ROW>(table: Table<ROW>, index: number) {
    return table.getRowModel().rows.at(index);
}

export function getCellValueByCoordinates<ROW, VALUE>(table: Table<ROW>, cell: CellCoordinates): VALUE {
    const column = getColumnByIndex(table, cell.columnIndex);
    const row = getRowByIndex(table, cell.rowIndex);
    return row.getValue(column.id);
}

export function tryToFindGridCell(target: Element): HTMLElement | undefined {
    return target.closest<HTMLElement>('.kp-generic-grid-cell');
}

export function extractCoordinates(cellElement: HTMLElement): CellCoordinates {
    const {rowIndex, columnIndex} = cellElement.dataset;
    return new CellCoordinates({
        rowIndex: Number.parseInt(rowIndex, 10),
        columnIndex: Number.parseInt(columnIndex, 10)
    });
}

export function tryToFindRowByElement<ROW>(element: Element, table: Table<ROW>): Row<ROW> | null {
    const cellElement = tryToFindGridCell(element);
    if (isNullOrUndefined(cellElement)) {
        return null;
    }
    const coordinates = extractCoordinates(cellElement);
    return getRowByIndex(table, coordinates.rowIndex);
}

export function getVisibleOrderedLeafColumns<ROW>(table: Table<ROW>){
    return [
        ...table.getLeftVisibleLeafColumns(),
        ...table.getCenterVisibleLeafColumns(),
        ...table.getRightVisibleLeafColumns(),
    ];
}