<script>
    import {Meta, Template, Story} from '@storybook/addon-svelte-csf';
    import KpPopover from './KpPopover.svelte';
</script>

<Meta title="Widgets/KpPopover"
      description="popover"
      component={KpPopover}

      argTypes={{
            buttonStyle: {
                control: { type: 'select' },
                options: ['default' , 'primary' , 'success' , 'info' , 'warning' , 'danger']
            },
            buttonSize: {
                control: { type: 'select' },
                options: ['xs' , 'sm' , 'md' , 'lg']
            },
            placement: {
                control: { type: 'select' },
                options: ['bottom' , 'right' , 'top' , 'left', 'bottom-start' , 'right-start' , 'top-start' , 'left-start', 'bottom-end' , 'right-end' , 'top-end' , 'left-end']
            },
            additionalPopoverButtonClasses: { control: 'text' },
            additionalPopoverPanelClasses: { control: 'text' },
            id: { control: 'text' },
            buttonSlot: { control: 'text' },
            titleSlot: { control: 'text' },
            contentSlot: { control: 'text' }


  }}
/>

<Template let:args>
    <KpPopover {...args}>
        <svelte:fragment slot="button">{args.buttonSlot}</svelte:fragment>
        <svelte:fragment slot="popover-title">{args.titleSlot}</svelte:fragment>
        <svelte:fragment slot="popover-content">{args.contentSlot}</svelte:fragment>
    </KpPopover>
</Template>

<Story name='Default'
       args={{buttonStyle: 'default', buttonSize: 'md', buttonSlot: 'button', titleSlot: 'Optional title', contentSlot: 'Content'}}
/>