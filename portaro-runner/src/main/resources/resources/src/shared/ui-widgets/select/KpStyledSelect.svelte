<script lang="ts" generics="VALUE">
    export let additionalClasses = '';
    export let value: VALUE;
</script>

<select class="kp-styled-select
        {additionalClasses}"
        {...$$restProps}
        bind:value
        on:change>

    <slot/>
</select>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    @selector-height: 28px;

    .kp-styled-select {
        color: @themed-text-default;
        background-color: @themed-body-bg;
        border: 1px solid @themed-border-default;
        border-radius: @border-radius-default;
        height: @selector-height;
        min-width: 56px;
        outline: none;
        transition: border-color 0.3s ease-in-out;

        &:focus {
            border-color: var(--accent-blue-new);
        }

        &:focus-visible {
            outline: none !important;
            box-shadow: none !important;
        }
    }
</style>