<script lang="ts">
    import type {Px} from 'shared/ui-widgets/types';
    import {exists} from 'shared/utils/custom-utils';

    export let marginVertical: Px = '5px';
    export let marginOverride: string | null = null;
    export let height = '100%';
</script>

<div class="kp-vertical-separator"
     style:--margin="{exists(marginOverride) ? marginOverride : `${marginVertical} 0`}"
     style:--height="{height}"
     role="separator">
</div>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";

    .kp-vertical-separator {
        clear: both;
        width: 1px;
        background-color: @themed-border-default;
        height: var(--height);
        margin: var(--margin);
    }
</style>