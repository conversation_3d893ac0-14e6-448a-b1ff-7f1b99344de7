<script>
    import {<PERSON>a, Story, Template} from '@storybook/addon-svelte-csf';
    import TestDependencyProvider from '../../../test-utils/TestDependencyProvider.svelte';
    import KpValueEditor from '../../../kp-value-editor/KpValueEditor.svelte';
    import TestValueEditorContainer from '../../../test-utils/TestValueEditorContainer.svelte';
    import {VALUE_EDITORS_COMMON_STORIES_ARG_TYPES, VALUE_EDITORS_COMMON_STORIES_ARGS} from '../../../test-utils/common-stories-settings';

    const continents = ['Asia', 'Africa', 'America', 'Europe', 'Australia and Oceania', 'Antarctic']; // mock autocomplete data
    const storiesArgs = {
        ...Object.fromEntries(Object.entries(VALUE_EDITORS_COMMON_STORIES_ARGS).map(([name, arg]) => [name, {...arg, options: {dataSource: () => Promise.resolve(continents)}}])),
    };

    const argTypes = {
        ...VALUE_EDITORS_COMMON_STORIES_ARG_TYPES,
        model: {
            control: 'text',
            description: 'model value',
            table: {
                type: {
                    summary: 'string',
                }
            },
        },
        options: {
            control: 'object',
            description: 'text value editor specific configuration options and flags',
            table: {
                defaultValue: { summary: '{}' },
                type: {
                    summary: 'AutocompleteValueEditorOptions',
                }
            },
        },
        validations: {
            control: 'object',
            description: 'object for configuring validations',
            table: {
                defaultValue: { summary: '{}' },
                type: {
                    summary: 'AutocompleteValueEditorValidations',
                }
            },
        },
        localizations: {
            control: 'object',
            description: 'dictionary for custom localization messages overriding default ones',
            table: {
                type: {
                    summary: 'AutocompleteValueEditorLocalizations',
                }
            },
        }
    }

    export let editor;
</script>


<Meta
        title="Value Editors/Autocomplete"
        component={KpValueEditor}
        {argTypes}
        parameters={{
            docs: {
                description: {
                    component: 'Text input editor with autocomplete popup'
                }
            }
        }}
/>


<Template let:args>
    <TestDependencyProvider>
        <TestValueEditorContainer label={args.editorId}
                                  editorId={args.editorId}
                                  formControl={editor ? editor.getFormController() : undefined}
                                  size={args.size}
                                  forceShowErrors={args.forceShowErrors}>
            <KpValueEditor {...args}
                           type="autocomplete"
                           model={args.model}
                           bind:this={editor}
                           on:model-change={args.onModelChange}/>
        </TestValueEditorContainer>
    </TestDependencyProvider>
</Template>

<Story name='Default' args={storiesArgs.default}/>
<Story name='Size SM' args={storiesArgs['size-sm']}/>
<Story name='Size XS' args={storiesArgs['size-xs']}/>
<Story name='With placeholder' args={storiesArgs.placeholder}/>
<Story name='With description' args={storiesArgs.description}/>
<Story name='Errored' args={storiesArgs.errored}/>
<Story name='Disabled' args={storiesArgs.disabled}/>
<Story name='Not visible' args={storiesArgs['not-visible']}/>

