import valueEditorModule from '../../../value-editors.base.module';
import {createTestContext} from '../../../../../test-utils/utils';
import IInjectorService = angular.auto.IInjectorService;
import {
    createEditor, getDescriptionElement,
    getErrorMessageElementByErrorType,
    getInputElement,
    setEditorModelValue,
    setInputValueOn,
    triggerBlurEventOn,
    triggerFocusEventOn,
    waitForEvent,
    waitForNextState
} from '../../../test-utils/utils';
import {firstValueFrom} from 'rxjs';
import {tick} from 'svelte';
import type {AutocompleteValueEditorLocalizations} from './types';
import {waitForElementToBeRemoved} from '@testing-library/svelte';
import {AngularManualInjector} from 'core/injector';

const mockData = [
    'France',
    'Germany',
    'United Kingdom',
    'Czechia',
    'Norway',
    'Netherlands',
    'Spain',
    'Italy',
    'Greece',
    'Turkey',
    'Poland',
    'Sweden',
    'Finland',
    'Denmark',
    'Belgium',
    'Austria',
    'Slovakia',
    'Slovenia',
    'United States of America'
]


describe('autocomplete', () => {

    let testContext;

    beforeEach(() => {
        angular.mock.module(valueEditorModule);

        inject(/*@ngInject*/ ($injector: IInjectorService) => {
            testContext = createTestContext(new AngularManualInjector($injector));
        });
    });

    it('should render editor', async () => {
        const {container, componentInstance, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext);

        expect(componentInstance).toBeTruthy();
        expect(getInputElement<HTMLInputElement>(container)).toBeTruthy();

        unmount();
    });

    it('should not render editorId', async () => {
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('id')).toBeFalsy();

        unmount();
    });

    it('should render editorName', async () => {
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('name')).toMatch(new RegExp('^autocomplete_[a-fA-F0-9-_]+'));

        unmount();
    });

    it('should render editorId and editorName', async () => {
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, null, {
            editorId: 'test-id',
            editorName: 'test-name'
        });

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('id')).toBe('test-id');
        expect(inputElement.getAttribute('name')).toBe('test-name');

        unmount();
    });

    it('should change model on input', async () => {
        const {container, componentInstance, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, 'world');

        const newModelValue: string = await waitForEvent(componentInstance, 'model-change');
        expect(newModelValue).toEqual('world');
        expect(componentInstance.model).toEqual('world');

        unmount();
    });

    it('should change value if model changed', async () => {
        const {container, componentInstance, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.value).toBe('');

        await setEditorModelValue(componentInstance, 'hello');
        expect(inputElement.value).toBe('hello');

        unmount();
    });

    it('should have working input and button disabling', async () => {
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, null, {
            isDisabled: true
        });

        expect(getInputElement<HTMLInputElement>(container).disabled).toBe(true);
        expect(getInputElement<HTMLInputElement>(container).parentElement.querySelector('button').disabled).toBe(true);
        unmount();
    });

    it('should have working required validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, 'hello', {
            validations: {
                required: true
            }
        });

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);

        setInputValueOn(inputElement, '');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('required')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeTruthy();

        setInputValueOn(inputElement, 'world');
        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('required')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'required'), {container});
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeFalsy();

        unmount();
    });

    it('should have working minlength validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, 'hello', {
            validations: {
                minlength: 3
            }
        });

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);

        setInputValueOn(inputElement, 'hi');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('minlength')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'minlength')).toBeTruthy();

        setInputValueOn(inputElement, 'world');
        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('minlength')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'minlength'), {container});
        expect(getErrorMessageElementByErrorType(container, 'minlength')).toBeFalsy();

        unmount();
    });

    it('should have working maxlength validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, 'hi', {
            validations: {
                maxlength: 3
            }
        });

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);

        setInputValueOn(inputElement, 'hello');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('maxlength')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'maxlength')).toBeTruthy();

        setInputValueOn(inputElement, 'pop');
        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('maxlength')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'maxlength'), {container});
        expect(getErrorMessageElementByErrorType(container, 'maxlength')).toBeFalsy();

        unmount();
    });

    it('should have working pattern validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, '42', {
            validations: {
                pattern: '[0-9]+'
            }
        });

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);

        setInputValueOn(inputElement, 'hello');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('pattern')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'pattern')).toBeTruthy();

        setInputValueOn(inputElement, '1212');
        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('pattern')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'pattern'), {container});
        expect(getErrorMessageElementByErrorType(container, 'pattern')).toBeFalsy();

        unmount();
    });

    it('should have working emptyAsNull option', async () => {
        const {container, componentInstance, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, 'hello', {
            options: {
                emptyAsNull: true
            }
        });

        expect(componentInstance.model).toEqual('hello');
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '');

        const newModelValue: string = await waitForEvent(componentInstance, 'model-change');
        expect(newModelValue).toBeNull();
        expect(componentInstance.model).toBeNull();

        unmount();
    });

    it('should have working notBlank validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, null, {
            validations: {
                notBlank: true
            }
        });

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);

        setInputValueOn(inputElement, '   ');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('notBlank')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'notBlank')).toBeTruthy();


        setInputValueOn(inputElement, '    hello');
        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('notBlank')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'notBlank'), {container});
        expect(getErrorMessageElementByErrorType(container, 'notBlank')).toBeFalsy();


        setInputValueOn(inputElement, 'hello     ');
        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('notBlank')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        expect(getErrorMessageElementByErrorType(container, 'notBlank')).toBeFalsy();

        setInputValueOn(inputElement, '   hello     hi');
        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('notBlank')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        expect(getErrorMessageElementByErrorType(container, 'notBlank')).toBeFalsy();


        setInputValueOn(inputElement, '');
        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('notBlank')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        expect(getErrorMessageElementByErrorType(container, 'notBlank')).toBeFalsy();

        unmount();
    });

    it('should be focused', async () => {
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, null, {isFocused: true}, true);

        expect(document.activeElement).toEqual(getInputElement<HTMLInputElement>(container));
        unmount();
    });

    it('should have working touched state', async () => {
        const {container, componentInstance, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, null);

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);

        expect(await firstValueFrom(controller.isTouched$())).toBe(false);
        triggerBlurEventOn(inputElement);

        expect(await firstValueFrom(controller.isTouched$())).toBe(true);

        unmount();
    });

    it('should render editors description', async () => {
        const DESCRIPTION = 'some-description';
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, null, {
            localizations: {description: DESCRIPTION} as AutocompleteValueEditorLocalizations
        });

        const descriptionElement = getDescriptionElement(container)
        expect(descriptionElement).toBeTruthy();
        expect(descriptionElement.textContent).toEqual(DESCRIPTION);
        expect(getInputElement(container).getAttribute('aria-describedby')).toEqual(descriptionElement.id);

        unmount();
    });

    it('should fetch items on focus with given parameters', async () => {
        const dataSourceSpy = jasmine.createSpy('dataSource').and.resolveTo([...mockData]);
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, 'an', {
            options: {
                dataSource: dataSourceSpy,
                staticParams: {remoteDataSourceUrl: 'world'}
            }
        });

        const inputElement = getInputElement<HTMLInputElement>(container);
        triggerFocusEventOn(inputElement);

        expect(dataSourceSpy).toHaveBeenCalledWith({model: 'an', staticParams: {remoteDataSourceUrl: 'world'}});

        unmount();
    });

    it('should open dropdown with model matching items on input focus', async () => {
        const dataSourceSpy = jasmine.createSpy('dataSource').and.resolveTo([...mockData]);
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, 'an', {
            options: {
                dataSource: dataSourceSpy,
            }
        });
        const inputElement = getInputElement<HTMLInputElement>(container);
        const parentElement = inputElement.parentElement;

        triggerFocusEventOn(inputElement);

        expect(dataSourceSpy).toHaveBeenCalled();

        await tick();
        await tick();
        await tick();

        const ulElement = parentElement.querySelector<HTMLUListElement>('ul');
        const liElements = parentElement.querySelectorAll<HTMLLIElement>('li');

        expect(ulElement).toBeTruthy();
        expect(ulElement.classList.contains('opened')).toBeTrue();
        expect(liElements.length).toBe(5);
        unmount();
    });

    it('should open dropdown with model matching items on button click', async () => {
        const dataSourceSpy = jasmine.createSpy('dataSource').and.resolveTo([...mockData]);
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, 'an', {
            options: {
                dataSource: dataSourceSpy,
            }
        }, true);
        const inputElement = getInputElement<HTMLInputElement>(container);
        const parentElement = inputElement.parentElement;

        parentElement.querySelector<HTMLButtonElement>('button').click();

        expect(dataSourceSpy).toHaveBeenCalled();

        await tick();
        await tick();
        await tick();

        const ulElement = parentElement.querySelector<HTMLUListElement>('ul');
        const liElements = parentElement.querySelectorAll<HTMLLIElement>('li');

        expect(ulElement).toBeTruthy();
        expect(ulElement.classList.contains('opened')).toBeTrue();
        expect(liElements.length).toBe(5);
        unmount();
    });

    it('should open dropdown with all items on button click if input is empty', async () => {
        const dataSourceSpy = jasmine.createSpy('dataSource').and.resolveTo([...mockData]);
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, null, {
            options: {
                dataSource: dataSourceSpy,
            }
        }, true);
        const inputElement = getInputElement<HTMLInputElement>(container);
        const parentElement = inputElement.parentElement;

        parentElement.querySelector<HTMLButtonElement>('button').click();

        expect(dataSourceSpy).toHaveBeenCalled();

        await tick();
        await tick();
        await tick();

        const ulElement = parentElement.querySelector<HTMLUListElement>('ul');
        const liElements = parentElement.querySelectorAll<HTMLLIElement>('li');

        expect(ulElement).toBeTruthy();
        expect(ulElement.classList.contains('opened')).toBeTrue();
        expect(liElements.length).toBe(mockData.length);
        unmount();
    });

    it('should open dropdown with model matching items on button click regardless of minLength', async () => {
        const dataSourceSpy = jasmine.createSpy('dataSource').and.resolveTo([...mockData]);
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, 'an', {
            options: {
                minLength: 3,
                dataSource: dataSourceSpy,
            }
        }, true);
        const inputElement = getInputElement<HTMLInputElement>(container);
        const parentElement = inputElement.parentElement;

        parentElement.querySelector<HTMLButtonElement>('button').click();

        expect(dataSourceSpy).toHaveBeenCalled();

        await tick();
        await tick();
        await tick();

        const ulElement = parentElement.querySelector<HTMLUListElement>('ul');
        const liElements = parentElement.querySelectorAll<HTMLLIElement>('li');

        expect(ulElement).toBeTruthy();
        expect(ulElement.classList.contains('opened')).toBeTrue();
        expect(liElements.length).toBe(5);
        unmount();
    });

    it('should not open dropdown on focus if models length is less then minLength', async () => {
        const dataSourceSpy = jasmine.createSpy('dataSource').and.resolveTo([...mockData]);
        const {container, unmount} = await createEditor<'autocomplete', string>('autocomplete', testContext, 'an', {
            options: {
                minLength: 3,
                dataSource: dataSourceSpy,
            }
        });
        const inputElement = getInputElement<HTMLInputElement>(container);
        const parentElement = inputElement.parentElement;

        triggerFocusEventOn(inputElement);

        expect(dataSourceSpy).toHaveBeenCalled();

        await tick();
        await tick();
        await tick();

        const ulElement = parentElement.querySelector<HTMLUListElement>('ul');
        const liElements = parentElement.querySelectorAll<HTMLLIElement>('li');

        expect(ulElement).toBeTruthy();
        expect(ulElement.classList.contains('opened')).toBeFalse();
        expect(liElements.length).toBe(0);
        unmount();
    });
});
