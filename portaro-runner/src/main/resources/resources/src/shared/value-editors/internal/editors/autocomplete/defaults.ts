import type {DefaultOptions} from '../../../types';
import type {AutocompleteValueEditorLocalizations, AutocompleteValueEditorOptions} from './types';

/**
 * @ngdoc constant
 * @name autocompleteValueEditorDefaultOptions
 * @module portaro.value-editors.autocomplete
 *
 * @description
 * For description see {@link AutocompleteValueEditorOptions}
 *
 * ```javascript
 * {
 *     minLength: 1,
 *     dataSource: () => Promise.resolve([]),
 *     staticParams: {
 *         remoteDataSourceUrl: null
 *     }
 * }
 * ```
 */
export const AUTOCOMPLETE_VALUE_EDITOR_DEFAULT_OPTIONS: DefaultOptions<AutocompleteValueEditorOptions> = {
    dataSource: () => Promise.resolve([]),
    minLength: 1,
    staticParams: {
        remoteDataSourceUrl: null
    }
};


/**
 * @ngdoc constant
 * @name AUTOCOMPLETE_VALUE_EDITOR_DEFAULT_LOCALIZATIONS
 * @module portaro.value-editors.number
 *
 * @description
 * ```
 * {
 *      description: '',
 *      showOptions: 'Show autocomplete options'
 * }
 * ```
 */
export const AUTOCOMPLETE_VALUE_EDITOR_DEFAULT_LOCALIZATIONS = Object.freeze<AutocompleteValueEditorLocalizations>({
    description: '',
    showOptions: 'Show suggested autocomplete options'
});