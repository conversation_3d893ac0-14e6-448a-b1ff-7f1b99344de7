import type {ValueEditorOptions} from '../../../kp-value-editor/types';
import type {CommonValueEditorLocalizations} from '../_shared/common-types';
import type {TextValueEditorValidations} from '../text/types';


/**
 * @ngdoc type
 * @name AutocompleteValueEditorOptions
 * @module portaro.value-editors.autocomplete
 *
 * @template PARAMS
 *
 * @property {function(AutocompleteRequestParams<PARAMS>): PromiseLike<string[]>} dataSource
 * ```
 * function({model, staticParams}: AutocompleteRequestParams<PARAMS>): PromiseLike<string[]>
 * ```
 * Function, which makes request and returns string array of values for autocomplete.
 *
 * | argument&nbsp;name | Description           |
 * | ------------------ | --------------------- |
 * | `model`            | current model value   |
 * | `staticParams`     | request static params |
 *
 *
 * @property {object} staticParams Any static params, which are passed to `dataSource` function.
 * @property {string} minLength Pull down popup input string length threshold.
 *
 *
 * @description
 * Extends {@link type:ValueEditorOptions}
 *
 * Default value: {@link AUTOCOMPLETE_VALUE_EDITOR_DEFAULT_OPTIONS}
 */

export interface AutocompleteValueEditorOptions<PARAMS extends DefaultAutocompleteRequestParams = DefaultAutocompleteRequestParams> extends ValueEditorOptions {
    dataSource?: (params: AutocompleteRequestParams<PARAMS>) => PromiseLike<string[]>;
    minLength?: number;
    staticParams?: PARAMS;
}


/**
 * @ngdoc type
 * @name AutocompleteRequestParams
 * @module portaro.value-editors.autocomplete
 *
 * @property {string} model current model value
 * @property {object} staticParams fetch request static params
 */
export interface AutocompleteRequestParams<PARAMS extends DefaultAutocompleteRequestParams> {
    model: string,
    staticParams: PARAMS
}

export interface DefaultAutocompleteRequestParams {
    remoteDataSourceUrl: string
}

/**
 * @ngdoc type
 * @name AutocompleteValueEditorLocalizations
 * @module portaro.value-editors.number
 *
 * @property {string} showOptions aria-label and title for button to show all autocomplete options
 *
 * @description
 * Extends {@link type:CommonValueEditorLocalizations}
 *
 * Default localizations: {@link AUTOCOMPLETE_VALUE_EDITOR_DEFAULT_LOCALIZATIONS}
 */
export interface AutocompleteValueEditorLocalizations extends CommonValueEditorLocalizations {
    showOptions: string
}

export interface AutocompleteValueEditorTypeMap {
    'autocomplete': {options: AutocompleteValueEditorOptions, validations: TextValueEditorValidations, localizations: AutocompleteValueEditorLocalizations}
}