import valueEditorModule from '../../../value-editors.base.module';
import {exists} from 'shared/utils/custom-utils';
import {createTestContext} from 'src/test-utils/utils';
import {
    createEditor, getDescriptionElement, getErrorMessageElementByErrorType,
    getInputElement,
    setEditorModelValue,
    setInputValueOn,
    waitForEvent, waitForNextState
} from '../../../test-utils/utils';
import {MODEL_CHANGE} from '../../../events';
import IInjectorService = angular.auto.IInjectorService;
import {DateTime, FixedOffsetZone, Settings} from 'luxon';
import {tick} from 'svelte';
import {waitForElementToBeRemoved} from '@testing-library/svelte';
import type {DateValueEditorLocalizations} from './types';
import {AngularManualInjector} from 'core/injector';


describe('date-value-editor', () => {
    let testContext;

    function getDatePickerButton(element: HTMLElement): HTMLButtonElement {
        return element.querySelector('button.btn.btn-default');
    }

    function isDatePickerVisible(element: HTMLElement): boolean {
        const picker = element.querySelector('.datetime-picker-container');
        return exists(picker) && picker.checkVisibility();
    }

    const defaultLocale = Settings.defaultLocale;
    const defaultTimeZone = Settings.defaultZone;

    beforeAll(() => {
        Settings.defaultLocale = DateTime.local().resolvedLocaleOptions().locale;
        Settings.defaultZone = FixedOffsetZone.utcInstance.name;
    });

    afterAll(() => {
        Settings.defaultLocale = defaultLocale;
        Settings.defaultZone = defaultTimeZone;
    });

    beforeEach(() => {
        angular.mock.module(valueEditorModule);

        inject(/*@ngInject*/ ($injector: IInjectorService) => {
            testContext = createTestContext(new AngularManualInjector($injector));
        });
    });


    it('should render editor', async () => {
        const {container, componentInstance, unmount} = await createEditor<'date', string>('date', testContext);

        expect(componentInstance).toBeTruthy();
        expect(getInputElement<HTMLInputElement>(container)).toBeTruthy();

        unmount();
    });

    it('should not render editorId', async () => {
        const {container, unmount} = await createEditor<'date', string>('date', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('id')).toBeFalsy();

        unmount();
    });

    it('should render editorName', async () => {
        const {container, unmount} = await createEditor<'date', string>('date', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('name')).toMatch(new RegExp('^date_[a-fA-F0-9-_]+'));

        unmount();
    });

    it('should render editorId and editorName', async () => {
        const {container, unmount} = await createEditor<'date', string>('date', testContext, null, {editorId: 'test-id', editorName: 'test-name'});

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('id')).toBe('test-id');
        expect(inputElement.getAttribute('name')).toBe('test-name');

        unmount();
    });


    it('should render initial model', async () => {
        const {container, unmount} = await createEditor<'date', string>('date', testContext, '2020-01-01T00:00:00.000Z');

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.value).toBe('1.1.2020');

        unmount();
    });

    it('should change model on input', async () => {
        const {container, componentInstance, unmount} = await createEditor<'date', string>('date', testContext, '2020-01-01T00:00:00.000Z');

        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '6.3.2022');

        let newModelValue: string = await waitForEvent(componentInstance, MODEL_CHANGE);
        expect(newModelValue).toBe('2022-03-06T00:00:00.000Z');
        expect(componentInstance.model).toBe('2022-03-06T00:00:00.000Z');

        setInputValueOn(inputElement, '');

        newModelValue = await waitForEvent(componentInstance, MODEL_CHANGE);
        expect(newModelValue).toBe(null);
        expect(componentInstance.model).toBe(null);

        unmount();

        unmount();
    });

    it('should change value if model is changed', async () => {
        const {container, componentInstance, unmount} = await createEditor<'date', string>('date', testContext, '2020-04-01T00:00:00.000Z');

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.value).toBe('1.4.2020');

        await setEditorModelValue(componentInstance, '2022-03-06T00:00:00.000Z')
        expect(inputElement.value).toBe('6.3.2022');

        unmount();
    });

    it('should have working implicit valid date validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'date', string>('date', testContext, '2020-01-01T00:00:00.000Z');

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, 'not a valid date');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('date')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'date')).toBeTruthy();

        setInputValueOn(inputElement, '1.1.1996');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('date')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'date'), {container});
        expect(getErrorMessageElementByErrorType(container, 'date')).toBeFalsy();
        unmount();
    });

    it('should have working required validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'date', string>('date', testContext, '2020-01-01T00:00:00.000Z', {validations: {required: true}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('required')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeTruthy();

        setInputValueOn(inputElement, '1.1.1996');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('required')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'required'), {container});
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeFalsy();
        unmount();
    });

    it('should have working minDate validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'date', string>('date', testContext, '2020-01-01T00:00:00.000Z', {validations: {minDate: '1980-01-01T00:00:00.000Z'}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '1.1.1969');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('minDate')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'minDate')).toBeTruthy();

        setInputValueOn(inputElement, '1.1.1996');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('minDate')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'minDate'), {container});
        expect(getErrorMessageElementByErrorType(container, 'minDate')).toBeFalsy();
        unmount();
    });

    it('should have working maxDate validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'date', string>('date', testContext, '2000-01-01T00:00:00.000Z', {validations: {maxDate: '2020-01-01T00:00:00.000Z'}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '1.1.2025');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('maxDate')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'maxDate')).toBeTruthy();

        setInputValueOn(inputElement, '1.1.2015');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('maxDate')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'maxDate'), {container});
        expect(getErrorMessageElementByErrorType(container, 'maxDate')).toBeFalsy();
        unmount();
    });

    it('open datepicker', async () => {
        const {container, unmount} = await createEditor<'date', string>('date', testContext, '2020-01-01T00:00:00.000Z', undefined, true);
        expect(isDatePickerVisible(container)).toBeFalse();
        const datePickerBtn = getDatePickerButton(container);
        datePickerBtn.click();
        await tick();
        expect(isDatePickerVisible(container)).toBeTrue();
        unmount();
    });

    it('should have working input disabling', async () => {
        const {container, unmount} = await createEditor<'date', string>('date', testContext, '2020-01-01T00:00:00.000Z', {isDisabled: true}, true);

        expect(getInputElement<HTMLInputElement>(container).disabled).toBeTrue();
        const datePickerBtn = getDatePickerButton(container);
        expect(datePickerBtn.disabled).toBeTrue();
        datePickerBtn.click();
        await tick();
        expect(isDatePickerVisible(container)).toBeFalse();
        unmount();
    });

    it('should correctly format model value as ISO-date (if onlyDate option set to true)', async () => {
        const {container,componentInstance, unmount} = await createEditor<'date', string>('date', testContext, null, {options: {onlyDate: true}});

        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '22.2.2022');

        let newModelValue: string = await waitForEvent(componentInstance, MODEL_CHANGE);
        expect(newModelValue).toBe('2022-02-22');
        expect(componentInstance.model).toBe('2022-02-22');

        setInputValueOn(inputElement, '');

        newModelValue = await waitForEvent(componentInstance, MODEL_CHANGE);
        expect(newModelValue).toBe(null);
        expect(componentInstance.model).toBe(null);

        unmount();
    });

    it('should correctly format view value from ISO-date model value (if onlyDate option set to true)', async () => {
        const {container, componentInstance, unmount} = await createEditor<'date', string>('date', testContext, '2022-02-22', {options: {onlyDate: true}});

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.value).toBe('22.2.2022');

        await setEditorModelValue(componentInstance, '2015-08-05')
        expect(inputElement.value).toBe('5.8.2015');

        unmount();
    });

    it('should be focused', async () => {
        const {container, unmount} = await createEditor<'date', string>('date', testContext, '2020-01-01T00:00:00.000Z', {isFocused: true}, true);

        expect(document.activeElement).toEqual(getInputElement<HTMLInputElement>(container));
        unmount();
    });

    it('should correctly parse year before 1892', async () => {
        const {container, componentInstance, unmount} = await createEditor<'date', string>('date', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '1.1.1666');

        const newModelValue: string = await waitForEvent(componentInstance, MODEL_CHANGE);
        expect(newModelValue).toBe('1666-01-01T00:00:00.000Z');
        expect(componentInstance.model).toBe('1666-01-01T00:00:00.000Z');

        unmount();
    });

    it('should render editors description', async () => {
        const DESCRIPTION = 'some-description';
        const {container, unmount} = await createEditor<'date', string>('date', testContext, null, {
            localizations: {description: DESCRIPTION} as DateValueEditorLocalizations
        });

        const descriptionElement = getDescriptionElement(container)
        expect(descriptionElement).toBeTruthy();
        expect(descriptionElement.textContent).toEqual(DESCRIPTION);
        expect(getInputElement(container).getAttribute('aria-describedby')).toEqual(descriptionElement.id);

        unmount();
    });
});