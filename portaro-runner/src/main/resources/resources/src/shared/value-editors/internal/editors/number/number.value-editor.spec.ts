import valueEditorModule from '../../../value-editors.base.module';
import {createTestContext} from '../../../../../test-utils/utils';
import IInjectorService = angular.auto.IInjectorService;
import {
    createEditor, getDescriptionElement, getErrorMessageElementByErrorType,
    getInputElement, setEditorModelValue, setInputValueOn,
    waitForEvent, waitForNextState
} from '../../../test-utils/utils';
import {MODEL_CHANGE} from '../../../events';
import {waitForElementToBeRemoved} from '@testing-library/svelte';
import {AngularManualInjector} from 'core/injector';

describe('number-value-editor', () => {
    let testContext;

    beforeEach(() => {
		angular.mock.module(valueEditorModule);

        inject(/*@ngInject*/ ($injector: IInjectorService) => {
            testContext = createTestContext(new AngularManualInjector($injector));
        });
	});


    it('should render editor', async () => {
        const {container, componentInstance, unmount} = await createEditor<'number', number>('number', testContext);

        expect(componentInstance).toBeTruthy();
        expect(getInputElement<HTMLInputElement>(container)).toBeTruthy();

        unmount();
    });

    it('should not render editorId', async () => {
        const {container, unmount} = await createEditor<'number', number>('number', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('id')).toBeFalsy();

        unmount();
    });


    it('should render editorName', async () => {
        const {container, unmount} = await createEditor<'number', number>('number', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('name')).toMatch(new RegExp('^number_[a-fA-F0-9-_]+'));

        unmount();
    });


    it('should render editorId and editorName', async () => {
        const {container, unmount} = await createEditor<'number', number>('number', testContext, null, {editorId: 'test-id', editorName: 'test-name'});

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('id')).toBe('test-id');
        expect(inputElement.getAttribute('name')).toBe('test-name');

        unmount();
    });


    it('should render initial model', async () => {
        const {container, unmount} = await createEditor<'number', number>('number', testContext, 42);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.value).toBe('42');

        unmount();
    });

    it('should change model on input', async () => {
        const {container, componentInstance, unmount} = await createEditor<'number', number>('number', testContext, 122);

        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '469');

        const newModelValue: number = await waitForEvent(componentInstance, MODEL_CHANGE);
        expect(newModelValue).toBe(469);
        expect(componentInstance.model).toBe(469);

        unmount();
    });

    it('should change value if model is changed', async () => {
        const {container, componentInstance, unmount} = await createEditor<'number', number>('number', testContext, 42);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.value).toBe('42');

        await setEditorModelValue(componentInstance, 321)
        expect(inputElement.value).toBe('321');

        unmount();
    });

    it('should have working required validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'number', number>('number', testContext, 42, {validations: {required: true}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('required')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeTruthy();

        setInputValueOn(inputElement, '123');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('required')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'required'), {container});
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeFalsy();
        unmount();
    });

     it('should have working min validation', async () => {
         const {container, componentInstance, unmount} = await createEditor<'number', number>('number', testContext, 42, {validations: {min: -100}});

         const controller = componentInstance.getFormController();
         const inputElement = getInputElement<HTMLInputElement>(container);
         setInputValueOn(inputElement, '-522');
         controller.setTouched(); // to show errors

         let state = await waitForNextState(componentInstance);
         expect(state.errors.includes('min')).toBeTrue();
         expect(inputElement.getAttribute('aria-invalid')).toBe('true');
         expect(getErrorMessageElementByErrorType(container, 'min')).toBeTruthy();

         setInputValueOn(inputElement, '123');

         state = await waitForNextState(componentInstance);
         expect(state.errors.includes('min')).toBeFalse();
         expect(inputElement.getAttribute('aria-invalid')).toBe('false');
         await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'min'), {container});
         expect(getErrorMessageElementByErrorType(container, 'min')).toBeFalsy();
         unmount();
    });

    it('should have working max validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'number', number>('number', testContext, 42, {validations: {max: 100}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '101');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('max')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'max')).toBeTruthy();

        setInputValueOn(inputElement, '42');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('max')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'max'), {container});
        expect(getErrorMessageElementByErrorType(container, 'max')).toBeFalsy();
        unmount();
    });

    it('should have working minlength validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'number', number>('number', testContext, 123, {validations: {minlength: 3}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '1');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('minlength')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'minlength')).toBeTruthy();

        setInputValueOn(inputElement, '123');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('minlength')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'minlength'), {container});
        expect(getErrorMessageElementByErrorType(container, 'minlength')).toBeFalsy();
        unmount();
    });

    it('should have working maxlength validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'number', number>('number', testContext, 10, {validations: {maxlength: 3}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '1001');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('maxlength')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'maxlength')).toBeTruthy();

        setInputValueOn(inputElement, '42');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('maxlength')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'maxlength'), {container});
        expect(getErrorMessageElementByErrorType(container, 'maxlength')).toBeFalsy();
        unmount();
    });

    it('should have working input disabling', async () => {
        const {container, unmount} = await createEditor<'number', number>('number', testContext, 10, {isDisabled: true});

        expect(getInputElement<HTMLInputElement>(container).disabled).toBe(true);
        unmount();
    });

    it('should have hidden spinner', async () => {
        const {container, unmount} = await createEditor<'number', number>('number', testContext, 10, {options: {hideSpinners: true}});

        const inputElement = getInputElement<HTMLInputElement>(container);

        expect(inputElement.classList).toContain('hide-spinners');
        unmount();
    });

    // working only in Firefox
    xit('should have implicit number validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'number', number>('number', testContext, 10);

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, 'hello');
        controller.setTouched(); // to show errors

        const state = await waitForNextState(componentInstance);
        expect(state.errors.includes('number')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'number')).toBeTruthy();

        unmount();
    });

    it('should have implicit step validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'number', number>('number', testContext, 10, {options: {step: 0.01}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '43.364');
        controller.setTouched(); // to show errors

        const state = await waitForNextState(componentInstance);
        expect(state.errors.includes('step')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'step')).toBeTruthy();

        unmount();
    });


    it('should be focused', async () => {
        const {container, unmount} = await createEditor<'number', number>('number', testContext, 10, {isFocused: true}, true);

        expect(document.activeElement).toEqual(getInputElement<HTMLInputElement>(container));
        unmount();
    });

    it('should render editors description', async () => {
        const DESCRIPTION = 'some-description';
        const {container, unmount} = await createEditor<'number', number>('number', testContext, null, {
            localizations: {description: DESCRIPTION}
        });

        const descriptionElement = getDescriptionElement(container)
        expect(descriptionElement).toBeTruthy();
        expect(descriptionElement.textContent).toEqual(DESCRIPTION);
        expect(getInputElement(container).getAttribute('aria-describedby')).toEqual(descriptionElement.id);

        unmount();
    });
});