import type {Valida<PERSON>} from 'svelte-forms';
import type {NumberValueEditorValidations} from './types';
import {isDefined} from 'shared/utils/custom-utils';
import {
    maxLengthValidator,
    maxValidator,
    minLengthValidator,
    minValidator,
    patternValidator,
    requiredValidator
} from '../../shared/validators';

export function validatorFactory(validations: NumberValueEditorValidations): Validator[] {
    const validators: Validator[] = [];

    if (isDefined(validations.required) && validations.required) {
        validators.push(requiredValidator())
    }

    if(isDefined(validations.max)) {
        validators.push(maxValidator(validations.max));
    }

    if (isDefined(validations.min)) {
        validators.push(minValidator(validations.min))
    }

    if (isDefined(validations.minlength)) {
        validators.push((value: number) => minLengthValidator(validations.minlength)(value.toString()))
    }

    if (isDefined(validations.maxlength)) {
        validators.push((value: number) => maxLengthValidator(validations.maxlength)(value.toString()))
    }

    if (isDefined(validations.pattern)) {
        validators.push(patternValidator(new RegExp(validations.pattern)));
    }

    return validators;
}