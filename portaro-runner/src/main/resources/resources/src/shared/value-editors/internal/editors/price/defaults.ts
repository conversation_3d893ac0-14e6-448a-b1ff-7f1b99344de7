import type {DefaultOptions} from '../../../types';
import type {PriceValueEditorLocalizations, PriceValueEditorModel, PriceValueEditorOptions} from './types';
import {isNullOrUndefined} from '../../../../utils/custom-utils';

/**
 * @ngdoc constant
 * @name PRICE_VALUE_EDITOR_DEFAULT_OPTIONS
 * @module portaro.value-editors.text-with-metadata
 *
 * @description
 * For description see {@link PriceValueEditorOptions}
 *
 * ```javascript
 * {
 *      customEmptyAsNullCheck: ({value}: {value: PriceValueEditorModel}) => isNullOrUndefined(value.amount)
 * }
 * ```
 */
export const PRICE_VALUE_EDITOR_DEFAULT_OPTIONS: DefaultOptions<PriceValueEditorOptions> = {
    visibleIfSingleCurrency: true,
    currencyOptions: [],
    customEmptyAsNullCheck: ({value}: {value: PriceValueEditorModel}) => isNullOrUndefined(value?.amount)
};

/**
 * @ngdoc constant
 * @name PRICE_VALUE_EDITOR_DEFAULT_LOCALIZATIONS
 * @module portaro.value-editors.text-with-metadata
 *
 * @description
 * ```
 * {
 *      currencySelectLabel: 'Select currency',
 *      currentSelectValue: 'Selected value',
 *      amountInputLabel: 'Enter amount',
 *      description: ''
 * }
 * ```
 */
export const PRICE_VALUE_EDITOR_DEFAULT_LOCALIZATIONS = Object.freeze<PriceValueEditorLocalizations>({
    currencySelectLabel: 'Select currency',
    currentSelectValue: 'Selected value',
    amountInputLabel: 'Enter amount',
    description: ''
});