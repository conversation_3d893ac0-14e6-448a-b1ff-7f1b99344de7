import type {BarcodeScannerService} from '../../../../barcode-scanner/barcode-scanner.service';
import type {Subscription} from 'rxjs';
import type {UserFeedbackService} from '../../../../services/user-feedback.service';
import {distinctUntilChanged, take, tap} from 'rxjs/operators';
import {Barcode} from '../../../../barcode-scanner/barcode';
import type {FormControl} from '../../forms/form-control';
import type {ActionReturn} from 'svelte/action';
import {exists, isNullOrUndefined} from '../../../../utils/custom-utils';

interface Attributes {
	'on:barcode-scan': (e: CustomEvent<void>) => void;
}

interface Parameters {
    formControl: FormControl<unknown>;
    barcodeScannerService: BarcodeScannerService;
    userFeedbackService: UserFeedbackService;
}

export function barcodeScanner(element: HTMLElement, {formControl, barcodeScannerService, userFeedbackService}: Parameters): ActionReturn<Parameters, Attributes> {

    const focusEvent = 'focus';
    const blurEvent = 'blur';
    const INVALID_ELEMENT_MSG = 'Barcode scanner directive requires input element';
    const DATA_TAG= 'data-barcode-scanner';

    let subscription: Subscription;
    // we use async initialization, so we need these flags to know current position in lifecycle
    let isRegistered = false;
    let isDestroyed = false;

    if (!(element instanceof HTMLInputElement)) {
        throw new TypeError(INVALID_ELEMENT_MSG);
    }
    element.setAttribute(DATA_TAG, 'true');
    barcodeScannerService.isUsable().then(register);

    function register(isUsable: boolean) {
        if (isDestroyed) {
            return; // stop register if action is already destroyed
        }

        if (!isUsable) {
            return;
        }

        element.addEventListener(focusEvent, subscribeValues);
        element.addEventListener(blurEvent, unsubscribeValues);

        barcodeScannerService.registerBarcodeScannerConsumer();
        isRegistered = true;
    }

    function subscribeValues() {
        if (exists(subscription)) {
            return;
        }

        subscription = barcodeScannerService.getScannedBarcode$()
            .pipe(
                distinctUntilChanged(Barcode.equals),
                take(1), // take only 1 barcode, then unsubscribe
                tap(signalSuccessfulScan), // beep & vibrate
                tap(() => barcodeScannerService.stopScanning())) // stop scanning
            .subscribe(fillInputValue);
    }

    function unsubscribeValues() {
        if (isNullOrUndefined(subscription)) {
            return;
        }
        subscription.unsubscribe();
        subscription = null;
    }

    function fillInputValue(barcode: Barcode) {
        formControl.setViewValue(barcode.text);
        element.dispatchEvent(new CustomEvent('barcode-scan'));
    }

    function signalSuccessfulScan() {
        userFeedbackService.beep(300);
        userFeedbackService.vibrate(500);
    }

    return {
        destroy() {
            isDestroyed = true;
            element.removeAttribute(DATA_TAG);
            unsubscribeValues();
            if (isRegistered) {

                barcodeScannerService.unregisterBarcodeScannerConsumer();

                element.removeEventListener(focusEvent, subscribeValues);
                element.removeEventListener(blurEvent, unsubscribeValues);
            }
        }
    }
}