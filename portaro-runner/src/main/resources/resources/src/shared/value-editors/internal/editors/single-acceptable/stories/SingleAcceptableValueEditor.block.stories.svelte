<script>
    import {<PERSON><PERSON>, Story, Template} from '@storybook/addon-svelte-csf';
    import TestDependencyProvider from '../../../../test-utils/TestDependencyProvider.svelte';
    import KpValueEditor from '../../../../kp-value-editor/KpValueEditor.svelte';
    import TestValueEditorContainer from '../../../../test-utils/TestValueEditorContainer.svelte';
    import {VALUE_EDITORS_COMMON_STORIES_ARG_TYPES, VALUE_EDITORS_COMMON_STORIES_ARGS} from '../../../../test-utils/common-stories-settings';
    import {SINGLE_ACCEPTABLE_VALUE_EDITOR_COMMON_ARG_TYPES} from './common-settings';
    import {VALUE_EDITOR_DEFAULTS} from '../../../../kp-value-editor/defaults';

    const continents = ['Asia', 'Africa', 'America', 'Europe', 'Australia and Oceania', 'Antarctic']; // mock data
    const storiesArgs = {
        ...Object.fromEntries(Object.entries(VALUE_EDITORS_COMMON_STORIES_ARGS).map(([name, arg]) => [name, {...arg, options: {acceptableValues: [...continents]}}])),
        'nullable': {...VALUE_EDITOR_DEFAULTS, editorId: 'nullable', options: {allowSelectNull: true, acceptableValues: [...continents]}},
        'in-row': {...VALUE_EDITOR_DEFAULTS, editorId: 'in-row', options: {blockInRow: true, switchToInlineModeThreshold: 0, acceptableValues: [...continents]}},
        'disabled-option': {...VALUE_EDITOR_DEFAULTS, editorId: 'disabled-option', options: {optionDisabledResolver: ({item, options}) => item === options.acceptableValues[2], acceptableValues: [...continents]}},
    };

    const argTypes = {
        ...VALUE_EDITORS_COMMON_STORIES_ARG_TYPES,
        ...SINGLE_ACCEPTABLE_VALUE_EDITOR_COMMON_ARG_TYPES
    }

    export let editor;
</script>

<Meta
        title="Value Editors/SingleAcceptable/Block"
        component={KpValueEditor}
        {argTypes}
        parameters={{
            docs: {
                description: {
                    component: 'Radio group for selecting single option'
                }
            }
        }}
/>

<Template let:args>
    <TestDependencyProvider>
        <TestValueEditorContainer label={args.editorId}
                                  editorId={args.editorId}
                                  formControl={editor ? editor.getFormController() : undefined}
                                  size={args.size}
                                  forceShowErrors={args.forceShowErrors}>
            <KpValueEditor {...args}
                           type="single-acceptable"
                           model={args.model}
                           bind:this={editor}
                           on:model-change={args.onModelChange}/>
        </TestValueEditorContainer>
    </TestDependencyProvider>
</Template>

<Story name='Default' args={storiesArgs.default}/>
<Story name='Size SM' args={storiesArgs['size-sm']}/>
<Story name='Size XS' args={storiesArgs['size-xs']}/>
<Story name='With description' args={storiesArgs.description}/>
<Story name='Errored' args={storiesArgs.errored}/>
<Story name='Disabled' args={storiesArgs.disabled}/>
<Story name='Not visible' args={storiesArgs['not-visible']}/>
<Story name='Nullable' args={storiesArgs.nullable}/>
<Story name='In row' args={storiesArgs['in-row']}/>
<Story name='With disabled options' args={storiesArgs['disabled-option']}/>