<script lang="ts">
    import type {FormControlOptions} from '../../forms/types';
    import type {EditorsLocalizationFunction} from '../../../localizations/types';
    import type {PhoneNumberEditationRequest} from 'typings/portaro.be.types';
    import type {ValueEditorSize} from '../../../types';
    import type {FormGroup} from 'shared/value-editors/internal/forms/form-group';
    import type {Observable} from 'rxjs';
    import {FormGroupBuilder} from 'shared/value-editors/internal/forms/form-group';
    import {combineLatest} from 'rxjs';
    import {getContext, onDestroy} from 'svelte';
    import {FormControlBuilder} from '../../forms/form-control';
    import {smsCapableTelephoneNumberFormatter, smsCapableTelephoneNumberParser, validatorFactory} from './utils';
    import {formControls} from '../../forms/use.form-controls';
    import {style} from 'svelte-forms';
    import {focusOnMount} from '../../shared/use.focus-on-mount';
    import {touched} from '../../shared/use.touched';
    import {ariaInvalid} from '../../errors/use.aria-invalid';
    import {ariaErrormessage} from '../../errors/use.aria-errormessage';
    import {ariaDescribedby} from '../../description/use.aria-describedby';
    import {createInputDescription} from '../../description/utils';
    import {required} from '../../forms/use.required';
    import {getLogger} from 'core/svelte-context/context';
    import {exists, isDefined} from 'shared/utils/custom-utils';
    import {addAttributes} from 'shared/value-editors/internal/shared/use.add-attributes';
    import {Switch, SwitchGroup, SwitchLabel} from '@rgossiaux/svelte-headlessui';
    import {writable} from 'svelte/store';
    import {trim} from 'shared/value-editors/internal/editors/_shared/editors-utils';
    import {map} from 'rxjs/operators';
    import {cloneDeep, isEmpty} from 'lodash-es';
    import {EDITORS_LOCALIZE} from '../../../context-keys';
    import {defaultContactSource} from 'shared/value-editors/internal/editors/telephone-number/types';
    import Description from '../../description/Description.svelte';
    import ErrorMessages from '../../errors/ErrorMessages.svelte';
    import TelephoneNumberValueEditorPrefixesDropdown from './TelephoneNumberValueEditorPrefixesDropdown.svelte';
    import type {
        TelephoneNumberPrefix,
        TelephoneNumberPrefixChangeEvent,
        TelephoneNumberValueEditorLocalizations,
        TelephoneNumberValueEditorOptions,
        TelephoneNumberValueEditorValidations
    } from 'shared/value-editors/internal/editors/telephone-number/types';

    export let model: PhoneNumberEditationRequest;
    export let formControlOptions: FormControlOptions;

    export let editorId: string;
    export let editorName: string;
    export let placeholder: string;

    export let isDisabled: boolean;
    export let isFocused: boolean;
    export let forceShowErrors: boolean;
    export let size: ValueEditorSize;

    export let options: TelephoneNumberValueEditorOptions;
    export let validations: TelephoneNumberValueEditorValidations;

    const logger = getLogger();
    const localize: EditorsLocalizationFunction<TelephoneNumberValueEditorLocalizations> = getContext(EDITORS_LOCALIZE);

    normalizeModel();
    const source = model.source;

    let smsCapableCheckboxValue = model?.smsCapable === true || options.forceSmsCapableOnly === true;
    let currentTelephonePrefix: TelephoneNumberPrefix | null = null;
    const smsCapableFormattingActiveWritable = writable<boolean>(smsCapableCheckboxValue);
    const currentTelephonePrefixWritable = writable<TelephoneNumberPrefix | null>(currentTelephonePrefix);
    $: smsCapableFormattingActiveWritable.set(smsCapableCheckboxValue);
    $: currentTelephonePrefixWritable.set(currentTelephonePrefix);

    const formControlInput = FormControlBuilder.for(`${editorId}Input`, `${editorName}Input`, model?.value)
        .withValidators(validatorFactory(addDefaultPatternValidation(validations)))
        .withOptions(formControlOptions)
        .withParsers([trim(options)])
        .withModifiers([smsCapableTelephoneNumberParser()])
        .withFormatters([smsCapableTelephoneNumberFormatter(smsCapableFormattingActiveWritable, currentTelephonePrefixWritable)])
        .build(logger);

    const formControlCheckbox = FormControlBuilder.for(`${editorId}Checkbox`, `${editorName}Checkbox`, smsCapableCheckboxValue)
        .build(logger);
    const checkboxAddAttributesOptions = new Map([
        ['name', editorName]
    ]);

    const formControlSource = FormControlBuilder.for(`${editorId}Source`, `${editorName}Source`, source)
        .build(logger);

    const formGroupBuilder = new FormGroupBuilder<PhoneNumberEditationRequest>();
    formGroupBuilder.registerFormControl(formControlInput);
    formGroupBuilder.registerFormControl(formControlCheckbox);
    formGroupBuilder.registerFormControl(formControlSource);

    const formControl = formGroupBuilder.build(editorId, editorName, new Map([
        [`${editorName}Checkbox`, 'smsCapable'],
        [`${editorName}Input`, 'value'],
        [`${editorName}Source`, 'source']
    ]));

    const subscriptionCheckbox = formControlCheckbox.getViewValue$().subscribe((value) => smsCapableCheckboxValue = value);  // two-way data binding -> sending updated value up to a parent
    $: formControlCheckbox.setViewValue(smsCapableCheckboxValue); // two-way data binding -> receiving new model value from parent

    const subscription = formControl.getModelValue$().subscribe((value) => model = value);  // two-way data binding -> sending updated value up to a parent
    $: formControl.setModelValue(model); // two-way data binding -> receiving new model value from parent

    const inputDescription = createInputDescription({editorId, editorName, description: localize('description')});

    // Initial reformating - adds space between prefix and number
    if (smsCapableCheckboxValue) reformatPhoneNumber();

    let lastValue = smsCapableCheckboxValue;
    const reformatPhoneNumberUnsubscribe = smsCapableFormattingActiveWritable.subscribe((value) => {
        if (lastValue === value) {
            return;
        }

        reformatPhoneNumber();
        lastValue = value;
    });

    onDestroy(() => {
        subscription.unsubscribe();
        subscriptionCheckbox.unsubscribe();
        reformatPhoneNumberUnsubscribe();
    });

    // noinspection JSUnusedGlobalSymbols
    export function getFormController(): FormGroup<PhoneNumberEditationRequest> {
        return formControl;
    }

    function addDefaultPatternValidation(telephoneNumberValueEditorValidations: TelephoneNumberValueEditorValidations): TelephoneNumberValueEditorValidations {
        if (isDefined(telephoneNumberValueEditorValidations.pattern)) {
            return telephoneNumberValueEditorValidations;
        }

        return {...telephoneNumberValueEditorValidations, pattern: '^\\s*\\+?[0-9\\s]+$'};
    }

    function normalizeModel() {
        if (!exists(model)) {
            model = {
                value: options.defaultPrefix,
                smsCapable: options.forceSmsCapableOnly === true,
                source: defaultContactSource
            }

            return;
        }

        if (isEmpty(model.value.trim())) {
            model.value = options.defaultPrefix
        }

        if (!exists(model.source)) {
            model.source = defaultContactSource;
        }
    }

    function reformatPhoneNumber() {
        const modelCopy = cloneDeep(model);
        formControl.setModelValue({value: '600700800', smsCapable: modelCopy.smsCapable, source: modelCopy.source});
        formControl.setModelValue(modelCopy);
    }

    const handleTelephonePrefixChange = (event: TelephoneNumberPrefixChangeEvent) => {
        if (!exists(model)) {
            model = {
                value: event.detail.newPrefix,
                smsCapable: smsCapableCheckboxValue,
                source: defaultContactSource
            };
            return;
        }

        if (!model.value) {
            model.value = event.detail.newPrefix;
            return;
        }

        if (model.value.includes(event.detail.oldPrefix)) {
            model.value = event.detail.newPrefix + model.value.replace(event.detail.oldPrefix, '');
            return;
        }

        model.value = event.detail.newPrefix + model.value;
    }

    // Filters out errors from formControl which are for input - they are also on input - otherwise there would be duplicity
    function filterErrors(...errors: Observable<string[]>[]): Observable<string[]> {
        return combineLatest(errors).pipe(
            map((errorArrays: string[][]) => {
                return errorArrays.flat().filter((error) => !error.includes(`${editorName}Input.`)).slice(0, 1);
            })
        );
    }
</script>

<div class="telephone-number input-group input-group-{size}" role="group">
    {#if smsCapableCheckboxValue}
        <div class="input-group-addon prefix">
            <TelephoneNumberValueEditorPrefixesDropdown phoneNumberValue="{model?.value ?? ''}"
                                                        bind:currentPrefix={currentTelephonePrefix}
                                                        on:change={handleTelephonePrefixChange}/>
        </div>
    {/if}

    <input class="form-control input-{size}"
           type="tel"
           id={editorId}
           name={editorName}
           placeholder={placeholder}
           disabled={isDisabled}
           use:required={validations.required}
           use:ariaErrormessage={formControl}
           use:ariaInvalid={formControl}
           use:focusOnMount={isFocused}
           use:formControls={formControlInput}
           use:style={{field: formControlInput.getFieldStateAsStore()}}
           use:touched={{formControl:formControlInput}}
           use:ariaDescribedby={inputDescription}
           aria-labelledby="{editorId}-main-input-label"

           data-main-input/>

    <div class="telephone-number-editor-checkbox input-group-addon suffix">
        {#if options.forceSmsCapableOnly === true}
            {localize('forSmsOnly')}
        {:else}
            <SwitchGroup class="switch-group">
                <SwitchLabel class="switch-label">{localize('forSmsMessages')}</SwitchLabel>
                <Switch class="{smsCapableCheckboxValue ? 'switch switch-enabled toggle-on' : 'switch switch-disabled toggle-off'} {isDisabled ? 'disabled' : ''}"
                        bind:checked={smsCapableCheckboxValue}
                        disabled={isDisabled}
                        aria-required={validations.required}
                        use={[
                        [addAttributes, checkboxAddAttributesOptions],
                        [touched, {formControl:formControlCheckbox}],
                        [focusOnMount, isFocused],
                        [ariaInvalid, formControlCheckbox],
                        [ariaErrormessage, formControl],
                        [style, {field: formControl.getFieldStateAsStore()}]]}
                        data-id={editorId}
                        data-main-input>
                </Switch>
            </SwitchGroup>
        {/if}
    </div>
</div>

<ErrorMessages formController={formControl}
               errors={filterErrors(formControl.getErrors$(), formControlInput.getErrors$())}
               {forceShowErrors}
               {size}/>

<Description {inputDescription}/>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "bootstrap-less/bootstrap/mixins";

    @switch-size: 1em;
    @switch-border-width: 0.125em;
    @switch-border-inner-radius: calc(@switch-size);
    @switch-border-outer-radius: calc(@switch-border-inner-radius + @switch-border-width);
    @disabled-opacity: 0.5;

    .switch-custom-focus() {
        @color: @input-border-focus;
        @color-rgba: rgba(red(@color), green(@color), blue(@color), .6);
        .box-shadow(~"inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px @{color-rgba}");
    }

    @media (max-width: @screen-xs-max) {
        .input-group-addon,
        input[type='tel'] {
            padding: 6px 8px;
        }
    }

    :global {
        .telephone-number-editor-checkbox {
            font-size: @font-size-small;

            .switch-group {
                display: flex;
                align-items: center;
                gap: 6px;
            }

            label {
                margin: 0;
                cursor: pointer;
            }

            .switch {
                all: unset;
                -webkit-appearance: none;
                appearance: none;
                box-sizing: content-box;
                position: relative;
                display: inline-block;
                vertical-align: middle;
                width: calc(@switch-size * 2);
                height: @switch-size;
                border: @switch-border-width solid @brand-primary;
                border-radius: @switch-border-outer-radius;
                transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s, background-color ease .3s;
                cursor: pointer;
            }

            .switch-enabled {
                background-color: lighten(@brand-primary, 10%); // #4d94d1
            }

            .switch-disabled {
                background-color: #e0e0e0;
            }

            .switch.disabled {
                cursor: @cursor-disabled;
                border-color: @gray-light;
                opacity: @disabled-opacity;
            }

            .switch:focus {
                .switch-custom-focus();
            }

            .switch:before {
                content: "";
                position: absolute;
                left: 0;
                top: 0;
                width: @switch-size;
                height: @switch-size;
                background: @input-bg;
                border-radius: @switch-border-inner-radius;
                transition: background 0.3s, left 0.3s;
            }

            .toggle-on:before {
                left: @switch-size;
            }

            .error-message.boolean-switch {
                left: 5px;
            }

            .boolean-value-editor {
                display: inline-block;

                &.size-xs {
                    font-size: 1em;
                }

                &.size-sm {
                    font-size: 1.35em;
                }

                &.size-md {
                    font-size: 1.7em;
                }
            }
        }
    }
</style>