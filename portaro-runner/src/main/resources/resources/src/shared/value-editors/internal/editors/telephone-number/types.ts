import type {ValueEditorOptions, ValueEditorValidations} from 'shared/value-editors/kp-value-editor/types';
import type {CommonValueEditorLocalizations} from 'shared/value-editors/internal/editors/_shared/common-types';

export interface TelephoneNumberValueEditorOptions extends ValueEditorOptions {
    defaultPrefix?: string;
    forceSmsCapableOnly?: boolean;
}

export interface TelephoneNumberValueEditorValidations extends ValueEditorValidations {
    pattern?: string | RegExp;
    notBlank?: boolean;
}

export interface TelephoneNumberValueEditorLocalizations extends CommonValueEditorLocalizations {
    forSmsMessages: string;
    forSmsOnly: string;
}

export interface TelephoneNumberValueEditorTypeMap {
    'telephone-number': {
        options: TelephoneNumberValueEditorOptions,
        validations: TelephoneNumberValueEditorValidations,
        localizations: TelephoneNumberValueEditorLocalizations
    }
}

export interface TelephoneNumberPrefix {
    name: string;
    localizedName: string;
    dialCode: string;
    flagUrl: string;
    code: string;
}

export interface TelephoneNumberPrefixChange {
    oldPrefix?: string;
    newPrefix: string;
}

export type TelephoneNumberPrefixChangeEvent = CustomEvent<TelephoneNumberPrefixChange>;

export const defaultContactSource = {
    id: 'internal',
    text: 'Internal'
}