<script>
    import {Meta, Story, Template} from '@storybook/addon-svelte-csf';
    import TestDependencyProvider from 'shared/value-editors/test-utils/TestDependencyProvider.svelte';
    import KpValueEditor from '../../../kp-value-editor/KpValueEditor.svelte';
    import TestValueEditorContainer from 'shared/value-editors/test-utils/TestValueEditorContainer.svelte';
    import {VALUE_EDITORS_COMMON_STORIES_ARG_TYPES, VALUE_EDITORS_COMMON_STORIES_ARGS} from '../../../test-utils/common-stories-settings';


    const storiesArgs = {
        ...Object.fromEntries(Object.entries(VALUE_EDITORS_COMMON_STORIES_ARGS).map(([name, arg]) => [name, {
            ...arg, options: {
                canDoAction: true,
                dataSource: () => {
                    return Promise.resolve(['one', 'two', 'three'])
                },
            }
        }])),
    };
    const argTypes = {
        ...VALUE_EDITORS_COMMON_STORIES_ARG_TYPES,
        model: {
            control: 'text',
            description: 'model value',
            table: {
                type: {
                    summary: 'string',
                }
            },
        },
        options: {
            control: 'object',
            description: 'text or select value editor specific configuration options and flags',
            table: {
                defaultValue: {summary: '{}'},
                type: {
                    summary: 'TextOrSelectValueEditorOptions',
                }
            },
        },
        validations: {
            control: 'object',
            description: 'object for configuring validations',
            table: {
                defaultValue: {summary: '{}'},
                type: {
                    summary: 'TextValueEditorValidations',
                }
            },
        },
        localizations: {
            control: 'object',
            description: 'dictionary for custom localization messages overriding default ones',
            table: {
                type: {
                    summary: 'TextOrSelectValueEditorLocalizations',
                }
            },
        }
    }

    export let editor;
</script>


<Meta
        title="Value Editors/TextOrSelect"
        component={KpValueEditor}
        {argTypes}
        parameters={{
            docs: {
                description: {
                    component: 'Text input editor where user can type or select value.'
                }
            }
        }}
/>


<Template let:args>
    <TestDependencyProvider>
        <TestValueEditorContainer label={args.editorId}
                                  editorId={args.editorId}
                                  formControl={editor ? editor.getFormController() : undefined}
                                  size={args.size}
                                  forceShowErrors={args.forceShowErrors}>
            <KpValueEditor {...args}
                           type="text-or-select"
                           model={args.model}
                           bind:this={editor}
                           on:model-change={args.onModelChange}/>
        </TestValueEditorContainer>
    </TestDependencyProvider>
</Template>

<Story name='Default' args={storiesArgs.default}/>
<Story name='Size SM' args={storiesArgs['size-sm']}/>
<Story name='Size XS' args={storiesArgs['size-xs']}/>
<Story name='With placeholder' args={storiesArgs.placeholder}/>
<Story name='With description' args={storiesArgs.description}/>
<Story name='Errored' args={storiesArgs.errored}/>
<Story name='Disabled' args={storiesArgs.disabled}/>
<Story name='Not visible' args={storiesArgs['not-visible']}/>

