<script lang="ts">
    import type {ValueEditorTypeOrAlias} from '../types';
    import type {ForceSetting} from './types';
    import {onDestroy, setContext} from 'svelte';
    import {ForceSettingsOptionsMerger} from './force-settings-options-merger';
    import {getLogger} from 'core/svelte-context/context';
    import {ALL_TYPES, DEFAULT_MERGE_STRATEGY} from './types';
    import {FORCE_SETTINGS} from '../context-keys';

    export let forceSettings: ForceSetting[];

    const logger = getLogger();
    const settings: Map<ValueEditorTypeOrAlias, Omit<ForceSetting, 'type'>> = new Map();

    forceSettings.forEach(({type, options, strategy}) => settings.set(type ?? ALL_TYPES, {
        options,
        strategy: strategy ?? DEFAULT_MERGE_STRATEGY
    }));

    setContext(FORCE_SETTINGS, new ForceSettingsOptionsMerger(settings));

    if (!$$slots.default) {
        logger.warn('Force settings used without any value editors');
    }

    onDestroy(() => {
        settings.clear();
    })
</script>

<slot/>