<script lang="ts">
    import type {IntervalString} from 'shared/ui-widgets/daterange-picker/date-range-util';
    import {ignoreUnusedProperties} from 'shared/utils/custom-utils';
    import KpDateRangePicker from 'shared/ui-widgets/daterange-picker/KpDateRangePicker.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let name = '';
    export let input = 0;

    ignoreUnusedProperties(name, input);

    const intervalString = 'empty' as IntervalString;
    let outputInterval = 'empty' as IntervalString;

    const handleIntervalChanged = (event: CustomEvent<IntervalString>) => {
        outputInterval = event.detail;
    };
</script>

<div class="container" data-routeid="test-route-1">
    <!--<Loc code="test.markdownTestLoc" params="{['PARAMETER TEST 1', 'PARAMETER TEST 2']}"/>

    <div class="row col-sm-12">
        <div aria-hidden="true">{name} - {input}</div>
        <KpSvelteTest/>
    </div>-->

    <Flex direction="column" gap="m">
        <div class="test-container">
            <KpDateRangePicker interval="{intervalString}" granularity="date-only" on:interval-changed={handleIntervalChanged}/>
        </div>

        {#if outputInterval}
            <div>Output: {outputInterval}</div>
        {/if}
    </Flex>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .test-container {
        border: 1px solid @themed-border-default;
        border-radius: @border-radius-large;
        width: 480px;
    }
</style>