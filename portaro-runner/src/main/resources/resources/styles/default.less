@import "~bootstrap-less/bootstrap/variables";
@import "../fonts/portaro.font.less";
@import "../fonts/uicons-bold.less";
@import "./partials/dropdowns";
@import "./partials/dropzone";
@import "./partials/tables";
@import "./partials/xs-inputs";
@import "./everbis-custom-styles";
@import "./portaro.variables";
@import "./portaro.themes";
@import "./portaro.media-queries";
@import "./portaro-erp";

:root {
    --danger-red: #E42C42;
    --success-green: #239F55;
    --warning-orange: #E48B2C;
    --info-blue: #2AA5E7;
    --brand-orange-new: #FF5712;
    --accent-blue-new: #2D52B2;
}

html,
body,
input[type="button"],
input[type="submit"],
input[type="reset"] {
    font-family: Ubuntu, Geneva, Tahoma, sans-serif;
}

html {
    padding: 0;
    margin: 0;
}

h1,
h2,
h3 {
    color: @themed-text-default;
}

a {
    color: darkblue;
    text-decoration: none;
}

a.no-decoration-anchor {
    text-decoration: none;
    color: inherit;

    &:hover {
        text-decoration: none;
    }
}

body {
    background-color: @themed-body-bg;
    color: @themed-text-default;
    display: flex;
    flex-direction: column;
    min-height: 100dvh;
}

img {
    border: none;
}

h1 {
    font-size: 2em;
}

h2 {
    font-size: 1.5em;
}

h3 {
    font-size: 1.3em;
}

h4 {
    font-size: 1.25em;
}

h1,
h2,
h3,
h4 {
    font-weight: normal;
}

dialog {
    padding: 0;
    border: 0;

    &:focus-visible {
        outline: none !important;
    }
}

[popover] {
    margin: 0;
    padding: 0;
    border: 0;

    &:focus-visible {
        outline: none !important;
    }
}

span {
    margin: 0;
}

a:hover {
    text-decoration: underline;
}

table {
    border-spacing: 0;
}

label {
    font-weight: normal;
}

ol {
    padding: 0;
    margin: 0;
}

ul {
    padding: 0;
    margin: 0;

    li {
        list-style-type: none;
    }
}

::placeholder {
    color: #bbb;
}

.unset-style {
    all: unset;
    box-sizing: border-box;
}

.cleaner {
    clear: both;
    float: none;
}

.subtitle {
    font-size: 1.1em;
    color: #999;
}

.error {
    color: #f00;
    background-color: #fff;
}

.errorPage {
    margin-top: 30px;
}

.global-alert {
    color: var(--danger-red);
    padding: 6px;
    text-align: center;

    &.last-page-alert {
        border-bottom: 1px solid var(--danger-red);
    }
}

.dodatkovyText {
    color: gray;
}

.overwritten-value {
    text-decoration: line-through;
}

/********* PANELY *********/

.kp-panels-wrapper {
    display: flex;
    flex-direction: column;

    @media (min-width: @screen-sm-min) {
        flex-direction: row;
    }
}

.kp-panel-container {
    display: flex;
    align-items: stretch;
    flex-direction: column;
}

.kp-panel-content {
    flex-grow: 1;
}

.searchStringInput {
    border-width: 1px;
    border-right: none;
    height: 50px;
    padding-left: 15px;
}

.searchStringSubmit {
    color: white;
    border-width: 1px;
    border-left-width: 0;
    border-radius: 0 @input-border-radius @input-border-radius 0;
    padding: 0 20px;
    height: 50px;
    font-size: 18px;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

/************  HEADER  ****************/
.logo-stripe {
    background-color: var(--header-bg-color);
    color: var(--header-text-color);
    min-height: 85px;
    padding: 20px 0;

    a:not(.btn) {
        color: var(--header-link-color);
    }
}

.logo-content-container {
    min-height: 85px;
    background-repeat: no-repeat;
    background-position: 0 50%;
}

.logo-search {
    margin-top: 20px;
    margin-bottom: 5px;
}

.logo-search .searchStringInput,
.index-search .index-search-input {
    font-size: 15px;
}

/**************************************   */

/************  FOOTER  *********************/
.upperFooter {
    clear: both;
    margin-top: 20px;
}

/************  HLAVNI OBSAH  ****************/
.main-content {
    display: flex;
    flex-direction: column;
    flex: 1 1 0;
    margin-bottom: 30px;
    animation: 0.3s ease-in-out 0s 1 content-slide-from-bottom;

    & > kp-svelte-component-wrapper {
        display: flex;
        flex-direction: column;
        flex: 1 1 0;
    }
}

@keyframes content-slide-from-bottom {
    0% {
        transform: translateY(0.75rem);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 100%;
    }
}

.main-panel-hlavni {
    margin-bottom: 30px; /* TODO: can we do better?  */
}

.side-column-panel {
    border-radius: 3px;
    box-shadow: 2px 1px 6px #eae7e7;
}

/*************  INDEX  **********************/
.main-content .index {
    padding: 20px; /* TODO: remove this and create uniform top and bottom margin of `.main-content` so it works everywhere */
}

.news-slider-header {
    background-color: #ebebeb;
    padding: 5px 10px 5px 100px;
    display: inline;
    border-radius: 0 7px 7px 0;
}

.news-slider {
    max-width: 90%;
    margin: 0 auto;
}

.news-slider .obalImg {
    display: inline;
}

.news-slider-item {
    text-align: center;
}

.news-record-subtitle {
    color: gray;
    font-size: 0.9em;
}

.portaroSearch {
    text-align: center;
}

.btn-slider {
    padding: 13px 2px;
}

.btn-slider-prev {
    float: left;
}

.btn-slider-next {
    float: right;
}

.novinkyVKataloguDiv {
    margin-top: 100px;
    max-width: 90vw;
}

.index .obalImg,
.detail-blok-main .obalImg {
    box-shadow: 1px 3px 6px lightgrey;
}

/************  HLEDANI  *******************/

.form-submit {
    margin-top: 20px;
}

/*************  DETAIL  *******************/

#detail-main-responsibility,
#detail-main-100abc,
.authority-100abcd { /* zneviditelneni - jen pro ty, co to mermomoci chteji */
    display: none;
}

.modal-body-block {
    &:not(:first-child) {
        margin-top: 10px;
        padding-top: 10px;
    }

    &:not(:last-child) {
        padding-bottom: 20px;
        border-bottom: 1px solid #e5e5e5;
    }
}


/************* TWITTER BOOTSTRAP *******************/

.button.btn,
a .button.btn,
input[type="button"].btn,
input[type="submit"].btn,
input[type="reset"].btn,
button.btn {
    background-image: none;
}

.popover {
    max-width: 800px;
    width: auto;
}

/************* STYLE 2.0 *******************/
.ace_editor {
    height: 400px;
}

.panel-heading-strong {
    font-weight: bold;
}

.main-panel-pravy .attachments-panel .list-group-item {
    padding: 7px 15px;
}

/*********  SPECIALNI UPRAVY  ******************/

.text-ellipsis {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.input-group .input-group-btn .input-group-addon {
    display: inline-table;
    margin-left: -1px;
}

.input-group-addon.input-group-addon-middle {
    border-left: 0;
}

.nowrap {
    white-space: nowrap;
}

// TODO: Remove these wrappers when migrating _layouts.ftl into Svelte
.kp-main-menu-svelte-component-wrapper {
    position: sticky;
    z-index: 100;
    top: 0;
}

.kp-messages-sidebar-component-wrapper {
    position: fixed;
    z-index: 110; // Above main menu
}

.kp-browser-progress-bar-component-wrapper {
    z-index: @browser-progress-bar-z-index;
    position: fixed;
}

.kp-toast-messages-component-wrapper {
    position: fixed;
    z-index: @toasts-z-index;
}

// Prevents header/footer layout shift
kp-svelte-component-wrapper > [data-slot] {
    display: none;
}

// New focus style that will override browser defaults
// in the name of consistency.
// we don't want to style Bootstrap input elements, that's why we omit everything with class form-control
// we dont want to outline elements that are focusable by JS only (noninteractive elements with custom interactive behavior)
:focus-visible:not(.form-control):not([tabindex="-1"]):not(dialog):not([popover]) {
    outline-offset: 1px !important;
    outline: 3px solid @themed-focus-outline-color !important;
    box-shadow: 0 0 0 5px @themed-focus-box-shadow-color !important;
}

[tabindex="-1"]:focus-visible {
    outline: none !important;
}

// Prevents transitions for users that prefer reduced motion
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
    }
}