import * as path from 'path';
import * as webpack from 'webpack';
import * as CleanWebpackPlugin from 'clean-webpack-plugin';
import {commonImportRegex, DEVELOPMENT_ENV, DEVELOPMENT_WITH_GRADLE_ENV} from './webpack.constants';
import {babelLoader, svelteLoaderFactory, svelteStylesLoader, tsLoader} from './webpack.loaders';
import LocalizationWebpackPlugin from './plugins/localization-webpack-plugin';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import ESLintPlugin from 'eslint-webpack-plugin';
// import * as WebpackConfigDumpPlugin from 'webpack-config-dump-plugin';

export default (dirname: string, env: string): webpack.Configuration => ({
    entry: {
        portaro: ['./src/app.ts'],
        print: ['./styles/print.less'],
    },

    output: {
        path: path.resolve(dirname, 'dist'),
        filename: 'scripts/[name].js',
        chunkFilename: 'scripts/[name].[contenthash].js',
        publicPath: '/resources/dist/',
    },

    module: {
        rules: [
            {
                test: /\.(js|ts)$/,
                include: [commonImportRegex],
                use: [
                    babelLoader,
                    tsLoader
                ]
            },
            {
                test: /\.svelte$/,
                use: [
                    babelLoader,
                    svelteLoaderFactory(env === DEVELOPMENT_ENV || env === DEVELOPMENT_WITH_GRADLE_ENV)
                ]
            },
            {
                // required to prevent errors from Svelte on Webpack 5+, omit on Webpack 4
                test: /\.m?js$/,
                include: /node_modules/,
                resolve: {
                    fullySpecified: false
                }
            },
            {
                test: /(\.css$)/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader
                    },
                    {
                        loader: 'css-loader',
                        options: {
                            sourceMap: true
                        }
                    },
                    svelteStylesLoader, // to fix problems with css generated by svelte
                ]
            },
            {
                test: /(\.less$)/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader
                    },
                    {
                        loader: 'css-loader',
                        options: {
                            sourceMap: true
                        }
                    },
                    {
                        loader: 'less-loader',
                        options: {
                            sourceMap: true
                        }
                    }
                ]
            },
            {
                test: /(\.sass$)|(\.scss$)/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader
                    },
                    {
                        loader: 'css-loader',
                        options: {
                            sourceMap: true
                        }
                    },
                    {
                        loader: 'sass-loader',
                        options: {
                            sourceMap: true,
                            api: "modern"
                        }
                    }
                ]
            },
            {
                test: /\.(gif|png|jpg|webp)$/,
                loader: 'file-loader',
                options: {
                    esModule: false,
                    name: 'assets/images/[name].[ext]'
                },
                type: 'javascript/auto'
            },
            {
                test: /\.(otf|eot|ttf|woff|woff2)$/,
                loader: 'file-loader',
                options: {
                    esModule: false,
                    name: 'assets/fonts/[name].[ext]'
                },
                type: 'javascript/auto'
            },
            {
                test: /\.svg$/,
                oneOf: [
                    {
                        resourceQuery: /inline/, // foo.svg?inline
                        loader: 'svg-inline-loader',
                        options: {
                            removeSVGTagAttrs: true
                        }
                    },
                    {
                        resourceQuery: /assets/, // foo.svg?assets
                        loader: 'file-loader',
                        options: {
                            esModule: false,
                            name: 'assets/images/[name].[ext]'
                        },
                        type: 'javascript/auto'
                    }
                ],
            },
            {
                test: /\.tpl\.ftl$/,
                loader: 'simple-ngtemplate-loader',
                options: {
                    relativeTo: path.resolve(dirname, 'src'),
                    prefix: '/ng-templates/'
                }
            },
            {
                test: /\.wasm$/,
                type: 'javascript/auto',
                loader: 'file-loader',
                options: {
                    publicPath: 'dist'
                },
            },
            {
                test: /\.md$/,
                resourceQuery: /raw/, // foo.md?raw
                type: 'asset/source'
            }
        ]
    },

    optimization: {
        splitChunks: {
            name: (module, chunks) => {
                return chunks.map((chunk) => chunk.name).join('.'); // rename common chunk to concatenation of chunks in which it is used (https://www.codemzy.com/blog/how-to-name-webpack-chunk)
            },
            cacheGroups: {
                defaultVendors: {
                    test: isVendor,
                    name: 'vendors',
                    chunks: 'initial'
                }
            }
        }
    },

    externals: {
        jquery: 'jQuery'
    },

    resolve: {
        extensions: ['.js', '.mjs', '.ts', '.svelte'],
        mainFields: ['svelte', 'browser', 'module', 'main'], // for svelte components import from node_modules, more info: https://github.com/sveltejs/svelte-loader#resolvemainfields
        conditionNames: ['svelte', 'browser', 'import'],
        alias: {
            src: path.resolve(dirname, 'src'),
            core: path.resolve(dirname, 'src', 'core'),
            shared: path.resolve(dirname, 'src', 'shared'),
            typings: path.resolve(dirname, 'src', 'typings'),
            svelte: path.resolve('node_modules', 'svelte/src/runtime'),
        }
    },

    devtool: 'source-map',

    plugins: [
        new ESLintPlugin({
            extensions: ['ts', 'svelte'],
            configType: 'flat',
            lintDirtyModulesOnly: env === DEVELOPMENT_ENV || env === DEVELOPMENT_WITH_GRADLE_ENV
        }),
        new MiniCssExtractPlugin({
            filename: 'assets/styles/[name].css',
            chunkFilename: 'assets/styles/[name].[contenthash].css'
        }),
        new CleanWebpackPlugin.CleanWebpackPlugin({
            verbose: true,
            cleanStaleWebpackAssets: false
        }),
        new LocalizationWebpackPlugin()
        /*, // plugin for investigating final (merged) webpack configuration
        new WebpackConfigDumpPlugin({
            outputPath: './',
            name: 'webpack.config.dump.js',
            depth: 15
        })*/
    ]
});

function isVendor({resource}) {
    return resource &&
        resource.indexOf('node_modules') >= 0 &&
        (resource.match(/.js$/) || resource.match(/.svelte$/));
}
