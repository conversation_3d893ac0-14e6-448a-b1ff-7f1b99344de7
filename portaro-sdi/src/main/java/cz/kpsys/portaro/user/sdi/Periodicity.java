package cz.kpsys.portaro.user.sdi;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.DefaultProvider;
import cz.kpsys.portaro.commons.util.DateUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.util.Date;

import static java.time.temporal.ChronoUnit.DAYS;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum Periodicity implements LabeledIdentified<Integer> {

    DAILY(0, Texts.ofMessageCoded("sdi.periodicity.Daily")),
    WEEKLY(1, Texts.ofMessageCoded("sdi.periodicity.Weekly")),
    MONTHLY(2, Texts.ofMessageCoded("sdi.periodicity.Monthly"));
    
    public static final Codebook<Periodicity, Integer> CODEBOOK = new StaticCodebook<>(values());
    public static final Provider<@NonNull Periodicity> DEFAULT_PROVIDER = DefaultProvider.byId(CODEBOOK, MONTHLY.getId());

    @NonNull Integer id;
    @NonNull Text text;

    /**
     * Vrati datum dalsi "spouste" podle periodicity.
     * <br/>
     * napr. pokud je periodicita mesic a previous = 27.4.2015, tak vrati 27.5.2015.
     */
    public Instant getNextPeriodDate(Instant previous) {
        return switch (this) {
            case DAILY -> previous.plus(1, DAYS);
            case WEEKLY -> DateUtils.addWeeks(Date.from(previous), 1).toInstant();
            case MONTHLY -> DateUtils.addMonths(Date.from(previous), 1).toInstant();
        };
    }
    
}
