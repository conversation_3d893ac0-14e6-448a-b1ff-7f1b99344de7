package cz.kpsys.portaro.search.view;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualFunction;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.search.field.DatatypeBySearchFieldLoader;
import cz.kpsys.portaro.search.field.SearchField;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DepartmentedSearchFieldToViewableSearchFieldConverter<CTX> implements AuthenticatedContextualFunction<SearchField, CTX, ViewableSearchField> {

    @NonNull SearchValueEditorLoader<CTX> valueEditorLoader;
    @NonNull DatatypeBySearchFieldLoader datatypeBySearchFieldLoader;

    @Override
    public @NonNull ViewableSearchField getOn(@NonNull SearchField source, @NonNull UserAuthentication currentAuth, @NonNull CTX ctx) {
        ValueEditor<?, ?, ?> editor = createValueEditor(currentAuth, ctx, source);
        return new ViewableSearchField(source.getId(), source.text(), editor);
    }

    private ValueEditor<?, ?, ?> createValueEditor(@NonNull UserAuthentication currentAuth, @NonNull CTX ctx, @NonNull SearchField source) {
        ScalarDatatype datatype = datatypeBySearchFieldLoader.getOptionalBySearchField(source)
                .orElse(CoreConstants.Datatype.TEXT);

        return valueEditorLoader.getSearchValueEditor(currentAuth, ctx, datatype);
    }

}
