package cz.kpsys.portaro.search.z;

import cz.kpsys.portaro.commons.convert.IdToObjectConverter;
import cz.kpsys.portaro.commons.convert.StringToAnyListConverter;
import cz.kpsys.portaro.commons.convert.StringToIntegerToAnyConverter;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.SpringDbLabeledIdentifiableLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbZServerLoader extends SpringDbLabeledIdentifiableLoader<ZServer, Integer> {

    @SuppressWarnings("TypeName")
    private static class DEF_ZSERVER {
        public static final String TABLE = "def_zserver";
        public static final String ID_ZSERVER = "id_zserver";
        public static final String JE_AUTORITNI = "je_autoritni";
        public static final String VALIDFOND = "validfond";
        public static final String PORADI = "poradi";
        public static final String POPIS = "popis";
        public static final String ADRESA = "adresa";
        public static final String DATABAZE = "databaze";
        public static final String CHARSET = "charset";
        public static final String FORMAT = "format";
        public static final String PRENOS = "prenos";
        public static final String UZIVATEL = "uzivatel";
        public static final String HESLO = "heslo";
        public static final String TIMEOUT = "timeout";
    }

    @NonNull Converter<@NonNull String, @NonNull List<Fond>> stringToFondsConverter;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;

    public SpringDbZServerLoader(NamedParameterJdbcOperations jdbcTemplate, QueryFactory queryFactory, @NonNull ByIdLoadable<Fond, Integer> fondLoader, @NonNull AllValuesProvider<Fond> enabledFondsProvider) {
        super(jdbcTemplate, queryFactory, DEF_ZSERVER.TABLE, DEF_ZSERVER.ID_ZSERVER, DEF_ZSERVER.POPIS, DEF_ZSERVER.PORADI);
        this.stringToFondsConverter = StringToAnyListConverter.create(StringToIntegerToAnyConverter.strict(new IdToObjectConverter<>(fondLoader))).throwWhenNull();
        this.enabledFondsProvider = enabledFondsProvider;
    }


    @Override
    protected ZServer createObject(Integer id, String name, ResultSet rs) throws SQLException {
        boolean authorityType = rs.getBoolean(DEF_ZSERVER.JE_AUTORITNI);
        List<Fond> validFonds = getValidFonds(authorityType, StringUtil.notBlankTrimmedString(rs.getString(DEF_ZSERVER.VALIDFOND)));
        return new ZServer(id, name, authorityType, validFonds);
    }

    private @NonNull List<Fond> getValidFonds(boolean authorityType, String notBlankTrimmedValidFondsValue) {
        if (notBlankTrimmedValidFondsValue == null) {
            return authorityType
                    ? Fond.filterAuthorityFonds(enabledFondsProvider.getAll())
                    : Fond.filterDocumentFonds(enabledFondsProvider.getAll());
        }
        return Objects.requireNonNull(stringToFondsConverter.convert(notBlankTrimmedValidFondsValue));
    }
}
