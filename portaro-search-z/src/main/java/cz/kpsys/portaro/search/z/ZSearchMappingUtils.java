package cz.kpsys.portaro.search.z;

import cz.kpsys.portaro.appserver.oxm.AppserverTag;
import org.jdom2.Element;

public class ZSearchMappingUtils {

    public static AppserverTag createServerElem(ZServer zServer) {
        return AppserverTag.empty("zserver").withParam("id", zServer.getId());
    }

    public static int getResultServerIdFromResultElem(Element resultElem) {
        return Integer.parseInt(resultElem.getChild("zserver").getAttributeValue("id"));
    }

    public static int getResultCountFromResultElem(Element resultElem) {
        return Integer.parseInt(resultElem.getChildTextNormalize("count"));
    }
}
