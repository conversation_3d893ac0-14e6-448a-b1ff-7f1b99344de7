package cz.kpsys.portaro.search.z;

import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import cz.kpsys.portaro.record.isbn.Isbn;
import cz.kpsys.portaro.record.isbn.IsbnChecker;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.BasicSearchField;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.EqWords;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;

import static cz.kpsys.portaro.search.field.StaticSearchFields.NAME;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ZServerQueryBuilder {

    @NonNull Converter<Restriction<? extends SearchField>, String> restrictionToZSearchRestrictionConverter;

    public ZServerQueryBuilder() {
        this(new RestrictionToZServerQueryConverter(new ZServerQueryFieldLoaderInMemory()));
    }


    public String build(MapBackedParams p) {
        Restriction<SearchField> restriction = createRestriction(p);
        return restrictionToZSearchRestrictionConverter.convert(restriction);
    }


    private Restriction<SearchField> createRestriction(MapBackedParams p) {
        if (p.get(RecordConstants.SearchParams.FOND).stream().anyMatch(FondTypeResolver::isDocumentFond)) {
            Conjunction<SearchField> conjunction = new Conjunction<>();

            if (p.hasNotNull(CoreSearchParams.Q)) {
                conjunction.add(new Term<>(RecordConstants.SearchFields.DOCUMENT_ALL, new EqWords(p.get(CoreSearchParams.Q))));
            }

            if (p.hasNotNull(RecordConstants.SearchParams.ISBN)) {
                String value = p.get(RecordConstants.SearchParams.ISBN);
                if (IsbnChecker.isValidIsbn(value)) {
                    String trimmedIsbn = new Isbn(value).getNormalizedValue();
                    conjunction.add(new Term<>(RecordConstants.SearchFields.DOCUMENT_ISBN, new Eq(trimmedIsbn)));
                } else {
                    conjunction.add(createFalseRestriction());
                }
            }

            if (p.hasNotNull(RecordConstants.SearchParams.ISSN)) {
                String value = p.get(RecordConstants.SearchParams.ISSN);
                if (IsbnChecker.isValidIssn(value)) {
                    String trimmedIsbn = new Isbn(value).getNormalizedValue();
                    conjunction.add(new Term<>(RecordConstants.SearchFields.DOCUMENT_ISSN, new Eq(trimmedIsbn)));
                } else {
                    conjunction.add(createFalseRestriction());
                }
            }

            if (p.hasNotNull(RecordConstants.SearchParams.ISBN_OR_ISSN)) {
                String value = p.get(RecordConstants.SearchParams.ISBN_OR_ISSN);
                if (IsbnChecker.isValidIsbn(value)) {
                    String trimmedIsbn = new Isbn(value).getNormalizedValue();
                    conjunction.add(new Term<>(RecordConstants.SearchFields.DOCUMENT_ISBN, new Eq(trimmedIsbn)));
                } else if (IsbnChecker.isValidIssn(value)) {
                    String trimmedIssn = new Isbn(value).getNormalizedValue();
                    conjunction.add(new Term<>(RecordConstants.SearchFields.DOCUMENT_ISSN, new Eq(trimmedIssn)));
                } else {
                    conjunction.add(createFalseRestriction());
                }
            }

            if (p.hasNotNull(CoreSearchParams.NAME)) {
                conjunction.add(new Term<>(NAME, new Eq(p.get(CoreSearchParams.NAME))));
            }

            if (p.hasNotNull(RecordConstants.SearchParams.AUTHOR)) {
                conjunction.add(new Term<>(RecordConstants.SearchFields.AUTHOR, new Eq(p.get(RecordConstants.SearchParams.AUTHOR))));
            }

            if (p.hasNotNull(RecordConstants.SearchParams.PUBLICATION_YEAR)) {
                conjunction.add(new Term<>(RecordConstants.SearchFields.DOCUMENT_YEAR, new Eq(p.get(RecordConstants.SearchParams.PUBLICATION_YEAR))));
            }

            return conjunction;
        }

        if (p.get(RecordConstants.SearchParams.FOND).stream().anyMatch(FondTypeResolver::isAuthorityFond)) {
            Assert.state(p.hasNotNull(CoreSearchParams.Q) || p.hasNotNull(CoreSearchParams.NAME), "Query is not specified");
            return new Conjunction<SearchField>()
                    .addIfHas(p, CoreSearchParams.Q, val -> new Term<>(RecordConstants.SearchFields.AUTHORITY_ALL, new EqWords(val)))
                    .addIfHas(p, CoreSearchParams.NAME, val -> new Term<>(RecordConstants.SearchFields.AUTHORITY_NAME, new Eq(val)));
        }

        throw new IllegalStateException("Search type is not defined in parameters");
    }

    private Term<BasicSearchField> createFalseRestriction() {
        return new Term<>(RecordConstants.SearchFields.DOCUMENT_ISBN, new Eq("INVALID_ISBN_OR_ISSN"));
    }


}
