package cz.kpsys.portaro.search.z;

import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.field.QueryFieldsBySearchFieldLoader;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.matcher.SearchMatcher;
import cz.kpsys.portaro.search.z.ZServiceConstants.SearchSemantic;
import lombok.NonNull;

import java.util.*;

import static cz.kpsys.portaro.search.field.StaticSearchFields.ALL;
import static cz.kpsys.portaro.search.field.StaticSearchFields.NAME;

public class ZServerQueryFieldLoaderInMemory implements QueryFieldsBySearchFieldLoader<ZQueryField> {

    private static final Map<SearchField, Collection<ZQueryField>> FIELDS_TO_Z_FIELD_SETS = new HashMap<>();

    static {
        FIELDS_TO_Z_FIELD_SETS.put(ALL, List.of(
                new ZQueryField(SearchSemantic.DOCUMENT_NAME),
                new ZQueryField(SearchSemantic.DOCUMENT_AUTHOR),
                new ZQueryField(SearchSemantic.DOCUMENT_AUTHOR_PERSON_NAME),
                new ZQueryField(SearchSemantic.AUTHORITY_PERSON_NAME),
                new ZQueryField(SearchSemantic.AUTHORITY_CORPORATION_NAME),
                new ZQueryField(SearchSemantic.AUTHORITY_CONFERENCE_NAME),
                new ZQueryField(SearchSemantic.KEYWORD_AUTHORITY_NAME),
                new ZQueryField(SearchSemantic.GEOGRAPHICAL_AUTHORITY_NAME)
        ));

        FIELDS_TO_Z_FIELD_SETS.put(RecordConstants.SearchFields.DOCUMENT_ALL, List.of(
                new ZQueryField(SearchSemantic.DOCUMENT_NAME),
                new ZQueryField(SearchSemantic.DOCUMENT_AUTHOR),
                new ZQueryField(SearchSemantic.DOCUMENT_AUTHOR_PERSON_NAME)
        ));

        FIELDS_TO_Z_FIELD_SETS.put(RecordConstants.SearchFields.DOCUMENT_ISBN_OR_ISSN, List.of(
                new ZQueryField(SearchSemantic.DOCUMENT_ISBN),
                new ZQueryField(SearchSemantic.DOCUMENT_ISSN)
        ));

        FIELDS_TO_Z_FIELD_SETS.put(RecordConstants.SearchFields.DOCUMENT_ISBN, List.of(
                new ZQueryField(SearchSemantic.DOCUMENT_ISBN)
        ));

        FIELDS_TO_Z_FIELD_SETS.put(RecordConstants.SearchFields.DOCUMENT_ISSN, List.of(
                new ZQueryField(SearchSemantic.DOCUMENT_ISSN)
        ));

        FIELDS_TO_Z_FIELD_SETS.put(NAME, List.of(
                new ZQueryField(SearchSemantic.DOCUMENT_NAME)
        ));

        FIELDS_TO_Z_FIELD_SETS.put(RecordConstants.SearchFields.AUTHOR, List.of(
                new ZQueryField(SearchSemantic.DOCUMENT_AUTHOR)
        ));

        FIELDS_TO_Z_FIELD_SETS.put(RecordConstants.SearchFields.DOCUMENT_YEAR, List.of(
                new ZQueryField(SearchSemantic.DOCUMENT_PUBLICATION_YEAR)
        ));

        FIELDS_TO_Z_FIELD_SETS.put(RecordConstants.SearchFields.AUTHORITY_ALL, List.of(
                new ZQueryField(SearchSemantic.AUTHORITY_PERSON_NAME),
                new ZQueryField(SearchSemantic.AUTHORITY_CORPORATION_NAME),
                new ZQueryField(SearchSemantic.AUTHORITY_CONFERENCE_NAME),
                new ZQueryField(SearchSemantic.KEYWORD_AUTHORITY_NAME),
                new ZQueryField(SearchSemantic.GEOGRAPHICAL_AUTHORITY_NAME)
        ));

        FIELDS_TO_Z_FIELD_SETS.put(RecordConstants.SearchFields.AUTHORITY_NAME, List.of(
                new ZQueryField(SearchSemantic.AUTHORITY_PERSON_NAME),
                new ZQueryField(SearchSemantic.AUTHORITY_CORPORATION_NAME),
                new ZQueryField(SearchSemantic.AUTHORITY_CONFERENCE_NAME),
                new ZQueryField(SearchSemantic.KEYWORD_AUTHORITY_NAME),
                new ZQueryField(SearchSemantic.GEOGRAPHICAL_AUTHORITY_NAME)
        ));
    }

    @Override
    public @NonNull List<ZQueryField> getAllBySearchField(@NonNull SearchField field, @NonNull SearchMatcher matcher) {
        Collection<ZQueryField> zQueryFields = Objects.requireNonNull(FIELDS_TO_Z_FIELD_SETS.get(field), () -> "Field " + field + " is not supported in z-search");
        return new ArrayList<>(zQueryFields);
    }

}
