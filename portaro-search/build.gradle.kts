dependencies {
    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testImplementation("org.hamcrest:hamcrest-library:+")
    testImplementation("uk.co.datumedge:hamcrest-json:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-appserver"))
    implementation(project(":portaro-batch"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-commons-db"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-sql-generator"))

    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework.data:spring-data-jpa:3.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.+")
}
