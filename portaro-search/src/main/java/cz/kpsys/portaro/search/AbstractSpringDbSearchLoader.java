package cz.kpsys.portaro.search;

import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.OrderByItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PROTECTED, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public abstract class AbstractSpringDbSearchLoader<P extends SearchParams, ITEM, PAGING extends Paging> implements PageSearchLoader<P, ITEM, PAGING> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public Chunk<ITEM, PAGING> getPage(@NonNull PAGING paging, @Nullable SortingItem customSorting, @NonNull P p) {
        SelectQuery sq = queryFactory.newSelectQuery();
        select(sq, p, customSorting);
        boolean valid = fillQuery(false, sq, p, customSorting);
        valid = valid && fillSorting(sq, p, customSorting);

        if (!valid) {
            return Chunk.ofNothing();
        }
        Sorting finalSorting = finalDbSorting(customSorting, p);

        sq.setPaging(paging, finalSorting);

        TimeMeter tm = TimeMeter.start();
        Chunk<ITEM, PAGING> result = jdbcTemplate.query(sq.getSql(), sq.getParamMap(), createResultSetExtractor(sq, p, paging, finalSorting));
        if (log.isDebugEnabled()) {
            log.debug("Time {} for {}", tm.elapsedTimeString(), sq.getRevealedParamsQuery());
        }
        return result;
    }

    @Override
    public int getTotalElements(@NonNull P p) {
        SelectQuery sq = queryFactory.newSelectQuery();
        selectCount(sq, p);
        boolean valid = fillQuery(true, sq, p, null);
        if (!valid) {
            return 0;
        }
        TimeMeter tm = TimeMeter.start();
        Integer result = jdbcTemplate.queryForObject(sq.getSql(), sq.getParamMap(), Integer.class);
        if (log.isDebugEnabled()) {
            log.debug("Time {} for {}", tm.elapsedTimeString(), sq.getRevealedParamsQuery());
        }
        return DbUtils.toInt(result);
    }

    protected void select(@NonNull SelectQuery sq, @NonNull P p, @Nullable SortingItem customSorting) {
        sq.select();
    }

    protected Map<String, String> sortToUniqueOrderDbColumnsMapping() {
        return Map.of();
    }

    protected Map<String, String> sortToDbResultSetLabelMapping() {
        return Map.of();
    }

    protected void selectCount(@NonNull SelectQuery sq, @NonNull P p) {
        sq.selectCount();
    }

    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull P p) {
        return Sorting.empty();
    }

    protected Optional<SortingItem> defaultOrCustomSorting(@Nullable SortingItem customSorting) {
        return Optional.empty();
    }

    protected @NonNull Sorting finalDbSorting(@Nullable SortingItem customSorting, @NonNull P p) {
        Optional<SortingItem> customOrDefaultSorting = defaultOrCustomSorting(customSorting);
        Sorting sorting = mandatorySorting(customSorting, p).appendNotEmptyOptionals(List.of(customOrDefaultSorting));
        return sorting.modifyFieldNames(s -> sortToUniqueOrderDbColumnsMapping().getOrDefault(s, s));
    }

    protected boolean fillSorting(@NonNull SelectQuery sq, @NonNull P p, @Nullable SortingItem customSorting) {
        Sorting sorting = finalDbSorting(customSorting, p);
        sq.orderBy().add(sorting.sortingItems().stream().map(source -> new OrderByItem(source.field(), source.asc(), false)).distinct().toList());
        return true;
    }

    protected abstract ResultSetExtractor<Chunk<ITEM, PAGING>> createResultSetExtractor(@NonNull SelectQuery sq, @NonNull P p, @NonNull PAGING paging, @NonNull Sorting sorting);

    protected abstract boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull P p, @Nullable SortingItem customSorting);

}
