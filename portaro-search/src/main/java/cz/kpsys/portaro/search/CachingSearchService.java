package cz.kpsys.portaro.search;

import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CachingSearchService<PAGING extends Paging> implements SearchService<InternalSearchResult<String, MapBackedParams, PAGING>, MapBackedParams>, CacheCleaner {

    public static final String LONG_TERM_CACHE_NAME = "rawableSearchLongTerm";

    @NonNull SearchService<InternalSearchResult<String, MapBackedParams, PAGING>, MapBackedParams> delegate;

    @Cacheable(sync = true, cacheResolver = "rawableSearchServiceCacheResolver")
    @Override
    public InternalSearchResult<String, MapBackedParams, PAGING> search(MapBackedParams params, Range range, SortingItem sorting, Department ctx, CacheMode cache) {
        return delegate.search(params, range, sorting, ctx, cache);
    }

    @CacheEvict(value = {LONG_TERM_CACHE_NAME}, allEntries = true)
    @Override
    public void clearCache() {
        log.info("Clearing {} caches", List.of(LONG_TERM_CACHE_NAME));
    }
}
