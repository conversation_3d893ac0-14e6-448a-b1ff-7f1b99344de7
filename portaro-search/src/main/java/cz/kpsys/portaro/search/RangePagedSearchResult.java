package cz.kpsys.portaro.search;

import cz.kpsys.portaro.search.lucene.Facet;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.Duration;
import java.util.List;

public record RangePagedSearchResult<ITEM>(

        @NonNull
        Chunk<ITEM, RangePaging> chunk,

        int totalElements,

        @NonNull
        RangePaging currentPaging,

        @NonNull
        Duration duration,

        @NonNull
        List<Facet> availableFacets,

        @Nullable
        String usedRawQuery

) implements PagedSearchResult<ITEM, RangePaging> {

    public int pageNumber() {
        return currentPaging.pageNumber();
    }

    public int totalPages() {
        return (int) Math.ceil((double) totalElements / pageSize());
    }

    public boolean hasPrevious() {
        return pageNumber() > 1;
    }

    public boolean isFirst() {
        return !hasPrevious();
    }

    public boolean hasNext() {
        return numberOfElements() == pageSize() && (pageNumber() * pageSize()) < totalElements();
    }

    public boolean isLast() {
        return !hasNext();
    }

    @Override
    public String toString() {
        return "Result page " + pageNumber() + "/" + totalPages() + " content: " + content();
    }
}
