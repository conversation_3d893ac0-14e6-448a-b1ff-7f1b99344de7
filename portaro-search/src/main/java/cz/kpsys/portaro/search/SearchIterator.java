package cz.kpsys.portaro.search;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheDeleter;
import cz.kpsys.portaro.department.Department;
import lombok.Getter;
import lombok.NonNull;

import java.util.List;
import java.util.function.Consumer;

public class SearchIterator<PARAMS extends SearchParams, ITEM> {

    public static final int DEFAULT_PAGE_SIZE = 10;
    public static final int LIMIT_ITEMS_NO_LIMIT = -1;

    @NonNull Search<PARAMS, ITEM, RangePaging> target;
    private RangePaging lastRange = RangePaging.forFirstPage(DEFAULT_PAGE_SIZE);
    private final PARAMS dynamicParams;
    @Getter
    private boolean pristine = true;
    private final Department department;
    private final UserAuthentication currentAuth;


    public SearchIterator(@NonNull Search<PARAMS, ITEM, RangePaging> target, Department department, UserAuthentication currentAuth) {
        this.target = target;
        this.dynamicParams = target.getLastDynamicParams();
        this.department = department;
        this.currentAuth = currentAuth;
    }

    public SearchIterator(@NonNull Search<PARAMS, ITEM, RangePaging> target, PARAMS customParams, Department department, UserAuthentication currentAuth) {
        this.target = target;
        this.dynamicParams = customParams;
        this.department = department;
        this.currentAuth = currentAuth;
    }

    public RangePagedSearchResult<ITEM> getLastResult() {
        return (RangePagedSearchResult<ITEM>) target.getLastResult();
    }

    public void fetchNextPage() {
        if (!hasNextPage()) {
            throw new IllegalStateException("Last search result was last page");
        }
        if (!pristine) {
            lastRange = lastRange.incrementPage();
        }
        target.fetch(dynamicParams, lastRange, currentAuth, this.department, CacheMode.NONE);
        pristine = false;
    }

    public void iterate(int limitItems, int maxPages, CacheDeleter<ITEM> cacheDeleter, Consumer<List<ITEM>> chunkConsumer) {
        while (hasNextPage() && (isPristine() || limitItems == LIMIT_ITEMS_NO_LIMIT || getLastPageNumber() < maxPages)) {
            fetchNextPage();
            List<ITEM> content = getLastResult().content();
            chunkConsumer.accept(content);
            content.forEach(cacheDeleter::deleteFromCache);
        }
    }

    public void setPageSize(int pageSize) {
        lastRange = RangePaging.forFirstPage(pageSize);
    }

    public boolean hasNextPage() {
        if (pristine) {
            return true;
        }
        return !getLastResult().isLast();
    }

    public int getLastPageNumber() {
        if (pristine) {
            return -1;
        }
        return getLastResult().pageNumber();
    }

}
