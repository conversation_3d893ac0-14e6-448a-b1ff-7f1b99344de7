package cz.kpsys.portaro.search;

import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TransactionalPageSearchLoader<P extends SearchParams, ITEM, PAGING extends Paging> implements PageSearchLoader<P, ITEM, PAGING> {

    @NonNull PageSearchLoader<P, ITEM, PAGING> delegate;
    @NonNull TransactionTemplate transactionTemplate;

    @Override
    public Chunk<ITEM, PAGING> getPage(PAGING paging, SortingItem customSorting, P params) {
        return transactionTemplate.execute(_ -> delegate.getPage(paging, customSorting, params));
    }

    @Override
    public int getTotalElements(P params) {
        Integer result = transactionTemplate.execute(txStatus -> delegate.getTotalElements(params));
        return Objects.requireNonNull(result);
    }
}
