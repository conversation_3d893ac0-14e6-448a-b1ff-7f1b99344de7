package cz.kpsys.portaro.search.facet;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum FacetDefinitionType implements LabeledIdentified<Integer>, Identified<Integer> {

    LIST(0, Texts.ofMessageCoded("util.TypDefinice.List")),
    SCRIPT(1, Texts.ofMessageCoded("util.TypDefinice.Script")),
    SCALA_SCRIPT(2, Texts.ofMessageCoded("util.TypDefinice.ScalaScript"));

    public static final Codebook<FacetDefinitionType, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer id;
    @NonNull Text text;
    
}
