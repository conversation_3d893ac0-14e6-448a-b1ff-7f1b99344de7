package cz.kpsys.portaro.search.facet;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum FacetScope implements LabeledIdentified<Integer> {

    DOCUMENTS(0, Texts.ofMessageCoded("hledani.facet.Documents")),
    AUTHORITIES(1, Texts.ofMessageCoded("hledani.facet.Authorities")),
    DOCUMENTS_AND_AUTHORITIES(2, Texts.ofMessageCoded("hledani.facet.DocAuthorities"));

    public static final Codebook<FacetScope, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer id;
    @NonNull Text text;
    
}
