package cz.kpsys.portaro.search.facet;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.NamedLabeledIdentifiedRecord;
import cz.kpsys.portaro.commons.object.NonNullOrderedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.NonNull;
import lombok.experimental.FieldNameConstants;
import org.jspecify.annotations.Nullable;

@FieldNameConstants
public record FacetType(

        @NonNull
        Integer id,

        @NonNull
        String name,

        @NonNull
        Integer order,

        @NullableNotBlank
        String definition,

        @NonNull
        FacetDefinitionType definitionType,

        boolean exemplarType,

        boolean enabled,

        @NonNull
        SortingItem sorting,

        @NonNull
        FacetScope scope,

        @Nullable
        ScalarDatatype datatype

) implements NamedLabeledIdentifiedRecord<Integer>, NonNullOrderedRecord {

    @Override
    public Text text() {
        return Texts.ofMessageCodedOrNativeOrEmptyNative("hledani.facet." + name(), name());
    }

    public SearchField toSearchField() {
        return new FacetTypeSearchField(name(), getText(), datatype);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, FacetType.class);
    }

    @Override
    public int hashCode() {
        return getId().hashCode();
    }
}
