package cz.kpsys.portaro.search.facet;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.Saver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FacetTypeCreator {

    @NonNull Saver<FacetType, FacetType> facetTypeSaver;
    @NonNull TransactionTemplate readwriteTransactionTemplate;
    @NonNull Codebook<FacetType, Integer> facetTypeLoader;

    public FacetType create(@NonNull FacetTypeCreationCommand command) {
        return readwriteTransactionTemplate.execute(_ -> {
            FacetType facetType = new FacetType(
                    Identified.getNewIntId(facetTypeLoader.getAll()),
                    command.name(),
                    command.order(),
                    command.definition(),
                    command.definitionType(),
                    command.exemplarType(),
                    command.enabled(),
                    command.sorting(),
                    command.scope(),
                    command.datatype()
            );
            return facetTypeSaver.save(facetType);
        });
    }
}
