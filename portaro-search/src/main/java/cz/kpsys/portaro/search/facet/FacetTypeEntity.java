package cz.kpsys.portaro.search.facet;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import static cz.kpsys.portaro.databasestructure.SearchDb.DEF_REZ.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor()
@Getter
public class FacetTypeEntity implements Identified<Integer> {

    @Id
    @Column(name = ID_REZ)
    @NonNull
    Integer id;

    @Column(name = NAZEV)
    @NonNull
    String name;

    @Column(name = PORADI)
    @NonNull
    Integer order;

    @Column(name = DEFINICE)
    @Nullable
    String definition;

    @Column(name = TYP)
    @NonNull
    Integer definitionTypeId;

    @Column(name = EXEMP)
    @NonNull
    Boolean exemplarType;

    @Column(name = INDEXOVAT)
    @NonNull
    Boolean enabled;

    @Column(name = RAZENI)
    @NonNull
    String sortingId;

    @Column(name = MNOZINA)
    @NonNull
    Integer scopeId;

    @Column(name = DATATYPE)
    @Nullable
    String datatype;
}
