package cz.kpsys.portaro.search.facet;

import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class FacetTypeFromEntityConverter implements Converter<FacetTypeEntity, FacetType> {

    @NonNull Codebook<SortingItem, String> facetTypeSortingLoader;
    @NonNull Codebook<FacetDefinitionType, Integer> facetDefinitionTypeLoader;
    @NonNull Codebook<FacetScope, Integer> facetScopeLoader;

    @Override
    public FacetType convert(@NonNull FacetTypeEntity entity) {
        return new FacetType(
                entity.getId(),
                entity.getName(),
                entity.getOrder(),
                entity.getDefinition(),
                facetDefinitionTypeLoader.getById(entity.getDefinitionTypeId()),
                entity.getExemplarType(),
                entity.getEnabled(),
                facetTypeSortingLoader.getById(entity.getSortingId()),
                facetScopeLoader.getById(entity.getScopeId()),
                ObjectUtil.elvis(entity.getDatatype(), Datatype::scalar)
        );
    }

}
