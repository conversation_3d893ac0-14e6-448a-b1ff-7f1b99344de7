package cz.kpsys.portaro.search.facet;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.search.field.SearchField;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

public record FacetTypeSearchField(

        @NonNull
        String id,

        @NonNull
        @NotEmpty
        Text text,

        @JsonIgnore
        @Nullable
        ScalarDatatype indexedValueDatatype

) implements SearchField {

    @Override
    public @NonNull String sourceDescription() {
        return "Facet";
    }

    @Override
    public boolean equals(Object o) {
        return o instanceof SearchField that && id().equals(that.id());
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }

    @Override
    public String toString() {
        return "FacetTypeSearchField " + id();
    }
}
