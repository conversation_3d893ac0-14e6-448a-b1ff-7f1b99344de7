package cz.kpsys.portaro.search.facet;

import cz.kpsys.portaro.sorting.SortingItem;

public class FacetTypeSorting {
    public static final SortingItem SORTING_FREQ_DESC = SortingItem.ofSimpleIdAsNativeTextDesc("freq");
    public static final SortingItem SORTING_ALPH_ASC = SortingItem.ofSimpleIdAsNativeTextAsc("alphabet");
    public static final SortingItem SORTING_ALPH_DESC = SortingItem.ofSimpleIdAsNativeTextDesc("alphabet");
    public static final SortingItem SORTING_CHRONO_ASC = SortingItem.ofSimpleIdAsNativeTextAsc("chrono");
    public static final SortingItem SORTING_CHRONO_DESC = SortingItem.ofSimpleIdAsNativeTextDesc("chrono");
    public static final SortingItem SORTING_NUM_ASC = SortingItem.ofSimpleIdAsNativeTextAsc("num");
    public static final SortingItem SORTING_NUM_DESC = SortingItem.ofSimpleIdAsNativeTextDesc("num");
}
