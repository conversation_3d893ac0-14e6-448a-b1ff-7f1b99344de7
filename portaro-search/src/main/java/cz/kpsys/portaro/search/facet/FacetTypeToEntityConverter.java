package cz.kpsys.portaro.search.facet;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.datatype.Datatype;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class FacetTypeToEntityConverter implements Converter<FacetType, FacetTypeEntity> {

    @Override
    public FacetTypeEntity convert(@NonNull FacetType source) {
        return new FacetTypeEntity(
                source.id(),
                source.name(),
                source.order(),
                source.definition(),
                source.definitionType().getId(),
                source.exemplarType(),
                source.enabled(),
                source.sorting().getId(),
                source.scope().getId(),
                ObjectUtil.elvis(source.datatype(), Datatype::getName)
        );
    }
}
