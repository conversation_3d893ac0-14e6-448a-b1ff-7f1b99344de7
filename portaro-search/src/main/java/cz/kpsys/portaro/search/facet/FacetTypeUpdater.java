package cz.kpsys.portaro.search.facet;

import cz.kpsys.portaro.commons.object.repo.Saver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FacetTypeUpdater {

    @NonNull Saver<FacetType, FacetType> facetTypeSaver;
    @NonNull TransactionTemplate readwriteTransactionTemplate;

    public FacetType update(@NonNull FacetTypeEditationCommand command) {
        FacetType facetType = new FacetType(
                command.id(),
                command.name(),
                command.order(),
                command.definition(),
                command.definitionType(),
                command.exemplarType(),
                command.enabled(),
                command.sorting(),
                command.scope(),
                command.datatype()
        );
        return readwriteTransactionTemplate.execute(_ -> facetTypeSaver.save(facetType));
    }


}
