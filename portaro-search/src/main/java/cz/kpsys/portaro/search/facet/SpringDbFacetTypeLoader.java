package cz.kpsys.portaro.search.facet;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.database.SpringDbLabeledIdentifiableLoader;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Optional;

import static cz.kpsys.portaro.databasestructure.SearchDb.DEF_REZ.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbFacetTypeLoader extends SpringDbLabeledIdentifiableLoader<FacetType, Integer> {

    @NonNull ByIdLoadable<SortingItem, String> facetTypeSortingLoader;

    public SpringDbFacetTypeLoader(NamedParameterJdbcOperations jdbcTemplate, QueryFactory queryFactory, @NonNull ByIdLoadable<SortingItem, String> facetTypeSortingLoader) {
        super(jdbcTemplate, queryFactory, TABLE, ID_REZ, NAZEV, PORADI);
        this.facetTypeSortingLoader = facetTypeSortingLoader;
    }

    @Override
    protected FacetType createObject(Integer id, String name, ResultSet rs) throws SQLException {
        return new FacetType(
                id,
                name,
                DbUtils.getIntegerNotNull(rs, PORADI),
                StringUtil.notEmptyString(rs.getString(DEFINICE)),
                FacetDefinitionType.CODEBOOK.getById(rs.getInt(TYP)),
                rs.getBoolean(EXEMP),
                rs.getBoolean(INDEXOVAT),
                facetTypeSortingLoader.getById(rs.getString(RAZENI)),
                FacetScope.CODEBOOK.getById(rs.getInt(MNOZINA)),
                Optional.ofNullable(rs.getString(DATATYPE)).map(Datatype::scalar).orElse(CoreConstants.Datatype.FACET_VALUE)
        );
    }
    
}
