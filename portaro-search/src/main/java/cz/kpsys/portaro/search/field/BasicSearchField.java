package cz.kpsys.portaro.search.field;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import lombok.With;
import org.jspecify.annotations.Nullable;

@With
public record BasicSearchField(

        @NonNull
        String id,

        @NonNull
        @NotEmpty
        Text text,

        @JsonIgnore
        @Nullable
        ScalarDatatype indexedValueDatatype,

        @Nullable
        @NullableNotBlank
        String sourceDescription

) implements SearchField {

    public static BasicSearchField createBuiltIn(String id, ScalarDatatype indexedValueDatatype) {
        return new BasicSearchField(id, Texts.ofNative(id), indexedValueDatatype, "Built-in");
    }

    public static BasicSearchField createBuiltIn(String id, ScalarDatatype indexedValueDatatype, Text text) {
        return new BasicSearchField(id, text, indexedValueDatatype, "Built-in");
    }

    public static BasicSearchField testingTextual(@NonNull String id) {
        return new BasicSearchField(id, Texts.ofNative(id), CoreConstants.Datatype.TEXT, "Testing search field");
    }

    @Override
    public boolean equals(Object o) {
        return o instanceof SearchField that && id().equals(that.id());
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }

    @Override
    public String toString() {
        return "BasicSearchField " + id();
    }
}
