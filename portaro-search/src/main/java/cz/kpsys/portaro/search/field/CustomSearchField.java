package cz.kpsys.portaro.search.field;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.NamedRecord;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.List;

public record CustomSearchField(

        @NonNull
        String id,

        @NonNull
        @NotBlank
        String name,

        @NonNull
        @NotEmpty
        Text text,

        @NonNull
        List<String> luceneKeys,

        @JsonIgnore
        @NonNull
        ScalarDatatype indexedValueDatatype,

        @Nullable
        @NullableNotBlank
        String sourceDescription

) implements SearchField, NamedRecord {

    public CustomSearchField(@NonNull String id,
                             @NonNull String name,
                             @NonNull List<String> luceneKeys,
                             @NonNull ScalarDatatype datatype,
                             @Nullable @NullableNotBlank String sourceDescription) {
        this(id, name, Texts.ofDefaulted(Texts.ofMessageCoded("search.customfield.%s".formatted(id)), Texts.ofNative(name)), luceneKeys, datatype, sourceDescription);
    }

    @Override
    public boolean equals(Object o) {
        return o instanceof SearchField that && id().equals(that.id());
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }

    @Override
    public String toString() {
        return "CustomSearchField " + id();
    }
}
