package cz.kpsys.portaro.search.field;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.search.restriction.matcher.SearchMatcher;

import java.util.Optional;

public interface DatatypeBySearchFieldLoader {

    Optional<ScalarDatatype> getOptionalBySearchField(SearchField searchField);

    Optional<ScalarDatatype> getOptionalBySearchFieldAndMatcher(SearchField searchField, SearchMatcher matcher);

}
