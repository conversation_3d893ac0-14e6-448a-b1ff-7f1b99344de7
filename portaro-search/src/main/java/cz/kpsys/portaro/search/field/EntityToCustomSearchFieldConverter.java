package cz.kpsys.portaro.search.field;

import cz.kpsys.portaro.commons.convert.StringToStringListConverter;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Objects;

public class EntityToCustomSearchFieldConverter implements Converter<CustomSearchFieldEntity, CustomSearchField> {

    @NonNull Converter<String, @NonNull List<String>> keyColumnValueToKeysConverter = new StringToStringListConverter(",").throwOnBlankItems();

    @Override
    public CustomSearchField convert(@NonNull CustomSearchFieldEntity source) {
        List<String> luceneKeys = Objects.requireNonNull(keyColumnValueToKeysConverter.convert(source.getLuceneKeysCommaDelimitedString()));
        return new CustomSearchField(
                source.getId(),
                source.getId(),
                luceneKeys,
                ScalarDatatype.scalar(source.getDatatype()),
                "Custom - %s".formatted(String.join(",", luceneKeys))
        );
    }
}
