package cz.kpsys.portaro.search.field;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.object.LabeledIdentifiedRecord;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

public interface SearchField extends LabeledIdentifiedRecord<String> {

    String UNKNOWN_SOURCE_DESCRIPTION = "Unknown source";

    /// represents datatype of an indexed value, not datatype of a searched value,
    /// Because e.g., search field with indexedValueDatatype = DATETIME_RANGE can have searched value of type DATETIME_RANGE or also single DATETIME (depending on search matcher)
    @JsonIgnore
    @NonNull
    ScalarDatatype indexedValueDatatype();

    @Nullable
    @NullableNotBlank
    default String sourceDescription() {
        return null;
    }

}
