package cz.kpsys.portaro.search.field;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.StaticCodebook;

public class StaticSearchFields {

    public static final BasicSearchField KIND = BasicSearchField.createBuiltIn("kind", CoreConstants.Datatype.TEXT);
    public static final BasicSearchField SUBKIND = BasicSearchField.createBuiltIn("subkind", CoreConstants.Datatype.TEXT);
    public static final BasicSearchField WHATEVER = BasicSearchField.createBuiltIn("whatever", CoreConstants.Datatype.TEXT, Texts.ofMessageCoded("search.builtinfield.whatever"));
    public static final BasicSearchField ALL = BasicSearchField.createBuiltIn("q", CoreConstants.Datatype.TEXT);
    public static final BasicSearchField NAME = BasicSearchField.createBuiltIn("name", CoreConstants.Datatype.TEXT);
    public static final BasicSearchField DEPARTMENT = BasicSearchField.createBuiltIn("department", CoreConstants.Datatype.DEPARTMENT, Texts.ofMessageCoded("commons.Department"));
    public static final BasicSearchField UNRESTRICTED_DEPARTMENT = BasicSearchField.createBuiltIn("unrestrictedDepartment", CoreConstants.Datatype.DEPARTMENT, Texts.ofMessageCoded("commons.Department"));
    public static final BasicSearchField DRAFT_DEPARTMENT = BasicSearchField.createBuiltIn("draftDepartment", CoreConstants.Datatype.DEPARTMENT, Texts.ofMessageCoded("commons.Department"));
    public static final BasicSearchField NOTE = BasicSearchField.createBuiltIn("note", CoreConstants.Datatype.TEXT, Texts.ofMessageCoded("commons.Note"));

    public static final StaticCodebook<SearchField, String> CODEBOOK = StaticCodebook.<SearchField, String>ofDynamicSize()
            .add(KIND)
            .add(WHATEVER)
            .add(ALL)
            .add(NAME)
            .add(DEPARTMENT)
            .add(UNRESTRICTED_DEPARTMENT)
            .add(DRAFT_DEPARTMENT)
            .add(NOTE);
}
