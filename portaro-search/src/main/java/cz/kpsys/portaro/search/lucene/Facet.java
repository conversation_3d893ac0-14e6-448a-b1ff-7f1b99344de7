package cz.kpsys.portaro.search.lucene;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledId;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class Facet extends LabeledId<String> {

    @NonNull ScalarDatatype facetDatatype;
    @NonNull List<FacetKey> facetKeys;

    public Facet(@NonNull String id, @NonNull ScalarDatatype facetDatatype, @NonNull List<FacetKey> facetKeys) {
        super(id, Texts.ofMessageCodedOrNativeOrEmptyNative(String.format("hledani.facet.%s", id), id));
        this.facetDatatype = facetDatatype;
        this.facetKeys = facetKeys;
    }

    @Override
    public String toString() {
        return "Facet:[id=" + getId() + " facetDatatype=" + facetDatatype + "]";
    }
}
