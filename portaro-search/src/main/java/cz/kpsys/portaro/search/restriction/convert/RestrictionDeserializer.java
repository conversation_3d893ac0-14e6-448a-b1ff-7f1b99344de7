package cz.kpsys.portaro.search.restriction.convert;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.search.restriction.*;
import cz.kpsys.portaro.search.restriction.matcher.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.List;

import static cz.kpsys.portaro.search.restriction.convert.RestrictionJsonConstants.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RestrictionDeserializer<FIELD> extends StdDeserializer<Restriction<FIELD>> {

    @NonNull Class<FIELD> searchFieldClass;

    public RestrictionDeserializer(@NonNull Class<FIELD> searchFieldClass) {
        super(Restriction.class);
        this.searchFieldClass = searchFieldClass;
    }


    @Override
    public Restriction<FIELD> deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        assertCurrentToken(jp, JsonToken.START_OBJECT);

        Conjunction<FIELD> conjunction = null;
        Disjunction<FIELD> disjunction = null;
        FIELD key = null;
        SearchMatcher matcher = null;
        Not<FIELD> not = null;
        RawRestriction<FIELD> raw = null;
        Term<FIELD> term = null;


        while (jp.nextToken() != JsonToken.END_OBJECT) {
            String name = jp.getCurrentName();
            jp.nextToken();

            switch (name) {
                case AND_FIELD_NAME -> conjunction = new Conjunction<>(deserializeTerms(jp, ctxt));
                case OR_FIELD_NAME -> disjunction = new Disjunction<>(deserializeTerms(jp, ctxt));
                case NOT_FIELD_NAME -> {
                    Restriction<FIELD> negatedRestriction = ctxt.readValue(jp, ctxt.getTypeFactory().constructParametricType(Restriction.class, searchFieldClass));
                    not = new Not<>(negatedRestriction);
                }
                case FIELD_FIELD_NAME -> key = ctxt.readValue(jp, searchFieldClass);
                case RAW_FIELD_NAME -> raw = ctxt.readValue(jp, RawRestriction.class);
                case EQUALS_TERM_FIELD_NAME -> matcher = ctxt.readValue(jp, Eq.class);
                case EQ_WORDS_TERM_FIELD_NAME -> matcher = ctxt.readValue(jp, EqWords.class);
                case STARTS_WITH_WORDS_TERM_FIELD_NAME -> matcher = ctxt.readValue(jp, StartsWithWords.class);
                case BETWEEN_TERM_FIELD_NAME -> matcher = ctxt.readValue(jp, Between.class);
                case IN_TERM_FIELD_NAME -> matcher = ctxt.readValue(jp, In.class);
                case INCLUDES_TERM_FIELD_NAME -> matcher = ctxt.readValue(jp, Includes.class);
                default -> throw new IllegalArgumentException(String.format("Unknown junction mark \"%s\"", name));
            }
        }

        if (key != null) {
            Assert.notNull(matcher, "Key of restriction is defined but matcher field not found");
            term = new Term<>(key, matcher);
        }
        return ObjectUtil.firstNotNullOptional(conjunction, disjunction, not, raw, term)
                .orElseThrow(() -> new IllegalStateException("JSON does not contain any field"));
    }


    /**
     * Vstupem je [{...}, {...}]
     * @param jp
     * @param ctxt
     * @return
     */
    private List<Restriction<? extends FIELD>> deserializeTerms(JsonParser jp, DeserializationContext ctxt) throws IOException {
        return ctxt.readValue(jp, ctxt.getTypeFactory().constructCollectionType(List.class, Restriction.class));
    }


    private void assertCurrentToken(JsonParser jp, JsonToken expected) {
        Assert.isTrue(jp.getCurrentToken() == expected, () -> "Invalid input json token: expected " + expected + ", but is " + jp.getCurrentToken());
    }
}
