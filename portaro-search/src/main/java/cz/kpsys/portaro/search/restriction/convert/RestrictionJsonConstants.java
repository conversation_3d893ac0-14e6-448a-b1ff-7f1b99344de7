package cz.kpsys.portaro.search.restriction.convert;

/**
 * Created by <PERSON> on 19.04.2017.
 */
public class RestrictionJsonConstants {
    public static final String FIELD_FIELD_NAME = "field";
    public static final String AND_FIELD_NAME = "and";
    public static final String OR_FIELD_NAME = "or";
    public static final String NOT_FIELD_NAME = "not";
    public static final String RAW_FIELD_NAME = "raw";
    public static final String EQUALS_TERM_FIELD_NAME = "eq";
    public static final String BETWEEN_TERM_FIELD_NAME = "between";
    public static final String INCLUDES_TERM_FIELD_NAME = "includes";
    public static final String IN_TERM_FIELD_NAME = "in";
    public static final String EQ_WORDS_TERM_FIELD_NAME = "eqWords";
    public static final String STARTS_WITH_WORDS_TERM_FIELD_NAME = "startsWithWords";
}
