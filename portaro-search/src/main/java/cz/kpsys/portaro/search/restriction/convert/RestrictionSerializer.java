package cz.kpsys.portaro.search.restriction.convert;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import cz.kpsys.portaro.search.restriction.*;
import cz.kpsys.portaro.search.restriction.matcher.*;

import java.io.IOException;

import static cz.kpsys.portaro.search.restriction.convert.RestrictionJsonConstants.*;

public class RestrictionSerializer extends StdSerializer<Restriction> {

    public RestrictionSerializer() {
        super(Restriction.class);
    }

    @Override
    public void serialize(Restriction restriction, JsonGenerator gen, SerializerProvider provider) throws IOException {
        switch (restriction) {
            case Junction<?> junction -> serializeJunction(junction, gen);
            case Term<?> term -> serializeTerm(term, gen);
            case Not<?> not -> serializeNot(not, gen);
            case RawRestriction<?> rawRestriction -> serializeRawRestriction(rawRestriction, gen);
            default -> throw new UnsupportedOperationException(String.format("Restriction type %s is not supported", restriction.getClass().getSimpleName()));
        }
    }


    private void serializeJunction(Junction<?> junction, JsonGenerator gen) throws IOException {
        String field = switch (junction) {
            case Conjunction<?> _ -> AND_FIELD_NAME;
            case Disjunction<?> _ -> OR_FIELD_NAME;
            default -> throw new UnsupportedOperationException(String.format("%s is not supported for serializing", junction.getClass().getSimpleName()));
        };

        gen.writeStartObject();
        gen.writeObjectField(field, junction.getItems());
        gen.writeEndObject();
    }


    private void serializeTerm(Term<?> term, JsonGenerator gen) throws IOException {
        Object key = term.field();
        SearchMatcher matcher = term.matcher();

        String field = switch (matcher) {
            case Eq _ -> EQUALS_TERM_FIELD_NAME;
            case EqWords _ -> EQ_WORDS_TERM_FIELD_NAME;
            case StartsWithWords _ -> STARTS_WITH_WORDS_TERM_FIELD_NAME;
            case Between<?> _ -> BETWEEN_TERM_FIELD_NAME;
            case In _ -> IN_TERM_FIELD_NAME;
            case Includes<?> _ -> INCLUDES_TERM_FIELD_NAME;
            default -> throw new UnsupportedOperationException(String.format("%s is not supported for serializing", matcher.getClass().getSimpleName()));
        };

        gen.writeStartObject();
        gen.writeObjectField(FIELD_FIELD_NAME, key);
        gen.writeObjectField(field, matcher);
        gen.writeEndObject();
    }


    private void serializeNot(Not not, JsonGenerator gen) throws IOException {
        gen.writeStartObject();
        gen.writeObjectField(NOT_FIELD_NAME, not.value());
        gen.writeEndObject();
    }


    private void serializeRawRestriction(RawRestriction rawRestriction, JsonGenerator gen) throws IOException {
        gen.writeStartObject();
        gen.writeObjectField(RAW_FIELD_NAME, rawRestriction.value());
        gen.writeEndObject();
    }

}
