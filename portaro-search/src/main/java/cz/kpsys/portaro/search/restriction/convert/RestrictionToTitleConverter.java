package cz.kpsys.portaro.search.restriction.convert;

import cz.kpsys.portaro.commons.convert.ObjectToTextConverter;
import cz.kpsys.portaro.commons.convert.PrefixingSuffixingStringConverter;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.*;
import cz.kpsys.portaro.search.restriction.matcher.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.support.GenericConversionService;

import java.util.Collection;
import java.util.List;
import java.util.Locale;

/**
 * Converts Restriction (Junction, Not, Term or RawRestriction)
 * Created by <PERSON> on 14.03.2017.
 */
public class RestrictionToTitleConverter extends GenericRestrictionConverter<SearchField, String> implements Converter<Restriction<? extends SearchField>, String> {

    private static final String CONJUNCTION_MARK = " AND ";
    private static final String DISJUNCTION_MARK = " OR ";
    private static final String NOT_MARK = "NOT ";

    public RestrictionToTitleConverter(final Translator<Department> translator, final Department currentDepartment, final Locale locale) {
        final Converter<Object, String> objectLocalizer = new Converter<>() {

            private final ObjectToTextConverter objectToTextConverter = new ObjectToTextConverter();

            @Override
            public @NonNull String convert(@NonNull Object source) {
                return objectToTextConverter
                        .convert(source)
                        .localize(translator, currentDepartment, locale);
            }
        };

        final GenericConversionService matcherConversionService = new GenericConversionService();
        matcherConversionService.addConverter(Eq.class, String.class, new EqTermConverter());
        matcherConversionService.addConverter(EqWords.class, String.class, new EqWordsTermConverter());
        matcherConversionService.addConverter(StartsWithWords.class, String.class, new StartsWithWordsTermConverter());
        matcherConversionService.addConverter(Between.class, String.class, new BetweenTermConverter());
        matcherConversionService.addConverter(In.class, String.class, new InTermConverter(objectLocalizer));
        matcherConversionService.addConverter(Includes.class, String.class, new IncludesTermConverter());

        final Converter<Term<SearchField>, String> keyTermConverter = source -> {
            SearchField key = source.field();
            SearchMatcher matcher = source.matcher();
            String convertedKey = objectLocalizer.convert(key);
            String convertedMatcher = matcherConversionService.convert(matcher, String.class);
            return convertedKey + convertedMatcher;
        };

        withJunctionConverter(new JunctionConverter(this));
        withTermConverter(keyTermConverter);
        withNotConverter(new NotConverter(this));
        withRawRestrictionConverter(rawRestriction -> "(" + rawRestriction.value() + ")");
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class JunctionConverter implements Converter<Junction<SearchField>, String> {

        @NonNull Converter<Restriction<? extends SearchField>, String> restrictionConverter;

        @Override
        public String convert(Junction<SearchField> junction) {
            List<Restriction<? extends SearchField>> conjunctionItems = junction.getItems();

            List<String> junctionItems = ListUtil.convert(conjunctionItems, restrictionConverter);

            String junctionMark = junction instanceof Conjunction ? CONJUNCTION_MARK : DISJUNCTION_MARK;
            String res = StringUtil.listToString(junctionItems, junctionMark);
            if (junctionItems.size() > 1) {
                res = "(" + res + ")";
            }
            return res;
        }

    }


    private static class EqTermConverter implements Converter<Eq, String> {
        @Override
        public String convert(Eq matcher) {
            return " = \"" + matcher.value() + "\"";
        }
    }


    private static class EqWordsTermConverter implements Converter<EqWords, String> {
        @Override
        public String convert(EqWords matcher) {
            List<String> split = List.of(matcher.value().split(" "));
            List<String> quotedItems = ListUtil.convert(split, new PrefixingSuffixingStringConverter("\"", "\""));
            return " = " + StringUtil.listToString(quotedItems, ",");
        }
    }


    private static class StartsWithWordsTermConverter implements Converter<StartsWithWords, String> {
        @Override
        public String convert(StartsWithWords matcher) {
            List<String> split = List.of(matcher.value().split(" "));
            List<String> asteriskedItems = ListUtil.convert(split, PrefixingSuffixingStringConverter.suffixing("*"));
            return " = " + StringUtil.listToString(asteriskedItems, ",");
        }
    }


    private static class BetweenTermConverter implements Converter<Between, String> {
        @Override
        public String convert(Between matcher) {
            return " = [" + ObjectUtil.firstNotNull(matcher.from(), "0") + " - " + ObjectUtil.firstNotNull(matcher.to(), "∞") + "]";
        }
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class InTermConverter implements Converter<In, String> {

        @NonNull Converter<Object, String> objectLocalizer;

        @Override
        public String convert(In matcher) {
            Collection<?> items = matcher.value();
            Collection<String> convertedItems = ListUtil.convert(items, objectLocalizer);
            return " = " + StringUtil.listToString(convertedItems, ",");
        }
    }


    private static class IncludesTermConverter implements Converter<Includes, String> {
        @Override
        public String convert(Includes matcher) {
            return " @> " + ObjectUtil.firstNotNull(matcher.value(), "0");
        }
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class NotConverter implements Converter<Not<SearchField>, String> {

        @NonNull Converter<Restriction<? extends SearchField>, String> restrictionConverter;

        @Override
        public String convert(Not<SearchField> not) {
            String negatedRestriction = restrictionConverter.convert(not.value());
            return NOT_MARK + negatedRestriction;
        }
    }


}
