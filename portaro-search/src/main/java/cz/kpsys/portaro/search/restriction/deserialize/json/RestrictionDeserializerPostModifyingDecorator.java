package cz.kpsys.portaro.search.restriction.deserialize.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.search.field.DatatypeBySearchFieldLoader;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.convert.RestrictionDeserializer;
import cz.kpsys.portaro.search.restriction.matcher.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RestrictionDeserializerPostModifyingDecorator extends StdDeserializer<Restriction<SearchField>> {

    @NonNull RestrictionDeserializer<SearchField> delegate;
    @NonNull DatatypableStringConverter datatypableStringConverter;
    @NonNull DatatypeBySearchFieldLoader datatypeBySearchFieldLoader;

    public RestrictionDeserializerPostModifyingDecorator(@NonNull RestrictionDeserializer<SearchField> delegate,
                                                         @NonNull DatatypableStringConverter datatypableStringConverter,
                                                         @NonNull DatatypeBySearchFieldLoader datatypeBySearchFieldLoader) {
        super(Restriction.class);
        this.delegate = delegate;
        this.datatypableStringConverter = datatypableStringConverter;
        this.datatypeBySearchFieldLoader = datatypeBySearchFieldLoader;
    }


    @Override
    public Restriction<SearchField> deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        Restriction<SearchField> deserialized = delegate.deserialize(jp, ctxt);
        if (!(deserialized instanceof Term<SearchField> originalTerm)) {
            return deserialized;
        }

        SearchField searchField = originalTerm.field();
        SearchMatcher originalMatcher = originalTerm.matcher();

        Optional<ScalarDatatype> maybeSearchFieldDatatype = datatypeBySearchFieldLoader.getOptionalBySearchFieldAndMatcher(searchField, originalMatcher);
        if (maybeSearchFieldDatatype.isEmpty()) {
            return originalTerm;
        }
        ScalarDatatype searchFieldDatatype = maybeSearchFieldDatatype.get();

        SearchMatcher convertedMatcher = convertMatcher(originalMatcher, searchFieldDatatype);

        return originalTerm.withMatcher(convertedMatcher);
    }

    private @NonNull SearchMatcher convertMatcher(@NonNull SearchMatcher matcher, ScalarDatatype searchFieldDatatype) {
        return switch (matcher) {
            case Eq eq -> {
                Object originalValue = eq.value();
                Object convertedValue = datatypableStringConverter.convertFromSimpleTypePreservingStructure(originalValue, searchFieldDatatype);
                yield new Eq(convertedValue);
            }
            case Between<?> between -> {
                Object originalFrom = between.from();
                Object originalTo = between.to();
                Object convertedFrom = datatypableStringConverter.convertFromSimpleTypePreservingStructure(originalFrom, searchFieldDatatype);
                Object convertedTo = datatypableStringConverter.convertFromSimpleTypePreservingStructure(originalTo, searchFieldDatatype);
                yield between.ofConverted(convertedFrom, convertedTo);
            }
            case In in -> {
                Collection<?> originalValue = in.value();
                Collection<?> convertedValue = (List<?>) datatypableStringConverter.convertFromSimpleTypePreservingStructure(originalValue, searchFieldDatatype);
                yield new In(convertedValue);
            }
            case Includes<?> includes -> {
                Object originalValue = includes.value();
                Object convertedValue = datatypableStringConverter.convertFromSimpleTypePreservingStructure(originalValue, searchFieldDatatype);
                yield new Includes<>(convertedValue);
            }
            default -> matcher;
        };
    }

}
