package cz.kpsys.portaro.search.restriction.serialize.lucene;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.search.field.DatatypeBySearchFieldLoader;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.matcher.Includes;
import cz.kpsys.portaro.search.restriction.matcher.SearchMatcher;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DatatypeBySearchFieldLoaderByLuceneQueryFieldLoader implements DatatypeBySearchFieldLoader {

    /// Deprecated - use version with matcher,
    /// For example - SearchField with DATETIME_RANGE datatype can have
    @Deprecated
    @Override
    public Optional<ScalarDatatype> getOptionalBySearchField(SearchField searchField) {
        return Optional.of(searchField.indexedValueDatatype());
    }

    @Override
    public Optional<ScalarDatatype> getOptionalBySearchFieldAndMatcher(SearchField searchField, SearchMatcher matcher) {
        if (matcher instanceof Includes<?>) { // TODO: move this to some configurable logic
            if (searchField.indexedValueDatatype().equals(CoreConstants.Datatype.DATETIME_RANGE)) {
                return Optional.of(CoreConstants.Datatype.DATETIME);
            }
        }
        return Optional.of(searchField.indexedValueDatatype());
    }

}
