package cz.kpsys.portaro.search.restriction.serialize.lucene;

import cz.kpsys.portaro.commons.object.repo.ByIdOptLoadable;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.search.field.CustomSearchField;
import cz.kpsys.portaro.search.field.QueryFieldsBySearchFieldLoader;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.matcher.SearchMatcher;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DispatchingLuceneQueryFieldLoader implements QueryFieldsBySearchFieldLoader<LuceneQueryField> {

    @NonNull Map<String, List<LuceneQueryField>> staticSearchFields;
    @NonNull ByIdOptLoadable<CustomSearchField, String> customSearchFieldLoader;
    @Setter @NonFinal DynamicFieldToLuceneSearchFieldFactory dynamicFieldToLuceneSearchFieldFactory = DynamicFieldToLuceneSearchFieldFactory.UNSUPPORTING;

    @NonNull
    @Override
    public List<LuceneQueryField> getAllBySearchField(@NonNull SearchField searchField, @NonNull SearchMatcher matcher) {
        return expandAndCollectSearchFields(searchField)
                .map(expandedSearchFieldName -> getLuceneFieldsByExpandedSearchKey(expandedSearchFieldName, searchField.indexedValueDatatype()))
                .flatMap(Collection::stream)
                .toList();
    }

    public List<LuceneQueryField> getLuceneFieldsByExpandedSearchKey(@NonNull String searchFieldName, ScalarDatatype customSearchFieldDatatype) {
        if (staticSearchFields.containsKey(searchFieldName)) {
            return staticSearchFields.get(searchFieldName);
        }

        if (dynamicFieldToLuceneSearchFieldFactory.supports(searchFieldName)) {
            return List.of(dynamicFieldToLuceneSearchFieldFactory.getLuceneSearchFieldByExpandedSearchField(searchFieldName));
        }

        // obecny search field -> predavame tak jak je do lucenu
        String luceneSearchFieldName = searchFieldName;
        return List.of(LuceneQueryField.create(luceneSearchFieldName, customSearchFieldDatatype));
    }

    /**
     * Recursive method for collecting all possible lucene search fields
     */
    private Stream<String> expandAndCollectSearchFields(SearchField searchField) {
        String searchFieldName = searchField.id();
        if (searchField instanceof CustomSearchField customSearchField && (customSearchField.luceneKeys().size() != 1 || !customSearchField.luceneKeys().getFirst().equals(searchFieldName))) {
            return customSearchField.luceneKeys().stream()
                    .flatMap(nestedSearchFieldNameOrFinalLuceneField -> {
                        Optional<CustomSearchField> nestedSearchField = customSearchFieldLoader.findById(nestedSearchFieldNameOrFinalLuceneField);
                        if (nestedSearchField.isEmpty()) {
                            return Stream.of(nestedSearchFieldNameOrFinalLuceneField);
                        }
                        return expandAndCollectSearchFields(nestedSearchField.get());
                    });
        }
        return Stream.of(searchFieldName);
    }

}
