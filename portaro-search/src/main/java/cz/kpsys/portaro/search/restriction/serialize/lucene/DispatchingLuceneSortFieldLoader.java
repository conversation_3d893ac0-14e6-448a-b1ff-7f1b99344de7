package cz.kpsys.portaro.search.restriction.serialize.lucene;

import cz.kpsys.portaro.search.sorting.SortFieldsBySearchFieldLoader;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class DispatchingLuceneSortFieldLoader implements SortFieldsBySearchFieldLoader<String> {

    @Setter @NonFinal DynamicFieldToLuceneSortFieldFactory dynamicFieldToLuceneSearchFieldFactory = DynamicFieldToLuceneSortFieldFactory.UNSUPPORTING;

    @Override
    public @NonNull String convertToSearchFieldName(@NonNull SortingItem sortingItem) {
        if (dynamicFieldToLuceneSearchFieldFactory.supports(sortingItem)) {
            return dynamicFieldToLuceneSearchFieldFactory.getLuceneSearchFieldByExpandedSearchField(sortingItem);
        }

        // obecny search field -> predavame tak jak je do lucenu
        return sortingItem.field();
    }

}
