package cz.kpsys.portaro.search.restriction.serialize.lucene;

import lombok.NonNull;

public interface DynamicFieldToLuceneSearchFieldFactory {

    DynamicFieldToLuceneSearchFieldFactory UNSUPPORTING = new UnsupportedDynamicFieldToLuceneSearchFieldFactory();

    boolean supports(@NonNull String searchFieldName);

    LuceneQueryField getLuceneSearchFieldByExpandedSearchField(@NonNull String searchFieldName);

    class UnsupportedDynamicFieldToLuceneSearchFieldFactory implements DynamicFieldToLuceneSearchFieldFactory {

        @Override
        public boolean supports(@NonNull String searchFieldName) {
            return false;
        }

        @Override
        public LuceneQueryField getLuceneSearchFieldByExpandedSearchField(@NonNull String searchFieldName) {
            throw new IllegalStateException();
        }
    }
}

