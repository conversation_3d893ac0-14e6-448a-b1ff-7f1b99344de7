package cz.kpsys.portaro.search.restriction.serialize.lucene;

import cz.kpsys.portaro.sorting.SortingItem;
import lombok.NonNull;

public interface DynamicFieldToLuceneSortFieldFactory {

    DynamicFieldToLuceneSortFieldFactory UNSUPPORTING = new UnsupportedDynamicFieldToLuceneSearchFieldFactory();

    boolean supports(@NonNull SortingItem sortingItem);

    String getLuceneSearchFieldByExpandedSearchField(@NonNull SortingItem sortingItem);

    class UnsupportedDynamicFieldToLuceneSearchFieldFactory implements DynamicFieldToLuceneSortFieldFactory {

        @Override
        public boolean supports(@NonNull SortingItem sortingItem) {
            return false;
        }

        @Override
        public String getLuceneSearchFieldByExpandedSearchField(@NonNull SortingItem sortingItem) {
            throw new IllegalStateException();
        }
    }
}

