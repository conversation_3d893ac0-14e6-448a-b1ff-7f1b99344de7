package cz.kpsys.portaro.search.restriction.serialize.lucene;

import cz.kpsys.portaro.commons.CommonsConstants;
import cz.kpsys.portaro.commons.date.InstantToStringConverter;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.time.Instant;

public class InstantToLuceneValueConverter implements Converter<@NonNull Instant, @NonNull String> {

    @NonNull InstantToStringConverter conv = new InstantToStringConverter(CommonsConstants.UTC_ZONE_PROVIDER, LuceneConstants.INSTANT_FORMAT);

    @Override
    public @NonNull String convert(@NonNull Instant source) {
        return "\"" + conv.convert(source) + "\"";
    }

}