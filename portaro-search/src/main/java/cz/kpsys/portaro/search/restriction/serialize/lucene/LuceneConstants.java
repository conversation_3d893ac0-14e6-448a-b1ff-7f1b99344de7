package cz.kpsys.portaro.search.restriction.serialize.lucene;

import java.time.format.DateTimeFormatter;

public class LuceneConstants {

    public static final DateTimeFormatter LOCAL_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter INSTANT_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSSSSS");
    public static final String FIELD_SEARCH_PREFIX = "P";
    public static final String LUCENE_FIELDTYPED_SEARCH_FIELD_FIELDTYPE_DELIMITER = "_";
    public static final String LUCENE_FIELDTYPED_SEARCH_FIELD_VALUE_SUFFIX = "_val";
    public static final String LUCENE_FIELDTYPED_SEARCH_FIELD_VALUE_LO_SUFFIX = "_lo";
    public static final String LUCENE_FIELDTYPED_SEARCH_FIELD_VALUE_HI_SUFFIX = "_hi";
    public static final String LUCENE_FIELDTYPED_SEARCH_FIELD_LINK_SUFFIX = "_link";
    public static final String LUCENE_FIELDTYPED_SEARCH_FIELD_ORIGIN_SUFFIX = "_origin";
    public static final String LUCENE_FIELDTYPED_SEARCH_FIELD_SORT_SUFFIX = "_sort";
}
