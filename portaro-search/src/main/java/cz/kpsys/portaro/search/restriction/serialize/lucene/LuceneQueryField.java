package cz.kpsys.portaro.search.restriction.serialize.lucene;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import lombok.NonNull;
import lombok.With;

public record LuceneQueryField(

        @With
        @NonNull
        String name,

        /// Deprecated - datatype should be fetched from generic search field, not from concrete (lucene) search field
        @Deprecated
        @NonNull
        ScalarDatatype datatype

) {

    public static LuceneQueryField create(@NonNull String value, @NonNull ScalarDatatype datatype) {
        return new Lucene<PERSON><PERSON>yField(value, datatype);
    }

    public static LuceneQueryField testingText(@NonNull String value) {
        return new LuceneQueryField(value, CoreConstants.Datatype.TEXT);
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public boolean equals(Object o) {
        return o instanceof LuceneQueryField that && name().equals(that.name());
    }

    @Override
    public int hashCode() {
        return name().hashCode();
    }
}