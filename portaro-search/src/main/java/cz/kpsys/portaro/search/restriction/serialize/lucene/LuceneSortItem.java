package cz.kpsys.portaro.search.restriction.serialize.lucene;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.NonNull;
import lombok.With;

import java.util.function.Function;

public record LuceneSortItem(

        @JsonIgnore
        @With
        @NonNull
        String field,

        @JsonIgnore
        @With
        boolean asc,

        @NonNull
        Text text

) implements SortingItem {

    public static LuceneSortItem of(@NonNull String field, boolean asc) {
        return new LuceneSortItem(field, asc, SortingItem.createDefaultText(field, asc));
    }

    public static LuceneSortItem ofAsc(@NonNull String field) {
        return of(field, true);
    }

    public static LuceneSortItem ofDesc(@NonNull String field) {
        return of(field, false);
    }

    @Override
    public LuceneSortItem reverse() {
        return withAsc(!asc);
    }

    @Override
    public LuceneSortItem mapField(Function<String, String> fieldNameConverter) {
        return withField(fieldNameConverter.apply(field()));
    }

    @Override
    public boolean equals(Object o) {
        return o instanceof SortingItem that && asc == that.asc() && field.equals(that.field());
    }

    @Override
    public int hashCode() {
        int result = field.hashCode();
        result = 31 * result + Boolean.hashCode(asc);
        return result;
    }

    @Override
    public @NonNull String toString() {
        return "LuceneSortItem " + id();
    }
}