package cz.kpsys.portaro.search.restriction.serialize.lucene;

import cz.kpsys.portaro.commons.convert.FlattingStringConverter;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.search.field.QueryFieldsBySearchFieldLoader;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.lucene.KeywordParseRequest;
import cz.kpsys.portaro.search.lucene.KeywordsParser;
import cz.kpsys.portaro.search.restriction.*;
import cz.kpsys.portaro.search.restriction.convert.*;
import cz.kpsys.portaro.search.restriction.matcher.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * Converts Restriction (Junction, Not, Term or RawRestriction)
 * Created by Jan Pachol on 14.03.2017.
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RestrictionToLuceneQueryConverterBuilder {

    @NonNull KeywordsParser keywordsParser;
    @NonNull QueryFieldsBySearchFieldLoader<LuceneQueryField> luceneQueryFieldLoader;
    @NonNull RestrictionToLuceneQueryConverterBuilder.EqLuceneTermConverter eqTermConverter = new EqLuceneTermConverter();
    @NonNull RestrictionToLuceneQueryConverterBuilder.BetweenLuceneTermConverter betweenTermConverter = new BetweenLuceneTermConverter();

    @NonNull MatchingCompositeTermConverter<SearchField, Restriction<? extends SearchField>> recursiveSearchFieldTermConverter = new MatchingCompositeTermConverter<>();
    @NonNull MatchingCompositeTermConverter<SearchField, Restriction<? extends LuceneQueryField>> nonRecursiveSearchFieldTermConverter = new MatchingCompositeTermConverter<>();
    @NonNull @NonFinal Consumer<MatchingCompositeTermConverter<SearchField, Restriction<? extends SearchField>>> recursiveSearchFieldConverterModifier = _ -> {};
    @NonNull @NonFinal Consumer<MatchingCompositeTermConverter<SearchField, Restriction<? extends LuceneQueryField>>> nonRecursiveSearchFieldConverterModifier = _ -> {};


    public <S, T extends String> RestrictionToLuceneQueryConverterBuilder registerEqValueConverter(Class<S> sourceType, Converter<? super S, ? extends T> converter) {
        eqTermConverter.registerEqValueConverter(sourceType, converter);
        return this;
    }


    public <S, T extends String> RestrictionToLuceneQueryConverterBuilder registerBetweenValueItemConverter(Class<S> sourceType, Converter<? super S, ? extends T> converter) {
        betweenTermConverter.registerBetweenValueItemConverter(sourceType, converter);
        return this;
    }


    public <S, T extends String> RestrictionToLuceneQueryConverterBuilder configureRecursiveSearchFieldTermConverter(Consumer<MatchingCompositeTermConverter<SearchField, Restriction<? extends SearchField>>> modifier) {
        this.recursiveSearchFieldConverterModifier = modifier;
        return this;
    }


    public <S, T extends String> RestrictionToLuceneQueryConverterBuilder configureNonRecursiveSearchFieldTermConverter(Consumer<MatchingCompositeTermConverter<SearchField, Restriction<? extends LuceneQueryField>>> modifier) {
        this.nonRecursiveSearchFieldConverterModifier = modifier;
        return this;
    }


    public Converter<Restriction<? extends SearchField>, String> build() {
        // search-fielded restriction -> lucene query string
        return buildSearchFieldRestrictionToLuceneFieldRestriction()
                .andThen(buildLuceneFieldRestrictionToQuery());
    }


    public Converter<Restriction<? extends SearchField>, Restriction<? extends LuceneQueryField>> buildSearchFieldRestrictionToLuceneFieldRestriction() {
        // search-fielded term -> lucene-fielded restriction
        SearchFieldTermToLuceneQueryFieldTermsDisjunctionConverter searchFieldTermToLuceneQueryFieldTermsConverter = new SearchFieldTermToLuceneQueryFieldTermsDisjunctionConverter(luceneQueryFieldLoader);

        recursiveSearchFieldConverterModifier.accept(recursiveSearchFieldTermConverter);
        nonRecursiveSearchFieldConverterModifier.accept(nonRecursiveSearchFieldTermConverter);


        // search-fielded restriction -> lucene-fielded restriction
        GenericRestrictionConverter<SearchField, Restriction<? extends LuceneQueryField>> searchFieldRestrictionToLuceneFieldRestrictionConverter = new GenericRestrictionConverter<>();
        searchFieldRestrictionToLuceneFieldRestrictionConverter
                .withTermConverter(searchFieldTerm -> {
                    if (recursiveSearchFieldTermConverter.canConvert(searchFieldTerm)) {
                        Restriction<? extends SearchField> intermediate = Objects.requireNonNull(recursiveSearchFieldTermConverter.convert(searchFieldTerm));
                        log.debug("First iteration of search field term ({} -> {})", searchFieldTerm, intermediate);
                        return searchFieldRestrictionToLuceneFieldRestrictionConverter.convert(intermediate);
                    }
                    if (nonRecursiveSearchFieldTermConverter.canConvert(searchFieldTerm)) {
                        return nonRecursiveSearchFieldTermConverter.convert(searchFieldTerm);
                    }
                    return searchFieldTermToLuceneQueryFieldTermsConverter.convert(searchFieldTerm);
                })
                .withJunctionConverter(new TypeConvertingJunctionConverter<>(searchFieldRestrictionToLuceneFieldRestrictionConverter))
                .withNotConverter(new TypeConvertingNotConverter<>(searchFieldRestrictionToLuceneFieldRestrictionConverter))
                .withRawRestrictionConverter(new LuceneRawRestrictionConverter())
                .withFalseRestrictionConverter(new TypeConvertingFalseRestrictionConverter<>());

        return searchFieldRestrictionToLuceneFieldRestrictionConverter;
    }


    public Converter<Restriction<? extends LuceneQueryField>, String> buildLuceneFieldRestrictionToQuery() {
        GenericRestrictionConverter<LuceneQueryField, String> luceneFieldRestrictionToQueryConverter = new GenericRestrictionConverter<>();

        LuceneFieldJunctionConverter junctionConverter = new LuceneFieldJunctionConverter(luceneFieldRestrictionToQueryConverter);

        // lucene-fielded restriction -> lucene query string
        MatchingCompositeTermConverter<LuceneQueryField, String> luceneTermConverter = new MatchingCompositeTermConverter<LuceneQueryField, String>()
                .addLastByMatcherClass(Eq.class, eqTermConverter)
                .addLastByMatcherClass(EqWords.class, new EqWordsLuceneTermConverter(keywordsParser, new FlattingStringConverter()))
                .addLastByMatcherClass(StartsWithWords.class, new StartsWithWordsLuceneTermConverter(keywordsParser, new FlattingStringConverter()))
                .addLastByMatcherClass(Between.class, betweenTermConverter)
                .addLastByMatcherClass(Includes.class, new IncludesLuceneTermConverter(junctionConverter))
                .addLastByMatcherClass(IsNull.class, new LuceneFieldIsNullRestrictionConverter());

        LuceneFieldNotConverter notConverter = new LuceneFieldNotConverter(luceneFieldRestrictionToQueryConverter);

        LuceneFieldRawRestrictionConverter rawRestrictionConverter = new LuceneFieldRawRestrictionConverter();

        LuceneFieldFalseRestrictionConverter falseRestrictionConverter = new LuceneFieldFalseRestrictionConverter();

        luceneFieldRestrictionToQueryConverter
                .withTermConverter(luceneTermConverter)
                .withJunctionConverter(junctionConverter)
                .withNotConverter(notConverter)
                .withRawRestrictionConverter(rawRestrictionConverter)
                .withFalseRestrictionConverter(falseRestrictionConverter);
        return luceneFieldRestrictionToQueryConverter;
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    private static class LuceneFieldJunctionConverter extends AbstractJunctionConverter<LuceneQueryField, String> {

        public LuceneFieldJunctionConverter(@NonNull Converter<Restriction<? extends LuceneQueryField>, String> restrictionConverter) {
            super(restrictionConverter);
        }

        protected String convertConjunction(List<String> convertedJunctionItems) {
            return LuceneQueryPartGenerator.createConjunction(convertedJunctionItems);
        }

        protected String convertDisjunction(List<String> convertedJunctionItems) {
            return LuceneQueryPartGenerator.createDisjunction(convertedJunctionItems);
        }
    }

    /**
     * Converts search field and matcher (authorityName="Babicka Nemcova") to string.
     * Spreads it by keys to disjunction P100="Babicka Nemcova" OR P110="Babicka Nemcova" OR P111="Babicka Nemcova"
     */
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class SearchFieldTermToLuceneQueryFieldTermsDisjunctionConverter implements Converter<Term<SearchField>, Restriction<? extends LuceneQueryField>> {

        @NonNull QueryFieldsBySearchFieldLoader<LuceneQueryField> luceneQueryFieldLoader;

        @Override
        public Restriction<LuceneQueryField> convert(@NonNull Term<SearchField> term) {
            List<LuceneQueryField> luceneQueryFields = luceneQueryFieldLoader.getAllBySearchField(term.field(), term.matcher());
            Disjunction<?> expandedDisjunction = luceneQueryFields.stream()
                    .map(term::withField)
                    .collect(Disjunction.restrictionsToDisjunctionCollector());
            return (Restriction<LuceneQueryField>) expandedDisjunction;
        }

    }


    /**
     * Converts single key term (P100="Babicka Nemcova")
     */
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class EqLuceneTermConverter implements Converter<Term<LuceneQueryField>, String> {

        @NonNull LuceneValueConverter matcherValueConversionService = new LuceneValueConverter();

        public <S, T extends String> EqLuceneTermConverter registerEqValueConverter(Class<S> sourceType, Converter<? super S, ? extends T> converter) {
            matcherValueConversionService.registerConverter(sourceType, converter);
            return this;
        }

        @Override
        public String convert(Term<LuceneQueryField> source) {
            final String luceneQueryFieldName = source.field().name();
            final ScalarDatatype luceneQueryFieldDatatype = source.field().datatype();
            final Object matcherValue = ((Eq) source.matcher()).value();

            //try to convert to lucene value (e.g., fond id 1 converts to lucene-format "01")
            Object luceneCompatibleValue = matcherValueConversionService.toLuceneValueOrOriginal(matcherValue);

            return LuceneQueryPartGenerator.createEqTerm(luceneQueryFieldName, luceneCompatibleValue);
        }

    }


    /**
     * Converts single key term (P100="Babicka Nemcova")
     */
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class EqWordsLuceneTermConverter implements Converter<Term<LuceneQueryField>, String> {

        @NonNull KeywordsParser keywordsParser;
        @NonNull Converter<String, String> wordConverter;

        @Override
        public String convert(Term<LuceneQueryField> source) {
            final String luceneQueryFieldName = source.field().name();
            final ScalarDatatype luceneQueryFieldDatatype = source.field().datatype();
            final String matcherValue = ((EqWords) source.matcher()).value();

            List<String> words = keywordsParser.parse(new KeywordParseRequest(luceneQueryFieldDatatype, luceneQueryFieldName, matcherValue));

            words = ListUtil.convert(words, wordConverter);

            return LuceneQueryPartGenerator.createConjunction(ListUtil.convert(words, word -> LuceneQueryPartGenerator.createEqTerm(luceneQueryFieldName, word)));
        }

    }


    /// Converts single key term (P100="Babicka Nemcova")
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class StartsWithWordsLuceneTermConverter implements Converter<Term<LuceneQueryField>, String> {

        @NonNull KeywordsParser keywordsParser;
        @NonNull Converter<String, String> wordConverter;

        @Override
        public String convert(Term<LuceneQueryField> source) {
            final String luceneQueryFieldName = source.field().name();
            final ScalarDatatype luceneQueryFieldDatatype = source.field().datatype();
            final String matcherValue = ((StartsWithWords) source.matcher()).value();

            List<String> words = keywordsParser.parse(new KeywordParseRequest(luceneQueryFieldDatatype, luceneQueryFieldName, matcherValue));

            words = ListUtil.convert(words, wordConverter);

            return LuceneQueryPartGenerator.createConjunction(ListUtil.convert(words, word -> LuceneQueryPartGenerator.createStartsWithTerm(luceneQueryFieldName, word)));
        }

    }



    /// Converts single key term (REZS_ROK between 2013,2015) to `REZS_ROK:[2013 TO 2015]`
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class BetweenLuceneTermConverter implements Converter<Term<LuceneQueryField>, String> {

        @NonNull LuceneValueConverter matcherValueConversionService = new LuceneValueConverter();

        public <S, T extends String> BetweenLuceneTermConverter registerBetweenValueItemConverter(Class<S> sourceType, Converter<? super S, ? extends T> converter) {
            matcherValueConversionService.registerConverter(sourceType, converter);
            return this;
        }

        @Override
        public String convert(Term<LuceneQueryField> source) {
            Between<?> matcher = (Between<?>) source.matcher();
            Object from = ObjectUtil.elvis(matcher.from(), matcherValueConversionService::toLuceneValueOrOriginal);
            Object to = ObjectUtil.elvis(matcher.to(), matcherValueConversionService::toLuceneValueOrOriginal);
            return LuceneQueryPartGenerator.createBetweenTerm(source.field().name(), from, true, to, !matcher.toIsExclusive());
        }
    }


    /// Converts single key term (Pd2050_a_val covers 2013) to `(Pd2050_a_val_lo:[* TO 2013] AND Pd2050_a_val_hi:[2013 TO *])`
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class IncludesLuceneTermConverter implements Converter<Term<LuceneQueryField>, String> {

        @NonNull LuceneFieldJunctionConverter junctionConverter;

        @Override
        public String convert(Term<LuceneQueryField> source) {
            LuceneQueryField field = source.field();
            LuceneQueryField loSuffixField = field.withName(field.name() + LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_VALUE_LO_SUFFIX);
            LuceneQueryField hiSuffixField = field.withName(field.name() + LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_VALUE_HI_SUFFIX);
            Includes<?> includes = (Includes<?>) source.matcher();
            return junctionConverter.convert(new Conjunction<>(
                    source.withField(loSuffixField).withMatcher(Between.ofClosed(null, includes.value())),
                    source.withField(hiSuffixField).withMatcher(Between.ofClosed(includes.value(), null))
            ));
        }
    }


    /// Converts NOT [any restriction]
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    public static class LuceneFieldNotConverter extends AbstractNotConverter<LuceneQueryField, String> {

        public LuceneFieldNotConverter(@NonNull Converter<Restriction<? extends LuceneQueryField>, String> restrictionConverter) {
            super(restrictionConverter);
        }

        protected String convertNot(String negatedValue) {
            return LuceneQueryPartGenerator.createNot(negatedValue);
        }
    }


    /**
     * Converts ([raw restriction])
     */
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class LuceneFieldRawRestrictionConverter implements Converter<RawRestriction<LuceneQueryField>, String> {

        @Override
        public String convert(RawRestriction<LuceneQueryField> raw) {
            return LuceneQueryPartGenerator.createRaw(raw.value());
        }
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class LuceneFieldFalseRestrictionConverter implements Converter<FalseRestriction<LuceneQueryField>, String> {

        @Override
        public String convert(@NonNull FalseRestriction<LuceneQueryField> falseRestriction) {
            return LuceneQueryPartGenerator.createFalseTerm();
        }
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class LuceneFieldIsNullRestrictionConverter implements Converter<Term<LuceneQueryField>, String> {

        @Override
        public String convert(Term<LuceneQueryField> source) {
            Assert.isInstanceOf(IsNull.class, source.matcher(), () -> String.format("Matcher is not instance of IsNull (term %s)", source));
            return LuceneQueryPartGenerator.createMissingField(source.field().name());
        }

    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class LuceneRawRestrictionConverter implements Converter<RawRestriction<SearchField>, Restriction<? extends LuceneQueryField>> {

        @Override
        public Restriction<? extends LuceneQueryField> convert(RawRestriction<SearchField> source) {
            return new RawRestriction<>(source.value());
        }
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class InMatcherTermToDisjunctionIntermediateConverter implements Converter<Term<SearchField>, Restriction<? extends SearchField>> {

        @NonNull Converter<Term<SearchField>, Disjunction<SearchField>> inTermToDisjunctionConverter = new InTermToDisjunctionConverter<>();

        @Override
        public Restriction<? extends SearchField> convert(Term<SearchField> source) {
            Assert.isInstanceOf(In.class, source.matcher(), () -> String.format("Matcher is not instance of In (term %s)", source));
            if (((In) source.matcher()).value().isEmpty()) {
                return FalseRestriction.create();
            }
            return inTermToDisjunctionConverter.convert(source);
        }

    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class LtMatcherToBetweenIntermediateConverter implements Converter<Term<SearchField>, Restriction<? extends SearchField>> {

        @Override
        public Restriction<? extends SearchField> convert(Term<SearchField> source) {
            if (source.matcher() instanceof Lt lt) {
                return source.withMatcher(Between.ofExclusiveTo(null, lt.value()));
            }
            throw new UnsupportedOperationException("Matcher is not instance of " + Lt.class.getSimpleName() + " (term " + source + ")");
        }

    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class LtEqMatcherToBetweenIntermediateConverter implements Converter<Term<SearchField>, Restriction<? extends SearchField>> {

        @Override
        public Restriction<? extends SearchField> convert(Term<SearchField> source) {
            if (source.matcher() instanceof LtEq lteq) {
                return source.withMatcher(Between.ofClosed(null, lteq.value()));
            }
            throw new UnsupportedOperationException("Matcher is not instance of " + LtEq.class.getSimpleName() + " (term " + source + ")");
        }

    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class GtEqMatcherToBetweenIntermediateConverter implements Converter<Term<SearchField>, Restriction<? extends SearchField>> {

        @Override
        public Restriction<? extends SearchField> convert(Term<SearchField> source) {
            if (source.matcher() instanceof GtEq gt) {
                return source.withMatcher(Between.ofClosed(gt.value(), null));
            }
            throw new UnsupportedOperationException("Matcher is not instance of " + GtEq.class.getSimpleName() + " (term " + source + ")");
        }

    }


}
