package cz.kpsys.portaro.search.restriction.convert;

import com.fasterxml.jackson.databind.module.SimpleModule;
import cz.kpsys.portaro.search.restriction.*;
import cz.kpsys.portaro.search.restriction.matcher.*;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
public class RestrictionDeserializerTest extends RestrictionDeserializerTestBase {

    @Override
    protected void configureModule(SimpleModule module) {
        module.addDeserializer(Restriction.class, new RestrictionDeserializer<>(Integer.TYPE));
    }

    @Test
    public void deserializeEmptyConjunction() throws Exception {
        Junction<?> deserialized = (Junction<?>) deserialize("""
                {
                    "and": []
                }
                """);

        assertEquals(0, deserialized.getItems().size());
    }


    @Test
    public void deserializeConjunctionWithOneEqMatcher() throws Exception {
        Junction<?> deserialized = (Junction<?>) deserialize("""
                {
                    "and": [
                        {
                            "field": 1,
                            "eq": {"value": "abc"}
                        }
                    ]
                }
                """);

        assertEquals(1, deserialized.getItems().size());
        assertEquals(1, ((Term<?>) deserialized.getItems().get(0)).field());
        assertEquals("abc", ((Eq) ((Term<?>) deserialized.getItems().get(0)).matcher()).value());
    }


    @Test
    public void deserializeConjunctionWithOneEqMatcherNegated() throws Exception {
        Junction<?> deserialized = (Junction<?>) deserialize("""
                {
                    "and": [
                        {
                            "not": {
                                "field": 1,
                                "eq": {"value": "abc"}
                            }
                        }
                    ]
                }
                """);

        assertEquals(1, deserialized.getItems().size());
        assertEquals(1, ((Term<?>) ((Not<?>) deserialized.getItems().get(0)).value()).field());
        assertEquals("abc", ((Eq) ((Term<?>) ((Not<?>) deserialized.getItems().get(0)).value()).matcher()).value());
    }


    @Test
    public void deserializeConjunctionWithAllSupportedMatchers() throws Exception {
        Junction<?> deserialized = (Junction<?>) deserialize("""
                {
                    "and": [
                        {
                            "field": 1,
                            "eq": {"value": "cde"}
                        },
                        {
                            "field": 1,
                            "startsWithWords": {"value": "abc def"}
                        },
                        {
                            "field": 1,
                            "between": {"from": 2013, "to": 2015}
                        },
                        {
                            "field": 1,
                            "in": {"value": [1,2,3]}
                        },
                        {
                            "field": 1,
                            "eqWords": {"value": "abc def"}
                        },
                        {
                            "field": 1,
                            "includes": {"value": "abc def"}
                        }
                    ]
                }
                """);

        assertEquals(6, deserialized.getItems().size());
        assertEquals("cde", ((Eq) ((Term<?>) deserialized.getItems().get(0)).matcher()).value());
        assertEquals("abc def", ((StartsWithWords) ((Term<?>) deserialized.getItems().get(1)).matcher()).value());
        assertEquals(2013, ((Between<Integer>) ((Term<?>) deserialized.getItems().get(2)).matcher()).from());
        assertEquals(2015, ((Between<Integer>) ((Term<?>) deserialized.getItems().get(2)).matcher()).to());
        assertArrayEquals(new Object[] {1, 2, 3}, ((In) ((Term<?>) deserialized.getItems().get(3)).matcher()).value().toArray());
        assertEquals("abc def", ((EqWords) ((Term<?>) deserialized.getItems().get(4)).matcher()).value());
        assertEquals("abc def", ((Includes<?>) ((Term<?>) deserialized.getItems().get(5)).matcher()).value());
    }


    @Test
    public void deserializeConjunctionOfDisjunctionOfConjunction() throws Exception {
        Junction<?> deserialized = (Junction<?>) deserialize("""
                {
                    "and": [{
                        "or": [{
                            "and": [{
                                "field": 1,
                                "eq": {"value": "abc"}
                            }]
                        }]
                    }]
                }
                """);

        assertEquals(1, deserialized.getItems().size());
        assertEquals(1, ((Disjunction<?>) deserialized.getItems().get(0)).getItems().size());
        assertEquals(1, ((Conjunction<?>) ((Disjunction<?>) deserialized.getItems().get(0)).getItems().get(0)).getItems().size());
        assertEquals("abc", ((Eq) ((Term<?>) ((Conjunction<?>) ((Disjunction<?>) deserialized.getItems().get(0)).getItems().get(0)).getItems().get(0)).matcher()).value());
    }

}