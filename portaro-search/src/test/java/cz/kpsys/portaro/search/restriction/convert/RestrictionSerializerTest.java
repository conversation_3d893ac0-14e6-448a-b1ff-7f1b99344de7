package cz.kpsys.portaro.search.restriction.convert;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import cz.kpsys.portaro.search.restriction.*;
import cz.kpsys.portaro.search.restriction.matcher.Between;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.In;
import cz.kpsys.portaro.search.restriction.matcher.Includes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static uk.co.datumedge.hamcrest.json.SameJSONAs.sameJSONAs;

@Tag("ci")
@Tag("unit")
public class RestrictionSerializerTest {

    private ObjectMapper mapper;

    @BeforeEach
    public void setup() {
        mapper = new ObjectMapper();
        mapper.enable(SerializationFeature.INDENT_OUTPUT);
        mapper.registerModule(
                    new SimpleModule("")
                        .addSerializer(Restriction.class, new RestrictionSerializer())
                )
                .enable(SerializationFeature.INDENT_OUTPUT);
    }


    @Test
    public void deserializeConjunctionWithOneEqMatcher() throws Exception {
        Junction<String> junction = new Conjunction<String>()
                .add(new Term<>("PALL", new Eq("Karel")))
                .add(new Term<>("ROK_VYD", Between.ofClosed(2014, 2016)))
                .add(new Disjunction<String>()
                        .add(new Term<>("PALL", new Eq("Karel")))
                        .add(new Term<>("PALL", new Eq("Novak"))))
                .add(new Term<>("BUDOVA", new In(List.of(1, 2, 4, 5, 6))));
        serialize(junction);
    }

    @Test
    public void deserializeConjunctionWithOneCoversMatcher() throws Exception {
        Junction<String> junction = new Conjunction<String>()
                .add(new Term<>("neco", new Includes<>(1)));
        String actual = serialize(junction);
        String expected = """
                {
                    "and" : [
                        {
                            "field" : "neco",
                            "includes" : {
                                "value" : 1
                            }
                        }
                    ]
                }
                """;
        assertThat(actual, sameJSONAs(expected));
    }


    private String serialize(Junction junction) throws IOException {
        System.out.println("Serializing");
        System.out.println(junction);
        String serialized = mapper.writeValueAsString(junction);
        System.out.println(serialized);
        return serialized;
    }

}