package cz.kpsys.portaro.search.restriction.convert;

import cz.kpsys.portaro.commons.localization.TestingTranslator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.field.BasicSearchField;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.*;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.EqWords;
import cz.kpsys.portaro.search.restriction.matcher.Includes;
import cz.kpsys.portaro.search.restriction.matcher.StartsWithWords;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
public class RestrictionToTitleConverterTest {


    private String convert(Restriction<SearchField> restriction) {
        String query = new RestrictionToTitleConverter(new TestingTranslator<>(), Department.testingRoot(), TestingTranslator.USED_LOCALE)
                .convert(restriction);
        System.out.println(query);
        return query;
    }


    @Test
    public void shouldNotGenerateQueryOnEmptyTree() {
        Junction<SearchField> tree = new Conjunction<>();

        String actual = convert(tree);

        assertEquals("", actual);
    }


    @Test
    public void shouldWriteUserFriendlyKeyName() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(BasicSearchField.testingTextual("signature"), new Eq("a")));

        String actual = convert(crit);

        assertEquals("signature = \"a\"", actual);
    }


    @Test
    public void shouldNotGenerateQueryWithParenthessesIfSingleFields() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(BasicSearchField.testingTextual("PA"), new EqWords("a")))
                .add(new Term<>(BasicSearchField.testingTextual("PB"), new EqWords("b")));

        String actual = convert(crit);

        assertEquals("(PA = \"a\" AND PB = \"b\")", actual);
    }


    @Test
    public void shouldSpreadTermsInEqRow() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(BasicSearchField.testingTextual("PA"), new EqWords("a b c")));

        String actual = convert(crit);

        assertEquals("PA = \"a\",\"b\",\"c\"", actual);
    }


    @Test
    public void shouldSpreadTermsInStartsWithWords() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(BasicSearchField.testingTextual("PA"), new StartsWithWords("a b")));

        String actual = convert(crit);

        assertEquals("PA = a*,b*", actual);
    }


    @Test
    public void shouldGenerateQueryWithNestedJunction() {
        Junction crit = new Disjunction<>()
                .add(new Term<>(BasicSearchField.testingTextual("PA"), new EqWords("a")))
                .add(new Conjunction<>()
                        .add(new Term<>(BasicSearchField.testingTextual("PB"), new EqWords("b")))
                        .add(new Term<>(BasicSearchField.testingTextual("PC"), new EqWords("c"))));

        String actual = convert(crit);

        assertEquals("(PA = \"a\" OR (PB = \"b\" AND PC = \"c\"))", actual);
    }

    @Test
    public void shouldGenerateQueryWithRangeCoversPoint() {
        Junction<SearchField> crit = new Disjunction<SearchField>()
                .add(new Term<>(BasicSearchField.testingTextual("PA"), new Includes<>("a")));

        String actual = convert(crit);

        assertEquals("PA @> a", actual);
    }

}