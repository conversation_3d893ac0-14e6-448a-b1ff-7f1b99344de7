package cz.kpsys.portaro.search.restriction.serialize.lucene;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.search.field.BasicSearchField;
import cz.kpsys.portaro.search.field.QueryFieldsBySearchFieldLoader;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.lucene.KeywordsParserSimpleSplittingBySpaceIgnoringDoubleQuotes;
import cz.kpsys.portaro.search.restriction.*;
import cz.kpsys.portaro.search.restriction.matcher.*;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

import static cz.kpsys.portaro.datatype.Datatype.scalar;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
public class RestrictionToLuceneQueryConverterTest {

    private static final SearchField SEARCH_FIELD_TITLE = BasicSearchField.testingTextual("TITLE");
    private static final SearchField SEARCH_FIELD_LANG = BasicSearchField.testingTextual("LANG");
    private static final SearchField SEARCH_FIELD_AUTHOR = BasicSearchField.testingTextual("AUTHOR");
    private static final SearchField SEARCH_FIELD_ISBN = BasicSearchField.testingTextual("ISBN");
    private static final SearchField SEARCH_FIELD_YEAR = BasicSearchField.testingTextual("YEAR");
    private static final SearchField SEARCH_FIELD_FOND = BasicSearchField.testingTextual("FOND");

    private final QueryFieldsBySearchFieldLoader<LuceneQueryField> luceneQueryFieldLoader = (key, matcher) -> {
        if (key == SEARCH_FIELD_TITLE) {
            return List.of(LuceneQueryField.testingText("P245"));
        }
        if (key == SEARCH_FIELD_AUTHOR) {
            return List.of(LuceneQueryField.testingText("P100"), LuceneQueryField.testingText("P110"));
        }
        if (key == SEARCH_FIELD_LANG) {
            return List.of(LuceneQueryField.testingText("P64"));
        }
        if (key == SEARCH_FIELD_ISBN) {
            return List.of(LuceneQueryField.testingText("P22"));
        }
        if (key == SEARCH_FIELD_YEAR) {
            return List.of(LuceneQueryField.create("ROK_VYD", CoreConstants.Datatype.NUMBER));
        }
        if (key == SEARCH_FIELD_FOND) {
            return List.of(LuceneQueryField.create("REZS_FOND", scalar("FOND")));
        }
        throw new IllegalStateException();
    };

    private String convert(Restriction<SearchField> restriction) {
        String query = new RestrictionToLuceneQueryConverterBuilder(new KeywordsParserSimpleSplittingBySpaceIgnoringDoubleQuotes(), luceneQueryFieldLoader)
                .configureRecursiveSearchFieldTermConverter(recursiveTermConverter -> {
                    recursiveTermConverter.addByMatcherClass(In.class, new RestrictionToLuceneQueryConverterBuilder.InMatcherTermToDisjunctionIntermediateConverter());
                    recursiveTermConverter.addByMatcherClass(Lt.class, new RestrictionToLuceneQueryConverterBuilder.LtMatcherToBetweenIntermediateConverter());
                    recursiveTermConverter.addByMatcherClass(LtEq.class, new RestrictionToLuceneQueryConverterBuilder.LtEqMatcherToBetweenIntermediateConverter());
                    recursiveTermConverter.addByMatcherClass(GtEq.class, new RestrictionToLuceneQueryConverterBuilder.GtEqMatcherToBetweenIntermediateConverter());
                })
                .registerEqValueConverter(Integer.class, new IdToLuceneValueConverter())
                .registerEqValueConverter(String.class, new StringToLuceneValueConverter())
                .build()
                .convert(restriction);
        System.out.println(query);
        return query;
    }



    @Test
    public void shouldNotGenerateQueryOnEmptyTree() {
        Junction<SearchField> tree = new Conjunction<>();

        String actual = convert(tree);

        assertEquals("", actual);
    }


    @Test
    public void shouldNotGenerateQueryWithParenthessesIfSingleFields() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_TITLE, new EqWords("a")))
                .add(new Term<>(SEARCH_FIELD_LANG, new EqWords("b")));

        String actual = convert(crit);

        assertEquals("(P245:a AND P64:b)", actual);
    }


    @Test
    public void shouldGenerateQueryWithParenthesses() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_TITLE, new EqWords("a")))
                .add(new Term<>(SEARCH_FIELD_AUTHOR, new EqWords("b")));

        String actual = convert(crit);

        assertEquals("(P245:a AND (P100:b OR P110:b))", actual);
    }


    @Test
    public void shouldSpreadTermsInEqRow() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_TITLE, new EqWords("a b c")));

        String actual = convert(crit);

        assertEquals("(P245:a AND P245:b AND P245:c)", actual);
    }


    @Test
    public void shouldQuoteSingleWordInEqWithStringConverter() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_TITLE, new Eq("a")));

        String actual = convert(crit);

        assertEquals("P245:\"a\"", actual);
    }


    @Test
    public void shouldQuoteMultipleWordsInEq() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_TITLE, new Eq("a b c")));

        String actual = convert(crit);

        assertEquals("P245:\"a b c\"", actual);
    }


    @Test
    public void shouldSpreadTermsInStartsWithWords() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_TITLE, new StartsWithWords("a b")));

        String actual = convert(crit);

        assertEquals("(P245:a* AND P245:b*)", actual);
    }


    @Test
    public void shouldGenerateQueryWithBetweenMatcher() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_YEAR, Between.ofClosed(2015, 2017)));

        String actual = convert(crit);

        assertEquals("ROK_VYD:[2015 TO 2017]", actual);
    }


    @Test
    public void shouldGenerateQueryWithInMatcher() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_TITLE, In.ofItems("a", "b", "c")));

        String actual = convert(crit);

        assertEquals("(P245:\"a\" OR P245:\"b\" OR P245:\"c\")", actual);
    }


    @Test
    public void shouldGenerateComprimedQueryWithIntegersInMatcher() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_YEAR, In.ofItems(5, 1, 3, 8, 2, 2, 7)));

        String actual = convert(crit);

        assertEquals("(ROK_VYD:[1 TO 3] OR ROK_VYD:5 OR ROK_VYD:[7 TO 8])", actual);
    }


    @Test
    public void shouldGenerateQueryWithNestedJunction() {
        Junction<SearchField> crit = new Disjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_TITLE, new EqWords("a")))
                .add(new Conjunction<SearchField>()
                        .add(new Term<>(SEARCH_FIELD_LANG, new EqWords("b")))
                        .add(new Term<>(SEARCH_FIELD_ISBN, new EqWords("c"))));

        String actual = convert(crit);

        assertEquals("(P245:a OR (P64:b AND P22:c))", actual);
    }


    @Test
    public void shouldGenerateQueryWithEqMatcherWithExoticObject() {
        Junction<SearchField> crit = new Conjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_FOND, new Eq((Identified<Integer>) () -> 1)));

        Converter<Restriction<? extends SearchField>, String> converter = new RestrictionToLuceneQueryConverterBuilder(new KeywordsParserSimpleSplittingBySpaceIgnoringDoubleQuotes(), luceneQueryFieldLoader)
                .registerEqValueConverter(Identified.class, source -> "xyz" + source.getId())
                .build();
        String actual = converter.convert(crit);

        assertEquals("REZS_FOND:xyz1", actual);
    }


    @Test
    public void shouldGenerateQueryWithNegation() {
        Restriction<SearchField> restriction = new Not<>(new Term<>(SEARCH_FIELD_TITLE, new EqWords("a")));

        String actual = convert(restriction);

        assertEquals("NOT P245:a", actual);
    }


    @Test
    public void shouldGenerateQueryWithNegationInDisjunction() {
        Junction<SearchField> crit = new Disjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_TITLE, new EqWords("a")))
                .add(new Not<>(new Term<>(SEARCH_FIELD_AUTHOR, new EqWords("a"))));

        String actual = convert(crit);

        assertEquals("(P245:a OR NOT (P100:a OR P110:a))", actual);
    }


    @Test
    public void shouldGenerateQueryWithRangeCoversPoint() {
        Junction<SearchField> crit = new Disjunction<SearchField>()
                .add(new Term<>(SEARCH_FIELD_TITLE, new Includes<>("2008")));

        String actual = convert(crit);

        assertEquals("(P245_lo:[* TO 2008] AND P245_hi:[2008 TO *])", actual);
    }

}