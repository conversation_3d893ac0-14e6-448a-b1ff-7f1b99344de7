### GET correctly parsing range request

@host = http://localhost
@username = su
@password = kp11r0ck

GET {{host}}/api/search
        ?kind=record
        &type=table
        &rootFond=18
        &pageSize=10
        &pageNumber=1
        &qt={"and":[{"field":{"id":"field_d2236_d_val","text":"field_d2236_d_val"},"includes":{"value":"2025-01-01T11:00:00.000000Z"}}]}
Authorization: Basic {{username}} {{password}}
Accept: application/json

###

