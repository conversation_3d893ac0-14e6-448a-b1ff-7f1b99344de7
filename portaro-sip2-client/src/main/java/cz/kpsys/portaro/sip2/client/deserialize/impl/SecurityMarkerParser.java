package cz.kpsys.portaro.sip2.client.deserialize.impl;

import cz.kpsys.portaro.sip2.client.deserialize.InvalidSip2ResponseValueException;
import cz.kpsys.portaro.sip2.client.model.SecurityMarker;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SecurityMarkerParser {

    /**
     * Returns the security marker that matches the given code.
     * @param code security marker code
     * @return security marker that matches the given code
     */
    public SecurityMarker getSecurityMarker(String code) throws InvalidSip2ResponseValueException {
        return switch (code) {
            case "00" -> SecurityMarker.OTHER;
            case "01" -> SecurityMarker.NONE;
            case "02" -> SecurityMarker.TATTLE_TAPE_SECURITY_STRIP_3M;
            case "03" -> SecurityMarker.WHISPER_TAPE_3M;
            default -> throw new InvalidSip2ResponseValueException("Invalid security marker code! The given code \"" + code + "\" doesn't match with any security marker!");
        };
    }    
}
