package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.sip2.client.model.CurrencyType;
import cz.kpsys.portaro.sip2.client.model.FeeType;
import cz.kpsys.portaro.sip2.client.model.PaymentType;
import cz.kpsys.portaro.sip2.client.model.Sip2FeePaidRequest;
import cz.kpsys.portaro.sip2.client.serialize.CommandSerializing;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2MessageSerializer;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2ValueSerializer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;

import static cz.kpsys.portaro.sip2.Sip2Constants.FieldCodes.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2FeePaidRequestSerializer implements TypedSip2MessageSerializer<Sip2FeePaidRequest> {

    @NonNull TypedSip2ValueSerializer<FeeType> feeTypeSerializer;
    @NonNull TypedSip2ValueSerializer<PaymentType> paymentTypeSerializer;
    @NonNull TypedSip2ValueSerializer<CurrencyType> currencyTypeSerializer;
    @NonNull TypedSip2ValueSerializer<Instant> instantSerializer;

    public static Sip2FeePaidRequestSerializer createDefault() {
        return new Sip2FeePaidRequestSerializer(
                new Sip2FeeTypeSerializer(),
                new Sip2PaymentTypeSerializer(),
                new Sip2CurrencyTypeSerializer(),
                new Sip2InstantSerializer()
        );
    }

    @Override
    public void serialize(@NonNull Sip2FeePaidRequest request, @NonNull CommandSerializing ser) {
        ser.commandIdentifier(request.getType().getId());

        instantSerializer.serialize(request.getTransactionDate(), ser.header());
        feeTypeSerializer.serialize(request.getFeeType(), ser.header());
        paymentTypeSerializer.serialize(request.getPaymentType(), ser.header());
        currencyTypeSerializer.serialize(request.getCurrencyType(), ser.header());

        ser.field(BV_FEE_AMOUNT).string(request.getFeeAmount());
        ser.field(AO_INSTITUTION_ID).string(request.getInstitutionId());
        ser.field(AA_PATRON_IDENTIFIER).string(request.getPatronIdentifier());
        ser.field(AC_TERMINAL_PASSWORD).whenNotNull(request.getTerminalPassword()).string(request.getTerminalPassword());
        ser.field(AD_PATRON_PASSWORD).whenNotNull(request.getPatronPassword()).string(request.getPatronPassword());
        ser.field(CG_FEE_IDENTIFIER).whenNotNull(request.getFeeIdentifier()).string(request.getFeeIdentifier());
        ser.field(BK_TRANSACTION_ID).whenNotNull(request.getTransactionId()).string(request.getTransactionId());
    }

}
