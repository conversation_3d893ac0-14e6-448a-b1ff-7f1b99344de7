package cz.kpsys.portaro.sip2.server.impl;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.exemplar.ExemplarViewFunctions;
import cz.kpsys.portaro.exemplar.volume.Volume;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2DocumentPrinter {

    @NonNull ByIdLoadable<Volume, Integer> volumeLoader;

    public String getTitleIdentifier(@NonNull Sip2Item sip2Item) {
        Record document = sip2Item.document();
        String s = document.query("d245.[a,b,n,p,c]").getRaw();

        if (sip2Item instanceof ExemplarBasedSip2Item exemplarBasedSip2Item) {
            Exemplar exemplar = exemplarBasedSip2Item.exemplar();
            if (exemplar.isIssue()) {
                Volume volume = volumeLoader.getById(exemplar.getVolumeId());
                s += " " + ExemplarViewFunctions.exemplarIssueInfo(exemplar, volume);
            }
            if (exemplar.isBundledVolume()) {
                s += " " + ExemplarViewFunctions.exemplarBundledVolumeInfo(exemplar);
            }
        }

        return s;
    }

    public String getDocumentAndExemplarPrintLine(Record document, String itemIdentifier) {
        return "%s %s".formatted(itemIdentifier, document.getName());
    }

}
