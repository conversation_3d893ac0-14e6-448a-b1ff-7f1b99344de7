package cz.kpsys.portaro.sip2.server.impl.handler;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.record.Record;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.EmptyResultDataAccessException;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2DocumentLoader {

    @NonNull ByIdLoadable<Record, UUID> detailedDocumentLoader;

    public Record getDocument(@NotNull UUID recordId) throws Sip2HandleException {
        try {
            return detailedDocumentLoader.getById(recordId);
        } catch (EmptyResultDataAccessException | ItemNotFoundException e) {
            throw new Sip2HandleException(Texts.ofNative("Dokument exemplare neexistuje"), e);
        }
    }

}
