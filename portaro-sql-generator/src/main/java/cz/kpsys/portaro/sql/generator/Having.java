package cz.kpsys.portaro.sql.generator;

import cz.kpsys.portaro.commons.util.ListUtil;

/**
 *
 * <AUTHOR>
 */
public class Having extends Brackets {

    public Having(Query query) {
        super(query);
    }

    @Override
    public String toString() {
        if (ListUtil.isNullOrEmpty(expressionList)) {
            return "";
        }
        return "having " + super.toString();
    }
    
}
