package cz.kpsys.portaro.sql.generator;

import java.util.ArrayList;
import java.util.function.Consumer;

public class JoinList extends QueryPart {
    
    private final ArrayList<Join> joins = new ArrayList<>();
    
    public JoinList(Query query) {
        super(query);
    }

    @Override
    public boolean willWrite() {
        return !joins.isEmpty();
    }
    
    /**
     * vytvori klasicky join (ani left ani right)
     * @param table
     * @param criterium 
     */
    public JoinList add(String table, String criterium) {
        return add(new Join(query, table, criterium));
    }

    public JoinList add(String table, Consumer<Brackets> customClause) {
        Brackets brackets = new Brackets(query);
        customClause.accept(brackets);
        return add(new Join(query, table, brackets.toString()));
    }
    
    public JoinList addLeft(String table, String criterium) {
        return add(new LeftJoin(query, table, criterium));
    }
    
    public JoinList addRight(String table, String criterium) {
        return add(new RightJoin(query, table, criterium));
    }
    
    public JoinList addFullOuter(String table, String criterium) {
        return add(new FullOuterJoin(query, table, criterium));
    }

    /**
     * testuje, zda join neni null, prazdny, nebo uz joinList tentor join obsahuje. pokud projde, prida objekt typu Join (Join, LeftJoinItem, RightJoinItem)
     * @param join 
     */
    private JoinList add(Join join) {
        if (join != null && !join.toString().isEmpty() && !joins.contains(join)) {
            joins.add(join);
        }
        return this;
    }
    
    /**
     * vygeneruje retezec ze seznamu joinu
     * @return 
     */
    @Override
    public String toString() {
        if (joins.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (Join join : joins) {
            if (sb.length() > 0) {
                sb.append(' ');
            }
            sb.append(join);
        }
        return sb.toString();
    }
    
}
