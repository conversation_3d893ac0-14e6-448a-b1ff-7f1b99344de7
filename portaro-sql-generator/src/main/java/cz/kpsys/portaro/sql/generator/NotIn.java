package cz.kpsys.portaro.sql.generator;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.Collection;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class NotIn extends Condition {

    @NonNull Collection<?> valueList;
    @NonNull String column;
    @NonNull String valuePlaceholder;

    public NotIn(@NonNull Query query, @NonNull String column, @NonNull Collection<?> valueList) {
        super(query);
        this.column = column;
        this.valueList = valueList;
        this.valuePlaceholder = query.nextParamPlaceholder(valueList);
    }

    @Override
    public String toString() {
        if (valueList.isEmpty()) {
            return "";
        }
        return "%s NOT IN (%s)".formatted(column, valuePlaceholder);
    }
}
