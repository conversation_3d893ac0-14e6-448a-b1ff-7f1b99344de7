package cz.kpsys.portaro.sql.generator;

/**
 *
 * <AUTHOR>
 */
public class NotLike extends SimpleCondition {
    

    public NotLike(Query query, String column, String value, boolean anythingInFront, boolean anythingInEnd) {
        super(query);
        
        if (anythingInFront) {
            value = "%" + value;
        }
        if (anythingInEnd) {
            value = value + "%";
        }
        
        setColumn(column);
        setOperator("NOT LIKE");
        setValue(value);
    }

    
}
