package cz.kpsys.portaro.test.e2e.api;

import cz.kpsys.portaro.commons.containers.server.GopayMockServer;
import cz.kpsys.portaro.test.e2e.TestEnvironmentFactory;
import cz.kpsys.portaro.test.e2e.web.testers.PortaroTester;
import cz.kpsys.portaro.test.e2e.web.utils.IniSetting;
import cz.kpsys.portaro.test.e2e.web.utils.LoggingExtension;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.testcontainers.junit.jupiter.Testcontainers;


@Testcontainers
@Tag("ci")
@Tag("e2e")
@ExtendWith(LoggingExtension.class)
public class GopayPaymentIntegrationTest {

    private static final String LIBRARY_NAME = "Testovací knihovna";
    private static final String READER_USER_NAME = "testReader";
    private static final String READER_USER_PASSWORD = "eAVMhFAO51Bq53A";
    private static final String READER_FIRST_NAME = "testFirstName";
    private static final String READER_LAST_NAME = "testLastName";
    private static final String READER_EMAIL = "testEmail";
    private static final String READER_BARCODE = "210123456789";
    private static final String GOPAY_PAYMENT_ID = "3123456789";

    private static PortaroTester portaroTester;
    private GopayMockServer gopayMockServer;

    @BeforeAll
    public static void startupEnv() {
        TestEnvironmentFactory.startup();
    }

    @BeforeEach
    public void startupTest() {
        portaroTester = TestEnvironmentFactory.portaroTester();
        gopayMockServer = TestEnvironmentFactory.getGopayMockServer();
        portaroTester.createLibraryAndLinkToRoot(LIBRARY_NAME);

        portaroTester.changeSetting(new IniSetting("integ.gopay", "enabled", "ANO"));
        portaroTester.changeSetting(new IniSetting("integ.gopay", "apiUrl", gopayMockServer.getInNetworkHttpUrl()));
        portaroTester.changeSetting(new IniSetting("integ.gopay", "clientId", "1607363901"));
        portaroTester.changeSetting(new IniSetting("integ.gopay", "clientSecret", "CfYA56PG"));
        portaroTester.changeSetting(new IniSetting("integ.gopay", "goId", "8149746451"));

        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SENDER_NAME", TestEnvironmentFactory.mailServerSmtpSenderName));
        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SENDER_ADDRESS", TestEnvironmentFactory.mailServerSmtpSenderAddress));
        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SERVER_ADDRESS", TestEnvironmentFactory.mailServerInnerNetworkSmtpHost));
        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SERVER_PORT", TestEnvironmentFactory.mailServerInnerNetworkSmtpPort));

        gopayMockServer.registerPaymentCreateResponse(GOPAY_PAYMENT_ID);
        gopayMockServer.registerOAuthTokenResponse();
    }

    @AfterEach
    public void cleanup() {
        gopayMockServer.reset();
        portaroTester.cleanup();
    }

    @Test
    void shouldCreateGopayPayment() {
        gopayMockServer.registerPaymentPaidStateResponse(GOPAY_PAYMENT_ID);

        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "S");

        portaroTester.createGopayPaymentUrlForUser(userId).assertHasRedirectUrl();
        portaroTester.notifyGopayPayment(GOPAY_PAYMENT_ID).assertHttpStatusOk();
        portaroTester.getUserTransactions(userId).assertTransactionSum(0, 10000);
    }

    @Test
    void shouldRefundGopayPayment() {
        gopayMockServer.registerPaymentPaidStateResponse(GOPAY_PAYMENT_ID);
        gopayMockServer.registerPaymentRefundedStateResponse(GOPAY_PAYMENT_ID);

        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "S");

        portaroTester.createGopayPaymentUrlForUser(userId);

        portaroTester.notifyGopayPayment(GOPAY_PAYMENT_ID); // PAID
        portaroTester.getUserTransactions(userId).assertNotEmpty();

        portaroTester.notifyGopayPayment(GOPAY_PAYMENT_ID); // REFUNDED
        portaroTester.getUserTransactions(userId).assertEmpty();

    }
}
