package cz.kpsys.portaro.test.e2e.api;

import cz.kpsys.portaro.finance.AmountTypeCategory;
import cz.kpsys.portaro.test.e2e.TestEnvironmentFactory;
import cz.kpsys.portaro.test.e2e.web.testers.PortaroTester;
import cz.kpsys.portaro.test.e2e.web.utils.IniSetting;
import cz.kpsys.portaro.test.e2e.web.utils.LoggingExtension;
import io.restassured.response.Response;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.http.HttpStatus;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Testcontainers
@Tag("ci")
@Tag("e2e")
@ExtendWith(LoggingExtension.class)
public class RegistrationExtensionIntegrationTest {

    private static final String READER_USER_NAME = "testReader";
    private static final String READER_USER_PASSWORD = "eAVMhFAO51Bq53A";
    private static final String READER_FIRST_NAME = "testFirstName";
    private static final String READER_LAST_NAME = "testLastName";
    private static final String READER_EMAIL = "testEmail";
    private static final String READER_BARCODE = "210123456789";
    private static final String LIBRARY_NAME = "Testovací knihovna";

    private static final int REGISTRATION_FEE = 150;
    private static final int FAMILY_REGISTRATION_FEE = REGISTRATION_FEE + 50;

    private static PortaroTester portaroTester;

    @BeforeAll
    public static void startupEnv() {
        TestEnvironmentFactory.startup();
        portaroTester = TestEnvironmentFactory.portaroTester();
    }

    @BeforeEach
    public void startupTest() {
        portaroTester.createLibraryAndLinkToRoot(LIBRARY_NAME);

        portaroTester.getPortaroApiAuthCaller().createNewReaderCategory("CT", "Čtenář", REGISTRATION_FEE);
        portaroTester.getPortaroApiAuthCaller().createNewReaderCategory("FA", "Family", FAMILY_REGISTRATION_FEE);
    }

    @AfterEach
    public void cleanup() {
        portaroTester.changeSetting(new IniSetting("readeraccount", "forcePaymentBeforeExtendingRegistrationPeriod", "false"));
        portaroTester.changeSetting(new IniSetting("auth.registration", "notAutoExtendingCategoriesOnRegistration", ""));
        portaroTester.cleanup();
    }

    @Test
    void newUserShouldHaveRegistrationWhenHisCategoryHasZeroRegistrationFee() {
        portaroTester.changeSetting(new IniSetting("auth.registration", "notAutoExtendingCategoriesOnRegistration", ""));
        String userId = portaroTester.createUser("firstUser",
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                "firstEmail",
                "************");

        portaroTester.getUser(userId)
                .assertHasRegistration()
                .assertReaderCategoryWithRegistrationFee(0);

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        portaroTester.changeSetting(new IniSetting("auth.registration", "notAutoExtendingCategoriesOnRegistration", "FA,CT"));
        userId = portaroTester.createUser("secondUser",
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                "secondEmail",
                "410123456789");

        portaroTester.getUser(userId)
                .assertHasRegistration()
                .assertReaderCategoryWithRegistrationFee(0);

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        portaroTester.changeSetting(new IniSetting("auth.registration", "notAutoExtendingCategoriesOnRegistration", "DO"));
        userId = portaroTester.createUser("thirdUser",
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                "thirdEmail",
                "510123456789");

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration()
                .assertReaderCategoryWithRegistrationFee(0);

        portaroTester.getUserTransactions(userId)
                .assertEmpty();
    }

    @Test
    void newUserShouldNotHaveRegistrationWhenHisCategoryHasNonZeroRegistrationFeeAndGetRegistrationAfterLibrarianExtendsIt() {
        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration()
                .assertReaderCategoryWithRegistrationFee(REGISTRATION_FEE);

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        portaroTester.extendUserRegistration(userId);

        portaroTester.getUser(userId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-REGISTRATION_FEE);
    }

    @Test
    void newUserShouldNotBeAbleToExtendUserRegistration() {
        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration()
                .assertReaderCategoryWithRegistrationFee(REGISTRATION_FEE);

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        Response response = portaroTester.getPortaroApiAuthCaller(READER_USER_NAME, READER_USER_PASSWORD).extendUserRegistration(userId);
        assertEquals(403, response.getStatusCode());
    }

    @Test
    void userShouldGetRegistrationAfterPayingFullRegistration() {
        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        portaroTester.createTransactionViaBackdoor(userId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -REGISTRATION_FEE, 0, 0);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration()
                .assertReaderCategoryWithRegistrationFee(REGISTRATION_FEE);

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-REGISTRATION_FEE);

        portaroTester.createPaymentForUser(userId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), REGISTRATION_FEE);

        portaroTester.getUser(userId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-REGISTRATION_FEE)
                .assertSecondTransactionSum(REGISTRATION_FEE);
    }

    @Test
    void userShouldNotGetRegistrationAfterNotPayingFullRegistration() {
        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        portaroTester.createTransactionViaBackdoor(userId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -REGISTRATION_FEE, 0, 0);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration()
                .assertReaderCategoryWithRegistrationFee(REGISTRATION_FEE);

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-REGISTRATION_FEE);

        portaroTester.createPaymentForUser(userId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), REGISTRATION_FEE - 50);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-REGISTRATION_FEE)
                .assertSecondTransactionSum(REGISTRATION_FEE - 50);
    }

    @Test
    void userShouldThrowExceptionWhenForcePaymentBeforeExtendingRegistrationPeriodSetTrue() {
        portaroTester.changeSetting(new IniSetting("readeraccount", "forcePaymentBeforeExtendingRegistrationPeriod", "true"));

        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        portaroTester.createTransactionViaBackdoor(userId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -REGISTRATION_FEE, 0, 0);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration()
                .assertReaderCategoryWithRegistrationFee(REGISTRATION_FEE);

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-REGISTRATION_FEE);

        portaroTester.extendUserRegistration(userId)
                .assertHttpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .assertResponseTypeWithText("exception", "Balance in user account is not sufficient to complete extending registration period.");

    }

    @Test
    void userShouldGetRegistrationWithoutPaymentWhenHasSufficientCreditInAccount() {
        portaroTester.changeSetting(new IniSetting("readeraccount", "forcePaymentBeforeExtendingRegistrationPeriod", "true"));

        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        portaroTester.createTransactionViaBackdoor(userId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), REGISTRATION_FEE + 100, 0, 0);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration()
                .assertReaderCategoryWithRegistrationFee(REGISTRATION_FEE);

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(REGISTRATION_FEE + 100);

        portaroTester.extendUserRegistration(userId);

        portaroTester.getUser(userId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(REGISTRATION_FEE + 100)
                .assertSecondTransactionSum(-REGISTRATION_FEE);
    }

    @Test
    void onlyFamilyGetDeptWhenRepresentativeExtendsRegistration() {
        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        String representativeId = portaroTester.createUser("representative",
                READER_USER_PASSWORD,
                "representative",
                "representative",
                "representativeEmail",
                "321123456789",
                "CT");

        String familyId = portaroTester.createFamily("Family", "FA");

        portaroTester.linkFamily(representativeId, List.of(userId), familyId);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        portaroTester.getUser(representativeId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertEmpty();

        portaroTester.getUser(familyId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertEmpty();

        portaroTester.extendUserRegistration(userId)
                .assertResponseTypeWithText("exception", "Subject user does not have any registration extendable users")
                .assertHttpStatusIn(HttpStatus.Series.CLIENT_ERROR);

        portaroTester.extendUserRegistration(representativeId)
                .assertHttpStatusOk();

        portaroTester.getUser(userId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        portaroTester.getUser(representativeId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertEmpty();

        portaroTester.getUser(familyId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE);

    }

    @Test
    void onlyFamilyGetDeptWhenFamilyExtendsRegistration() {
        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        String representativeId = portaroTester.createUser("representative",
                READER_USER_PASSWORD,
                "representative",
                "representative",
                "representativeEmail",
                "321123456789",
                "CT");

        String familyId = portaroTester.createFamily("Family", "FA");

        portaroTester.linkFamily(representativeId, List.of(userId), familyId);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        portaroTester.getUser(representativeId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertEmpty();

        portaroTester.getUser(familyId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertEmpty();

        portaroTester.extendUserRegistration(userId)
                .assertResponseTypeWithText("exception", "Subject user does not have any registration extendable users")
                .assertHttpStatusIn(HttpStatus.Series.CLIENT_ERROR);

        portaroTester.extendUserRegistration(familyId)
                .assertHttpStatusOk();

        portaroTester.getUser(userId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        portaroTester.getUser(representativeId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertEmpty();

        portaroTester.getUser(familyId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE);

    }

    @Test
    void familyMembersDeptShouldBeCanceledWhenRepresentativePayHisFullDept() {
        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        String representativeId = portaroTester.createUser("representative",
                READER_USER_PASSWORD,
                "representative",
                "representative",
                "representativeEmail",
                "321123456789",
                "CT");

        String familyId = portaroTester.createFamily("Family", "FA");

        portaroTester.linkFamily(representativeId, List.of(userId), familyId);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        portaroTester.getUser(representativeId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertEmpty();

        portaroTester.getUser(familyId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertEmpty();

        portaroTester.createTransactionViaBackdoor(userId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -REGISTRATION_FEE, 0, 0);
        portaroTester.createTransactionViaBackdoor(representativeId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -FAMILY_REGISTRATION_FEE, 0, 0);
        portaroTester.createTransactionViaBackdoor(familyId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -FAMILY_REGISTRATION_FEE, 0, 0);

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-REGISTRATION_FEE);

        portaroTester.getUserTransactions(representativeId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE);

        portaroTester.getUserTransactions(familyId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE);

        portaroTester.createPaymentForUser(representativeId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), FAMILY_REGISTRATION_FEE);

        portaroTester.getUser(userId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(0);

        portaroTester.getUser(representativeId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE)
                .assertSecondTransactionSum(FAMILY_REGISTRATION_FEE);

        portaroTester.getUser(familyId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertNotEmpty()
                .assertFirstTransactionSum(0);
    }

    @Test
    void familyMembersDeptShouldNotBeCanceledWhenRepresentativeNotPayHisFullDept() {
        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        String representativeId = portaroTester.createUser("representative",
                READER_USER_PASSWORD,
                "representative",
                "representative",
                "representativeEmail",
                "321123456789",
                "CT");

        String familyId = portaroTester.createFamily("Family", "FA");

        portaroTester.linkFamily(representativeId, List.of(userId), familyId);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        portaroTester.getUser(representativeId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertEmpty();

        portaroTester.getUser(familyId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertEmpty();

        portaroTester.createTransactionViaBackdoor(userId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -REGISTRATION_FEE, 0, 0);
        portaroTester.createTransactionViaBackdoor(representativeId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -FAMILY_REGISTRATION_FEE, 0, 0);
        portaroTester.createTransactionViaBackdoor(familyId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -FAMILY_REGISTRATION_FEE, 0, 0);

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-REGISTRATION_FEE);

        portaroTester.getUserTransactions(representativeId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE);

        portaroTester.getUserTransactions(familyId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE);

        portaroTester.createPaymentForUser(representativeId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), FAMILY_REGISTRATION_FEE - 50);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-REGISTRATION_FEE);

        portaroTester.getUser(representativeId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE)
                .assertSecondTransactionSum(FAMILY_REGISTRATION_FEE - 50);

        portaroTester.getUser(familyId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE);

        portaroTester.createPaymentForUser(representativeId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), 50);

        portaroTester.getUser(userId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(0);

        portaroTester.getUser(representativeId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE)
                .assertSecondTransactionSum(FAMILY_REGISTRATION_FEE - 50)
                .assertThirdTransactionSum(50);

        portaroTester.getUser(familyId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertNotEmpty()
                .assertFirstTransactionSum(0);
    }

    @Test
    void familyMembersDeptShouldBeCanceledWhenRepresentativePayHisOwnEvenWhenTheyHasValidRegistration() {
        String userId = portaroTester.createUser(READER_USER_NAME,
                READER_USER_PASSWORD,
                READER_FIRST_NAME,
                READER_LAST_NAME,
                READER_EMAIL,
                READER_BARCODE,
                "CT");

        String representativeId = portaroTester.createUser("representative",
                READER_USER_PASSWORD,
                "representative",
                "representative",
                "representativeEmail",
                "321123456789",
                "CT");

        String familyId = portaroTester.createFamily("Family", "FA");

        portaroTester.linkFamily(representativeId, List.of(userId), familyId);

        portaroTester.getUser(userId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(userId)
                .assertEmpty();

        portaroTester.getUser(representativeId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertEmpty();

        portaroTester.getUser(familyId)
                .assertExpiredOrNoRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertEmpty();

        portaroTester.createTransactionViaBackdoor(userId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -REGISTRATION_FEE, 0, 0);
        portaroTester.createTransactionViaBackdoor(representativeId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -FAMILY_REGISTRATION_FEE, 0, 0);
        portaroTester.createTransactionViaBackdoor(familyId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), -FAMILY_REGISTRATION_FEE, 0, 0);

        portaroTester.extendUserRegistration(representativeId)
                .assertHttpStatusOk();

        portaroTester.getUser(userId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-REGISTRATION_FEE);

        portaroTester.getUser(representativeId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE);

        portaroTester.getUser(familyId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE);

        portaroTester.createPaymentForUser(representativeId, new AmountTypeCategory(AmountTypeCategory.REGISTRATION_FEE_ID), FAMILY_REGISTRATION_FEE);

        portaroTester.getUser(userId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(userId)
                .assertNotEmpty()
                .assertFirstTransactionSum(0);

        portaroTester.getUser(representativeId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(representativeId)
                .assertNotEmpty()
                .assertFirstTransactionSum(-FAMILY_REGISTRATION_FEE)
                .assertSecondTransactionSum(FAMILY_REGISTRATION_FEE);

        portaroTester.getUser(familyId)
                .assertHasRegistration();

        portaroTester.getUserTransactions(familyId)
                .assertNotEmpty()
                .assertFirstTransactionSum(0);
    }
}
