package cz.kpsys.portaro.test.e2e.web;

import cz.kpsys.portaro.commons.containers.mail.MailReader;
import cz.kpsys.portaro.test.e2e.TestEnvironmentFactory;
import cz.kpsys.portaro.test.e2e.web.testers.PortaroGuiTester;
import cz.kpsys.portaro.test.e2e.web.testers.PortaroTester;
import cz.kpsys.portaro.test.e2e.web.utils.IniSetting;
import cz.kpsys.portaro.test.e2e.web.utils.Junit5VncRecorder;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.time.Duration;

import static cz.kpsys.portaro.test.e2e.TestConstants.DEFAULT_DOCUMENT_245_A;
import static cz.kpsys.portaro.test.e2e.web.testers.datacreation.DocumentRecordCreation.DEFAULT_DOCUMENT_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Testcontainers
@Tag("ci")
@Tag("e2e")
@ExtendWith(Junit5VncRecorder.class)
public class OrderLendReturnDocumentTest {

    private PortaroTester portaroTester;
    private PortaroGuiTester portaroGuiTester;

    private static final String LIBRARIAN_USER_NAME = "librarian";
    private static final String LIBRARIAN_USER_PASSWORD = "eAVMhFAO51Bq53A";
    private static final int LIBRARIAN_GROUP_ID = 1104;

    private static final String READER_1_USER_NAME = "reader1";
    private static final String READER_1_USER_PASSWORD = "eAVMhFAO51Bq53A";
    private static final String READER_1_FIRST_NAME = "reader1FirstName";
    private static final String READER_1_LAST_NAME = "reader1LastName";
    private static final String READER_1_EMAIL = "reader1Email";
    private static final String READER_1_BARCODE = "110123456789";

    private static final String READER_2_USER_NAME = "reader2";
    private static final String READER_2_USER_PASSWORD = "eAVMhFAO51Bq53A";
    private static final String READER_2_FIRST_NAME = "reader2FirstName";
    private static final String READER_2_LAST_NAME = "reader2LastName";
    private static final String READER_2_EMAIL = "reader2Email";
    private static final String READER_2_BARCODE = "210123456789";

    private static final String READER_3_USER_NAME = "reader3";
    private static final String READER_3_USER_PASSWORD = "eAVMhFAO51Bq53A";
    private static final String READER_3_FIRST_NAME = "reader3FirstName";
    private static final String READER_3_LAST_NAME = "reader3LastName";
    private static final String READER_3_EMAIL = "reader3Email";
    private static final String READER_3_BARCODE = "310123456789";

    private static final String EXEMPLAR_BARCODE = "349600078749";



    @BeforeAll
    public static void startupEnv() {
        TestEnvironmentFactory.startup();
        TestEnvironmentFactory.startPortaroGuiTester();
    }

    @BeforeEach
    public void startupTest() {
        portaroTester = TestEnvironmentFactory.portaroTester();
        portaroGuiTester = TestEnvironmentFactory.portaroGuiTester();

        portaroTester.setupNecessarySettings();
        portaroTester.changeSetting(new IniSetting("VYPUC", "POVOLIT_OBJEDNAVKY", "ANO"));
//

        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SENDER_NAME", TestEnvironmentFactory.mailServerSmtpSenderName));
        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SENDER_ADDRESS", TestEnvironmentFactory.mailServerSmtpSenderAddress));
        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SERVER_ADDRESS", TestEnvironmentFactory.mailServerInnerNetworkSmtpHost));
        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SERVER_PORT", TestEnvironmentFactory.mailServerInnerNetworkSmtpPort));
    }

    @AfterEach
    public void cleanup(TestInfo testInfo) {
        portaroGuiTester.getScreenshotAndLogout(testInfo);
        portaroTester.cleanup();
    }

    @AfterAll
    public static void cleanupEnv() {
        TestEnvironmentFactory.stopPortaroGuiTester();
    }

    @Test
    void shouldOrderLendReturnDocumentTest() {
        // Test Environment SETUP
        portaroTester.createLibrarian(LIBRARIAN_USER_NAME, LIBRARIAN_USER_PASSWORD, LIBRARIAN_GROUP_ID);
        portaroTester.createUser(READER_1_USER_NAME, READER_1_USER_PASSWORD, READER_1_FIRST_NAME, READER_1_LAST_NAME, READER_1_EMAIL, READER_1_BARCODE);
        portaroTester.createUser(READER_2_USER_NAME, READER_2_USER_PASSWORD, READER_2_FIRST_NAME, READER_2_LAST_NAME, READER_2_EMAIL, READER_2_BARCODE);
        portaroTester.createUser(READER_3_USER_NAME, READER_3_USER_PASSWORD, READER_3_FIRST_NAME, READER_3_LAST_NAME, READER_3_EMAIL, READER_3_BARCODE);

        String recordId = portaroTester.createDefaultDocumentWithExemplar(EXEMPLAR_BARCODE).recordId();

        portaroGuiTester.getPortaroBrowserCaller().goToHomepage();

        portaroGuiTester.getTestingSequence()
                .orderDocumentByReader(recordId, DEFAULT_DOCUMENT_NAME, READER_1_USER_NAME, READER_1_USER_PASSWORD)
                .orderDocumentByReader(recordId, DEFAULT_DOCUMENT_NAME, READER_2_USER_NAME, READER_2_USER_PASSWORD)
                .loginAsUser(LIBRARIAN_USER_NAME, LIBRARIAN_USER_PASSWORD);

        portaroGuiTester.getPages().topMainMenu()
                .clickOnLoansToProcessMenuButton();

        portaroGuiTester.getPages().loansToProcessPage()
                .clickOnProcessLoanButton();

        portaroGuiTester.getPages().searchSelectionModal()
                .enterBarcode(EXEMPLAR_BARCODE)
                .waitForDocumentName(DEFAULT_DOCUMENT_245_A)
                .clickOnSelectableExemplarCard();

        String reader1FullName = "%s %s".formatted(READER_1_FIRST_NAME, READER_1_LAST_NAME);
        String reader2FullName = "%s %s".formatted(READER_2_FIRST_NAME, READER_2_LAST_NAME);

        TestEnvironmentFactory.mailTester().assertReceivedIncomingMessage(Duration.ofSeconds(5), MailReader.reservationReadyMailPredicate(DEFAULT_DOCUMENT_245_A), () -> {
            portaroGuiTester.getPages().loanSelectionModal()
                    .waitForLoanUserName(reader1FullName)
                    .findReaderAndProcessLoanByName(reader1FullName);

            portaroGuiTester.getPages().loansToProcessPage()
                    .waitForDisappearanceOfLoanByName(reader1FullName);
        });

        portaroGuiTester.getPages().topMainMenu()
                .clickOnLoansToPickupMenuButton();

        portaroGuiTester.getPages().loansToPickupPage()
                .checkLoansToPickup(DEFAULT_DOCUMENT_NAME, reader1FullName);

        portaroGuiTester.getTestingSequence()
                .goToUserAccountByName(reader1FullName)
                .lendBookToUser(DEFAULT_DOCUMENT_245_A, EXEMPLAR_BARCODE);

        portaroGuiTester.getPages().searchSelectionModal()
                .waitForSelectionModal()
                .clickOnCloseSelectionModal();

        portaroGuiTester.getTestingSequence()
                .checkIfDocumentIsBorrowedByUser(recordId, DEFAULT_DOCUMENT_NAME);

        portaroGuiTester.getPortaroBrowserCaller().goToHomepage();

        portaroGuiTester.getPages().topMainMenu()
                .clickOnReadersMenuButton();

        portaroGuiTester.getPages().readersPage()
                .clickOnReturnBookButton();

        portaroGuiTester.getPages().searchSelectionModal()
                .enterBarcode(EXEMPLAR_BARCODE)
                .waitForDocumentName(DEFAULT_DOCUMENT_NAME)
                .clickOnSelectableExemplarCard();

        TestEnvironmentFactory.mailTester().assertReceivedIncomingMessage(Duration.ofSeconds(5), MailReader.reservationReadyMailPredicate(DEFAULT_DOCUMENT_245_A), () -> {

            portaroGuiTester.getPages().loanReadySendNoticeModal()
                    .clickOnSendNoticeYes();

            portaroGuiTester.getPages().searchSelectionModal()
                    .waitForSelectionModal()
                    .clickOnCloseSelectionModal();
        });

        portaroGuiTester.getTestingSequence()
                .goToUserAccountByName(reader1FullName);

        portaroGuiTester.getPages().userDetailPage()
                .clickOnEndedLoansTab();

        assertEquals(DEFAULT_DOCUMENT_NAME, portaroGuiTester.getPages().userDetailPage().getNameOfEndedDocument(recordId));

        portaroGuiTester.getPages().topMainMenu()
                .clickOnLogoutSubmenuButton();

        /// Reservation

        portaroGuiTester.getPortaroBrowserCaller().goToHomepage();

        portaroGuiTester.getPages().topMainMenu()
                .clickOnSearchMenuButton();

        portaroGuiTester.getPages().searchPage()
                .checkSearchFormStatus(true)
                .clickOnMonographyFormOptionCheckbox()
                .checkSearchFormStatus(false)
                .clickOnSearchButton()
                .clickOnRecordByDocumentName(DEFAULT_DOCUMENT_245_A);

        portaroGuiTester.getPages().documentDetailPage()
                .waitAndClickOnReservationOfDocument();

        portaroGuiTester.getTestingSequence()
                .loginFirst(READER_3_USER_NAME, READER_3_USER_PASSWORD);

        portaroGuiTester.getPages().loanRequestModal()
                .clickOnReservationDocumentButton();

        portaroGuiTester.getTestingSequence()
                .checkWaitingDocumentInUserAccount(recordId, DEFAULT_DOCUMENT_NAME);

        portaroGuiTester.getPages().userDetailPage()
                .clickOnCancelWaitingLoanButton()
                .waitForDisappearanceOfWaitingDocumentReservation(recordId);

        portaroGuiTester.getPages().topMainMenu()
                .clickOnLogoutSubmenuButton();

        portaroGuiTester.getTestingSequence()
                .loginAsUser(LIBRARIAN_USER_NAME, LIBRARIAN_USER_PASSWORD)
                .goToUserAccountByName(reader2FullName)
                .lendBookToUser(DEFAULT_DOCUMENT_245_A, EXEMPLAR_BARCODE);

        portaroGuiTester.getPages().searchSelectionModal()
                .waitForSelectionModal()
                .clickOnCloseSelectionModal();

        portaroGuiTester.getTestingSequence()
                .checkIfDocumentIsBorrowedByUser(recordId, DEFAULT_DOCUMENT_NAME);

        portaroGuiTester.getPages().topMainMenu()
                .clickOnReadersMenuButton();

        portaroGuiTester.getPages().readersPage()
                .clickOnReturnBookButton();

        portaroGuiTester.getPages().searchSelectionModal()
                .enterBarcode(EXEMPLAR_BARCODE)
                .waitForDocumentName(DEFAULT_DOCUMENT_245_A)
                .clickOnSelectableExemplarCard();

        portaroGuiTester.getPages().searchSelectionModal()
                .waitForSelectionModal()
                .clickOnCloseSelectionModal();

        portaroGuiTester.getTestingSequence()
                .goToUserAccountByName(reader2FullName);

        portaroGuiTester.getPages().userDetailPage()
                .clickOnEndedLoansTab();

        assertEquals(DEFAULT_DOCUMENT_NAME, portaroGuiTester.getPages().userDetailPage().getNameOfEndedDocument(recordId));
    }
}
