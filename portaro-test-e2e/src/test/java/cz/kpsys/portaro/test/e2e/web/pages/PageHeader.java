package cz.kpsys.portaro.test.e2e.web.pages;

import cz.kpsys.portaro.test.e2e.web.utils.PageUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class PageHeader {

    @NonNull final WebDriver driver;
    @NonNull final PageUtils pageUtils;
    @NonNull final Pages pages;
    @NonNull final By logoSearchQueryInput = By.cssSelector(".logo-search .searchStringInput");
    @NonNull final By logoSearchSubmitButton = By.cssSelector(".logo-search .searchStringSubmit");

    public PageHeader(@NonNull WebDriver driver, @NonNull Pages pages) {
        this.driver = driver;
        this.pages = pages;
        pageUtils = new PageUtils(driver);
    }

    public PageHeader enterLogoSearchQuery(String query) {
        pageUtils.waitForElementAndSendKeys(logoSearchQueryInput, query);
        return this;
    }

    public PageHeader clickOnLogoSearchSubmitButton(Consumer<RecordSearchPage> recordSearchPage) {
        driver.findElement(logoSearchSubmitButton).click();
        recordSearchPage.accept(pages.recordSearchPage());
        return this;
    }
}
