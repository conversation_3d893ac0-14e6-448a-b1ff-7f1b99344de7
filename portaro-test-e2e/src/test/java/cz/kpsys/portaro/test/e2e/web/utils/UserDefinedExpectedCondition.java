package cz.kpsys.portaro.test.e2e.web.utils;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.openqa.selenium.*;
import org.openqa.selenium.support.ui.ExpectedCondition;

public class UserDefinedExpectedCondition {

    public static ExpectedCondition<WebElement> steadinessOfElementLocated(final WebElement element) {
        return new ElementSteadyCondition(element);
    }

    public static ExpectedCondition<WebElement> steadinessOfElementLocated(final By locator) {
        return new ElementSteadyConditionBy(locator);
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class ElementSteadyCondition implements ExpectedCondition<WebElement> {

        @NonNull WebElement element;
        @NonFinal Point previousLocation = null;

        @Override
        public WebElement apply(WebDriver driver) {
            try {
                if (element.isDisplayed()) {
                    Point currentLocation = element.getLocation();
                    if (currentLocation.equals(previousLocation)) {
                        return element;
                    }
                    previousLocation = currentLocation;
                }
                return null;
            } catch (StaleElementReferenceException e) {
                // Returns null because the element is no longer or not present in DOM.
                return null;
            }
        }

        @Override
        public String toString() {
            return "Steadiness of element %s".formatted(element);
        }
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class ElementSteadyConditionBy implements ExpectedCondition<WebElement> {

        @NonNull By locator;
        @NonFinal Point previousLocation = null;

        @Override
        public WebElement apply(WebDriver driver) {
            WebElement element = driver.findElement(locator);
            try {
                if (element != null && element.isDisplayed()) {
                    Point currentLocation = driver.findElement(locator).getLocation();
                    if (currentLocation.equals(previousLocation)) {
                        return element;
                    }
                    previousLocation = currentLocation;
                }
                return null;
            } catch (StaleElementReferenceException e) {
                // Returns null because the element is no longer or not present in DOM.
                return null;
            }
        }

        @Override
        public String toString() {
            return "Steadiness of element %s".formatted(locator);
        }
    }
}
