# This is a Gradle generated file for dependency locking.
# Manual edits can break the build and are not advised.
# This file is expected to be part of source control.
ch.qos.logback:logback-classic:1.5.18=testCompileClasspath,testRuntimeClasspath
ch.qos.logback:logback-core:1.5.18=testCompileClasspath,testRuntimeClasspath
com.adobe.xmp:xmpcore:6.1.11=testRuntimeClasspath
com.auth0:java-jwt:4.5.0=testRuntimeClasspath
com.drewnoakes:metadata-extractor:2.19.0=testRuntimeClasspath
com.ethlo.time:itu:1.7.0=testRuntimeClasspath
com.fasterxml.jackson.core:jackson-annotations:2.20=testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.core:jackson-core:2.20.0=testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.core:jackson-databind:2.20.0=testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.20.0=testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.20.0=testRuntimeClasspath
com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.20.0=testRuntimeClasspath
com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.20.0=testRuntimeClasspath
com.fasterxml.jackson.module:jackson-module-parameter-names:2.20.0=testRuntimeClasspath
com.fasterxml.jackson:jackson-bom:2.20.0=testCompileClasspath,testRuntimeClasspath
com.fasterxml.uuid:java-uuid-generator:5.1.0=testRuntimeClasspath
com.fasterxml.woodstox:woodstox-core:7.1.1=testCompileClasspath,testRuntimeClasspath
com.fasterxml:classmate:1.5.1=testRuntimeClasspath
com.github.cliftonlabs:json-simple:3.0.2=testRuntimeClasspath
com.github.docker-java:docker-java-api:3.4.2=testRuntimeClasspath
com.github.docker-java:docker-java-transport-zerodep:3.4.2=testRuntimeClasspath
com.github.docker-java:docker-java-transport:3.4.2=testRuntimeClasspath
com.github.java-json-tools:btf:1.3=testRuntimeClasspath
com.github.java-json-tools:jackson-coreutils-equivalence:1.0=testRuntimeClasspath
com.github.java-json-tools:jackson-coreutils:2.0=testRuntimeClasspath
com.github.java-json-tools:json-patch:1.13=testRuntimeClasspath
com.github.java-json-tools:json-schema-core:1.2.14=testRuntimeClasspath
com.github.java-json-tools:json-schema-validator:2.2.14=testRuntimeClasspath
com.github.java-json-tools:msg-simple:1.2=testRuntimeClasspath
com.github.java-json-tools:uri-template:0.10=testRuntimeClasspath
com.github.kagkarlsson:db-scheduler-spring-boot-starter:16.1.0=testRuntimeClasspath
com.github.kagkarlsson:db-scheduler:16.1.0=testRuntimeClasspath
com.github.stephenc.jcip:jcip-annotations:1.0-1=testRuntimeClasspath
com.google.auto.service:auto-service-annotations:1.1.1=testRuntimeClasspath
com.google.code.findbugs:jsr305:3.0.2=checkstyle,testRuntimeClasspath
com.google.errorprone:error_prone_annotations:2.28.0=checkstyle
com.google.errorprone:error_prone_annotations:2.36.0=testRuntimeClasspath
com.google.guava:failureaccess:1.0.2=checkstyle
com.google.guava:failureaccess:1.0.3=testRuntimeClasspath
com.google.guava:guava:33.3.1-jre=checkstyle
com.google.guava:guava:33.4.8-jre=testRuntimeClasspath
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava=checkstyle,testRuntimeClasspath
com.google.j2objc:j2objc-annotations:3.0.0=checkstyle,testRuntimeClasspath
com.googlecode.libphonenumber:libphonenumber:8.13.55=testRuntimeClasspath
com.itextpdf:barcodes:9.3.0=testRuntimeClasspath
com.itextpdf:bouncy-castle-connector:9.3.0=testRuntimeClasspath
com.itextpdf:commons:9.3.0=testRuntimeClasspath
com.itextpdf:font-asian:9.3.0=testRuntimeClasspath
com.itextpdf:forms:9.3.0=testRuntimeClasspath
com.itextpdf:hyph:9.3.0=testRuntimeClasspath
com.itextpdf:io:9.3.0=testRuntimeClasspath
com.itextpdf:itext-core:9.3.0=testRuntimeClasspath
com.itextpdf:kernel:9.3.0=testRuntimeClasspath
com.itextpdf:layout:9.3.0=testRuntimeClasspath
com.itextpdf:pdfa:9.3.0=testRuntimeClasspath
com.itextpdf:pdfua:9.3.0=testRuntimeClasspath
com.itextpdf:sign:9.3.0=testRuntimeClasspath
com.itextpdf:styled-xml-parser:9.3.0=testRuntimeClasspath
com.itextpdf:svg:9.3.0=testRuntimeClasspath
com.jayway.jsonpath:json-path:2.7.0=testRuntimeClasspath
com.jcraft:jzlib:1.1.3=testRuntimeClasspath
com.lmax:disruptor:3.4.4=testRuntimeClasspath
com.networknt:json-schema-validator:1.0.76=testRuntimeClasspath
com.nimbusds:content-type:2.3=testRuntimeClasspath
com.nimbusds:lang-tag:1.7=testRuntimeClasspath
com.nimbusds:nimbus-jose-jwt:10.4.2=testRuntimeClasspath
com.nimbusds:oauth2-oidc-sdk:11.28=testRuntimeClasspath
com.opencsv:opencsv:5.12.0=testRuntimeClasspath
com.puppycrawl.tools:checkstyle:10.19.0=checkstyle
com.samskivert:jmustache:1.15=testRuntimeClasspath
com.sun.istack:istack-commons-runtime:4.1.1=testRuntimeClasspath
com.sun.mail:mailapi:1.6.2=testRuntimeClasspath
com.sun.xml.bind:jaxb-core:4.0.1=testRuntimeClasspath
com.sun.xml.bind:jaxb-impl:4.0.1=testRuntimeClasspath
com.twelvemonkeys.common:common-image:3.11.0=testRuntimeClasspath
com.twelvemonkeys.common:common-io:3.11.0=testRuntimeClasspath
com.twelvemonkeys.common:common-lang:3.11.0=testRuntimeClasspath
com.twelvemonkeys.imageio:imageio-core:3.11.0=testRuntimeClasspath
com.twelvemonkeys.imageio:imageio-metadata:3.11.0=testRuntimeClasspath
com.twelvemonkeys.imageio:imageio-webp:3.11.0=testRuntimeClasspath
com.zaxxer:HikariCP:6.3.2=testRuntimeClasspath
com.zaxxer:SparseBitSet:1.3=testRuntimeClasspath
commons-beanutils:commons-beanutils:1.11.0=testRuntimeClasspath
commons-beanutils:commons-beanutils:1.9.4=checkstyle
commons-codec:commons-codec:1.15=checkstyle
commons-codec:commons-codec:1.18.0=testRuntimeClasspath
commons-collections:commons-collections:3.2.2=checkstyle,testRuntimeClasspath
commons-fileupload:commons-fileupload:1.5=testRuntimeClasspath
commons-io:commons-io:2.20.0=testRuntimeClasspath
commons-logging:commons-logging:1.3.5=testRuntimeClasspath
commons-net:commons-net:3.12.0=testRuntimeClasspath
info.picocli:picocli:4.7.6=checkstyle
io.github.classgraph:classgraph:4.8.154=testRuntimeClasspath
io.github.openfeign:feign-core:13.6=testRuntimeClasspath
io.github.openfeign:feign-form-spring:13.6=testRuntimeClasspath
io.github.openfeign:feign-form:13.6=testRuntimeClasspath
io.github.openfeign:feign-jackson:13.6=testRuntimeClasspath
io.github.openfeign:feign-slf4j:13.6=testRuntimeClasspath
io.micrometer:micrometer-commons:1.14.10=testCompileClasspath
io.micrometer:micrometer-commons:1.15.2=testRuntimeClasspath
io.micrometer:micrometer-observation:1.14.10=testCompileClasspath
io.micrometer:micrometer-observation:1.15.2=testRuntimeClasspath
io.netty:netty-buffer:4.1.86.Final=testRuntimeClasspath
io.netty:netty-codec-http2:4.1.86.Final=testRuntimeClasspath
io.netty:netty-codec-http:4.1.86.Final=testRuntimeClasspath
io.netty:netty-codec-socks:4.1.86.Final=testRuntimeClasspath
io.netty:netty-codec:4.1.86.Final=testRuntimeClasspath
io.netty:netty-common:4.1.86.Final=testRuntimeClasspath
io.netty:netty-handler-proxy:4.1.86.Final=testRuntimeClasspath
io.netty:netty-handler:4.1.86.Final=testRuntimeClasspath
io.netty:netty-resolver:4.1.86.Final=testRuntimeClasspath
io.netty:netty-tcnative-boringssl-static:2.0.56.Final=testRuntimeClasspath
io.netty:netty-tcnative-classes:2.0.56.Final=testRuntimeClasspath
io.netty:netty-transport-native-unix-common:4.1.86.Final=testRuntimeClasspath
io.netty:netty-transport:4.1.86.Final=testRuntimeClasspath
io.opentelemetry:opentelemetry-api:1.53.0=testRuntimeClasspath
io.opentelemetry:opentelemetry-common:1.53.0=testRuntimeClasspath
io.opentelemetry:opentelemetry-context:1.53.0=testRuntimeClasspath
io.opentelemetry:opentelemetry-exporter-logging:1.53.0=testRuntimeClasspath
io.opentelemetry:opentelemetry-sdk-common:1.53.0=testRuntimeClasspath
io.opentelemetry:opentelemetry-sdk-extension-autoconfigure-spi:1.53.0=testRuntimeClasspath
io.opentelemetry:opentelemetry-sdk-extension-autoconfigure:1.53.0=testRuntimeClasspath
io.opentelemetry:opentelemetry-sdk-logs:1.53.0=testRuntimeClasspath
io.opentelemetry:opentelemetry-sdk-metrics:1.53.0=testRuntimeClasspath
io.opentelemetry:opentelemetry-sdk-trace:1.53.0=testRuntimeClasspath
io.opentelemetry:opentelemetry-sdk:1.53.0=testRuntimeClasspath
io.projectreactor:reactor-core:3.7.8=testRuntimeClasspath
io.prometheus:simpleclient:0.16.0=testRuntimeClasspath
io.prometheus:simpleclient_common:0.16.0=testRuntimeClasspath
io.prometheus:simpleclient_httpserver:0.16.0=testRuntimeClasspath
io.prometheus:simpleclient_tracer_common:0.16.0=testRuntimeClasspath
io.prometheus:simpleclient_tracer_otel:0.16.0=testRuntimeClasspath
io.prometheus:simpleclient_tracer_otel_agent:0.16.0=testRuntimeClasspath
io.smallrye:jandex:3.1.2=testRuntimeClasspath
io.swagger.core.v3:swagger-annotations-jakarta:2.2.36=testRuntimeClasspath
io.swagger.core.v3:swagger-annotations:2.2.8=testRuntimeClasspath
io.swagger.core.v3:swagger-core-jakarta:2.2.36=testRuntimeClasspath
io.swagger.core.v3:swagger-core:2.2.8=testRuntimeClasspath
io.swagger.core.v3:swagger-models-jakarta:2.2.36=testRuntimeClasspath
io.swagger.core.v3:swagger-models:2.2.8=testRuntimeClasspath
io.swagger.parser.v3:swagger-parser-core:2.1.10=testRuntimeClasspath
io.swagger.parser.v3:swagger-parser-v2-converter:2.1.10=testRuntimeClasspath
io.swagger.parser.v3:swagger-parser-v3:2.1.10=testRuntimeClasspath
io.swagger.parser.v3:swagger-parser:2.1.10=testRuntimeClasspath
io.swagger:swagger-annotations:1.6.9=testRuntimeClasspath
io.swagger:swagger-compat-spec-parser:1.0.64=testRuntimeClasspath
io.swagger:swagger-core:1.6.9=testRuntimeClasspath
io.swagger:swagger-models:1.6.9=testRuntimeClasspath
io.swagger:swagger-parser:1.0.64=testRuntimeClasspath
jakarta.activation:jakarta.activation-api:2.1.3=testRuntimeClasspath
jakarta.annotation:jakarta.annotation-api:2.1.1=testRuntimeClasspath
jakarta.authentication:jakarta.authentication-api:3.0.0=testRuntimeClasspath
jakarta.authorization:jakarta.authorization-api:2.1.0=testRuntimeClasspath
jakarta.batch:jakarta.batch-api:2.1.1=testRuntimeClasspath
jakarta.ejb:jakarta.ejb-api:4.0.1=testRuntimeClasspath
jakarta.el:jakarta.el-api:5.0.1=testRuntimeClasspath
jakarta.enterprise:jakarta.enterprise.cdi-api:4.0.1=testRuntimeClasspath
jakarta.enterprise:jakarta.enterprise.lang-model:4.0.1=testRuntimeClasspath
jakarta.faces:jakarta.faces-api:4.0.1=testRuntimeClasspath
jakarta.inject:jakarta.inject-api:2.0.1=testRuntimeClasspath
jakarta.interceptor:jakarta.interceptor-api:2.1.0=testRuntimeClasspath
jakarta.jms:jakarta.jms-api:3.1.0=testRuntimeClasspath
jakarta.json.bind:jakarta.json.bind-api:3.0.0=testRuntimeClasspath
jakarta.json:jakarta.json-api:2.1.0=testRuntimeClasspath
jakarta.mail:jakarta.mail-api:2.1.4=testRuntimeClasspath
jakarta.persistence:jakarta.persistence-api:3.1.0=testRuntimeClasspath
jakarta.platform:jakarta.jakartaee-api:10.0.0=testRuntimeClasspath
jakarta.platform:jakarta.jakartaee-web-api:10.0.0=testRuntimeClasspath
jakarta.resource:jakarta.resource-api:2.1.0=testRuntimeClasspath
jakarta.security.enterprise:jakarta.security.enterprise-api:3.0.0=testRuntimeClasspath
jakarta.servlet.jsp.jstl:jakarta.servlet.jsp.jstl-api:3.0.0=testRuntimeClasspath
jakarta.servlet.jsp:jakarta.servlet.jsp-api:3.1.0=testRuntimeClasspath
jakarta.servlet:jakarta.servlet-api:6.0.0=testRuntimeClasspath
jakarta.transaction:jakarta.transaction-api:2.0.1=testRuntimeClasspath
jakarta.validation:jakarta.validation-api:3.1.1=testRuntimeClasspath
jakarta.websocket:jakarta.websocket-api:2.1.0=testRuntimeClasspath
jakarta.websocket:jakarta.websocket-client-api:2.1.0=testRuntimeClasspath
jakarta.ws.rs:jakarta.ws.rs-api:3.1.0=testRuntimeClasspath
jakarta.xml.bind:jakarta.xml.bind-api:4.0.0=testRuntimeClasspath
javax.annotation:javax.annotation-api:1.3.2=testRuntimeClasspath
javax.servlet:javax.servlet-api:4.0.1=testRuntimeClasspath
jline:jline:2.14.6=testRuntimeClasspath
joda-time:joda-time:2.10.5=testRuntimeClasspath
junit:junit:4.13.2=testRuntimeClasspath
net.bytebuddy:byte-buddy-agent:1.17.6=testCompileClasspath,testRuntimeClasspath
net.bytebuddy:byte-buddy:1.17.6=testCompileClasspath,testRuntimeClasspath
net.java.dev.jna:jna:5.13.0=testRuntimeClasspath
net.javacrumbs.json-unit:json-unit-core:2.36.0=testRuntimeClasspath
net.minidev:accessors-smart:2.5.2=testRuntimeClasspath
net.minidev:json-smart:2.5.2=testRuntimeClasspath
net.sf.jopt-simple:jopt-simple:5.0.4=testRuntimeClasspath
net.sf.saxon:Saxon-HE:12.5=checkstyle
org.antlr:antlr4-runtime:4.13.0=testRuntimeClasspath
org.antlr:antlr4-runtime:4.13.2=checkstyle
org.apache.commons:commons-collections4:4.5.0=testRuntimeClasspath
org.apache.commons:commons-compress:1.24.0=testRuntimeClasspath
org.apache.commons:commons-digester3:3.2=testRuntimeClasspath
org.apache.commons:commons-exec:1.5.0=testRuntimeClasspath
org.apache.commons:commons-imaging:1.0.0-alpha6=testRuntimeClasspath
org.apache.commons:commons-lang3:3.18.0=testRuntimeClasspath
org.apache.commons:commons-lang3:3.8.1=checkstyle
org.apache.commons:commons-math3:3.6.1=testRuntimeClasspath
org.apache.commons:commons-text:1.14.0=testRuntimeClasspath
org.apache.commons:commons-text:1.3=checkstyle
org.apache.httpcomponents.client5:httpclient5:5.1.3=checkstyle
org.apache.httpcomponents.client5:httpclient5:5.5=testCompileClasspath,testRuntimeClasspath
org.apache.httpcomponents.core5:httpcore5-h2:5.1.3=checkstyle
org.apache.httpcomponents.core5:httpcore5-h2:5.3.4=testCompileClasspath,testRuntimeClasspath
org.apache.httpcomponents.core5:httpcore5:5.1.3=checkstyle
org.apache.httpcomponents.core5:httpcore5:5.3.4=testCompileClasspath,testRuntimeClasspath
org.apache.httpcomponents:httpclient:4.5.13=checkstyle,testRuntimeClasspath
org.apache.httpcomponents:httpcore:4.4.13=testRuntimeClasspath
org.apache.httpcomponents:httpcore:4.4.14=checkstyle
org.apache.logging.log4j:log4j-api:2.24.3=testCompileClasspath,testRuntimeClasspath
org.apache.logging.log4j:log4j-to-slf4j:2.24.3=testCompileClasspath,testRuntimeClasspath
org.apache.maven.doxia:doxia-core:1.12.0=checkstyle
org.apache.maven.doxia:doxia-logging-api:1.12.0=checkstyle
org.apache.maven.doxia:doxia-module-xdoc:1.12.0=checkstyle
org.apache.maven.doxia:doxia-sink-api:1.12.0=checkstyle
org.apache.pdfbox:fontbox:3.0.5=testRuntimeClasspath
org.apache.pdfbox:pdfbox-io:3.0.5=testRuntimeClasspath
org.apache.pdfbox:pdfbox:3.0.5=testRuntimeClasspath
org.apache.poi:poi:5.4.1=testRuntimeClasspath
org.apache.santuario:xmlsec:3.0.6=testRuntimeClasspath
org.apache.tomcat.embed:tomcat-embed-el:10.1.43=testRuntimeClasspath
org.apache.velocity.tools:velocity-tools-generic:3.1=testRuntimeClasspath
org.apache.velocity:velocity-engine-core:2.3=testRuntimeClasspath
org.apache.velocity:velocity-engine-scripting:2.3=testRuntimeClasspath
org.apache.xbean:xbean-reflect:3.7=checkstyle
org.apiguardian:apiguardian-api:1.1.2=testCompileClasspath
org.aspectj:aspectjweaver:1.9.24=testRuntimeClasspath
org.assertj:assertj-core:4.0.0-M1=testCompileClasspath,testRuntimeClasspath
org.beryx:awt-color-factory:1.0.1=testRuntimeClasspath
org.beryx:text-io:3.4.1=testRuntimeClasspath
org.bouncycastle:bcpkix-jdk18on:1.72=testRuntimeClasspath
org.bouncycastle:bcprov-jdk18on:1.72=testRuntimeClasspath
org.bouncycastle:bcutil-jdk18on:1.72=testRuntimeClasspath
org.checkerframework:checker-qual:3.48.1=checkstyle
org.checkerframework:checker-qual:3.49.3=testRuntimeClasspath
org.codehaus.plexus:plexus-classworlds:2.6.0=checkstyle
org.codehaus.plexus:plexus-component-annotations:2.1.0=checkstyle
org.codehaus.plexus:plexus-container-default:2.1.0=checkstyle
org.codehaus.plexus:plexus-utils:3.3.0=checkstyle
org.codehaus.woodstox:stax2-api:4.2.2=testCompileClasspath,testRuntimeClasspath
org.eclipse.angus:angus-activation:2.0.0=testRuntimeClasspath
org.firebirdsql.***************************************
org.glassfish.jaxb:jaxb-core:4.0.2=testRuntimeClasspath
org.glassfish.jaxb:jaxb-runtime:4.0.2=testRuntimeClasspath
org.glassfish.jaxb:txw2:4.0.2=testRuntimeClasspath
org.hamcrest:hamcrest-core:2.2=testRuntimeClasspath
org.hamcrest:hamcrest:2.2=testRuntimeClasspath
org.hibernate.common:hibernate-commons-annotations:6.0.6.Final=testRuntimeClasspath
org.hibernate.javax.persistence:hibernate-jpa-2.1-api:1.0.2.Final=testRuntimeClasspath
org.hibernate.orm:hibernate-core:6.5.3.Final=testRuntimeClasspath
org.hibernate.validator:hibernate-validator:8.0.2.Final=testRuntimeClasspath
org.imgscalr:imgscalr-lib:4.2=testRuntimeClasspath
org.javassist:javassist:3.28.0-GA=checkstyle
org.jboss.logging:jboss-logging:3.5.0.Final=testRuntimeClasspath
org.jdom:jdom2:*******=testRuntimeClasspath
org.jetbrains.kotlin:kotlin-stdlib:2.2.10=testRuntimeClasspath
org.jetbrains:annotations:17.0.0=testRuntimeClasspath
org.jspecify:jspecify:1.0.0=testCompileClasspath,testRuntimeClasspath
org.junit.jupiter:junit-jupiter-api:6.0.0-RC2=testCompileClasspath,testRuntimeClasspath
org.junit.jupiter:junit-jupiter-engine:6.0.0-RC2=testRuntimeClasspath
org.junit.jupiter:junit-jupiter-params:6.0.0-RC2=testCompileClasspath,testRuntimeClasspath
org.junit.jupiter:junit-jupiter:6.0.0-RC2=testCompileClasspath,testRuntimeClasspath
org.junit.platform:junit-platform-commons:6.0.0-RC2=testCompileClasspath,testRuntimeClasspath
org.junit.platform:junit-platform-engine:6.0.0-RC2=testRuntimeClasspath
org.junit.platform:junit-platform-launcher:6.0.0-RC2=testRuntimeClasspath
org.junit:junit-bom:6.0.0-RC2=testCompileClasspath,testRuntimeClasspath
org.mapstruct:mapstruct:1.6.3=testRuntimeClasspath
org.mock-server:mockserver-client-java:5.15.0=testRuntimeClasspath
org.mock-server:mockserver-core:5.15.0=testRuntimeClasspath
org.mockito:mockito-core:5.19.0=testCompileClasspath,testRuntimeClasspath
org.mozilla:rhino:1.7.7.2=testRuntimeClasspath
org.objenesis:objenesis:3.3=testRuntimeClasspath
org.opentest4j:opentest4j:1.3.0=testCompileClasspath,testRuntimeClasspath
org.ow2.asm:asm:9.7.1=testRuntimeClasspath
org.postgresql:postgresql:42.7.7=testRuntimeClasspath
org.projectlombok:lombok:1.18.40=annotationProcessor,compileClasspath,testAnnotationProcessor,testCompileClasspath
org.reactivestreams:reactive-streams:1.0.4=testRuntimeClasspath
org.reflections:reflections:0.10.2=checkstyle
org.rnorth.duct-tape:duct-tape:1.0.8=testRuntimeClasspath
org.seleniumhq.selenium:selenium-api:4.35.0=testRuntimeClasspath
org.seleniumhq.selenium:selenium-chrome-driver:4.35.0=testRuntimeClasspath
org.seleniumhq.selenium:selenium-chromium-driver:4.35.0=testRuntimeClasspath
org.seleniumhq.selenium:selenium-http:4.35.0=testRuntimeClasspath
org.seleniumhq.selenium:selenium-json:4.35.0=testRuntimeClasspath
org.seleniumhq.selenium:selenium-manager:4.35.0=testRuntimeClasspath
org.seleniumhq.selenium:selenium-os:4.35.0=testRuntimeClasspath
org.seleniumhq.selenium:selenium-remote-driver:4.35.0=testRuntimeClasspath
org.seleniumhq.selenium:selenium-support:4.35.0=testRuntimeClasspath
org.slf4j:jul-to-slf4j:2.0.17=testCompileClasspath,testRuntimeClasspath
org.slf4j:slf4j-api:2.0.17=testCompileClasspath
org.slf4j:slf4j-api:2.1.0-alpha1=testRuntimeClasspath
org.springdoc:springdoc-openapi-starter-common:2.8.10=testRuntimeClasspath
org.springframework.boot:spring-boot-actuator:3.5.5=testRuntimeClasspath
org.springframework.boot:spring-boot-autoconfigure:3.5.4=testRuntimeClasspath
org.springframework.boot:spring-boot-starter-aop:3.5.5=testRuntimeClasspath
org.springframework.boot:spring-boot-starter-logging:3.5.5=testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-validation:3.5.4=testRuntimeClasspath
org.springframework.boot:spring-boot-starter:3.5.5=testRuntimeClasspath
org.springframework.boot:spring-boot:3.5.4=testCompileClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-commons:4.3.0=testRuntimeClasspath
org.springframework.cloud:spring-cloud-context:4.3.0=testRuntimeClasspath
org.springframework.cloud:spring-cloud-openfeign-core:4.3.0=testRuntimeClasspath
org.springframework.cloud:spring-cloud-starter-openfeign:4.3.0=testRuntimeClasspath
org.springframework.cloud:spring-cloud-starter:4.3.0=testRuntimeClasspath
org.springframework.data:spring-data-commons:3.5.3=testRuntimeClasspath
org.springframework.data:spring-data-jpa:3.5.3=testRuntimeClasspath
org.springframework.integration:spring-integration-core:6.5.1=testRuntimeClasspath
org.springframework.retry:spring-retry:2.0.12=testRuntimeClasspath
org.springframework.security:spring-security-config:6.5.3=testRuntimeClasspath
org.springframework.security:spring-security-core:6.5.3=testRuntimeClasspath
org.springframework.security:spring-security-crypto:6.5.3=testRuntimeClasspath
org.springframework.security:spring-security-oauth2-core:6.5.3=testRuntimeClasspath
org.springframework.security:spring-security-oauth2-jose:6.5.3=testRuntimeClasspath
org.springframework.security:spring-security-web:6.5.3=testRuntimeClasspath
org.springframework:spring-aop:6.2.10=testCompileClasspath,testRuntimeClasspath
org.springframework:spring-beans:6.2.10=testCompileClasspath,testRuntimeClasspath
org.springframework:spring-context-support:6.2.10=testRuntimeClasspath
org.springframework:spring-context:6.2.10=testCompileClasspath,testRuntimeClasspath
org.springframework:spring-core:6.2.10=testCompileClasspath,testRuntimeClasspath
org.springframework:spring-expression:6.2.10=testCompileClasspath,testRuntimeClasspath
org.springframework:spring-jcl:6.2.10=testCompileClasspath,testRuntimeClasspath
org.springframework:spring-jdbc:6.2.10=testCompileClasspath,testRuntimeClasspath
org.springframework:spring-messaging:6.2.9=testRuntimeClasspath
org.springframework:spring-orm:6.2.10=testRuntimeClasspath
org.springframework:spring-tx:6.2.10=testCompileClasspath,testRuntimeClasspath
org.springframework:spring-web:6.2.10=testCompileClasspath,testRuntimeClasspath
org.springframework:spring-webmvc:6.2.10=testRuntimeClasspath
org.testcontainers:junit-jupiter:1.21.3=testRuntimeClasspath
org.testcontainers:mockserver:1.21.3=testRuntimeClasspath
org.testcontainers:selenium:1.20.6=testRuntimeClasspath
org.testcontainers:testcontainers:1.21.3=testRuntimeClasspath
org.xmlresolver:xmlresolver:5.2.2=checkstyle
org.xmlunit:xmlunit-core:2.9.1=testRuntimeClasspath
org.xmlunit:xmlunit-placeholders:2.9.1=testRuntimeClasspath
org.yaml:snakeyaml:2.4=testRuntimeClasspath
empty=runtimeClasspath
