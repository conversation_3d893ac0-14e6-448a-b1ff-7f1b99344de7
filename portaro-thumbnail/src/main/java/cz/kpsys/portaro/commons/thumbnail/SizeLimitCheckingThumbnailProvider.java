package cz.kpsys.portaro.commons.thumbnail;

import cz.kpsys.portaro.commons.file.FileUtils;
import cz.kpsys.portaro.file.IdentifiedFile;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SizeLimitCheckingThumbnailProvider implements ThumbnailProvider {

    private static final long DEFAULT_MAX_BLOB_SIZE = FileUtils.mBToB(20);

    @NonFinal long maxFileSize = DEFAULT_MAX_BLOB_SIZE;
    @NonNull ThumbnailProvider target;

    public SizeLimitCheckingThumbnailProvider withMaxFileSize(long maxFileSize) {
        this.maxFileSize = maxFileSize;
        return this;
    }

    @Override
    public Thumbnail getThumbnail(IdentifiedFile file) {
        if (file.getSize() != null && file.getSize() > maxFileSize) {
            throw new ThumbnailSizeLimitExceededException(file.getSize(), maxFileSize);
        }
        return target.getThumbnail(file);
    }

}
