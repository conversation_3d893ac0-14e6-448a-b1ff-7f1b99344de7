dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation("org.assertj:assertj-core:+")
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")
    implementation("org.mapstruct:mapstruct:+")
    annotationProcessor("org.mapstruct:mapstruct-processor:+")

    implementation(project(":portaro-appserver"))
    implementation(project(":portaro-auth"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-commons-db"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-file"))
    implementation(project(":portaro-finance"))
    implementation(project(":portaro-form"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-form-config"))
    implementation(project(":portaro-licence"))
    implementation(project(":portaro-payment"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-sql-generator"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-user-preferences"))
    implementation(project(":portaro-web"))

    implementation("org.springframework:spring-context:6.+")
    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework.security:spring-security-crypto:6.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.+")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("com.google.guava:guava:+")
    implementation("org.jdom:jdom2:2.+")
}
