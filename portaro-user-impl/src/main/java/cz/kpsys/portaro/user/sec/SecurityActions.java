package cz.kpsys.portaro.user.sec;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.search.facet.FacetType;
import cz.kpsys.portaro.security.Action;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import lombok.NonNull;

public class SecurityActions {

    //auth
    public static final Action<BasicUser> AUTH_SWITCH_ACTIVE_USER = Action.withSubject("AuthSwitchActiveUser", BasicUser.class);


    public static final Action<Void> VISITED_DOCUMENTS_USE = Action.withoutSubject("VisitedDocumentsUse");
    public static final Action<Void> STATS_SHOW = Action.withoutSubject("StatsShow");
    public static final Action<Void> UTIL_SHOW = Action.withoutSubject("UtilShow");
    public static final Action<@NonNull String> SERVICE_COMMAND_RUN = Action.withSubject("ServiceCommandRun", String.class);
    public static final Action<Void> SERVICE_SQL_QUERY_RUN = Action.withoutSubject("ServiceSqlQueryRun");
    public static final Action<Void> LOG_SHOW = Action.withoutSubject("LogShow");
    public static final Action<Void> SYSTEM_GC = Action.withoutSubject("SystemGc");
    public static final Action<Void> SYSTEM_RESTART = Action.withoutSubject("SystemRestart");
    public static final Action<Void> SYSTEM_UPDATE = Action.withoutSubject("SystemUpdate");
    public static final Action<Void> DATA_COPY = Action.withoutSubject("DataCopy");
    public static final Action<Void> SETTINGS_EDIT = Action.withoutSubject("SettingsEdit");
    public static final Action<Void> LOCALIZATION_EDIT = Action.withoutSubject("LocalizationEdit");
    public static final Action<Void> DESIGN_EDIT = Action.withoutSubject("DesignEdit");
    public static final Action<Void> FACET_TYPE_EDIT_ANY = Action.withoutSubject("FacetTypeEditAny");
    public static final Action<FacetType> FACET_TYPE_EDIT = Action.withSubject("FacetTypeEdit", FacetType.class);
    public static final Action<Void> FACET_TYPE_CREATE = Action.withoutSubject("FacetTypeCreate");
    public static final Action<Void> MEDIA_VIEWER_EDIT = Action.withoutSubject("MediaViewerEdit");

    //acquisition
    public static final Action<Void> TENDERS_USE = Action.withoutSubject("TendersUse");

    //placement (= budovy, pujcovny, lokace)
    public static final Action<Void> PLACEMENTS_SHOW = Action.withoutSubject("PlacementsShow");
    public static final Action<Void> PLACEMENT_CREATE = Action.withoutSubject("PlacementsCreate");
    public static final Action<Location> LOCATION_EDIT = Action.withSubject("LocationEdit", Location.class);
    public static final Action<Location> LOCATION_DELETE = Action.withSubject("LocationDelete", Location.class);
    public static final Action<Department> DEPARTMENT_EDIT = Action.withSubject("DepartmentEdit", Department.class);
    public static final Action<Department> DEPARTMENT_DELETE = Action.withSubject("DepartmentDelete", Department.class);

    //users
    public static final Action<Void> USERS_SHOW = Action.withoutSubject("UsersShow");
    public static final Action<Department> USERS_SHOW_OF_DEPARTMENT = Action.withSubject("UsersShowOfDepartment", Department.class);
    public static final Action<BasicUser> USER_SHOW = Action.withSubject("UserShow", BasicUser.class);
    public static final Action<Void> USER_CREATE = Action.withoutSubject("UserCreate");
    public static final Action<BasicUser> USER_EDIT = Action.withSubject("UserEdit", BasicUser.class);
    public static final Action<BasicUser> USER_DELETE = Action.withSubject("UserDelete", BasicUser.class);
    public static final Action<User> USER_REGISTRATION_PERIOD_EXTEND = Action.withSubject("UserRegistrationPeriodExtend", User.class);
    public static final Action<User> USER_REGISTRATION_AGREEMENT_PRINT = Action.withSubject("UserRegistrationAgreementPrint", User.class);
    public static final Action<User> USER_PRINT = Action.withSubject("UserPrint", User.class);
    public static final Action<BasicUser> USER_PREFERENCES_EDIT = Action.withSubject("UserPreferencesEdit", BasicUser.class);
    public static final Action<BasicUser> USER_PERSONAL_DATA_EVIDENCE_EXPORT = Action.withSubject("UserPersonalDataEvidenceExport", BasicUser.class);
    public static final Action<Void> SOFTWARE_USER_CREATE = Action.withoutSubject("SoftwareUserCreate");
    public static final Action<Void> SEND_EMAIL_TO_USER = Action.withoutSubject("SendEmailToUser");
    public static final Action<Void> SEND_SMS_TO_USER = Action.withoutSubject("SendSmsToUser");
    public static final Action<Void> SHOW_LIBRARIAN_MESSAGE = Action.withoutSubject("ShowLibrarianMessage");

    //utils
    public static final Action<Void> ACTIVE_CLIENT_SESSIONS_SHOW = Action.withoutSubject("ActiveClientSessions");

    //sync
    public static final Action<Void> FOREIGN_SYSTEM_SYNCHRONIZATION_RUN = Action.withoutSubject("ForeignSystemSynchronization");

}
