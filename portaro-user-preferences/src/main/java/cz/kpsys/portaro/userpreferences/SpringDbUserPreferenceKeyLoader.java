package cz.kpsys.portaro.userpreferences;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.SpringDbLabeledIdentifiableLoader;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.datatype.Structure;
import cz.kpsys.portaro.form.editor.ValueEditorByDatatypeLoader;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditor;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;

import static cz.kpsys.portaro.databasestructure.UserPreferenceDb.USER_PREFS_KEYS.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbUserPreferenceKeyLoader extends SpringDbLabeledIdentifiableLoader<UserPreferenceKey, Integer> {

    @NonNull DatatypableStringConverter datatypableStringConverter;
    @NonNull ValueEditorByDatatypeLoader valueEditorByDatatypeLoader;
    @NonNull ByIdLoadable<UserPreferenceKeyPermission, Integer> userPreferenceKeyPermissionLoader;

    public SpringDbUserPreferenceKeyLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                           @NonNull QueryFactory queryFactory,
                                           @NonNull DatatypableStringConverter datatypableStringConverter,
                                           @NonNull ValueEditorByDatatypeLoader valueEditorByDatatypeLoader,
                                           @NonNull ByIdLoadable<UserPreferenceKeyPermission, Integer> userPreferenceKeyPermissionLoader) {
        super(jdbcTemplate, queryFactory, USER_PREFS_KEYS, ID_KEY, NAME);
        this.datatypableStringConverter = datatypableStringConverter;
        this.valueEditorByDatatypeLoader = valueEditorByDatatypeLoader;
        this.userPreferenceKeyPermissionLoader = userPreferenceKeyPermissionLoader;
    }

    @Override
    public UserPreferenceKey createObject(Integer id, String textRaw, ResultSet rs) throws SQLException {
        String defaultValueString = StringUtil.notNullString(rs.getString(DEFVAL));
        boolean autoGenerated = rs.getBoolean(AUTO_GENERATED);
        String datatypeName = rs.getString(DATATYPE);
        Structure structure = Structure.valueOf(rs.getString(STRUCTURE));

        Datatype datatype = Datatype.of(datatypeName, structure);
        Object defaultValue = datatypableStringConverter.convertFromString(defaultValueString, datatype);

        UserPreferenceKeyPermission readerPermission = userPreferenceKeyPermissionLoader.getById(rs.getInt(PRAVA_CTENAR));
        UserPreferenceKeyPermission librarianPermission = userPreferenceKeyPermissionLoader.getById(rs.getInt(PRAVA_KNIHOVNIK));
        UserPreferenceKeyPermission personPermission = userPreferenceKeyPermissionLoader.getById(rs.getInt(PRAVA_OSOBA));

        ValueEditor<?, ?, ?> editor = valueEditorByDatatypeLoader.getEditor(datatype)
                .orElseGet(TextValueEditor::getEmptyEditor)
                .withEditorName("user-preferences-key-%s".formatted(id));

        return new UserPreferenceKey(id, textRaw, defaultValue, autoGenerated, datatype, structure, editor, readerPermission, librarianPermission, personPermission);
    }

}
