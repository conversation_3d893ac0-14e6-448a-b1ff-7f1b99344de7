package cz.kpsys.portaro.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum Gender implements Identified<String> {
    MALE("male"), FEMALE("female"), OTHER("other");

    public static final Codebook<Gender, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String id;

    @Override
    public String toString() {
        return id;
    }
}
