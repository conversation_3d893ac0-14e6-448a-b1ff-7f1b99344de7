package cz.kpsys.portaro.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;

import static cz.kpsys.portaro.user.BasicUser.createNewUserId;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
@FieldNameConstants
public class Software extends User implements NativeNamedUser {

    @Deprecated
    final String kind = "SOFTWARE";

    @NullableNotBlank
    String name;

    @NonNull
    Boolean webCrawler;

    public Software(@NonNull Integer id,
                    @NullableNotBlank String name,
                    @NonNull Boolean webCrawler) {
        super(id);
        this.name = name;
        this.webCrawler = webCrawler;
    }

    public static Software createNew() {
        return new Software(createNewUserId(), null, false);
    }

    @JsonIgnore
    @Override
    protected @NonNull String getPrettyName() {
        return name;
    }

    @NullableNotBlank
    @JsonIgnore
    @Override
    public String getNativeName() {
        return name;
    }
}
