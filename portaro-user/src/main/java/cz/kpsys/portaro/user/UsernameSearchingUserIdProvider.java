package cz.kpsys.portaro.user;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.StaticParamsModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UsernameSearchingUserIdProvider implements Provider<Integer> {

    @NonNull ParameterizedSearchLoader<MapBackedParams, Integer> userIdSearchLoader;
    @NonNull String username;

    @Override
    public Integer get() {
        return userIdSearchLoader.getOne("User id", StaticParamsModifier.of(UserConstants.SearchParams.USERNAME, username));
    }
}
