package cz.kpsys.portaro.user.contact;

import cz.kpsys.portaro.commons.util.StringUtil;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;


public record BirthNumber(
        @Nullable
        Integer id,

        Integer userId,

        @NonNull
        ContactType type,

        @NonNull
        @NotBlank
        String value,

        @NonNull
        SourceOfData source

) implements Contact {

    public BirthNumber {
        Assert.state(type.isBirthNumber(), () -> "Contact type must be payment account, but is " + type);
        value = StringUtil.assertHasTrimmedLength(value);
    }

    public BirthNumber(Integer id, Integer userId, @NonNull String value, @NonNull SourceOfData source) {
        this(id, userId, ContactType.BIRTH_NUMBER, value, source);
    }

    @Override
    public String toString() {
        return source + " " + type + " " + value;
    }
}
