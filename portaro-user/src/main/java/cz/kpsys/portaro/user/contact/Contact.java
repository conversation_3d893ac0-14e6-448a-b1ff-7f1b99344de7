package cz.kpsys.portaro.user.contact;

import cz.kpsys.portaro.commons.geo.Country;
import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.object.ValuableRecord;
import cz.kpsys.portaro.commons.util.ListUtil;
import jakarta.validation.Valid;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

public sealed interface Contact extends IdentifiedRecord<Integer>, ValuableRecord<String>, Sourceable permits BirthNumber, EmailContact, IdCard, PaymentAccount, PhoneContact {

    int PROPERTY_VALUE_MAX_LENGTH = 80;

    static Contact createExisting(@NonNull Integer id, @NonNull Integer userId, @NonNull ContactType type, @NonNull String value, @NonNull SourceOfData source, @Nullable LocalDate validityEndDate, Country issuingCountry, @Nullable String issuer, @Nullable LocalDate issueDate, String paymentAccountCurrency) {
        if (type.isEmail()) {
            return new EmailContact(id, userId, value, source, validityEndDate);
        } else if (type.isPhone()) {
            return new PhoneContact(id, userId, type, value, source, validityEndDate);
        } else if (type.isPaymentAccount()) {
            return new PaymentAccount(id, userId, value, source, paymentAccountCurrency);
        } else if (type.isIdCard()) {
            return new IdCard(id, userId, type, value, source, validityEndDate, issuingCountry, issuer, issueDate);
        } else if (type.isBirthNumber()) {
            return new BirthNumber(id, userId, value, source);
        }
        throw new IllegalArgumentException("Unsupported contact type: " + type);
    }

    static Contact createNewEmail(Integer userId, String value, SourceOfData source) {
        return new EmailContact(null, userId, value, source, null);
    }

    static Contact createNewPhone(Integer userId, ContactType type, String value, SourceOfData source) {
        return new PhoneContact(null, userId, type, value, source, null);
    }

    static Contact createNewSmsPhone(Integer userId, String value, SourceOfData source) {
        return new PhoneContact(null, userId, ContactType.SMS_PHONE, value, source, null);
    }

    static Contact createNewIdCard(Integer userId, ContactType type, String value, SourceOfData source, LocalDate validityEndDate, Country issuingCountry, String issuer, LocalDate issueDate) {
        return new IdCard(null, userId, type, value, source, validityEndDate, issuingCountry, issuer, issueDate);
    }

    static Contact createNewPaymentAccount(Integer userId, String value, SourceOfData source, String paymentAccountCurrency) {
        return new PaymentAccount(null, userId, value, source, paymentAccountCurrency);
    }

    static Contact createNewBirthNumber(Integer userId, String value, SourceOfData source) {
        return new BirthNumber(null, userId, value, source);
    }


    @NonNull
    Integer userId();

    @NonNull
    @Valid
    SourceOfData source();

    @NonNull
    ContactType type();

    /**
     * Compares contacts based on values (ignoring different ids and user ids)
     */
    default boolean typeAndValueEquals(@NonNull Contact c) {
        return Objects.equals(type(), c.type()) && Objects.equals(getValue(), c.getValue());
    }

    default void overwriteOrAppendTo(List<Contact> list) {
        ListUtil.mergeOrAppend(list, this::typeAndValueEquals, this, existingContact -> this);
    }

    default void skipOrAppendTo(List<Contact> list) {
        ListUtil.mergeOrAppend(list, this::typeAndValueEquals, this, existingContact -> existingContact);
    }

}
