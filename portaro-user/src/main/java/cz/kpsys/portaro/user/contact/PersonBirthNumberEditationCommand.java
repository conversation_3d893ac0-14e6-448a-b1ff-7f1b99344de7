package cz.kpsys.portaro.user.contact;

import cz.kpsys.portaro.commons.object.ValuableRecord;
import jakarta.validation.constraints.NotNull;
import lombok.NonNull;
import lombok.With;
import lombok.experimental.FieldNameConstants;

@With
@FieldNameConstants
public record PersonBirthNumberEditationCommand(

        @NonNull
        String value,

        @NotNull
        SourceOfData source

) implements ValuableRecord<String>, SourceChangeable<PersonBirthNumberEditationCommand> {

}
