package cz.kpsys.portaro.user.matcher;

import cz.kpsys.portaro.matcher.AbstractMatcher;
import cz.kpsys.portaro.user.AuthableUser;
import cz.kpsys.portaro.user.role.editor.EditorAccount;

public class EditorAllowingMatcher<E extends Access<?>> extends AbstractMatcher<E> {

    public EditorAllowingMatcher() {
        super("Logged user have editor role");
    }

    @Override
    public boolean matches(E item) {
        AuthableUser initiator = item.getUser();
        return initiator.hasRole(EditorAccount.class);
    }
    
}
