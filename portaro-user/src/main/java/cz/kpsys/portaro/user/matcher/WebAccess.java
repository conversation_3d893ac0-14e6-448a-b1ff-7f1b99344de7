package cz.kpsys.portaro.user.matcher;

import cz.kpsys.portaro.user.AuthableUser;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WebAccess<ITEM> extends Access<ITEM> {

    @NonNull HttpServletRequest request;

    public WebAccess(@NonNull AuthableUser user, @NonNull ITEM item, @NonNull HttpServletRequest request) {
        super(user, item);
        this.request = request;
    }

    public @NonNull HttpServletRequest getRequest() {
        return request;
    }
}
