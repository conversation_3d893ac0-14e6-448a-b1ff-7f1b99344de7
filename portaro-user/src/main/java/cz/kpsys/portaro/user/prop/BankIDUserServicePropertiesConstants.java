package cz.kpsys.portaro.user.prop;

public final class BankIDUserServicePropertiesConstants {

    public static final String BANKID_SERVICE = "bankid";
    public static final String BANKID_ID = "id";
    public static final String BANKID_TXN = "txn";
    public static final String BANKID_PERSON_MAJORITY  = "majority";
    public static final String BANKID_PERSON_BIRTHPLACE  = "birthplace";
    public static final String BANKID_PERSON_BIRTH_COUNTRY  = "birthCountry";
    public static final String BANKID_PERSON_NATIONALITIES  = "nationalities";
    public static final String BANKID_PERSON_MARITAL_STATUS  = "maritalStatus";
    public static final String BANKID_PERSON_PEP  = "pep";
    public static final String BANKID_PERSON_LIMITED_LEGAL_CAPACITY  = "limitedLegalCapacity";
    public static final String BANKID_PERSON_UPDATED_AT  = "updatedAt";
    public static final String BANKID_OAUTH2_REFRESH_TOKEN  = "refreshToken";
}
