package cz.kpsys.portaro.user.prop;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.user.User;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.ConversionService;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserServicePropertyHelper {

    public static final String SERVICE_GLOBAL = "global";

    @NonNull UserServicePropertyLoader userServicePropertyLoader;
    @NonNull Saver<UserServiceProperty, ?> userServicePropertySaver;
    @NonNull ConversionService conversionService;

    @NonNull
    public String getPropertyOrCreateNew(@NonNull String service,
                                         @NonNull Integer userId,
                                         @NonNull String propertyName,
                                         @NonNull Supplier<String> newValueProvider) {
        List<UserServiceProperty> userProperties = userServicePropertyLoader.getAllByServiceAndUserId(service, userId);
        Optional<UserServiceProperty> existing = userProperties.stream()
                .filter(userServiceProperty -> userServiceProperty.name().equals(propertyName))
                .findFirst();
        if (existing.isPresent()) {
            return existing.get().value();
        }

        UserServiceProperty newProperty = UserServiceProperty.withoutValidity(
                UuidGenerator.forIdentifier(),
                userId,
                service,
                propertyName,
                newValueProvider.get()
        );
        userServicePropertySaver.save(newProperty);
        return newProperty.value();
    }

    @NonNull
    public Optional<String> searchForPropValue(@NonNull User user,
                                               @NonNull String service,
                                               @NonNull String propertyName) {
        return searchForPropStream(user, service, propertyName)
                .map(UserServiceProperty::value)
                .findFirst();
    }

    @NonNull
    public <T> Optional<T> searchForPropValueOfType(@NonNull User user,
                                                         @NonNull String service,
                                                         @NonNull String propertyName,
                                                         @NonNull Class<T> type) {
        return searchForPropStream(user, service, propertyName)
                .map(UserServiceProperty::value)
                .findFirst()
                .map(value -> conversionService.convert(value, type));
    }

    @NonNull
    public <T> Optional<T> searchForValidPropValueOfType(@NonNull User user,
                                                    @NonNull String service,
                                                    @NonNull String propertyName,
                                                    @NonNull Class<T> type) {
        return searchForPropStream(user, service, propertyName)
                .filter(UserServicePropertyHelper::nowIsBeforeValidityEnd)
                .map(UserServiceProperty::value)
                .findFirst()
                .map(value -> conversionService.convert(value, type));
    }

    private static @NonNull Stream<@Valid UserServiceProperty> searchForPropStream(@NonNull User user, @NonNull String service, @NonNull String propertyName) {
        return user.getUserServiceProperties().stream()
                .filter(userServiceProperty -> userServiceProperty.service().equals(service))
                .filter(userServiceProperty -> userServiceProperty.name().equals(propertyName));
    }

    private static boolean nowIsBeforeValidityEnd(@NonNull UserServiceProperty userServiceProperty) {
        if (userServiceProperty.validityEndDate() == null) {
            return false;
        }
        return Instant.now().isBefore(userServiceProperty.validityEndDate());
    }

}
