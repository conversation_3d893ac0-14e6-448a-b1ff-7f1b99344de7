package cz.kpsys.portaro.user.provider;

import cz.kpsys.portaro.FieldsNotFilledValidationException;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.StringPairUserProvider;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByContextualIdentifierLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FirstEntryOnlyDelegatingStringPairUserProvider implements StringPairUserProvider<Department> {

    @NonNull UserByContextualIdentifierLoader<User> userByFirstFieldLoader;

    @Override
    public Optional<? extends User> get(Department ctx, String cn, String ignored) {
        FieldsNotFilledValidationException.throwIfNullOrEmpty(cn);
        return userByFirstFieldLoader.getByCtxAndIdentifier(ctx, cn);
    }
    
}