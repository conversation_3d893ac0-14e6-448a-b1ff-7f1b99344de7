package cz.kpsys.portaro.user.relation;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

public class UserRelationsToEntitiesConverter implements Converter<List<UserRelation>, List<UserRelationEntity>> {

    @Override
    public List<UserRelationEntity> convert(@NonNull List<UserRelation> source) {
        return ListUtil.convertStrict(source, userRelation -> new UserRelationEntity(
                userRelation.getSource().getId().longValue(),
                userRelation.getTarget().getId().longValue(),
                userRelation.getRelationType().getId()
        ));
    }
}
