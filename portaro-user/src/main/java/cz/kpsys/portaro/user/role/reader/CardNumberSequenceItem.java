package cz.kpsys.portaro.user.role.reader;

import cz.kpsys.portaro.commons.object.IdSettable;
import cz.kpsys.portaro.commons.util.SorterStringBuilder;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.sequence.SequenceItem;
import cz.kpsys.portaro.sequence.SequenceItemWithBody;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class CardNumberSequenceItem extends SequenceItemWithBody implements SequenceItem, IdSettable<Long> {

    @Setter
    @NonFinal
    Long id;
    @Getter
    String prefix;
    @Getter
    int bodyLength;

    public CardNumberSequenceItem(long id, long body, String prefix, int bodyLength, boolean previousValueKnown) {
        super(body, previousValueKnown);
        this.id = id;
        this.prefix = prefix == null ? "" : prefix;
        this.bodyLength = bodyLength;
    }


    @Override
    public Long getId() {
        return id;
    }


    @Override
    public String getValue() {
        return prefix + getFilledBody();
    }


    @Override
    public CardNumberSequenceItem next() {
        return new CardNumberSequenceItem(id, body + 1, prefix, bodyLength, true);
    }


    @Override
    public String getSorter() {
        return SorterStringBuilder.fillToSize(prefix, getFilledBody(), "");
    }


    private String getFilledBody() {
        String filledBody = getBody() + "";
        if (bodyLength > 0) {
            filledBody = StringUtil.prependToSize(filledBody, '0', bodyLength);
        }
        return filledBody;
    }

    @Override
    public String toString() {
        return "CardNumberSequenceItem %s id=%s prefix=%s body=%s".formatted(getValue(), id, prefix, body);
    }
}
