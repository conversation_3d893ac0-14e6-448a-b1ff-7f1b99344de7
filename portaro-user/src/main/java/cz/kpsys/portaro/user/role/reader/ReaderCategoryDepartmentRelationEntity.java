package cz.kpsys.portaro.user.role.reader;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.databasestructure.UserDb;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD.ID;

@Entity
@Table(name = UserDb.DEF_CTENKAT_BUD.TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ReaderCategoryDepartmentRelationEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = UserDb.DEF_CTENKAT_BUD.FK_CTENKAT)
    @NotNull
    String readerCategoryId;

    @Column(name = UserDb.DEF_CTENKAT_BUD.FK_PUJC)
    @NotNull
    Integer departmentId;
}
