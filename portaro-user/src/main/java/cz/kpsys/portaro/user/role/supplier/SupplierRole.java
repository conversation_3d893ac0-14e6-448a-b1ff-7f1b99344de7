package cz.kpsys.portaro.user.role.supplier;

import cz.kpsys.portaro.user.UserRole;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class SupplierRole implements UserRole {

    @EqualsAndHashCode.Include
    @NonNull
    Integer id;

    @NonNull
    @Getter
    Integer userId;

    public static SupplierRole create(@NonNull Integer id, @NonNull Integer userId) {
        return new SupplierRole(id, userId);
    }

    @Override
    public String toString() {
        return "SupplierRole_%s".formatted(id);
    }

}
