package cz.kpsys.portaro.util.logging;

import cz.kpsys.portaro.commons.ip.IpAddress;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.web.client.ClientRequest;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import lombok.experimental.NonFinal;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.List;

@Value
@With
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ClientSession implements Identified<String> {

    @EqualsAndHashCode.Include
    @NonNull
    String id;

    @Nullable
    String httpSessionId;

    @NonNull
    Instant creationDate;

    @Setter
    @NonFinal
    Instant endDate;

    @NonNull
    IpAddress ipAdress;

    @NonNull
    Boolean bot;

    @NonNull
    String userAgent;

    @NonNull
    Boolean internalAccess;

    @NotEmpty
    @NonNull
    List<ClientRequest> requestEvents;

    @NonNull
    Department currentDepartment;

    public ClientSession withAddedEvent(@NonNull ClientRequest event) {
        return this.withRequestEvents(ListUtil.createNewListAppending(requestEvents, event));
    }

}
