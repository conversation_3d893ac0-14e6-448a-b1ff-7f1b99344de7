package cz.kpsys.portaro.util.logging;

import cz.kpsys.portaro.web.bot.BotFinder;
import cz.kpsys.portaro.web.client.WebClientRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;


@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ClientSessionByClientRequestUpdater {
    @NonNull ClientSessionRepository clientSessionRepository;
    @NonNull BotFinder botFinder;

    public void verifySession(WebClientRequest clientRequest, boolean isBotFromClientTest) {

        ClientSession currentSession = clientRequest.getHttpSessionId()
                .flatMap(clientSessionRepository::getByHttpSessionId)
                .orElseThrow(() -> new IllegalStateException("Session must exist to be verified"));

        boolean isBotFromServerTest = !botFinder.isProbablyHuman(clientRequest);

        clientSessionRepository.save(currentSession.withBot(isBotFromServerTest || isBotFromClientTest));
    }
}
