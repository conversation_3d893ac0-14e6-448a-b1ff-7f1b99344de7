dependencies {
    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")
    testImplementation("org.apache.commons:commons-lang3:3.+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-auth"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-exemplar"))
    implementation(project(":portaro-form"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-form-config"))
    implementation(project(":portaro-loan"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-verbisboxer-manager"))
    implementation(project(":portaro-web"))
    implementation(project(":portaro-security"))

    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-context:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework.security:spring-security-core:6.+")
    implementation("org.springframework.boot:spring-boot:3.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("org.hibernate.orm:hibernate-core:6.5.+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("com.github.kagkarlsson:db-scheduler-spring-boot-starter:+")
    implementation("io.swagger.core.v3:swagger-annotations-jakarta:+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
}
