# This is a Gradle generated file for dependency locking.
# Manual edits can break the build and are not advised.
# This file is expected to be part of source control.
ch.qos.logback:logback-classic:1.5.18=runtimeClasspath,testRuntimeClasspath
ch.qos.logback:logback-core:1.5.18=runtimeClasspath,testRuntimeClasspath
com.adobe.xmp:xmpcore:6.1.11=runtimeClasspath,testRuntimeClasspath
com.auth0:java-jwt:4.5.0=runtimeClasspath,testRuntimeClasspath
com.drewnoakes:metadata-extractor:2.19.0=runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson.core:jackson-annotations:2.20=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.fasterxml.jackson.core:jackson-core:2.20.0=runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson.core:jackson-databind:2.20.0=runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.20.0=runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.20.0=runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.20.0=runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.20.0=runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson.module:jackson-module-parameter-names:2.20.0=runtimeClasspath,testRuntimeClasspath
com.fasterxml.jackson:jackson-bom:2.20.0=runtimeClasspath,testRuntimeClasspath
com.fasterxml.uuid:java-uuid-generator:5.1.0=runtimeClasspath,testRuntimeClasspath
com.fasterxml.woodstox:woodstox-core:7.1.1=runtimeClasspath,testRuntimeClasspath
com.fasterxml:classmate:1.5.1=runtimeClasspath,testRuntimeClasspath
com.github.kagkarlsson:db-scheduler-spring-boot-starter:16.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.github.kagkarlsson:db-scheduler:16.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
com.github.stephenc.jcip:jcip-annotations:1.0-1=runtimeClasspath,testRuntimeClasspath
com.google.code.findbugs:jsr305:3.0.2=checkstyle
com.google.errorprone:error_prone_annotations:2.28.0=checkstyle
com.google.errorprone:error_prone_annotations:2.36.0=runtimeClasspath,testRuntimeClasspath
com.google.guava:failureaccess:1.0.2=checkstyle
com.google.guava:failureaccess:1.0.3=runtimeClasspath,testRuntimeClasspath
com.google.guava:guava:33.3.1-jre=checkstyle
com.google.guava:guava:33.4.8-jre=runtimeClasspath,testRuntimeClasspath
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava=checkstyle,runtimeClasspath,testRuntimeClasspath
com.google.j2objc:j2objc-annotations:3.0.0=checkstyle,runtimeClasspath,testRuntimeClasspath
com.googlecode.libphonenumber:libphonenumber:8.13.55=runtimeClasspath,testRuntimeClasspath
com.itextpdf:barcodes:9.3.0=testRuntimeClasspath
com.itextpdf:bouncy-castle-connector:9.3.0=testRuntimeClasspath
com.itextpdf:commons:9.3.0=testRuntimeClasspath
com.itextpdf:font-asian:9.3.0=testRuntimeClasspath
com.itextpdf:forms:9.3.0=testRuntimeClasspath
com.itextpdf:hyph:9.3.0=testRuntimeClasspath
com.itextpdf:io:9.3.0=testRuntimeClasspath
com.itextpdf:itext-core:9.3.0=testRuntimeClasspath
com.itextpdf:kernel:9.3.0=testRuntimeClasspath
com.itextpdf:layout:9.3.0=testRuntimeClasspath
com.itextpdf:pdfa:9.3.0=testRuntimeClasspath
com.itextpdf:pdfua:9.3.0=testRuntimeClasspath
com.itextpdf:sign:9.3.0=testRuntimeClasspath
com.itextpdf:styled-xml-parser:9.3.0=testRuntimeClasspath
com.itextpdf:svg:9.3.0=testRuntimeClasspath
com.nimbusds:content-type:2.3=runtimeClasspath,testRuntimeClasspath
com.nimbusds:lang-tag:1.7=runtimeClasspath,testRuntimeClasspath
com.nimbusds:nimbus-jose-jwt:10.4.2=runtimeClasspath,testRuntimeClasspath
com.nimbusds:oauth2-oidc-sdk:11.28=runtimeClasspath,testRuntimeClasspath
com.opencsv:opencsv:5.12.0=runtimeClasspath,testRuntimeClasspath
com.puppycrawl.tools:checkstyle:10.19.0=checkstyle
com.sun.istack:istack-commons-runtime:4.1.1=runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.common:common-image:3.11.0=runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.common:common-io:3.11.0=runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.common:common-lang:3.11.0=runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.imageio:imageio-core:3.11.0=runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.imageio:imageio-metadata:3.11.0=runtimeClasspath,testRuntimeClasspath
com.twelvemonkeys.imageio:imageio-webp:3.11.0=runtimeClasspath,testRuntimeClasspath
com.zaxxer:HikariCP:6.3.3=runtimeClasspath,testRuntimeClasspath
com.zaxxer:SparseBitSet:1.3=runtimeClasspath,testRuntimeClasspath
commons-beanutils:commons-beanutils:1.11.0=runtimeClasspath,testRuntimeClasspath
commons-beanutils:commons-beanutils:1.9.4=checkstyle
commons-codec:commons-codec:1.15=checkstyle
commons-codec:commons-codec:1.18.0=runtimeClasspath,testRuntimeClasspath
commons-collections:commons-collections:3.2.2=checkstyle,runtimeClasspath,testRuntimeClasspath
commons-fileupload:commons-fileupload:1.5=runtimeClasspath,testRuntimeClasspath
commons-io:commons-io:2.20.0=runtimeClasspath,testRuntimeClasspath
commons-logging:commons-logging:1.3.5=runtimeClasspath,testRuntimeClasspath
commons-net:commons-net:3.12.0=runtimeClasspath,testRuntimeClasspath
info.picocli:picocli:4.7.6=checkstyle
io.github.openfeign:feign-core:13.6=runtimeClasspath,testRuntimeClasspath
io.github.openfeign:feign-form-spring:13.6=runtimeClasspath,testRuntimeClasspath
io.github.openfeign:feign-form:13.6=runtimeClasspath,testRuntimeClasspath
io.github.openfeign:feign-jackson:13.6=runtimeClasspath,testRuntimeClasspath
io.github.openfeign:feign-slf4j:13.6=runtimeClasspath,testRuntimeClasspath
io.micrometer:micrometer-commons:1.14.11=compileClasspath,testCompileClasspath
io.micrometer:micrometer-commons:1.15.2=runtimeClasspath,testRuntimeClasspath
io.micrometer:micrometer-observation:1.14.11=compileClasspath,testCompileClasspath
io.micrometer:micrometer-observation:1.15.2=runtimeClasspath,testRuntimeClasspath
io.projectreactor:reactor-core:3.7.8=runtimeClasspath,testRuntimeClasspath
io.smallrye:jandex:3.1.2=runtimeClasspath,testRuntimeClasspath
io.swagger.core.v3:swagger-annotations-jakarta:2.2.36=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
io.swagger.core.v3:swagger-core-jakarta:2.2.36=runtimeClasspath,testRuntimeClasspath
io.swagger.core.v3:swagger-models-jakarta:2.2.36=runtimeClasspath,testRuntimeClasspath
jakarta.activation:jakarta.activation-api:2.1.0=compileClasspath,testCompileClasspath
jakarta.activation:jakarta.activation-api:2.1.3=runtimeClasspath,testRuntimeClasspath
jakarta.annotation:jakarta.annotation-api:2.1.1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.authentication:jakarta.authentication-api:3.0.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.authorization:jakarta.authorization-api:2.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.batch:jakarta.batch-api:2.1.1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.ejb:jakarta.ejb-api:4.0.1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.el:jakarta.el-api:5.0.1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.enterprise:jakarta.enterprise.cdi-api:4.0.1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.enterprise:jakarta.enterprise.lang-model:4.0.1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.faces:jakarta.faces-api:4.0.1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.inject:jakarta.inject-api:2.0.1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.interceptor:jakarta.interceptor-api:2.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.jms:jakarta.jms-api:3.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.json.bind:jakarta.json.bind-api:3.0.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.json:jakarta.json-api:2.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.mail:jakarta.mail-api:2.1.0=compileClasspath,testCompileClasspath
jakarta.mail:jakarta.mail-api:2.1.4=runtimeClasspath,testRuntimeClasspath
jakarta.persistence:jakarta.persistence-api:3.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.platform:jakarta.jakartaee-api:10.0.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.platform:jakarta.jakartaee-web-api:10.0.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.resource:jakarta.resource-api:2.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.security.enterprise:jakarta.security.enterprise-api:3.0.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.servlet.jsp.jstl:jakarta.servlet.jsp.jstl-api:3.0.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.servlet.jsp:jakarta.servlet.jsp-api:3.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.servlet:jakarta.servlet-api:6.0.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.transaction:jakarta.transaction-api:2.0.1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.validation:jakarta.validation-api:3.1.1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.websocket:jakarta.websocket-api:2.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.websocket:jakarta.websocket-client-api:2.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.ws.rs:jakarta.ws.rs-api:3.1.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
jakarta.xml.bind:jakarta.xml.bind-api:4.0.0=runtimeClasspath,testRuntimeClasspath
javax.annotation:javax.annotation-api:1.3.2=runtimeClasspath,testRuntimeClasspath
jline:jline:2.14.6=runtimeClasspath,testRuntimeClasspath
net.bytebuddy:byte-buddy-agent:1.17.6=testCompileClasspath,testRuntimeClasspath
net.bytebuddy:byte-buddy:1.14.15=runtimeClasspath
net.bytebuddy:byte-buddy:1.17.6=testCompileClasspath,testRuntimeClasspath
net.minidev:accessors-smart:2.5.2=runtimeClasspath,testRuntimeClasspath
net.minidev:json-smart:2.5.2=runtimeClasspath,testRuntimeClasspath
net.sf.saxon:Saxon-HE:12.5=checkstyle
org.antlr:antlr4-runtime:4.13.0=runtimeClasspath,testRuntimeClasspath
org.antlr:antlr4-runtime:4.13.2=checkstyle
org.apache.commons:commons-collections4:4.5.0=runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-imaging:1.0.0-alpha6=runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-lang3:3.18.0=runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.commons:commons-lang3:3.8.1=checkstyle
org.apache.commons:commons-math3:3.6.1=runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-text:1.14.0=runtimeClasspath,testRuntimeClasspath
org.apache.commons:commons-text:1.3=checkstyle
org.apache.httpcomponents.client5:httpclient5:5.1.3=checkstyle
org.apache.httpcomponents.client5:httpclient5:5.5=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.httpcomponents.core5:httpcore5-h2:5.1.3=checkstyle
org.apache.httpcomponents.core5:httpcore5-h2:5.3.4=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.httpcomponents.core5:httpcore5:5.1.3=checkstyle
org.apache.httpcomponents.core5:httpcore5:5.3.4=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.apache.httpcomponents:httpclient:4.5.13=checkstyle
org.apache.httpcomponents:httpcore:4.4.14=checkstyle
org.apache.logging.log4j:log4j-api:2.24.3=runtimeClasspath,testRuntimeClasspath
org.apache.logging.log4j:log4j-to-slf4j:2.24.3=runtimeClasspath,testRuntimeClasspath
org.apache.maven.doxia:doxia-core:1.12.0=checkstyle
org.apache.maven.doxia:doxia-logging-api:1.12.0=checkstyle
org.apache.maven.doxia:doxia-module-xdoc:1.12.0=checkstyle
org.apache.maven.doxia:doxia-sink-api:1.12.0=checkstyle
org.apache.pdfbox:fontbox:3.0.5=testRuntimeClasspath
org.apache.pdfbox:pdfbox-io:3.0.5=testRuntimeClasspath
org.apache.pdfbox:pdfbox:3.0.5=testRuntimeClasspath
org.apache.poi:poi:5.4.1=runtimeClasspath,testRuntimeClasspath
org.apache.santuario:xmlsec:3.0.6=testRuntimeClasspath
org.apache.tomcat.embed:tomcat-embed-el:10.1.43=runtimeClasspath,testRuntimeClasspath
org.apache.xbean:xbean-reflect:3.7=checkstyle
org.apiguardian:apiguardian-api:1.1.2=testCompileClasspath
org.aspectj:aspectjweaver:1.9.24=runtimeClasspath,testRuntimeClasspath
org.beryx:awt-color-factory:1.0.1=runtimeClasspath,testRuntimeClasspath
org.beryx:text-io:3.4.1=runtimeClasspath,testRuntimeClasspath
org.bouncycastle:bcpkix-jdk18on:1.72=runtimeClasspath,testRuntimeClasspath
org.bouncycastle:bcprov-jdk18on:1.72=runtimeClasspath,testRuntimeClasspath
org.bouncycastle:bcutil-jdk18on:1.72=runtimeClasspath,testRuntimeClasspath
org.checkerframework:checker-qual:3.48.1=checkstyle
org.checkerframework:checker-qual:3.49.3=runtimeClasspath,testRuntimeClasspath
org.codehaus.plexus:plexus-classworlds:2.6.0=checkstyle
org.codehaus.plexus:plexus-component-annotations:2.1.0=checkstyle
org.codehaus.plexus:plexus-container-default:2.1.0=checkstyle
org.codehaus.plexus:plexus-utils:3.3.0=checkstyle
org.codehaus.woodstox:stax2-api:4.2.2=runtimeClasspath,testRuntimeClasspath
org.eclipse.angus:angus-activation:2.0.0=runtimeClasspath,testRuntimeClasspath
org.firebirdsql.***********************************,testRuntimeClasspath
org.glassfish.jaxb:jaxb-core:4.0.2=runtimeClasspath,testRuntimeClasspath
org.glassfish.jaxb:jaxb-runtime:4.0.2=runtimeClasspath,testRuntimeClasspath
org.glassfish.jaxb:txw2:4.0.2=runtimeClasspath,testRuntimeClasspath
org.hibernate.common:hibernate-commons-annotations:6.0.6.Final=runtimeClasspath,testRuntimeClasspath
org.hibernate.javax.persistence:hibernate-jpa-2.1-api:1.0.2.Final=runtimeClasspath,testRuntimeClasspath
org.hibernate.orm:hibernate-core:6.5.3.Final=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.hibernate.validator:hibernate-validator:8.0.2.Final=runtimeClasspath,testRuntimeClasspath
org.imgscalr:imgscalr-lib:4.2=runtimeClasspath,testRuntimeClasspath
org.javassist:javassist:3.28.0-GA=checkstyle
org.jboss.logging:jboss-logging:3.5.0.Final=runtimeClasspath,testRuntimeClasspath
org.jdom:jdom2:*******=runtimeClasspath,testRuntimeClasspath
org.jetbrains.kotlin:kotlin-stdlib:2.2.10=testRuntimeClasspath
org.jetbrains:annotations:13.0=testRuntimeClasspath
org.jspecify:jspecify:1.0.0=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.junit.jupiter:junit-jupiter-api:6.0.0-RC3=testCompileClasspath,testRuntimeClasspath
org.junit.jupiter:junit-jupiter-engine:6.0.0-RC3=testRuntimeClasspath
org.junit.jupiter:junit-jupiter-params:6.0.0-RC3=testCompileClasspath,testRuntimeClasspath
org.junit.jupiter:junit-jupiter:6.0.0-RC3=testCompileClasspath,testRuntimeClasspath
org.junit.platform:junit-platform-commons:6.0.0-RC3=testCompileClasspath,testRuntimeClasspath
org.junit.platform:junit-platform-engine:6.0.0-RC3=testRuntimeClasspath
org.junit.platform:junit-platform-launcher:6.0.0-RC3=testRuntimeClasspath
org.junit:junit-bom:6.0.0-RC3=testCompileClasspath,testRuntimeClasspath
org.mapstruct:mapstruct:1.6.3=runtimeClasspath,testRuntimeClasspath
org.mockito:mockito-core:5.19.0=testCompileClasspath,testRuntimeClasspath
org.objenesis:objenesis:3.3=testRuntimeClasspath
org.opentest4j:opentest4j:1.3.0=testCompileClasspath,testRuntimeClasspath
org.ow2.asm:asm:9.7.1=runtimeClasspath,testRuntimeClasspath
org.postgresql:postgresql:42.7.7=runtimeClasspath,testRuntimeClasspath
org.projectlombok:lombok:1.18.40=annotationProcessor,compileClasspath
org.reactivestreams:reactive-streams:1.0.4=runtimeClasspath,testRuntimeClasspath
org.reflections:reflections:0.10.2=checkstyle
org.slf4j:jul-to-slf4j:2.0.17=runtimeClasspath
org.slf4j:jul-to-slf4j:2.1.0-alpha1=testRuntimeClasspath
org.slf4j:log4j-over-slf4j:2.1.0-alpha1=testRuntimeClasspath
org.slf4j:slf4j-api:2.1.0-alpha1=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springdoc:springdoc-openapi-starter-common:2.8.10=runtimeClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-actuator:3.4.5=compileClasspath,testCompileClasspath
org.springframework.boot:spring-boot-actuator:3.5.5=runtimeClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-autoconfigure:3.5.4=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-aop:3.5.5=runtimeClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-logging:3.5.5=runtimeClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter-validation:3.5.4=runtimeClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot-starter:3.5.5=runtimeClasspath,testRuntimeClasspath
org.springframework.boot:spring-boot:3.5.4=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-commons:4.3.0=runtimeClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-context:4.3.0=runtimeClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-openfeign-core:4.3.0=runtimeClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-starter-openfeign:4.3.0=runtimeClasspath,testRuntimeClasspath
org.springframework.cloud:spring-cloud-starter:4.3.0=runtimeClasspath,testRuntimeClasspath
org.springframework.data:spring-data-commons:3.5.4=runtimeClasspath,testRuntimeClasspath
org.springframework.data:spring-data-jpa:3.5.4=runtimeClasspath,testRuntimeClasspath
org.springframework.integration:spring-integration-core:6.5.1=runtimeClasspath,testRuntimeClasspath
org.springframework.retry:spring-retry:2.0.12=runtimeClasspath,testRuntimeClasspath
org.springframework.security:spring-security-config:6.5.4=runtimeClasspath,testRuntimeClasspath
org.springframework.security:spring-security-core:6.5.4=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.security:spring-security-crypto:6.5.4=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework.security:spring-security-oauth2-core:6.5.4=runtimeClasspath,testRuntimeClasspath
org.springframework.security:spring-security-oauth2-jose:6.5.4=runtimeClasspath,testRuntimeClasspath
org.springframework.security:spring-security-web:6.5.4=runtimeClasspath,testRuntimeClasspath
org.springframework:spring-aop:6.2.11=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-beans:6.2.11=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-context-support:6.2.11=runtimeClasspath,testRuntimeClasspath
org.springframework:spring-context:6.2.11=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-core:6.2.11=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-expression:6.2.11=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-jcl:6.2.11=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-jdbc:6.2.11=runtimeClasspath,testRuntimeClasspath
org.springframework:spring-jdbc:6.2.6=compileClasspath,testCompileClasspath
org.springframework:spring-messaging:6.2.9=runtimeClasspath,testRuntimeClasspath
org.springframework:spring-orm:6.2.11=runtimeClasspath,testRuntimeClasspath
org.springframework:spring-tx:6.2.11=runtimeClasspath,testRuntimeClasspath
org.springframework:spring-tx:6.2.6=compileClasspath,testCompileClasspath
org.springframework:spring-web:6.2.11=compileClasspath,runtimeClasspath,testCompileClasspath,testRuntimeClasspath
org.springframework:spring-webmvc:6.2.11=runtimeClasspath,testRuntimeClasspath
org.xmlresolver:xmlresolver:5.2.2=checkstyle
org.yaml:snakeyaml:2.4=runtimeClasspath,testRuntimeClasspath
empty=testAnnotationProcessor
