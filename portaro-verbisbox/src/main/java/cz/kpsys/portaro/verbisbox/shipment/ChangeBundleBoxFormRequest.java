package cz.kpsys.portaro.verbisbox.shipment;


import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledId;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.ConfirmableRequest;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.IntegrityValidation;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.singleacceptable.SingleAcceptableEditor;
import cz.kpsys.portaro.formconfig.valueeditor.AcceptableValuesResolver;
import cz.kpsys.portaro.verbisboxer.manager.ContextualVerbisboxerManagerApiClient;
import cz.kpsys.portaro.verbisboxer.manager.layout.PhysicalBoxResponse;
import cz.kpsys.portaro.verbisboxer.manager.layout.StationLayoutResponse;
import cz.kpsys.portaro.verbisboxer.manager.state.BoxResponse;
import cz.kpsys.portaro.verbisboxer.manager.state.StationStateResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.UUID;

@Form(id = "changeBundleBoxForm", title = "{verbisbox.bundle.ChangeBundleBox}")
@FormSubmit(path = "/api/box-stations/#{formObject.stationId}/bundles/change-box")
@With
public record ChangeBundleBoxFormRequest(
        Boolean confirmed,

        @Schema(description = "Id of station")
        @NotNull(groups = IntegrityValidation.class)
        UUID stationId,

        @Schema(description = "Id of bundle to change box for")
        @NotNull(groups = IntegrityValidation.class)
        UUID bundleId,

        @Schema(description = "Id of box to change to. If null, items will be moved to the random box.")
        @FormPropertyLabel("{verbisbox.Box}")
        @Nullable
        @SingleAcceptableEditor(valuesSourceBean = "changeBundleBoxFormBoxResolver")
        UUID boxId

) implements ConfirmableRequest<ChangeBundleBoxFormRequest> {

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class ChangeBundleBoxFormBoxResolver implements AcceptableValuesResolver<ChangeBundleBoxFormRequest, LabeledId<UUID>> {

        @NonNull ContextualVerbisboxerManagerApiClient<Department> contextualVerbisboxerManagerApiClient;

        @Override
        public List<LabeledId<UUID>> resolveAcceptableValues(ChangeBundleBoxFormRequest formObject, Department ctx) {
            StationLayoutResponse stationLayout = contextualVerbisboxerManagerApiClient.getStationLayout(ctx, formObject.stationId());
            StationStateResponse stationState = contextualVerbisboxerManagerApiClient.getStationState(ctx, formObject.stationId());
            List<UUID> availableBoxes = stationState.boxes().stream()
                    .filter(boxResponse -> boxResponse.assignDate() == null)
                    .map(BoxResponse::id)
                    .toList();

            return stationLayout.boxes().stream()
                    .filter(box -> availableBoxes.contains(box.id()))
                    .map(box -> new LabeledId<>(box.id(), getBoxLabel(box)))
                    .toList();
        }

        private Text getBoxLabel(PhysicalBoxResponse box) {
            return MultiText.ofTexts("{} - {}", Texts.ofNative(box.label()), getBoxSizeResponse(box));
        }

        private Text getBoxSizeResponse(PhysicalBoxResponse box) {
            if (box.size().height() <= 230) {
                return Texts.ofMessageCodedOrNative("verbisbox.BoxSmall");
            } else {
                return Texts.ofMessageCodedOrNative("verbisbox.BoxLarge");
            }
        }
    }
}
