package cz.kpsys.portaro.verbisbox.shipment;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.verbisboxer.manager.listener.UserInsightResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.Nullable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ToViewableUserConverter implements Converter<UserInsightResponse, ViewableUser> {

    @NonNull ByIdLoadable<BasicUser, Integer> basicUserLoader;

    @Nullable
    @Override
    public ViewableUser convert(@Nullable UserInsightResponse user) {
        if (user == null) {
            return null;
        }
        try {
            BasicUser basic = basicUserLoader.getById(Integer.parseInt(user.externalId()));
            return new ViewableUser(basic.getId(), Texts.ofNative(user.name()));
        } catch (ItemNotFoundException e) {
            return new ViewableUser(null, Texts.ofNative(user.name()));
        }
    }
}
