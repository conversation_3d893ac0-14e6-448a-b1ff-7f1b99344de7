package cz.kpsys.portaro.verbisbox.station;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.localization.ContextualLocaleLocalizer;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.phonenumber.PhoneNumberValidator;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.contact.ContactManager;
import cz.kpsys.portaro.user.contact.Email;
import cz.kpsys.portaro.verbisbox.VerbisboxSecurityActions;
import cz.kpsys.portaro.verbisbox.serviceaccess.ServiceAccessRequest;
import cz.kpsys.portaro.verbisbox.shipment.ChangeBundleBoxFormRequest;
import cz.kpsys.portaro.verbisbox.shipment.ItemsMoveFormRequest;
import cz.kpsys.portaro.verbisbox.shipment.ViewableShipmentInsight;
import cz.kpsys.portaro.verbisbox.state.ViewableStationState;
import cz.kpsys.portaro.verbisboxer.manager.ContactRequest;
import cz.kpsys.portaro.verbisboxer.manager.ContactType;
import cz.kpsys.portaro.verbisboxer.manager.ContextualVerbisboxerManagerApiClient;
import cz.kpsys.portaro.verbisboxer.manager.UserRequest;
import cz.kpsys.portaro.verbisboxer.manager.layout.StationLayoutResponse;
import cz.kpsys.portaro.verbisboxer.manager.listener.ShipmentInsightResponse;
import cz.kpsys.portaro.verbisboxer.manager.serviceaccess.ServiceAccessCreationRequest;
import cz.kpsys.portaro.verbisboxer.manager.shipment.ChangeBundleBoxRequest;
import cz.kpsys.portaro.verbisboxer.manager.shipment.ItemsMoveRequest;
import cz.kpsys.portaro.verbisboxer.manager.shipment.ShipmentSearchRequest;
import cz.kpsys.portaro.verbisboxer.manager.state.StationStateResponse;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Tag(name = "box-station", description = "Endpoints for managing stations (verbisboxes)")
@RequestMapping("/api/box-stations")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BoxStationApiController extends GenericApiController {

    @NonNull Codebook<BoxStation, UUID> boxStationCodebook;
    @NonNull ContextualVerbisboxerManagerApiClient<Department> contextualVerbisboxerManagerApiClient;
    @NonNull Converter<StationStateResponse, ViewableStationState> toViewableStationStateConverter;
    @NonNull ContextualFunction<ShipmentInsightResponse, Department, ViewableShipmentInsight> toViewableShipmentInsightConverter;
    @NonNull SecurityManager securityManager;
    @NonNull ContextualLocaleLocalizer<Department> localizer;
    @NonNull ContactManager contactManager;
    @NonNull PhoneNumberValidator phoneNumberValidator;


    @GetMapping
    public List<BoxStation> getStations(@CurrentDepartment Department ctx,
                                        UserAuthentication currentAuth) {
        var stations = boxStationCodebook.getAll();
        return ListUtil.filter(stations, station -> securityManager.can(VerbisboxSecurityActions.BOX_STATION_SHOW, currentAuth, ctx, station));
    }

    @GetMapping("{id}")
    public BoxStation getById(@PathVariable("id") UUID id,
                              @CurrentDepartment Department ctx,
                              UserAuthentication currentAuth) {
        var station = boxStationCodebook.getById(id);
        securityManager.throwIfCannot(VerbisboxSecurityActions.BOX_STATION_SHOW, currentAuth, ctx, station);
        return station;
    }

    @GetMapping("/{id}/layout")
    public StationLayoutResponse getStationLayout(@PathVariable("id") UUID stationId,
                                                  @CurrentDepartment Department ctx,
                                                  UserAuthentication currentAuth) {
        var station = boxStationCodebook.getById(stationId);
        securityManager.throwIfCannot(VerbisboxSecurityActions.BOX_STATION_SHOW, currentAuth, ctx, station);
        return contextualVerbisboxerManagerApiClient.getStationLayout(ctx, station.getId());
    }

    @GetMapping("/{id}/state")
    public ViewableStationState getStationState(@PathVariable("id") UUID stationId,
                                                @CurrentDepartment Department ctx,
                                                UserAuthentication currentAuth) {
        var station = boxStationCodebook.getById(stationId);
        securityManager.throwIfCannot(VerbisboxSecurityActions.BOX_STATION_SHOW, currentAuth, ctx, station);
        var stationStateResponse = contextualVerbisboxerManagerApiClient.getStationState(ctx, station.getId());
        return toViewableStationStateConverter.convert(stationStateResponse);
    }

    @GetMapping("/{id}/shipment/active")
    public List<ViewableShipmentInsight> getStationShipment(@PathVariable("id") UUID stationId,
                                                            @CurrentDepartment Department currentDepartment,
                                                            UserAuthentication currentAuth) {
        var station = boxStationCodebook.getById(stationId);
        securityManager.throwIfCannot(VerbisboxSecurityActions.BOX_STATION_SHOW, currentAuth, currentDepartment, station);
        return contextualVerbisboxerManagerApiClient.searchShipmentInfo(currentDepartment, new ShipmentSearchRequest(station.getId())).stream().map(response -> toViewableShipmentInsightConverter.getOn(response, currentDepartment)).toList();
    }


    @Operation(summary = "Move item to different bundle")
    @PostMapping("/{id}/items/move")
    public ActionResponse moveToNewBundle(@PathVariable("id") UUID stationId,
                                          @RequestBody @ValidFormObject ItemsMoveFormRequest request,
                                          @CurrentDepartment Department currentDepartment,
                                          UserAuthentication currentAuth) {
        var station = boxStationCodebook.getById(stationId);
        securityManager.throwIfCannot(VerbisboxSecurityActions.BOX_STATION_SHOW, currentAuth, currentDepartment, station);
        contextualVerbisboxerManagerApiClient.moveToNewBundle(currentDepartment, new ItemsMoveRequest(List.of(request.itemId()), request.newBundle() ? null : request.bundleId()));
        return FinishedActionResponse.ok();
    }

    @Operation(summary = "Change box of the bundle to different box.")
    @PostMapping("/{id}/bundles/change-box")
    public ActionResponse changeBoxOfBundle(@PathVariable("id") UUID stationId,
                                            @RequestBody @ValidFormObject ChangeBundleBoxFormRequest request,
                                            @CurrentDepartment Department currentDepartment,
                                            UserAuthentication currentAuth) {
        var station = boxStationCodebook.getById(stationId);
        securityManager.throwIfCannot(VerbisboxSecurityActions.BOX_STATION_SHOW, currentAuth, currentDepartment, station);
        contextualVerbisboxerManagerApiClient.changeBoxOfBundle(currentDepartment, new ChangeBundleBoxRequest(request.bundleId(), request.boxId()));
        return FinishedActionResponse.ok();
    }

    @PostMapping("/{id}/service-access")
    public FinishedActionResponse createServiceAccess(@PathVariable("id") UUID stationId,
                                                      @RequestBody @ValidFormObject ServiceAccessRequest request,
                                                      UserAuthentication currentAuth,
                                                      @CurrentDepartment Department ctx) {
        var station = boxStationCodebook.getById(stationId);
        securityManager.throwIfCannot(VerbisboxSecurityActions.BOX_STATION_SHOW, currentAuth, ctx, station);
        Set<ContactRequest> contacts = getContacts(currentAuth.getActiveUser());
        Assert.state(!contacts.isEmpty(), "Uživatel musí mít alespoň jeden kontakt, aby mohl vytvořit servisní přístup.");

        var response = contextualVerbisboxerManagerApiClient.createServiceAccess(ctx, new ServiceAccessCreationRequest(
                UuidGenerator.forIdentifier(),
                station.getId(),
                new UserRequest(
                        currentAuth.getActiveUser().getId().toString(),
                        localizer.localize(currentAuth.getActiveUser().getText(), ctx),
                        contacts
                ),
                request.picking(),
                Set.copyOf(request.boxes())
        ));

        return new FinishedActionResponse(Texts.ofNative("PIN: " + response.pin()));
    }

    private @NonNull Set<ContactRequest> getContacts(BasicUser user) {
        List<Email> allEmails = contactManager.getAllEmails(user);
        List<String> allSmsPhones = contactManager.getAllSmsPhones(user);
        Set<ContactRequest> contacts = new HashSet<>();
        contacts.addAll(allEmails.stream().map(Email::value).map(email -> new ContactRequest(ContactType.EMAIL, email)).toList());
        contacts.addAll(allSmsPhones.stream().map(phoneNumber -> new ContactRequest(ContactType.PHONE_NUMBER, phoneNumberValidator.toE164(phoneNumber))).toList());
        return contacts;
    }
}
