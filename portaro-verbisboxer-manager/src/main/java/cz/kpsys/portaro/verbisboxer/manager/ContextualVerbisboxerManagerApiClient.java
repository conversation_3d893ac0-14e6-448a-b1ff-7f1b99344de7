package cz.kpsys.portaro.verbisboxer.manager;

import cz.kpsys.portaro.verbisboxer.manager.enablement.StationEnablementSetRequest;
import cz.kpsys.portaro.verbisboxer.manager.layout.StationLayoutResponse;
import cz.kpsys.portaro.verbisboxer.manager.listener.ShipmentInsightResponse;
import cz.kpsys.portaro.verbisboxer.manager.listener.ItemInsightResponse;
import cz.kpsys.portaro.verbisboxer.manager.serviceaccess.ServiceAccessCreationRequest;
import cz.kpsys.portaro.verbisboxer.manager.serviceaccess.ServiceAccessCreationResponse;
import cz.kpsys.portaro.verbisboxer.manager.shipment.*;
import cz.kpsys.portaro.verbisboxer.manager.state.StationStateResponse;

import java.util.List;
import java.util.UUID;

public interface ContextualVerbisboxerManagerApiClient<CTX> {

    StationLayoutResponse getStationLayout(CTX ctx, UUID stationId);

    StationStateResponse getStationState(CTX ctx, UUID stationId);

    void setStationEnablement(CTX ctx, UUID stationId, StationEnablementSetRequest request);

    ItemInsightResponse getItem(CTX ctx, UUID itemId);

    void cancelItem(CTX ctx, ShipmentItemCancellationRequest request);

    void createShipment(CTX ctx, ShipmentCreationRequest request);

    void setItemIdentifiers(CTX ctx, ShipmentItemIdentifiersCreationRequest request);

    ServiceAccessCreationResponse createServiceAccess(CTX ctx, ServiceAccessCreationRequest request);

    List<ShipmentInsightResponse> searchShipmentInfo(CTX ctx, ShipmentSearchRequest request);

    void changeBoxOfBundle(CTX ctx, ChangeBundleBoxRequest request);

    void moveToNewBundle(CTX ctx, ItemsMoveRequest request);

}
