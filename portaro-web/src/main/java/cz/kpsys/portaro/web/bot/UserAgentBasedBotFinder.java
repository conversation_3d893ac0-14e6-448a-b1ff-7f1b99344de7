package cz.kpsys.portaro.web.bot;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.matcher.Matcher;
import cz.kpsys.portaro.web.client.WebClientRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserAgentBasedBotFinder implements BotProbabilityProvider {

    @NonNull List<? extends Matcher<String>> anonymousBotUserAgentMatchers;

    @Override
    public double getHumanProbability(@NonNull WebClientRequest clientRequest) {
        if (StringUtil.isNullOrEmpty(clientRequest.getUserAgent())) {
            return -0.6;
        }
        if (isBotUserAgent(clientRequest.getUserAgent())) {
            return -1;
        }
        return 0.2;
    }


    public boolean isBotUserAgent(@NonNull String userAgent) {
        return anonymousBotUserAgentMatchers.stream()
                .anyMatch(matcher -> matcher.matches(userAgent));
    }
}
