package cz.kpsys.portaro.web.server;

import cz.kpsys.portaro.web.UrlWebResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebArgumentResolver;
import org.springframework.web.context.request.NativeWebRequest;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ServerUrlWebArgumentResolver implements WebArgumentResolver {

    @NonNull UrlWebResolver serverUrlWebResolver;

    @Override
    public Object resolveArgument(MethodParameter param, @NonNull NativeWebRequest request) {
        if (param.getParameterAnnotation(ServerUrl.class) != null) {
            HttpServletRequest httpServletRequest = Objects.requireNonNull(request.getNativeRequest(HttpServletRequest.class));
            return serverUrlWebResolver.resolve(httpServletRequest);
        }
        return WebArgumentResolver.UNRESOLVED;
    }
}
